declare namespace Eps {
	interface BaseSysDepartmentEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 部门名称
		 */
		name?: string;

		/**
		 * 创建者ID
		 */
		userId?: number;

		/**
		 * 上级部门ID
		 */
		parentId?: number;

		/**
		 * 排序
		 */
		orderNum?: number;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysLogEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 用户ID
		 */
		userId?: number;

		/**
		 * 行为
		 */
		action?: string;

		/**
		 * ip
		 */
		ip?: string;

		/**
		 * 参数
		 */
		params?: json;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 姓名
		 */
		name?: string;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysMenuEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 父菜单ID
		 */
		parentId?: number;

		/**
		 * 菜单名称
		 */
		name?: string;

		/**
		 * 菜单地址
		 */
		router?: string;

		/**
		 * 权限标识
		 */
		perms?: string;

		/**
		 * 类型 0-目录 1-菜单 2-按钮
		 */
		type?: number;

		/**
		 * 图标
		 */
		icon?: string;

		/**
		 * 排序
		 */
		orderNum?: number;

		/**
		 * 视图地址
		 */
		viewPath?: string;

		/**
		 * 路由缓存
		 */
		keepAlive?: boolean;

		/**
		 * 是否显示
		 */
		isShow?: boolean;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysParamEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 键
		 */
		keyName?: string;

		/**
		 * 名称
		 */
		name?: string;

		/**
		 * 数据
		 */
		data?: string;

		/**
		 * 数据类型 0-字符串 1-富文本 2-文件
		 */
		dataType?: number;

		/**
		 * 备注
		 */
		remark?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysRoleEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 用户ID
		 */
		userId?: string;

		/**
		 * 名称
		 */
		name?: string;

		/**
		 * 角色标签
		 */
		label?: string;

		/**
		 * 角色类型
		 */
		type?: string;

		/**
		 * 备注
		 */
		remark?: string;

		/**
		 * 数据权限是否关联上下级
		 */
		relevance?: boolean;

		/**
		 * 菜单权限
		 */
		menuIdList?: json;

		/**
		 * 部门权限
		 */
		departmentIdList?: json;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysUserEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 部门ID
		 */
		departmentId?: number;

		/**
		 * 创建者ID
		 */
		userId?: number;

		/**
		 * 姓名
		 */
		name?: string;

		/**
		 * 用户名
		 */
		username?: string;

		/**
		 * 铁建通ID
		 */
		subId?: string;

		/**
		 * 密码
		 */
		password?: string;

		/**
		 * 密码版本, 作用是改完密码，让原来的token失效
		 */
		passwordV?: number;

		/**
		 * 昵称
		 */
		nickName?: string;

		/**
		 * 头像
		 */
		headImg?: string;

		/**
		 * 手机
		 */
		phone?: string;

		/**
		 * 邮箱
		 */
		email?: string;

		/**
		 * 备注
		 */
		remark?: string;

		/**
		 * 状态 0-禁用 1-启用
		 */
		status?: number;

		/**
		 * socketId
		 */
		socketId?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface DataOperationDescEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * data库表名
		 */
		tableName?: string;

		/**
		 * 操作说明
		 */
		operationDesc?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface CloudDBEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 名称
		 */
		name?: string;

		/**
		 * 说明
		 */
		readme?: string;

		/**
		 * 内容
		 */
		content?: string;

		/**
		 * 列信息
		 */
		colInfo?: json;

		/**
		 * 表信息
		 */
		tableInfo?: json;

		/**
		 * 类名
		 */
		className?: string;

		/**
		 * 操作说明
		 */
		operationDesc?: string;

		/**
		 * 打印配置
		 */
		printConfig?: json;

		/**
		 * 表名
		 */
		tableName?: string;

		/**
		 * 表单类型
		 */
		tableType?: string;

		/**
		 * 表单目录
		 */
		formTypeId?: number;

		/**
		 * 状态 0-禁用 1-启用
		 */
		status?: number;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface UserFavoriteEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 表单id
		 */
		formId?: number;

		/**
		 * 用户id
		 */
		userId?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface FormTypeEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 父节点id
		 */
		parentId?: number;

		/**
		 * 名称
		 */
		name?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface LuruPermissionTableEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 录入人
		 */
		录入人?: string;

		/**
		 * 审核人
		 */
		审核人?: string;

		/**
		 * 审核时间
		 */
		审核时间?: Date;

		/**
		 * 审核状态
		 */
		审核状态?: number;

		/**
		 * 审核说明
		 */
		审核说明?: string;

		/**
		 * 权限所属人
		 */
		权限所属人?: string;

		/**
		 * 权限所属人代码
		 */
		权限所属人代码?: string;

		/**
		 * 权限所属角色
		 */
		权限所属角色?: string;

		/**
		 * 权限所属角色代码
		 */
		权限所属角色代码?: string;

		/**
		 * 权限类型
		 */
		权限类型?: enum;

		/**
		 * 项目代码
		 */
		projectCode?: string;

		/**
		 * 项目名称
		 */
		项目名称?: string;

		/**
		 * 表单权限
		 */
		表单权限?: string;

		/**
		 * 表单权限代码
		 */
		表单权限代码?: string;

		/**
		 * 功能权限
		 */
		功能权限?: string;

		/**
		 * 功能权限代码
		 */
		功能权限代码?: string;

		/**
		 * 备注说明
		 */
		备注?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface DictInfoEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 类型ID
		 */
		typeId?: number;

		/**
		 * 来源
		 */
		source?: string;

		/**
		 * 名称
		 */
		name?: string;

		/**
		 * 值
		 */
		value?: string;

		/**
		 * 排序
		 */
		orderNum?: number;

		/**
		 * 备注
		 */
		remark?: string;

		/**
		 * 父ID
		 */
		parentId?: string;

		/**
		 * 启用状态
		 */
		status?: number;

		/**
		 * 字典类型
		 */
		type?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface DictTypeEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 父节点id
		 */
		parentId?: number;

		/**
		 * 来源
		 */
		source?: string;

		/**
		 * 字典类型
		 */
		type?: string;

		/**
		 * 状态
		 */
		status?: number;

		/**
		 * 名称
		 */
		name?: string;

		/**
		 * 字典加载类型
		 */
		dictLoadType?: string;

		/**
		 * 标识
		 */
		key?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface FlowDefinitionEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 流程名称
		 */
		name?: string;

		/**
		 * 流程版本
		 */
		version?: number;

		/**
		 * 表单id
		 */
		formId?: number;

		/**
		 * 流程管理员
		 */
		managers?: json;

		/**
		 * 流程定义
		 */
		flowJson?: json;

		/**
		 * 流程久悬通知时间/天
		 */
		flowUnDoneNoticeDay?: number;

		/**
		 * 是否激活
		 */
		active?: boolean;

		/**
		 * 流程描述
		 */
		description?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface FlowInstanceEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 流程定义ID
		 */
		definitionId?: number;

		/**
		 * 发起人id
		 */
		initiatorId?: number;

		/**
		 * 发起人
		 */
		initiatorName?: string;

		/**
		 * 业务ID
		 */
		businessId?: number;

		/**
		 * 表单ID
		 */
		formId?: number;

		/**
		 * 当前节点ID
		 */
		currentNodeId?: string;

		/**
		 * 表单数据
		 */
		formData?: json;

		/**
		 * 流程变量
		 */
		variables?: json;

		/**
		 * 状态: running/completed/terminated
		 */
		status?: string;

		/**
		 * 已完成节点
		 */
		completedNodes?: json;

		/**
		 * 当前审批人
		 */
		currentApprovers?: json;

		/**
		 * 审批记录
		 */
		approvalHistory?: json;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface FlowTaskEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 流程实例ID
		 */
		instanceId?: number;

		/**
		 * 节点ID
		 */
		nodeId?: string;

		/**
		 * 候选审批人列表
		 */
		candidates?: json;

		/**
		 * 审批结果: approve/reject/transfer
		 */
		result?: string;

		/**
		 * 状态: pending/completed
		 */
		status?: string;

		/**
		 * 通过比例
		 */
		signRatio?: number;

		/**
		 * 审批结果记录
		 */
		approvalResults?: json;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface MessageGroupEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 组名称
		 */
		name?: string;

		/**
		 * 组描述
		 */
		description?: string;

		/**
		 * 用户IDs
		 */
		userIds?: json;

		/**
		 * 角色IDs
		 */
		roleIds?: json;

		/**
		 * 是否全员组
		 */
		isAllUser?: boolean;

		/**
		 * 创建人ID
		 */
		creatorId?: number;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface NotificationEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 消息类型
		 */
		noticeType?: string;

		/**
		 * 标题
		 */
		title?: string;

		/**
		 * 内容
		 */
		content?: string;

		/**
		 * 消息组ID
		 */
		groupId?: string;

		/**
		 * 更新人
		 */
		updateUser?: string;

		/**
		 * 关联任务ID
		 */
		taskId?: number;

		/**
		 * 关联业务ID
		 */
		businessId?: number;

		/**
		 * 消息有效期，仅当消息类型为公告时有效
		 */
		effectiveTime?: Date;

		/**
		 * 业务数据
		 */
		businessData?: json;

		/**
		 * 状态 0-禁用 1-启用
		 */
		status?: number;

		/**
		 * 备注
		 */
		remark?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface OidcClientEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * client_id
		 */
		clientId?: string;

		/**
		 * client_secret
		 */
		clientSecret?: string;

		/**
		 * 名称
		 */
		name?: string;

		/**
		 * 回调地址
		 */
		redirectUris?: json;

		/**
		 * 授权类型
		 */
		grantTypes?: json;

		/**
		 * 响应类型
		 */
		responseTypes?: json;

		/**
		 * token 端点认证方式
		 */
		tokenEndpointAuthMethod?: string;

		/**
		 * 启用 0-禁用 1-启用
		 */
		status?: number;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PluginInfoEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 名称
		 */
		name?: string;

		/**
		 * 简介
		 */
		description?: string;

		/**
		 * Key名
		 */
		keyName?: string;

		/**
		 * Hook
		 */
		hook?: string;

		/**
		 * 描述
		 */
		readme?: string;

		/**
		 * 版本
		 */
		version?: string;

		/**
		 * Logo(base64)
		 */
		logo?: string;

		/**
		 * 作者
		 */
		author?: string;

		/**
		 * 状态 0-禁用 1-启用
		 */
		status?: number;

		/**
		 * 内容
		 */
		content?: json;

		/**
		 * ts内容
		 */
		tsContent?: json;

		/**
		 * 插件的plugin.json
		 */
		pluginJson?: json;

		/**
		 * 配置
		 */
		config?: json;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface CheckRuleEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 规则名称
		 */
		name?: string;

		/**
		 * 主表ID
		 */
		formId?: string;

		/**
		 * 主表名称
		 */
		formName?: string;

		/**
		 * 检查SQL
		 */
		checkSql?: string;

		/**
		 * 联合主键配置
		 */
		compositeKeyConfig?: json;

		/**
		 * 数据治理通知模板, js模板字符串，需要包含表单名、行id
		 */
		noticeTemplate?: string;

		/**
		 * 处理人参数表
		 */
		handlerQualityConfig?: json;

		/**
		 * 是否启用
		 */
		status?: boolean;

		/**
		 * 发送数据治理时间配置
		 */
		internalConfig?: json;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface TaskInfoEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 任务ID
		 */
		jobId?: string;

		/**
		 * 任务配置
		 */
		repeatConf?: string;

		/**
		 * 名称
		 */
		name?: string;

		/**
		 * cron
		 */
		cron?: string;

		/**
		 * 最大执行次数 不传为无限次
		 */
		limit?: number;

		/**
		 * 每间隔多少毫秒执行一次 如果cron设置了 这项设置就无效
		 */
		every?: number;

		/**
		 * 备注
		 */
		remark?: string;

		/**
		 * 状态 0-停止 1-运行
		 */
		status?: number;

		/**
		 * 开始时间
		 */
		startDate?: Date;

		/**
		 * 结束时间
		 */
		endDate?: Date;

		/**
		 * 数据
		 */
		data?: string;

		/**
		 * 执行的service实例ID
		 */
		service?: string;

		/**
		 * 状态 0-系统 1-用户
		 */
		type?: number;

		/**
		 * 下一次执行时间
		 */
		nextRunTime?: Date;

		/**
		 * 状态 0-cron 1-时间间隔
		 */
		taskType?: number;

		/**
		 * undefined
		 */
		lastExecuteTime?: Date;

		/**
		 * undefined
		 */
		lockExpireTime?: Date;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface CustomerContactsEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 录入人
		 */
		录入人?: string;

		/**
		 * 项目名称
		 */
		项目名称?: string;

		/**
		 * projectCode
		 */
		projectCode?: string;

		/**
		 * 客商名称
		 */
		客商名称?: string;

		/**
		 * 客商名称代码
		 */
		客商名称代码?: string;

		/**
		 * 对接人
		 */
		对接人?: string;

		/**
		 * 对接人代码
		 */
		对接人代码?: string;

		/**
		 * 对接人邮箱
		 */
		对接人邮箱?: string;

		/**
		 * 对接人手机号码
		 */
		对接人手机号码?: string;

		/**
		 * 客商联系人
		 */
		客商联系人?: string;

		/**
		 * 客商联系人邮箱
		 */
		客商联系人邮箱?: string;

		/**
		 * 客商联系人手机号码
		 */
		客商联系人手机号码?: string;

		/**
		 * 最新编辑人
		 */
		最新编辑人?: string;

		/**
		 * 审核状态
		 */
		审核状态?: number;

		/**
		 * 审核时间
		 */
		审核时间?: Date;

		/**
		 * 审核人
		 */
		审核人?: string;

		/**
		 * 审核说明
		 */
		审核说明?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface SendStrategyEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 录入人
		 */
		录入人?: string;

		/**
		 * 审核人
		 */
		审核人?: string;

		/**
		 * 审核时间
		 */
		审核时间?: Date;

		/**
		 * 审核状态
		 */
		审核状态?: number;

		/**
		 * 审核说明
		 */
		审核说明?: string;

		/**
		 * 项目代码
		 */
		projectCode?: string;

		/**
		 * 项目名称
		 */
		项目名称?: string;

		/**
		 * 客商名称
		 */
		客商名称?: string;

		/**
		 * 客商名称代码
		 */
		客商名称代码?: string;

		/**
		 * 发函策略名称代码
		 */
		发函策略代码?: string;

		/**
		 * 发函策略名称
		 */
		发函策略?: string;

		/**
		 * 发函策略周期
		 */
		发函策略周期?: string;

		/**
		 * 发函策略周期代码
		 */
		发函策略周期代码?: string;

		/**
		 * 发函策略周期间隔
		 */
		发函策略周期间隔?: number;

		/**
		 * 发函策略时间
		 */
		发函策略时间?: Date;

		/**
		 * 最新编辑人
		 */
		最新编辑人?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface XzhEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 询证函单号
		 */
		询证函单号?: string;

		/**
		 * 应收账款会计期
		 */
		应收账款会计期?: string;

		/**
		 * 所属项目代码
		 */
		projectCode?: string;

		/**
		 * 所属项目名称
		 */
		所属项目名称?: string;

		/**
		 * 客商名称代码
		 */
		客商名称代码?: string;

		/**
		 * 客商名称
		 */
		客商名称?: string;

		/**
		 * 应收账款总金额
		 */
		应收账款总金额?: number;

		/**
		 * 已确权总金额
		 */
		已确权总金额?: number;

		/**
		 * 未回函总金额
		 */
		未回函总金额?: number;

		/**
		 * 已回函总金额
		 */
		已回函总金额?: number;

		/**
		 * 责任人
		 */
		责任人?: string;

		/**
		 * 责任人邮箱
		 */
		责任人邮箱?: string;

		/**
		 * 责任人手机号码
		 */
		责任人手机号码?: string;

		/**
		 * 客商联系人
		 */
		客商联系人?: string;

		/**
		 * 客商联系人邮箱
		 */
		客商联系人邮箱?: string;

		/**
		 * 客商联系人手机号码
		 */
		客商联系人手机号码?: string;

		/**
		 * 发函状态
		 */
		发函状态?: number;

		/**
		 * 询证函的电子原件
		 */
		询证函的电子原件?: string;

		/**
		 * 预计发函时间
		 */
		预计发函时间?: Date;

		/**
		 * 实际发函时间
		 */
		实际发函时间?: Date;

		/**
		 * 回函时间
		 */
		回函时间?: Date;

		/**
		 * 回函人邮箱
		 */
		回函人邮箱?: string;

		/**
		 * 回函确认状态
		 */
		回函确认状态?: number;

		/**
		 * 回函附件
		 */
		回函附件?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface XzhDetailEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 询证函单号
		 */
		询证函单号?: string;

		/**
		 * 所属项目代码
		 */
		projectCode?: string;

		/**
		 * 所属项目名称
		 */
		所属项目名称?: string;

		/**
		 * 客商名称代码
		 */
		客商名称代码?: string;

		/**
		 * 科目
		 */
		科目?: string;

		/**
		 * 客商名称
		 */
		客商名称?: string;

		/**
		 * 凭证日期
		 */
		凭证日期?: string;

		/**
		 * 凭证类别
		 */
		凭证类别?: string;

		/**
		 * 凭证号
		 */
		凭证号?: string;

		/**
		 * 合同代码
		 */
		合同代码?: string;

		/**
		 * 合同名称
		 */
		合同名称?: string;

		/**
		 * 账龄
		 */
		账龄?: number;

		/**
		 * 账期
		 */
		账期?: string;

		/**
		 * 责任人
		 */
		责任人?: string;

		/**
		 * 待确权金额
		 */
		待确权金额?: number;

		/**
		 * 已确权金额
		 */
		已确权金额?: number;

		/**
		 * 合同函证影像
		 */
		合同函证影像?: string;

		/**
		 * 确权状态
		 */
		确权状态?: number;

		/**
		 * 回函确认状态
		 */
		回函确认状态?: number;

		/**
		 * 确权时间
		 */
		确权时间?: Date;

		/**
		 * 备注
		 */
		备注?: string;

		/**
		 * 确权人
		 */
		确权人?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface XzhRpaLogEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * undefined
		 */
		unique_id?: string;

		/**
		 * undefined
		 */
		sender_email?: string;

		/**
		 * undefined
		 */
		receive_time?: Date;

		/**
		 * undefined
		 */
		subject?: string;

		/**
		 * undefined
		 */
		email_body?: string;

		/**
		 * undefined
		 */
		has_qrcode?: boolean;

		/**
		 * undefined
		 */
		qrcode_content?: string;

		/**
		 * undefined
		 */
		qrcode_decoded?: string;

		/**
		 * undefined
		 */
		local_files?: string;

		/**
		 * undefined
		 */
		archive_info?: string;

		/**
		 * undefined
		 */
		process_status?: string;

		/**
		 * undefined
		 */
		process_log?: string;

		/**
		 * undefined
		 */
		seal_ocr_result?: string;

		/**
		 * undefined
		 */
		checkbox_ocr_result?: string;

		/**
		 * undefined
		 */
		ocr_result?: string;

		/**
		 * undefined
		 */
		ocr_status?: boolean;

		/**
		 * undefined
		 */
		update_status?: boolean;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface XzhSendLogEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 询证函单号
		 */
		询证函单号?: string;

		/**
		 * 应收账款会计期
		 */
		应收账款会计期?: string;

		/**
		 * 发函策略
		 */
		发函策略?: string;

		/**
		 * 发函策略代码
		 */
		发函策略代码?: string;

		/**
		 * 发函策略周期
		 */
		发函策略周期?: string;

		/**
		 * 发函策略周期代码
		 */
		发函策略周期代码?: string;

		/**
		 * 发函人
		 */
		发函人?: string;

		/**
		 * 项目名称
		 */
		项目名称?: string;

		/**
		 * 客商名称
		 */
		客商名称?: string;

		/**
		 * 发函策略时间
		 */
		发函策略时间?: string;

		/**
		 * 下次发函时间
		 */
		下次发函时间?: string;

		/**
		 * 发送状态
		 */
		发送状态?: number;

		/**
		 * 接收邮箱
		 */
		接收邮箱?: string;

		/**
		 * 发函日志
		 */
		发函日志?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface ZTdepartmentJCEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 部门代码
		 */
		part_code?: string;

		/**
		 * 部门名称
		 */
		part_name?: string;

		/**
		 * 项目代码
		 */
		xm_code?: string;

		/**
		 * 项目名称
		 */
		xm_name?: string;

		/**
		 * 部门名称（简称）
		 */
		part_name_jc?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface DuibiaodanweiEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 单位名称
		 */
		name?: string;

		/**
		 * 社会统一代码
		 */
		only_code?: string;

		/**
		 * 名称（简称）
		 */
		as_name?: string;

		/**
		 * 股票代码
		 */
		stock_code?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface EmailFlowEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 流程名称
		 */
		name?: string;

		/**
		 * 备注
		 */
		remark?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface EmailUesrEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 姓名
		 */
		name?: string;

		/**
		 * 邮箱
		 */
		email?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface EmailUserFlowEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 邮箱用户ID
		 */
		userId?: BigInt;

		/**
		 * 流程名称ID
		 */
		flowId?: BigInt;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface YuebiaoEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 姓名
		 */
		name?: string;

		/**
		 * 邮箱
		 */
		email?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface YusuanPartEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 项目名称
		 */
		xm_name?: string;

		/**
		 * 部门代码
		 */
		part_code?: string;

		/**
		 * 部门名称
		 */
		part_name?: string;

		/**
		 * 经费类别
		 */
		jflb?: string;

		/**
		 * 预算大类
		 */
		ysdl?: string;

		/**
		 * 年初预算
		 */
		ncys?: float;

		/**
		 * 预算调整
		 */
		ystz?: float;

		/**
		 * 金额
		 */
		jine?: float;

		/**
		 * 控制系数
		 */
		xishu?: float;

		/**
		 * 预算控制数
		 */
		yusuan_num?: float;

		/**
		 * 预算占用数
		 */
		yusuan_used?: float;

		/**
		 * 新增控制系数
		 */
		new_xishu?: float;

		/**
		 * 控制类型
		 */
		kongzhi_type?: string;

		/**
		 * 状态
		 */
		status?: string;

		/**
		 * 备注
		 */
		mark?: string;

		/**
		 * 用户名
		 */
		username?: string;

		/**
		 * 年份
		 */
		year?: string;

		/**
		 * 唯一值
		 */
		only_code?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface YusuanXmEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 项目
		 */
		xiangmu?: string;

		/**
		 * 名称
		 */
		name?: string;

		/**
		 * 经费类别
		 */
		jflb?: string;

		/**
		 * 预算大类
		 */
		ysdl?: string;

		/**
		 * 金额
		 */
		jine?: float;

		/**
		 * 控制系数
		 */
		xishu?: float;

		/**
		 * 预算控制数
		 */
		yusuan_num?: float;

		/**
		 * 预算占用数
		 */
		yusuan_used?: float;

		/**
		 * 新增控制系数
		 */
		new_xishu?: float;

		/**
		 * 控制类型
		 */
		kongzhi_type?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface ZdfysLogEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 用户名
		 */
		username?: string;

		/**
		 * 项目名称
		 */
		xm_name?: string;

		/**
		 * 部门代码
		 */
		part_code?: string;

		/**
		 * 部门名称
		 */
		part_name?: string;

		/**
		 * 经费类别
		 */
		jflb?: string;

		/**
		 * 预算大类
		 */
		ysdl?: string;

		/**
		 * 年初预算
		 */
		ncys?: float;

		/**
		 * 预算调整
		 */
		ystz?: float;

		/**
		 * 金额
		 */
		jine?: float;

		/**
		 * 控制系数
		 */
		xishu?: float;

		/**
		 * 预算控制数
		 */
		yusuan_num?: float;

		/**
		 * 预算占用数
		 */
		yusuan_used?: float;

		/**
		 * 新增控制系数
		 */
		new_xishu?: float;

		/**
		 * 控制类型
		 */
		kongzhi_type?: string;

		/**
		 * 状态
		 */
		status?: string;

		/**
		 * 备注
		 */
		mark?: string;

		/**
		 * 年份
		 */
		year?: string;

		/**
		 * 唯一值
		 */
		only_code?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface ZTEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 单据编号
		 */
		danju_code?: string;

		/**
		 * 入库单号
		 */
		danhao?: string;

		/**
		 * 出库单号
		 */
		ck_danhao?: string;

		/**
		 * 项目
		 */
		xiangmu?: string;

		/**
		 * 保管部门
		 */
		baoguan_part?: string;

		/**
		 * 领用部门
		 */
		lingyong_part?: string;

		/**
		 * 入库时间
		 */
		time_ruku?: string;

		/**
		 * 货物名称
		 */
		huowu_name?: string;

		/**
		 * 单位（酒：瓶；茶叶：斤）
		 */
		huowu_danwei?: string;

		/**
		 * 入库单价
		 */
		ruku_danjia?: float;

		/**
		 * 入库数量
		 */
		ruku_num?: number;

		/**
		 * 入库金额
		 */
		ruku_money?: string;

		/**
		 * 出库时间
		 */
		time_chuku?: string;

		/**
		 * 出库单价
		 */
		chuku_danjia?: float;

		/**
		 * 出库数量
		 */
		chuku_num?: number;

		/**
		 * 出库金额
		 */
		chuku_money?: string;

		/**
		 * 审核状态
		 */
		states?: string;

		/**
		 * 结存数量
		 */
		residue_num?: number;

		/**
		 * 用户名
		 */
		username?: string;

		/**
		 * 驳回原因
		 */
		reject_reason?: string;

		/**
		 * 审核员
		 */
		checker?: string;

		/**
		 * 凭证号
		 */
		pz_code?: string;

		/**
		 * 凭证类别
		 */
		pz_type?: string;

		/**
		 * 凭证日期
		 */
		pz_date?: string;

		/**
		 * 出库人
		 */
		ck_person?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface ZTdetailsEntity {
		/**
		 * ID
		 */
		id?: number;

		/**
		 * 单号
		 */
		danhao?: string;

		/**
		 * 项目
		 */
		xiangmu?: string;

		/**
		 * 保管部门
		 */
		baoguan_part?: string;

		/**
		 * 货物名称
		 */
		huowu_name?: string;

		/**
		 * 单位（酒：瓶；茶叶：斤）
		 */
		huowu_danwei?: string;

		/**
		 * 结存数量
		 */
		residue_num?: number;

		/**
		 * 结存单价
		 */
		residue_danjia?: float;

		/**
		 * 结存金额
		 */
		residue_money?: float;

		/**
		 * 更新时间
		 */
		finally_time?: string;

		/**
		 * 审核员
		 */
		checker?: string;

		/**
		 * 创建时间
		 */
		createTime?: Date;

		/**
		 * 更新时间
		 */
		updateTime?: Date;

		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	type json = any;

	interface BaseCoding {
		/**
		 * 获取模块目录结构
		 */
		getModuleTree(data?: any): Promise<any>;

		/**
		 * 创建代码
		 */
		createCode(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: { getModuleTree: string; createCode: string };

		/**
		 * 权限状态
		 */
		_permission: { getModuleTree: boolean; createCode: boolean };

		request: Service["request"];
	}

	interface BaseComm {
		/**
		 * 修改个人信息
		 */
		personUpdate(data?: any): Promise<any>;

		/**
		 * getAllUsers
		 */
		getAllUsers(data?: any): Promise<any>;

		/**
		 * 文件上传模式
		 */
		uploadMode(data?: any): Promise<any>;

		/**
		 * 权限与菜单
		 */
		permmenu(data?: any): Promise<any>;

		/**
		 * 编程
		 */
		program(data?: any): Promise<any>;

		/**
		 * 个人信息
		 */
		person(data?: any): Promise<any>;

		/**
		 * 文件上传
		 */
		upload(data?: any): Promise<any>;

		/**
		 * 退出
		 */
		logout(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			personUpdate: string;
			getAllUsers: string;
			uploadMode: string;
			permmenu: string;
			program: string;
			person: string;
			upload: string;
			logout: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			personUpdate: boolean;
			getAllUsers: boolean;
			uploadMode: boolean;
			permmenu: boolean;
			program: boolean;
			person: boolean;
			upload: boolean;
			logout: boolean;
		};

		request: Service["request"];
	}

	interface BaseOpen {
		/**
		 * todoListMobile
		 */
		todoListMobile(data?: any): Promise<any>;

		/**
		 * 刷新token
		 */
		refreshToken(data?: any): Promise<any>;

		/**
		 * 登录
		 */
		tokenlogin(data?: any): Promise<any>;

		/**
		 * todoList
		 */
		todoList(data?: any): Promise<any>;

		/**
		 * 登录
		 */
		rpalogin(data?: any): Promise<any>;

		/**
		 * 验证码
		 */
		captcha(data?: any): Promise<any>;

		/**
		 * 登录
		 */
		login(data?: any): Promise<any>;

		/**
		 * 获得网页内容的参数值
		 */
		html(data?: any): Promise<any>;

		/**
		 * 实体信息与路径
		 */
		eps(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			todoListMobile: string;
			refreshToken: string;
			tokenlogin: string;
			todoList: string;
			rpalogin: string;
			captcha: string;
			login: string;
			html: string;
			eps: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			todoListMobile: boolean;
			refreshToken: boolean;
			tokenlogin: boolean;
			todoList: boolean;
			rpalogin: boolean;
			captcha: boolean;
			login: boolean;
			html: boolean;
			eps: boolean;
		};

		request: Service["request"];
	}

	interface BaseSysDepartment {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 排序
		 */
		order(data?: any): Promise<any>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<BaseSysDepartmentEntity[]>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: { delete: string; update: string; order: string; list: string; add: string };

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			order: boolean;
			list: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface BaseSysLog {
		/**
		 * 日志保存时间
		 */
		setKeep(data?: any): Promise<any>;

		/**
		 * 获得日志保存时间
		 */
		getKeep(data?: any): Promise<any>;

		/**
		 * 清理
		 */
		clear(data?: any): Promise<any>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: BaseSysLogEntity[];
			[key: string]: any;
		}>;

		/**
		 * 权限标识
		 */
		permission: { setKeep: string; getKeep: string; clear: string; page: string };

		/**
		 * 权限状态
		 */
		_permission: { setKeep: boolean; getKeep: boolean; clear: boolean; page: boolean };

		request: Service["request"];
	}

	interface BaseSysMenu {
		/**
		 * 创建代码
		 */
		create(data?: any): Promise<any>;

		/**
		 * 导出
		 */
		export(data?: any): Promise<any>;

		/**
		 * 导入
		 */
		import(data?: any): Promise<any>;

		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 解析
		 */
		parse(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<BaseSysMenuEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<BaseSysMenuEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: BaseSysMenuEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			create: string;
			export: string;
			import: string;
			delete: string;
			update: string;
			parse: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			create: boolean;
			export: boolean;
			import: boolean;
			delete: boolean;
			update: boolean;
			parse: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface BaseSysParam {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 获得网页内容的参数值
		 */
		html(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<BaseSysParamEntity>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: BaseSysParamEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			html: string;
			info: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			html: boolean;
			info: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface BaseSysRole {
		/**
		 * 根据角色id获取用户信息
		 */
		listUsers(data?: any): Promise<any>;

		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<BaseSysRoleEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<BaseSysRoleEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: BaseSysRoleEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			listUsers: string;
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			listUsers: boolean;
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface BaseSysUser {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 移动部门
		 */
		move(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<BaseSysUserEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<BaseSysUserEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: BaseSysUserEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			move: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			move: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface CloudDataOperationDesc {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<DataOperationDescEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<DataOperationDescEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: DataOperationDescEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface CloudDb {
		/**
		 * 更改表格分组
		 */
		updateFormTypeId(data?: any): Promise<any>;

		/**
		 * 修改表名
		 */
		modifyTableName(data?: any): Promise<any>;

		/**
		 * 未审核数据
		 */
		uncheckedDetail(data?: any): Promise<any>;

		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 审核数据
		 */
		audit(data?: any): Promise<any>;

		/**
		 * 数据操作
		 */
		data(data?: any): Promise<any>;

		/**
		 * 自动关联表
		 */
		join(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<CloudDBEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<CloudDBEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: CloudDBEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			updateFormTypeId: string;
			modifyTableName: string;
			uncheckedDetail: string;
			delete: string;
			update: string;
			audit: string;
			data: string;
			join: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			updateFormTypeId: boolean;
			modifyTableName: boolean;
			uncheckedDetail: boolean;
			delete: boolean;
			update: boolean;
			audit: boolean;
			data: boolean;
			join: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface CloudFavorite {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<UserFavoriteEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<UserFavoriteEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: UserFavoriteEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface CloudFormType {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<FormTypeEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<FormTypeEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: FormTypeEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface LuruLuruPermission {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<LuruPermissionTableEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<LuruPermissionTableEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: LuruPermissionTableEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface DictInfo {
		/**
		 * 获取所有子字典
		 */
		getAllChildren(data?: any): Promise<any>;

		/**
		 * 搜索字典数据
		 */
		search(data?: any): Promise<any>;

		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 获得所有字典类型
		 */
		types(data?: any): Promise<any>;

		/**
		 * 获得字典数据
		 */
		data(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<DictInfoEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<DictInfoEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: DictInfoEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			getAllChildren: string;
			search: string;
			delete: string;
			update: string;
			types: string;
			data: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			getAllChildren: boolean;
			search: boolean;
			delete: boolean;
			update: boolean;
			types: boolean;
			data: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface DictType {
		/**
		 * 获取字典类型版本
		 */
		getVersions(data?: any): Promise<any>;

		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<DictTypeEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<DictTypeEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: DictTypeEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			getVersions: string;
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			getVersions: boolean;
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface FlowFlow {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<FlowDefinitionEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<FlowDefinitionEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: FlowDefinitionEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface FlowFlowInstance {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<FlowInstanceEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<FlowInstanceEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: FlowInstanceEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface FlowFlowTask {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<FlowTaskEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<FlowTaskEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: FlowTaskEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface FlowProcess {
		/**
		 * history
		 */
		history(data?: any): Promise<any>;

		/**
		 * completedTasks
		 */
		completedTasks(data?: any): Promise<any>;

		/**
		 * rollbackNodes
		 */
		rollbackNodes(data?: any): Promise<any>;

		/**
		 * unbindFlow
		 */
		unbindFlow(data?: any): Promise<any>;

		/**
		 * startTasks
		 */
		startTasks(data?: any): Promise<any>;

		/**
		 * removeSign
		 */
		removeSign(data?: any): Promise<any>;

		/**
		 * transfer
		 */
		transfer(data?: any): Promise<any>;

		/**
		 * rollback
		 */
		rollback(data?: any): Promise<any>;

		/**
		 * approve
		 */
		approve(data?: any): Promise<any>;

		/**
		 * addSign
		 */
		addSign(data?: any): Promise<any>;

		/**
		 * predict
		 */
		predict(data?: any): Promise<any>;

		/**
		 * revoke
		 */
		revoke(data?: any): Promise<any>;

		/**
		 * reject
		 */
		reject(data?: any): Promise<any>;

		/**
		 * start
		 */
		start(data?: any): Promise<any>;

		/**
		 * tasks
		 */
		tasks(data?: any): Promise<any>;

		/**
		 * urge
		 */
		urge(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			history: string;
			completedTasks: string;
			rollbackNodes: string;
			unbindFlow: string;
			startTasks: string;
			removeSign: string;
			transfer: string;
			rollback: string;
			approve: string;
			addSign: string;
			predict: string;
			revoke: string;
			reject: string;
			start: string;
			tasks: string;
			urge: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			history: boolean;
			completedTasks: boolean;
			rollbackNodes: boolean;
			unbindFlow: boolean;
			startTasks: boolean;
			removeSign: boolean;
			transfer: boolean;
			rollback: boolean;
			approve: boolean;
			addSign: boolean;
			predict: boolean;
			revoke: boolean;
			reject: boolean;
			start: boolean;
			tasks: boolean;
			urge: boolean;
		};

		request: Service["request"];
	}

	interface MessagesComm {
		/**
		 * unread
		 */
		unread(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: { unread: string };

		/**
		 * 权限状态
		 */
		_permission: { unread: boolean };

		request: Service["request"];
	}

	interface MessagesGroup {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<MessageGroupEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<MessageGroupEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: MessageGroupEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface MessagesNotification {
		/**
		 * allNotification
		 */
		allNotification(data?: any): Promise<any>;

		/**
		 * announcement
		 */
		announcement(data?: any): Promise<any>;

		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * read
		 */
		read(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<NotificationEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<NotificationEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: NotificationEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			allNotification: string;
			announcement: string;
			delete: string;
			update: string;
			read: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			allNotification: boolean;
			announcement: boolean;
			delete: boolean;
			update: boolean;
			read: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface OidcClient {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<OidcClientEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<OidcClientEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: OidcClientEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface PluginInfo {
		/**
		 * 安装插件
		 */
		install(data?: any): Promise<any>;

		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<PluginInfoEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<PluginInfoEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: PluginInfoEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			install: string;
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			install: boolean;
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface QualityCheck {
		/**
		 * outterSchemas
		 */
		outterSchemas(data?: any): Promise<any>;

		/**
		 * outterRecords
		 */
		outterRecords(data?: any): Promise<any>;

		/**
		 * doQuerySql
		 */
		doQuerySql(data?: any): Promise<any>;

		/**
		 * handleOnce
		 */
		handleOnce(data?: any): Promise<any>;

		/**
		 * handleAll
		 */
		handleAll(data?: any): Promise<any>;

		/**
		 * formList
		 */
		formList(data?: any): Promise<any>;

		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<CheckRuleEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<CheckRuleEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: CheckRuleEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			outterSchemas: string;
			outterRecords: string;
			doQuerySql: string;
			handleOnce: string;
			handleAll: string;
			formList: string;
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			outterSchemas: boolean;
			outterRecords: boolean;
			doQuerySql: boolean;
			handleOnce: boolean;
			handleAll: boolean;
			formList: boolean;
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface TaskInfo {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 开始
		 */
		start(data?: any): Promise<any>;

		/**
		 * 执行一次
		 */
		once(data?: any): Promise<any>;

		/**
		 * 停止
		 */
		stop(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<TaskInfoEntity>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: TaskInfoEntity[];
			[key: string]: any;
		}>;

		/**
		 * 日志
		 */
		log(data?: any): Promise<any>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			start: string;
			once: string;
			stop: string;
			info: string;
			page: string;
			log: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			start: boolean;
			once: boolean;
			stop: boolean;
			info: boolean;
			page: boolean;
			log: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface YszkCustomermatchmaker {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<CustomerContactsEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<CustomerContactsEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: CustomerContactsEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface YszkSendstrategy {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<SendStrategyEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<SendStrategyEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: SendStrategyEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface YszkXzh {
		/**
		 * sendFilePreviewHtml
		 */
		sendFilePreviewHtml(data?: any): Promise<any>;

		/**
		 * sendFilePreview
		 */
		sendFilePreview(data?: any): Promise<any>;

		/**
		 * sendXzh
		 */
		sendXzh(data?: any): Promise<any>;

		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<XzhEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<XzhEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: XzhEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			sendFilePreviewHtml: string;
			sendFilePreview: string;
			sendXzh: string;
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			sendFilePreviewHtml: boolean;
			sendFilePreview: boolean;
			sendXzh: boolean;
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface YszkXzhDetail {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<XzhDetailEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<XzhDetailEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: XzhDetailEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface YszkXzhRpaLog {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: XzhRpaLogEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: { delete: string; update: string; page: string; add: string };

		/**
		 * 权限状态
		 */
		_permission: { delete: boolean; update: boolean; page: boolean; add: boolean };

		request: Service["request"];
	}

	interface YszkXzhSendLog {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<XzhSendLogEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<XzhSendLogEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: XzhSendLogEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface ZhongtieDepartment_jc {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<ZTdepartmentJCEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<ZTdepartmentJCEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: ZTdepartmentJCEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface ZhongtieDuibiaodanwei {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<DuibiaodanweiEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<DuibiaodanweiEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: DuibiaodanweiEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface ZhongtieEmail_flow {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<EmailFlowEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<EmailFlowEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: EmailFlowEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface ZhongtieEmail_user {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<EmailUesrEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<EmailUesrEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: EmailUesrEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface ZhongtieEmail_user_flow {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<EmailUserFlowEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<EmailUserFlowEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: EmailUserFlowEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface ZhongtieYuebiao {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<YuebiaoEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<YuebiaoEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: YuebiaoEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface ZhongtieYusuan_part {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<YusuanPartEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<YusuanPartEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: YusuanPartEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface ZhongtieYusuan_xm {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<YusuanXmEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<YusuanXmEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: YusuanXmEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface ZhongtieZdfysLog {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<ZdfysLogEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<ZdfysLogEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: ZdfysLogEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface ZhongtieZhongtie_kucun {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<ZTEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<ZTEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: ZTEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	interface ZhongtieZhongtie_kucun_details {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;

		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;

		/**
		 * 单个信息
		 */
		info(data?: any): Promise<ZTdetailsEntity>;

		/**
		 * 列表查询
		 */
		list(data?: any): Promise<ZTdetailsEntity[]>;

		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: ZTdetailsEntity[];
			[key: string]: any;
		}>;

		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;

		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};

		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};

		request: Service["request"];
	}

	type Service = {
		/**
		 * 基础请求
		 */
		request(options?: {
			url: string;
			method?: "POST" | "GET" | "PUT" | "DELETE" | "PATCH" | "HEAD" | "OPTIONS";
			data?: any;
			params?: any;
			headers?: {
				authorization?: string;
				[key: string]: any;
			};
			timeout?: number;
			proxy?: boolean;
			[key: string]: any;
		}): Promise<any>;

		base: {
			coding: BaseCoding;
			comm: BaseComm;
			open: BaseOpen;
			sys: {
				department: BaseSysDepartment;
				log: BaseSysLog;
				menu: BaseSysMenu;
				param: BaseSysParam;
				role: BaseSysRole;
				user: BaseSysUser;
			};
		};
		cloud: {
			dataOperationDesc: CloudDataOperationDesc;
			db: CloudDb;
			favorite: CloudFavorite;
			formType: CloudFormType;
		};
		luru: { luruPermission: LuruLuruPermission };
		dict: { info: DictInfo; type: DictType };
		flow: {
			flow: FlowFlow;
			flowInstance: FlowFlowInstance;
			flowTask: FlowFlowTask;
			process: FlowProcess;
		};
		messages: { comm: MessagesComm; group: MessagesGroup; notification: MessagesNotification };
		oidc: { client: OidcClient };
		plugin: { info: PluginInfo };
		quality: { check: QualityCheck };
		task: { info: TaskInfo };
		yszk: {
			customermatchmaker: YszkCustomermatchmaker;
			sendstrategy: YszkSendstrategy;
			xzh: YszkXzh;
			xzhDetail: YszkXzhDetail;
			xzhRpaLog: YszkXzhRpaLog;
			xzhSendLog: YszkXzhSendLog;
		};
		zhongtie: {
			department_jc: ZhongtieDepartment_jc;
			duibiaodanwei: ZhongtieDuibiaodanwei;
			email_flow: ZhongtieEmail_flow;
			email_user: ZhongtieEmail_user;
			email_user_flow: ZhongtieEmail_user_flow;
			yuebiao: ZhongtieYuebiao;
			yusuan_part: ZhongtieYusuan_part;
			yusuan_xm: ZhongtieYusuan_xm;
			zdfysLog: ZhongtieZdfysLog;
			zhongtie_kucun: ZhongtieZhongtie_kucun;
			zhongtie_kucun_details: ZhongtieZhongtie_kucun_details;
		};
	};

	type DictKey =
		| "project_system"
		| "货物表"
		| "存货单位表"
		| "经费类别表"
		| "预算控制大类"
		| "人员类别"
		| "职称"
		| "职务"
		| "是/否"
		| "银行账户类别"
		| "账龄"
		| "合同项目"
		| "研发项目"
		| "应收账款大类"
		| "客商大类"
		| "坏账计提比例"
		| "印章类型"
		| "客商"
		| "管控类大类"
		| "项目（报表）"
		| "项目（核算）"
		| "一级税收类别"
		| "资产分类"
		| "主数据字典"
		| "自有字典（录入系统）"
		| "部门"
		| "jquser_system"
		| "自有字典-客户及供应商数据"
		| "自有字典-合同数据"
		| "自有字典-人事数据"
		| "自有字典-组织数据"
		| "自有字典-预算"
		| "自有字典-会计核算等"
		| "自有字典-涉税相关"
		| "自有字典-图章类"
		| "自有字典-管理管控类指标字典"
		| "自有字典-研发项目"
		| "一级科目"
		| "二级科目"
		| "三级科目"
		| "科研是否归档"
		| "科研是否完成"
		| "科研标准类别"
		| "科研计划类别"
		| "是否最高技术技能"
		| "技能鉴定工种"
		| "技术技能等级"
		| "技术技能系列"
		| "合同所属类型"
		| "获奖集团奖励金额"
		| "称谓"
		| "专利获奖等级"
		| "从事专业"
		| "性别(01男/02女)"
		| "国家"
		| "人员级别"
		| "是否经营线项目"
		| "是否已立项"
		| "是否登记"
		| "是否有权证/空地"
		| "1是/0否"
		| "业务类型"
		| "业绩等级"
		| "板块细分"
		| "婚姻状况"
		| "评奖单位"
		| "软件采购方式"
		| "人员省市区"
		| "合同省/市"
		| "设备状况"
		| "任职类型"
		| "使用权人/权利人"
		| "折旧方式"
		| "权证类型"
		| "配套计量方式"
		| "投资主体"
		| "公司级别"
		| "房屋结构"
		| "台账归属"
		| "未来利用计划"
		| "使用权类型"
		| "有无他项权利"
		| "资产来源"
		| "资产状态"
		| "采购模式"
		| "使用方式"
		| "授权形式"
		| "是否国产"
		| "软件类型"
		| "资产处置方式"
		| "款项来源"
		| "设备状态"
		| "计量单位"
		| "在用/闲置"
		| "采购方式"
		| "设备类别"
		| "资产取得方式"
		| "资产类别"
		| "资产分类"
		| "折旧状态"
		| "费用类型"
		| "专利阶段"
		| "国际阶段"
		| "国际类型"
		| "专利状态"
		| "股份公司专业一级/二级领域"
		| "专业领域"
		| "管理权属"
		| "获奖类型/获奖级别/获奖等级"
		| "颁奖单位"
		| "奖项级别"
		| "平台/工作站类别"
		| "组织分类"
		| "营业执照类型"
		| "供应商-业务类型"
		| "企业登记注册类型"
		| "客户属性"
		| "启用状态"
		| "账户性质"
		| "资质等级"
		| "单位类型"
		| "客户类型"
		| "国内外"
		| "盖章类型"
		| "考核类型"
		| "人员类别"
		| "非经济合同类型"
		| "计税方式"
		| "账户类别"
		| "账户类型"
		| "科目大类"
		| "科目属性"
		| "项目角色"
		| "阶段"
		| "科目类型"
		| "课题等级分类"
		| "主持/参与"
		| "科研项目类别"
		| "立项单位"
		| "研发中心"
		| "文件语言"
		| "项目来源"
		| "项目级别"
		| "项目板块"
		| "项目属地"
		| "项目性质"
		| "项目状态"
		| "组织状态"
		| "是否虚拟组织"
		| "组织类型"
		| "是否最高学历"
		| "是否第一学历"
		| "保管状态"
		| "证书名称"
		| "专家名称"
		| "专家层次"
		| "学位"
		| "教育类型"
		| "合同类型"
		| "奖励层次"
		| "岗位性质"
		| "学历"
		| "入职来源"
		| "政治面貌"
		| "因私出入境是否备案"
		| "民族"
		| "人才统计类别"
		| "技术技能名称"
		| "岗位档级"
		| "现行政级别"
		| "岗位职务"
		| "性别"
		| "人员岗位状态"
		| "国籍"
		| "项目等级"
		| "票据付款类型"
		| "合同分类"
		| "委外类别"
		| "付款类型"
		| "收费方式"
		| "开票类型"
		| "业务范围"
		| "内部客商"
		| "承揽经营分院"
		| "路内外"
		| "建设单位性质"
		| "领域细分"
		| "领域"
		| "任务来源"
		| "指挥部"
		| "合同状态"
		| "板块"
		| "设计阶段"
		| "四级科目"
		| "五级科目"
		| "六级科目"
		| "二级税收类别"
		| "客商小类"
		| "管控类小类"
		| "应收账款小类"
		| "项目-公司（报表）"
		| "项目-分公司（报表）"
		| "二级部门"
		| "自有字典-角色"
		| "role_system"
		| "form_system"
		| "组织"
		| "发票票种代码"
		| "发票状态"
		| "发票风险等级"
		| "发票进销项名称"
		| "发票来源"
		| "票据类型"
		| "标签种类"
		| "银行简称"
		| "周期"
		| "发函策略"
		| "研发字典"
		| "三级部门"
		| "总包项目收入利润字典"
		| "permission_system"
		| "合同字典"
		| "自有字典-行政区划"
		| "省"
		| "市"
		| "单位类型一级"
		| "款项性质一级"
		| "币种"
		| "期初款项类型"
		| "期末款项类型"
		| "坏账准备计提类型"
		| "到期年限（年）"
		| "单位类型二级"
		| "款项性质二级"
		| "checkStatus_system";
}
