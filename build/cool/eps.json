[{"prefix": "/admin/base/coding", "name": "", "api": [{"method": "get", "path": "/getModuleTree"}, {"method": "post", "path": "/createCode"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/comm", "name": "", "api": [{"method": "post", "path": "/personUpdate"}, {"method": "get", "path": "/getAllUsers"}, {"method": "get", "path": "/uploadMode"}, {"method": "get", "path": "/permmenu"}, {"method": "get", "path": "/program"}, {"method": "get", "path": "/person"}, {"method": "post", "path": "/upload"}, {"method": "post", "path": "/logout"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/open", "name": "", "api": [{"method": "get", "path": "/todoListMobile"}, {"method": "get", "path": "/refreshToken"}, {"method": "post", "path": "/tokenlogin"}, {"method": "get", "path": "/todoList"}, {"method": "post", "path": "/rpalogin"}, {"method": "get", "path": "/captcha"}, {"method": "post", "path": "/login"}, {"method": "get", "path": "/html"}, {"method": "get", "path": "/eps"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/sys/department", "name": "BaseSysDepartmentEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/order"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/sys/log", "name": "BaseSysLogEntity", "api": [{"method": "post", "path": "/setKeep"}, {"method": "get", "path": "/getKeep"}, {"method": "post", "path": "/clear"}, {"method": "post", "path": "/page"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "姓名", "nullable": true, "source": "b.name"}, {"propertyName": "action", "type": "string", "length": "", "comment": "行为", "nullable": false, "source": "a.action"}, {"propertyName": "ip", "type": "string", "length": "", "comment": "ip", "nullable": true, "source": "a.ip"}]}}, {"prefix": "/admin/base/sys/menu", "name": "BaseSysMenuEntity", "api": [{"method": "post", "path": "/create"}, {"method": "post", "path": "/export"}, {"method": "post", "path": "/import"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/parse"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/sys/param", "name": "BaseSysParamEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/html"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "dataType", "type": "number", "length": "", "comment": "数据类型 0-字符串 1-富文本 2-文件 ", "nullable": false, "defaultValue": 0, "source": "a.dataType"}, {"propertyName": "keyName", "type": "string", "length": "", "comment": "键", "nullable": false, "source": "a.key<PERSON>"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "名称", "nullable": false, "source": "a.name"}, {"propertyName": "keyName", "type": "string", "length": "", "comment": "键", "nullable": false, "source": "a.key<PERSON>"}]}}, {"prefix": "/admin/base/sys/role", "name": "BaseSysRoleEntity", "api": [{"method": "post", "path": "/listUsers"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "name", "type": "string", "length": "", "comment": "名称", "nullable": false, "source": "a.name"}, {"propertyName": "label", "type": "string", "length": "50", "comment": "角色标签", "nullable": true, "source": "a.label"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "名称", "nullable": false, "source": "a.name"}, {"propertyName": "label", "type": "string", "length": "50", "comment": "角色标签", "nullable": true, "source": "a.label"}]}}, {"prefix": "/admin/base/sys/user", "name": "BaseSysUserEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/move"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "username", "type": "string", "length": "100", "comment": "用户名", "nullable": false, "source": "a.username"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "姓名", "nullable": true, "source": "a.name"}, {"propertyName": "username", "type": "string", "length": "100", "comment": "用户名", "nullable": false, "source": "a.username"}]}}, {"prefix": "/admin/cloud/dataOperationDesc", "name": "DataOperationDescEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/cloud/db", "name": "CloudDBEntity", "api": [{"method": "post", "path": "/updateFormTypeId"}, {"method": "post", "path": "/modifyTableName"}, {"method": "post", "path": "/uncheckedDetail"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/audit"}, {"method": "post", "path": "/data"}, {"method": "post", "path": "/join"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "name", "type": "string", "length": "", "comment": "名称", "nullable": false, "source": "a.name"}, {"propertyName": "colInfo", "type": "json", "length": "", "comment": "列信息", "nullable": false, "source": "a.colInfo"}, {"propertyName": "tableName", "type": "string", "length": "", "comment": "表名", "nullable": true, "source": "a.<PERSON><PERSON>"}, {"propertyName": "readme", "type": "string", "length": "", "comment": "说明", "nullable": true, "source": "a.readme"}, {"propertyName": "tableType", "type": "string", "length": "", "comment": "表单类型", "nullable": false, "defaultValue": "user", "source": "a.tableType"}, {"propertyName": "status", "type": "number", "length": "", "comment": "状态 0-禁用 1-启用", "nullable": false, "defaultValue": 1, "source": "a.status"}, {"propertyName": "formTypeId", "type": "number", "length": "", "comment": "表单目录", "nullable": false, "defaultValue": 1, "source": "a.formTypeId"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "名称", "nullable": false, "source": "a.name"}, {"propertyName": "colInfo", "type": "json", "length": "", "comment": "列信息", "nullable": false, "source": "a.colInfo"}, {"propertyName": "readme", "type": "string", "length": "", "comment": "说明", "nullable": true, "source": "a.readme"}]}}, {"prefix": "/admin/cloud/favorite", "name": "UserFavoriteEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/cloud/formType", "name": "FormTypeEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/luru/luruPermission", "name": "LuruPermissionTableEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "录入人", "type": "string", "length": "", "comment": "录入人", "nullable": false, "source": "a.录入人"}, {"propertyName": "审核人", "type": "string", "length": "", "comment": "审核人", "nullable": true, "source": "a.审核人"}, {"propertyName": "审核时间", "type": "date", "length": "", "comment": "审核时间", "nullable": true, "source": "a.审核时间"}, {"propertyName": "审核状态", "type": "number", "length": "", "comment": "审核状态", "nullable": false, "defaultValue": 0, "source": "a.审核状态"}, {"propertyName": "权限所属人", "type": "string", "length": "", "comment": "权限所属人", "nullable": true, "source": "a.权限所属人"}, {"propertyName": "权限所属人代码", "type": "string", "length": "", "comment": "权限所属人代码", "nullable": true, "source": "a.权限所属人代码"}, {"propertyName": "projectCode", "type": "string", "length": "", "comment": "项目代码", "nullable": false, "source": "a.projectCode"}, {"propertyName": "项目名称", "type": "string", "length": "", "comment": "项目名称", "nullable": false, "source": "a.项目名称"}, {"propertyName": "功能权限", "type": "string", "length": "", "comment": "功能权限", "nullable": false, "source": "a.功能权限"}, {"propertyName": "功能权限代码", "type": "string", "length": "", "comment": "功能权限代码", "nullable": false, "source": "a.功能权限代码"}, {"propertyName": "表单权限", "type": "string", "length": "", "comment": "表单权限", "nullable": false, "source": "a.表单权限"}, {"propertyName": "表单权限代码", "type": "string", "length": "", "comment": "表单权限代码", "nullable": false, "source": "a.表单权限代码"}, {"propertyName": "备注", "type": "string", "length": "", "comment": "备注说明", "nullable": true, "source": "a.备注"}]}}, {"prefix": "/admin/dict/info", "name": "DictInfoEntity", "api": [{"method": "post", "path": "/getAllChildren"}, {"method": "post", "path": "/search"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/types"}, {"method": "post", "path": "/data"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "typeId", "type": "number", "length": "", "comment": "类型ID", "nullable": false, "source": "a.typeId"}, {"propertyName": "source", "type": "string", "length": "", "comment": "来源", "nullable": true, "source": "a.source"}, {"propertyName": "status", "type": "number", "length": "", "comment": "启用状态", "nullable": false, "defaultValue": 1, "source": "a.status"}, {"propertyName": "parentId", "type": "string", "length": "", "comment": "父ID", "nullable": true, "defaultValue": null, "source": "a.parentId"}, {"propertyName": "name", "type": "string", "length": "2000", "comment": "名称", "nullable": false, "source": "a.name"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "2000", "comment": "名称", "nullable": false, "source": "a.name"}, {"propertyName": "remark", "type": "string", "length": "", "comment": "备注", "nullable": true, "source": "a.remark"}]}}, {"prefix": "/admin/dict/type", "name": "DictTypeEntity", "api": [{"method": "post", "path": "/getVersions"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/flow/flow", "name": "FlowDefinitionEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/flow/flowInstance", "name": "FlowInstanceEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/flow/flowTask", "name": "FlowTaskEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/flow/process", "name": "", "api": [{"method": "get", "path": "/task/history"}, {"method": "get", "path": "/completedTasks"}, {"method": "get", "path": "/rollbackNodes"}, {"method": "get", "path": "/unbindFlow"}, {"method": "get", "path": "/startTasks"}, {"method": "post", "path": "/removeSign"}, {"method": "post", "path": "/transfer"}, {"method": "post", "path": "/rollback"}, {"method": "post", "path": "/approve"}, {"method": "post", "path": "/addSign"}, {"method": "post", "path": "/predict"}, {"method": "post", "path": "/revoke"}, {"method": "post", "path": "/reject"}, {"method": "post", "path": "/start"}, {"method": "get", "path": "/tasks"}, {"method": "post", "path": "/urge"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/messages/comm", "name": "", "api": [{"method": "get", "path": "/unread"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/messages/group", "name": "MessageGroupEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/messages/notification", "name": "NotificationEntity", "api": [{"method": "post", "path": "/allNotification"}, {"method": "post", "path": "/announcement"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/read"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/oidc/client", "name": "OidcClientEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/plugin/info", "name": "PluginInfoEntity", "api": [{"method": "post", "path": "/install"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/quality/check", "name": "CheckRuleEntity", "api": [{"method": "post", "path": "/outterSchemas"}, {"method": "post", "path": "/outterRecords"}, {"method": "post", "path": "/doQuerySql"}, {"method": "post", "path": "/handleOnce"}, {"method": "post", "path": "/handleAll"}, {"method": "post", "path": "/formList"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "formId", "type": "string", "length": "", "comment": "主表ID", "nullable": false, "source": "a.formId"}], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/task/info", "name": "TaskInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/start"}, {"method": "post", "path": "/once"}, {"method": "post", "path": "/stop"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/page"}, {"method": "get", "path": "/log"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "status", "type": "number", "length": "", "comment": "状态 0-停止 1-运行", "nullable": false, "defaultValue": 1, "source": "a.status"}, {"propertyName": "type", "type": "number", "length": "", "comment": "状态 0-系统 1-用户", "nullable": false, "defaultValue": 0, "source": "a.type"}], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/yszk/customermatchmaker", "name": "CustomerContactsEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/yszk/sendstrategy", "name": "SendStrategyEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/yszk/xzh", "name": "XzhEntity", "api": [{"method": "post", "path": "/sendFilePreviewHtml"}, {"method": "post", "path": "/sendFilePreview"}, {"method": "post", "path": "/sendXzh"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "询证函单号", "type": "string", "length": "", "comment": "询证函单号", "nullable": false, "source": "a.询证函单号"}, {"propertyName": "projectCode", "type": "string", "length": "", "comment": "所属项目代码", "nullable": false, "source": "a.projectCode"}, {"propertyName": "所属项目名称", "type": "string", "length": "", "comment": "所属项目名称", "nullable": false, "source": "a.所属项目名称"}, {"propertyName": "客商名称代码", "type": "string", "length": "", "comment": "客商名称代码", "nullable": false, "source": "a.客商名称代码"}, {"propertyName": "客商名称", "type": "string", "length": "", "comment": "客商名称", "nullable": false, "source": "a.客商名称"}, {"propertyName": "责任人", "type": "string", "length": "", "comment": "责任人", "nullable": true, "source": "a.责任人"}, {"propertyName": "应收账款会计期", "type": "string", "length": "", "comment": "应收账款会计期", "nullable": false, "source": "a.应收账款会计期"}, {"propertyName": "责任人", "type": "string", "length": "", "comment": "责任人", "nullable": true, "source": "a.责任人"}, {"propertyName": "责任人邮箱", "type": "string", "length": "", "comment": "责任人邮箱", "nullable": true, "source": "a.责任人邮箱"}, {"propertyName": "责任人手机号码", "type": "string", "length": "", "comment": "责任人手机号码", "nullable": true, "source": "a.责任人手机号码"}, {"propertyName": "客商联系人", "type": "string", "length": "100", "comment": "客商联系人", "nullable": true, "source": "a.客商联系人"}, {"propertyName": "客商联系人邮箱", "type": "string", "length": "100", "comment": "客商联系人邮箱", "nullable": true, "source": "a.客商联系人邮箱"}, {"propertyName": "客商联系人手机号码", "type": "string", "length": "100", "comment": "客商联系人手机号码", "nullable": true, "source": "a.客商联系人手机号码"}, {"propertyName": "发函状态", "type": "number", "length": "", "comment": "发函状态", "nullable": false, "defaultValue": 0, "source": "a.发函状态"}, {"propertyName": "预计发函时间", "type": "date", "length": "", "comment": "预计发函时间", "nullable": true, "source": "a.预计发函时间"}, {"propertyName": "实际发函时间", "type": "date", "length": "", "comment": "实际发函时间", "nullable": true, "source": "a.实际发函时间"}, {"propertyName": "回函时间", "type": "date", "length": "", "comment": "回函时间", "nullable": true, "source": "a.回函时间"}, {"propertyName": "回函人邮箱", "type": "string", "length": "", "comment": "回函人邮箱", "nullable": true, "source": "a.回函人邮箱"}, {"propertyName": "回函确认状态", "type": "number", "length": "", "comment": "回函确认状态", "nullable": false, "defaultValue": 0, "source": "a.回函确认状态"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "询证函单号", "type": "string", "length": "", "comment": "询证函单号", "nullable": false, "source": "a.询证函单号"}, {"propertyName": "projectCode", "type": "string", "length": "", "comment": "所属项目代码", "nullable": false, "source": "a.projectCode"}, {"propertyName": "所属项目名称", "type": "string", "length": "", "comment": "所属项目名称", "nullable": false, "source": "a.所属项目名称"}, {"propertyName": "客商名称代码", "type": "string", "length": "", "comment": "客商名称代码", "nullable": false, "source": "a.客商名称代码"}, {"propertyName": "客商名称", "type": "string", "length": "", "comment": "客商名称", "nullable": false, "source": "a.客商名称"}, {"propertyName": "责任人", "type": "string", "length": "", "comment": "责任人", "nullable": true, "source": "a.责任人"}, {"propertyName": "应收账款会计期", "type": "string", "length": "", "comment": "应收账款会计期", "nullable": false, "source": "a.应收账款会计期"}, {"propertyName": "责任人", "type": "string", "length": "", "comment": "责任人", "nullable": true, "source": "a.责任人"}, {"propertyName": "责任人邮箱", "type": "string", "length": "", "comment": "责任人邮箱", "nullable": true, "source": "a.责任人邮箱"}, {"propertyName": "责任人手机号码", "type": "string", "length": "", "comment": "责任人手机号码", "nullable": true, "source": "a.责任人手机号码"}, {"propertyName": "客商联系人", "type": "string", "length": "100", "comment": "客商联系人", "nullable": true, "source": "a.客商联系人"}, {"propertyName": "客商联系人邮箱", "type": "string", "length": "100", "comment": "客商联系人邮箱", "nullable": true, "source": "a.客商联系人邮箱"}, {"propertyName": "客商联系人手机号码", "type": "string", "length": "100", "comment": "客商联系人手机号码", "nullable": true, "source": "a.客商联系人手机号码"}, {"propertyName": "发函状态", "type": "number", "length": "", "comment": "发函状态", "nullable": false, "defaultValue": 0, "source": "a.发函状态"}, {"propertyName": "预计发函时间", "type": "date", "length": "", "comment": "预计发函时间", "nullable": true, "source": "a.预计发函时间"}, {"propertyName": "实际发函时间", "type": "date", "length": "", "comment": "实际发函时间", "nullable": true, "source": "a.实际发函时间"}, {"propertyName": "回函时间", "type": "date", "length": "", "comment": "回函时间", "nullable": true, "source": "a.回函时间"}, {"propertyName": "回函人邮箱", "type": "string", "length": "", "comment": "回函人邮箱", "nullable": true, "source": "a.回函人邮箱"}, {"propertyName": "回函确认状态", "type": "number", "length": "", "comment": "回函确认状态", "nullable": false, "defaultValue": 0, "source": "a.回函确认状态"}]}}, {"prefix": "/admin/yszk/xzhDetail", "name": "XzhDetailEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "询证函单号", "type": "string", "length": "", "comment": "询证函单号", "nullable": false, "source": "a.询证函单号"}, {"propertyName": "projectCode", "type": "string", "length": "", "comment": "所属项目代码", "nullable": false, "source": "a.projectCode"}, {"propertyName": "所属项目名称", "type": "string", "length": "", "comment": "所属项目名称", "nullable": false, "source": "a.所属项目名称"}, {"propertyName": "客商名称代码", "type": "string", "length": "", "comment": "客商名称代码", "nullable": false, "source": "a.客商名称代码"}, {"propertyName": "客商名称", "type": "string", "length": "", "comment": "客商名称", "nullable": false, "source": "a.客商名称"}, {"propertyName": "责任人", "type": "string", "length": "", "comment": "责任人", "nullable": true, "source": "a.责任人"}, {"propertyName": "账期", "type": "string", "length": "", "comment": "账期", "nullable": false, "source": "a.账期"}, {"propertyName": "责任人", "type": "string", "length": "", "comment": "责任人", "nullable": true, "source": "a.责任人"}, {"propertyName": "合同代码", "type": "string", "length": "", "comment": "合同代码", "nullable": false, "source": "a.合同代码"}, {"propertyName": "合同名称", "type": "string", "length": "", "comment": "合同名称", "nullable": true, "source": "a.合同名称"}, {"propertyName": "账龄", "type": "number", "length": "", "comment": "账龄", "nullable": false, "source": "a.账龄"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "询证函单号", "type": "string", "length": "", "comment": "询证函单号", "nullable": false, "source": "a.询证函单号"}, {"propertyName": "projectCode", "type": "string", "length": "", "comment": "所属项目代码", "nullable": false, "source": "a.projectCode"}, {"propertyName": "所属项目名称", "type": "string", "length": "", "comment": "所属项目名称", "nullable": false, "source": "a.所属项目名称"}, {"propertyName": "客商名称代码", "type": "string", "length": "", "comment": "客商名称代码", "nullable": false, "source": "a.客商名称代码"}, {"propertyName": "客商名称", "type": "string", "length": "", "comment": "客商名称", "nullable": false, "source": "a.客商名称"}, {"propertyName": "责任人", "type": "string", "length": "", "comment": "责任人", "nullable": true, "source": "a.责任人"}, {"propertyName": "合同代码", "type": "string", "length": "", "comment": "合同代码", "nullable": false, "source": "a.合同代码"}, {"propertyName": "合同名称", "type": "string", "length": "", "comment": "合同名称", "nullable": true, "source": "a.合同名称"}, {"propertyName": "账期", "type": "string", "length": "", "comment": "账期", "nullable": false, "source": "a.账期"}, {"propertyName": "账龄", "type": "number", "length": "", "comment": "账龄", "nullable": false, "source": "a.账龄"}]}}, {"prefix": "/admin/yszk/xzhRpaLog", "name": "XzhRpaLogEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/yszk/xzhSendLog", "name": "XzhSendLogEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/zhongtie/department_jc", "name": "ZTdepartmentJCEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "part_code", "type": "string", "length": "", "comment": "部门代码", "nullable": false, "source": "a.part_code"}, {"propertyName": "part_name", "type": "string", "length": "", "comment": "部门名称", "nullable": false, "source": "a.part_name"}, {"propertyName": "xm_code", "type": "string", "length": "", "comment": "项目代码", "nullable": false, "source": "a.xm_code"}, {"propertyName": "xm_name", "type": "string", "length": "", "comment": "项目名称", "nullable": false, "source": "a.xm_name"}, {"propertyName": "part_name_jc", "type": "string", "length": "", "comment": "部门名称（简称）", "nullable": false, "source": "a.part_name_jc"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "part_code", "type": "string", "length": "", "comment": "部门代码", "nullable": false, "source": "a.part_code"}, {"propertyName": "part_name", "type": "string", "length": "", "comment": "部门名称", "nullable": false, "source": "a.part_name"}, {"propertyName": "xm_code", "type": "string", "length": "", "comment": "项目代码", "nullable": false, "source": "a.xm_code"}, {"propertyName": "xm_name", "type": "string", "length": "", "comment": "项目名称", "nullable": false, "source": "a.xm_name"}, {"propertyName": "part_name_jc", "type": "string", "length": "", "comment": "部门名称（简称）", "nullable": false, "source": "a.part_name_jc"}]}}, {"prefix": "/admin/zhongtie/duibiaodanwei", "name": "DuibiaodanweiEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "name", "type": "string", "length": "", "comment": "单位名称", "nullable": false, "source": "a.name"}, {"propertyName": "only_code", "type": "string", "length": "", "comment": "社会统一代码", "nullable": false, "source": "a.only_code"}, {"propertyName": "as_name", "type": "string", "length": "", "comment": "名称（简称）", "nullable": false, "source": "a.as_name"}, {"propertyName": "stock_code", "type": "string", "length": "", "comment": "股票代码", "nullable": false, "source": "a.stock_code"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "单位名称", "nullable": false, "source": "a.name"}, {"propertyName": "only_code", "type": "string", "length": "", "comment": "社会统一代码", "nullable": false, "source": "a.only_code"}, {"propertyName": "as_name", "type": "string", "length": "", "comment": "名称（简称）", "nullable": false, "source": "a.as_name"}, {"propertyName": "stock_code", "type": "string", "length": "", "comment": "股票代码", "nullable": false, "source": "a.stock_code"}]}}, {"prefix": "/admin/zhongtie/email_flow", "name": "EmailFlowEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "name", "type": "string", "length": "", "comment": "流程名称", "nullable": false, "source": "a.name"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "流程名称", "nullable": false, "source": "a.name"}]}}, {"prefix": "/admin/zhongtie/email_user", "name": "EmailUesrEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "name", "type": "string", "length": "", "comment": "姓名", "nullable": false, "source": "a.name"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "姓名", "nullable": false, "source": "a.name"}]}}, {"prefix": "/admin/zhongtie/email_user_flow", "name": "EmailUserFlowEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/zhongtie/yuebiao", "name": "YuebiaoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "name", "type": "string", "length": "", "comment": "姓名", "nullable": false, "source": "a.name"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "姓名", "nullable": false, "source": "a.name"}]}}, {"prefix": "/admin/zhongtie/yusuan_part", "name": "YusuanPartEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "xm_name", "type": "string", "length": "", "comment": "项目名称", "nullable": false, "source": "a.xm_name"}, {"propertyName": "part_code", "type": "string", "length": "", "comment": "部门代码", "nullable": false, "source": "a.part_code"}, {"propertyName": "part_name", "type": "string", "length": "", "comment": "部门名称", "nullable": false, "source": "a.part_name"}, {"propertyName": "kongzhi_type", "type": "string", "length": "", "comment": "控制类型", "nullable": true, "source": "a.kongzhi_type"}, {"propertyName": "jflb", "type": "string", "length": "", "comment": "经费类别", "nullable": true, "source": "a.jflb"}, {"propertyName": "ysdl", "type": "string", "length": "", "comment": "预算大类", "nullable": true, "source": "a.ysdl"}, {"propertyName": "status", "type": "string", "length": "", "comment": "状态", "nullable": false, "source": "a.status"}, {"propertyName": "username", "type": "string", "length": "", "comment": "用户名", "nullable": false, "source": "a.username"}, {"propertyName": "year", "type": "string", "length": "", "comment": "年份", "nullable": false, "source": "a.year"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "xm_name", "type": "string", "length": "", "comment": "项目名称", "nullable": false, "source": "a.xm_name"}, {"propertyName": "part_code", "type": "string", "length": "", "comment": "部门代码", "nullable": false, "source": "a.part_code"}, {"propertyName": "part_name", "type": "string", "length": "", "comment": "部门名称", "nullable": false, "source": "a.part_name"}, {"propertyName": "kongzhi_type", "type": "string", "length": "", "comment": "控制类型", "nullable": true, "source": "a.kongzhi_type"}, {"propertyName": "jflb", "type": "string", "length": "", "comment": "经费类别", "nullable": true, "source": "a.jflb"}, {"propertyName": "ysdl", "type": "string", "length": "", "comment": "预算大类", "nullable": true, "source": "a.ysdl"}, {"propertyName": "status", "type": "string", "length": "", "comment": "状态", "nullable": false, "source": "a.status"}, {"propertyName": "username", "type": "string", "length": "", "comment": "用户名", "nullable": false, "source": "a.username"}, {"propertyName": "year", "type": "string", "length": "", "comment": "年份", "nullable": false, "source": "a.year"}]}}, {"prefix": "/admin/zhongtie/yusuan_xm", "name": "YusuanXmEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "xiang<PERSON>", "type": "string", "length": "", "comment": "项目", "nullable": false, "source": "a<PERSON>xiang<PERSON>"}, {"propertyName": "name", "type": "string", "length": "", "comment": "名称", "nullable": false, "source": "a.name"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "xiang<PERSON>", "type": "string", "length": "", "comment": "项目", "nullable": false, "source": "a<PERSON>xiang<PERSON>"}, {"propertyName": "name", "type": "string", "length": "", "comment": "名称", "nullable": false, "source": "a.name"}]}}, {"prefix": "/admin/zhongtie/zdfysLog", "name": "ZdfysLogEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "username", "type": "string", "length": "", "comment": "用户名", "nullable": false, "source": "a.username"}, {"propertyName": "xm_name", "type": "string", "length": "", "comment": "项目名称", "nullable": false, "source": "a.xm_name"}, {"propertyName": "part_code", "type": "string", "length": "", "comment": "部门代码", "nullable": false, "source": "a.part_code"}, {"propertyName": "part_name", "type": "string", "length": "", "comment": "部门名称", "nullable": false, "source": "a.part_name"}, {"propertyName": "only_code", "type": "string", "length": "", "comment": "唯一值", "nullable": false, "source": "a.only_code"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "username", "type": "string", "length": "", "comment": "用户名", "nullable": false, "source": "a.username"}, {"propertyName": "xm_name", "type": "string", "length": "", "comment": "项目名称", "nullable": false, "source": "a.xm_name"}, {"propertyName": "part_code", "type": "string", "length": "", "comment": "部门代码", "nullable": false, "source": "a.part_code"}, {"propertyName": "part_name", "type": "string", "length": "", "comment": "部门名称", "nullable": false, "source": "a.part_name"}, {"propertyName": "only_code", "type": "string", "length": "", "comment": "唯一值", "nullable": false, "source": "a.only_code"}]}}, {"prefix": "/admin/zhongtie/zhongtie_kucun", "name": "ZTEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "id", "type": "number", "length": "", "comment": "ID", "nullable": false, "source": "a.id"}, {"propertyName": "danhao", "type": "string", "length": "", "comment": "入库单号", "nullable": true, "source": "a.<PERSON>"}, {"propertyName": "ck_danhao", "type": "string", "length": "", "comment": "出库单号", "nullable": true, "source": "a.ck_danhao"}, {"propertyName": "xiang<PERSON>", "type": "string", "length": "", "comment": "项目", "nullable": true, "source": "a<PERSON>xiang<PERSON>"}, {"propertyName": "states", "type": "string", "length": "", "comment": "审核状态", "nullable": true, "source": "a.states"}, {"propertyName": "username", "type": "string", "length": "", "comment": "用户名", "nullable": false, "source": "a.username"}, {"propertyName": "danju_code", "type": "string", "length": "", "comment": "单据编号", "nullable": true, "source": "a.danju_code"}, {"propertyName": "baoguan_part", "type": "string", "length": "", "comment": "保管部门", "nullable": true, "source": "a.baoguan_part"}, {"propertyName": "huowu_name", "type": "string", "length": "", "comment": "货物名称", "nullable": true, "source": "a.huowu_name"}, {"propertyName": "reject_reason", "type": "string", "length": "", "comment": "驳回原因", "nullable": true, "source": "a.reject_reason"}, {"propertyName": "checker", "type": "string", "length": "", "comment": "审核员", "nullable": false, "source": "a.checker"}, {"propertyName": "lingyong_part", "type": "string", "length": "", "comment": "领用部门", "nullable": true, "source": "a.lingyong_part"}, {"propertyName": "pz_code", "type": "string", "length": "", "comment": "凭证号", "nullable": true, "source": "a.pz_code"}, {"propertyName": "pz_type", "type": "string", "length": "", "comment": "凭证类别", "nullable": true, "source": "a.pz_type"}, {"propertyName": "pz_date", "type": "string", "length": "", "comment": "凭证日期", "nullable": true, "source": "a.pz_date"}, {"propertyName": "ck_person", "type": "string", "length": "", "comment": "出库人", "nullable": true, "source": "a.ck_person"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "id", "type": "number", "length": "", "comment": "ID", "nullable": false, "source": "a.id"}, {"propertyName": "danhao", "type": "string", "length": "", "comment": "入库单号", "nullable": true, "source": "a.<PERSON>"}, {"propertyName": "ck_danhao", "type": "string", "length": "", "comment": "出库单号", "nullable": true, "source": "a.ck_danhao"}, {"propertyName": "xiang<PERSON>", "type": "string", "length": "", "comment": "项目", "nullable": true, "source": "a<PERSON>xiang<PERSON>"}, {"propertyName": "states", "type": "string", "length": "", "comment": "审核状态", "nullable": true, "source": "a.states"}, {"propertyName": "username", "type": "string", "length": "", "comment": "用户名", "nullable": false, "source": "a.username"}, {"propertyName": "danju_code", "type": "string", "length": "", "comment": "单据编号", "nullable": true, "source": "a.danju_code"}, {"propertyName": "baoguan_part", "type": "string", "length": "", "comment": "保管部门", "nullable": true, "source": "a.baoguan_part"}, {"propertyName": "huowu_name", "type": "string", "length": "", "comment": "货物名称", "nullable": true, "source": "a.huowu_name"}, {"propertyName": "reject_reason", "type": "string", "length": "", "comment": "驳回原因", "nullable": true, "source": "a.reject_reason"}, {"propertyName": "checker", "type": "string", "length": "", "comment": "审核员", "nullable": false, "source": "a.checker"}, {"propertyName": "lingyong_part", "type": "string", "length": "", "comment": "领用部门", "nullable": true, "source": "a.lingyong_part"}, {"propertyName": "pz_code", "type": "string", "length": "", "comment": "凭证号", "nullable": true, "source": "a.pz_code"}, {"propertyName": "pz_type", "type": "string", "length": "", "comment": "凭证类别", "nullable": true, "source": "a.pz_type"}, {"propertyName": "pz_date", "type": "string", "length": "", "comment": "凭证日期", "nullable": true, "source": "a.pz_date"}, {"propertyName": "ck_person", "type": "string", "length": "", "comment": "出库人", "nullable": true, "source": "a.ck_person"}]}}, {"prefix": "/admin/zhongtie/zhongtie_kucun_details", "name": "ZTdetailsEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "danhao", "type": "string", "length": "", "comment": "单号", "nullable": false, "source": "a.<PERSON>"}, {"propertyName": "xiang<PERSON>", "type": "string", "length": "", "comment": "项目", "nullable": false, "source": "a<PERSON>xiang<PERSON>"}, {"propertyName": "huowu_name", "type": "string", "length": "", "comment": "货物名称", "nullable": false, "source": "a.huowu_name"}, {"propertyName": "baoguan_part", "type": "string", "length": "", "comment": "保管部门", "nullable": false, "source": "a.baoguan_part"}, {"propertyName": "checker", "type": "string", "length": "", "comment": "审核员", "nullable": false, "source": "a.checker"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "danhao", "type": "string", "length": "", "comment": "单号", "nullable": false, "source": "a.<PERSON>"}, {"propertyName": "xiang<PERSON>", "type": "string", "length": "", "comment": "项目", "nullable": false, "source": "a<PERSON>xiang<PERSON>"}, {"propertyName": "huowu_name", "type": "string", "length": "", "comment": "货物名称", "nullable": false, "source": "a.huowu_name"}, {"propertyName": "baoguan_part", "type": "string", "length": "", "comment": "保管部门", "nullable": false, "source": "a.baoguan_part"}, {"propertyName": "checker", "type": "string", "length": "", "comment": "审核员", "nullable": false, "source": "a.checker"}]}}]