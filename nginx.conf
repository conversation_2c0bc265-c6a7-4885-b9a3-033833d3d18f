user  root;
worker_processes  2;
error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;
events {
  use epoll;
  worker_connections  10240;
}
http {
  include       /etc/nginx/mime.types;
  default_type  application/octet-stream;
  types {
      application/javascript mjs;
  }
  log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
  access_log  /var/log/nginx/access.log  main;
  client_max_body_size     200m;
  sendfile        on;
  keepalive_timeout  65;
  
  ## 启用gzip 静态文件返回，vite需要启用 vite-plugin-compression 插件
  gzip_static on;
  upstream cool {
      #keepalive 32;
      #keepalived_requests 100;
      #keepalive_timeout 60s;
      server ${be_name}:${be_port};
  }

  ## 同时处理uat 和prd的跨域请求
  map $http_origin $cors_origin {
      default "";
      "https://st-bi.sh-sxzn.com"          "https://st-bi.sh-sxzn.com";
      "https://st-bi.uat.sh-sxzn.com"      "https://st-bi.uat.sh-sxzn.com";
  }
  server {
    listen 80;
    server_name localhost;
    location / {
      root /app;
      index index.html;
      try_files $uri $uri/ /index.html;
    if ($request_method = 'OPTIONS') {
        add_header 'Access-Control-Allow-Origin' $cors_origin;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
        add_header 'Access-Control-Max-Age' 1728000;
        add_header 'Content-Type' 'text/plain charset=UTF-8';
        add_header 'Content-Length' 0;
        return 204;
    }

    add_header 'Access-Control-Allow-Origin' $cors_origin  always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range' always;
    add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;

    }
    location ^~ /oidc/ {
    proxy_pass http://cool/oidc/;
    proxy_set_header Host $http_host;  # 改为 $http_host 包含端口
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $http_host;  # 添加这行
    proxy_set_header X-Forwarded-Port $server_port;  # 添加这行
    proxy_redirect off;

    # 持久化连接相关配置
    proxy_connect_timeout 3000s;
    proxy_read_timeout 86400s;
    proxy_send_timeout 3000s;

    add_header X-Cache $upstream_cache_status;
}
    location /luru/
    {
        proxy_pass http://cool/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_redirect off;

      #缓存相关配置
      #proxy_cache cache_one;
      #proxy_cache_key $host$request_uri$is_args$args;
      #proxy_cache_valid 200 304 301 302 1h;

      #持久化连接相关配置
      proxy_connect_timeout 3000s;
      proxy_read_timeout 86400s;
      proxy_send_timeout 3000s;
      #proxy_http_version 1.1;
      #proxy_set_header Upgrade $http_upgrade;
      #proxy_set_header Connection "upgrade";

      add_header X-Cache $upstream_cache_status;

      #expires 12h;
    }
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
      root   /usr/share/nginx/html;
    }
  }
}