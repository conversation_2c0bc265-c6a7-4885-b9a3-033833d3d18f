{"name": "fe", "version": "8.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "build-static": "vite build --mode static", "build-demo": "vite build --mode demo", "preview": "vite preview", "build-only": "NODE_OPTIONS=--max_old_space_size=6000  vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --fix", "lint-staged": "lint-staged", "format": "prettier --write src/", "prepare": "husky install", "sort-pkg": "sort-package-json"}, "dependencies": {"@cool-vue/crud": "^8.0.4", "@element-plus/icons-vue": "^2.3.1", "@form-create/designer": "^3.3.0", "@form-create/element-ui": "^3.2.19", "@vueuse/core": "^12.5.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "alasql": "^4.5.2", "axios": "^1.7.9", "chardet": "^2.0.0", "core-js": "^3.40.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.3", "epic-designer": "^0.9.30", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "idb": "^8.0.0", "lodash-es": "^4.17.21", "marked": "^14.1.3", "mitt": "^3.0.1", "mockjs": "^1.1.0", "moment": "^2.29.4", "monaco-editor": "^0.52.0", "nprogress": "^0.2.0", "pdfjs-dist": "^5.2.133", "pinia": "^2.3.1", "randomcolor": "^0.6.2", "socket.io-client": "^4.8.0", "store": "^2.0.12", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-i18n": "^11.0.1", "vue-router": "^4.5.0", "vue-tables-2": "^2.3.5", "vuedraggable": "^4.1.0", "vxe-table": "^4.5.11", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@cool-vue/vite-plugin": "^8.1.2", "@intlify/unplugin-vue-i18n": "^6.0.3", "@rushstack/eslint-patch": "^1.10.5", "@tsconfig/node20": "^20.1.4", "@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/node": "^20.17.17", "@types/nprogress": "^0.2.3", "@types/store": "^2.0.5", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/compiler-sfc": "^3.5.13", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.3.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.20", "code-inspector-plugin": "^0.20.10", "eslint": "^9.19.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^9.32.0", "husky": "^8.0.0", "jsdom": "^25.0.1", "lint-staged": "^15.2.10", "npm-run-all2": "^6.2.3", "postcss": "^8.5.1", "prettier": "^3.4.2", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.81.0", "sort-package-json": "^2.10.1", "tailwindcss": "^3.4.17", "terser": "^5.36.0", "typescript": "~5.5.4", "vite": "^5.4.14", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "^7.7.1", "vue-tsc": "^2.2.0"}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": ["eslint --fix", "prettier --write"], "*.{css,scss,less,json,md}": ["prettier --write"]}}