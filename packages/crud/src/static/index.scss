.cl-crud {
	height: 100%;
	overflow: hidden auto;
	position: relative;
	box-sizing: border-box;
	background-color: #fff;

	&.is-border {
		border: 1px solid var(--el-border-color);
		border-radius: var(--el-border-radius-base);
	}

	& > .cl-row {
		width: 100%;

		&:not(.cl-row--last) > * {
			margin: 0 10px 10px 0;

			&:last-child {
				margin-right: 0;
			}
		}

		.cl-flex1 {
			margin-right: 0;
		}
	}
}

.cl-flex1 {
	flex: 1;
	font-size: 12px;
}

.cl-search-key {
	display: inline-flex;

	&__select {
		margin-right: 10px;

		&.el-select {
			width: 100px;
		}
	}

	&__wrap {
		display: inline-flex;

		.el-input {
			flex: 1;
		}

		.el-button {
			margin-left: 10px;
		}
	}
}

.cl-table {
	width: 100%;

	.el-table {
		&.el-loading-parent--relative {
			box-sizing: border-box;
		}

		&__header {
			.el-table__cell {
				background-color: var(--el-fill-color-lighter) !important;
				color: var(--el-text-color-primary);

				.cell {
					line-height: normal;
				}

				&.is-sortable {
					.cl-table-header__search {
						width: auto;

						&.is-input {
							width: calc(100% - 24px) !important;
						}
					}
				}
			}
		}

		&__empty-block {
			height: auto !important;
		}
	}

	.el-loading-mask {
		.el-loading-spinner {
			.el-icon-loading {
				font-size: 25px;
				color: #000;
			}

			.el-loading-text {
				color: var(--el-text-color-secondary);
				margin-top: 5px;
			}
		}
	}

	&__op {
		margin-bottom: -5px;

		.el-button {
			margin-bottom: 5px;
			outline-offset: -2px !important;

			&.is-text {
				border: 1px solid var(--el-button-border-color) !important;

				&:hover {
					background-color: var(--el-button-bg-color) !important;
				}
			}
		}
	}

	.cl-table-header__search {
		display: inline-flex;
		align-items: center;
		gap: 5px;
		width: 100%;
		cursor: pointer;

		&-label {
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 5px;
		}

		&:hover {
			color: var(--el-color-primary);
		}

		&-inner {
			flex: 1;

			& > div {
				width: 100%;

				.el-date-editor {
					margin-right: 0;
				}
			}
		}

		&-close {
			font-size: 14px;
			height: 30px;
			width: 30px;
			border: 1px solid var(--el-border-color);
			border-radius: 6px;
			background-color: var(--el-fill-color-blank);
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.2s;
			color: var(--el-text-color-secondary);

			&:hover {
				border-color: var(--el-border-color-hover);
				color: var(--el-text-color-primary);
			}
		}
	}

	.is-left {
		.cl-table-header__search-label {
			justify-content: flex-start;
		}
	}

	.is-right {
		.cl-table-header__search-label {
			justify-content: flex-end;
		}
	}
}

.cl-pagination {
	&.el-pagination {
		--el-pagination-border-radius: var(--el-border-radius-base);
	}

	.btn-prev,
	.btn-next,
	.el-pager li {
		border-radius: var(--el-border-radius-base);
	}
}

.cl-pagination {
	&.el-pagination {
		--el-pagination-border-radius: var(--el-border-radius-base);
	}

	.btn-prev,
	.btn-next,
	.el-pager li {
		border-radius: var(--el-border-radius-base);
	}
}

.cl-filter {
	display: flex;
	align-items: center;
	margin: 0 10px;

	&__label {
		font-size: 12px;
		margin-right: 10px;
		white-space: nowrap;
	}

	.el-select {
		min-width: 120px;
	}
}

.cl-search {
	&__btns {
		margin-left: 5px;
	}

	&__more {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.el-form-item {
			margin-bottom: 0;
		}
	}

	.el-form:not(.el-form--label-top) {
		.el-form-item__label {
			&:empty {
				display: none;
			}
		}

		.cl-search__btns {
			.el-form-item__label {
				display: none;
			}
		}
	}

	.cl-search__btns {
		.el-button + .el-button {
			margin-left: 10px;
		}
	}

	&.is-inline {
		margin-bottom: 0 !important;
	}

	&.is-collapse {
		background-color: var(--el-fill-color-lighter);
		padding: 10px;
		margin-bottom: 10px !important;
		border-radius: 6px;
		border: 1px solid var(--el-border-color-extra-light);

		&.is-fold {
			.cl-search__form {
				max-height: 42px;
				overflow: hidden;

				&:has(.el-form--label-top) {
					max-height: 68px;
				}
			}
		}
	}
}

.cl-adv-btn {
	margin-left: 10px;

	.el-icon {
		margin-right: 5px;
	}
}

.cl-adv-search {
	&.el-drawer {
		background-color: #fff;
	}

	.el-drawer__body {
		padding: 0;
	}

	&__header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 50px;
		padding: 0 15px 0 20px;
		user-select: none;

		.text {
			font-size: 16px;
		}

		.el-icon {
			cursor: pointer;

			&:hover {
				color: red;
			}
		}
	}

	&__container {
		height: calc(100% - 110px);
		overflow-y: auto;
		padding: 10px 20px;
		box-sizing: border-box;

		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-thumb {
			border-radius: 6px;
			background-color: rgba(144, 147, 153, 0.3);
		}

		.el-form-item__content {
			& > div {
				width: 100%;
			}
		}
	}

	&__footer {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		height: 60px;
		border-top: 1px solid var(--el-border-color-extra-light);
		padding: 0 10px;
		box-sizing: border-box;
	}
}

.cl-form {
	[class*="el-col-"].is-guttered {
		min-height: 0;
	}

	&-item {
		display: flex;

		&__component {
			display: flex;

			&.flex1 {
				flex: 1;
				width: 100%;

				& > div {
					width: 100%;
				}
			}
		}

		&__prepend {
			margin-right: 10px;
		}

		&__append {
			margin-left: 10px;
		}

		&__collapse {
			width: 100%;
			font-size: 12px;
			cursor: pointer;

			.el-divider {
				margin: 16px 0;

				&__text {
					font-size: 12px;
				}
			}

			i {
				margin-left: 6px;
			}
		}

		.el-table__header tr {
			line-height: normal;
		}
	}

	&__footer {
		display: flex;
		justify-content: flex-end;
	}

	.cl-crud {
		line-height: normal;
	}

	.el-form {
		.el-form-item {
			.el-form-item {
				margin-bottom: 18px;
			}

			.el-input-number {
				&__decrease,
				&__increase {
					border: 0;
					background-color: transparent;
				}
			}

			&__label {
				.el-tooltip {
					i {
						margin-left: 5px;
					}
				}
			}

			&__content {
				min-width: 0px;

				& > div {
					width: 100%;
				}
			}
		}

		&:not(.el-form--label-top) {
			.el-form-item {
				&.no-label {
					& > .el-form-item__label {
						padding: 0;
						display: none;
					}
				}
			}
		}

		&.el-form--label-top {
			.el-form-item {
				margin-bottom: 22px;
			}

			.el-form-item__label {
				margin: 0 0 4px 0;
				min-height: 22px;
			}

			.el-form-item__error {
				padding-top: 4px;
			}
		}

		&.el-form--inline {
			.cl-form__items {
				display: flex;
				flex-wrap: wrap;
			}

			.el-form-item {
				margin: 0 10px 10px 0;

				.el-date-editor {
					box-sizing: border-box;

					.el-range-input {
						&:nth-child(2) {
							margin-left: 5px;
						}
					}
				}

				.el-select {
					width: 173px;
				}

				&:last-child {
					margin-right: 0;
				}
			}
		}
	}
}

.cl-form-tabs {
	border-bottom: 1px solid var(--el-border-color);
	overflow: hidden;
	width: calc(100% - 10px);
	margin: 0 5px 20px 5px;

	&__wrap {
		height: 35px;
		width: 100%;
		overflow-x: auto;
		overflow-y: hidden;
		scrollbar-width: none;
		-ms-overflow-style: none;
		position: relative;

		&::-webkit-scrollbar {
			display: none;
		}
	}

	ul {
		display: inline-flex;
		white-space: nowrap;
		margin: 0;
		padding: 0;

		li {
			display: inline-flex;
			align-items: center;
			list-style: none;
			padding: 0 20px;
			height: 35px;
			cursor: pointer;

			.el-icon {
				margin-right: 5px;
				font-size: 16px;
			}

			&.is-active {
				color: var(--el-color-primary);
			}
		}
	}

	&__line {
		height: 3px;
		width: 100%;
		position: absolute;
		bottom: -1px;
		left: 0;
		transition:
			transform 0.3s ease-in-out,
			width 0.2s 0.1s cubic-bezier(0.645, 0.045, 0.355, 1);
		background-color: var(--el-color-primary);
	}

	&--card {
		.cl-form-tabs__line {
			display: none;
		}

		ul {
			border: 1px solid var(--el-border-color);
			border-top-left-radius: 5px;
			border-top-right-radius: 5px;

			li {
				border-left: 1px solid var(--el-border-color);

				&:first-child {
					border-left-width: 0;
				}
			}
		}
	}
}

.cl-form-card {
	margin-top: 0;

	&__header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 14px;
		height: 32px;
		line-height: normal;
		font-weight: bold;
		padding: 0 10px;
		background-color: var(--el-fill-color-lighter);
		border-radius: var(--el-border-radius-base);
		cursor: pointer;
		user-select: none;

		&:hover {
			background-color: var(--el-fill-color-light);
		}
	}

	&__container {
		transition: all 0.3s;
		display: grid;
		grid-template-rows: 0fr;

		> .cl-form-item__children {
			min-height: 0;
			overflow: hidden;

			.el-row {
				margin-top: 10px;
			}
		}
	}

	&.is-expand {
		> .cl-form-card__container {
			grid-template-rows: 1fr;
			margin-bottom: -18px;
		}
	}

	.cl-form-card {
		margin-left: 10px;
	}
}

.cl-dialog {
	display: flex;
	flex-direction: column;

	&.el-dialog {
		padding: 0;
		border-radius: 8px;
	}

	.el-dialog {
		&__header {
			padding: 0;
			margin-right: 0;

			&-slot {
				&.is-drag {
					-moz-user-select: none;
					-webkit-user-select: none;
					-ms-user-select: none;
					-khtml-user-select: none;
					user-select: none;
					cursor: move;
				}
			}
		}

		&__body {
			padding: 0;
			box-sizing: border-box;
			flex: 1;
			overflow: hidden;
		}

		&__footer {
			padding: 0;
		}
	}

	&__header {
		position: relative;
		padding: 10px;
		border-bottom: 1px solid var(--el-border-color-extra-light);
		text-align: center;
		user-select: none;
	}

	&__default {
		box-sizing: border-box;
	}

	&__footer {
		border-top: 1px solid var(--el-border-color-extra-light);
		padding: 20px;
	}

	&__title {
		display: block;
		font-size: 15px;
		letter-spacing: 0.5px;
	}

	&__controls {
		display: flex;
		justify-content: flex-end;
		position: absolute;
		right: 8px;
		top: 8px;
		z-index: 9;
		width: 100%;

		&-icon,
		.minimize,
		.maximize,
		.close {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 28px;
			width: 28px;
			border: 0;
			background-color: transparent;
			cursor: pointer;
			outline: none;
			border-radius: 6px;
			margin-left: 5px;

			i {
				font-size: 18px;
			}

			&:hover {
				background-color: var(--el-fill-color-darker);
			}
		}
	}

	&.hidden-header {
		.el-dialog__header {
			display: none;
		}
	}

	&.is-fullscreen {
		height: 100vh !important;
		border-radius: 0;
		overflow: hidden;

		.cl-dialog__container {
			height: 100% !important;
		}
	}

	&.is-transparent {
		background-color: transparent !important;
	}
}

.cl-context-menu {
	position: absolute;
	z-index: 9999;

	&__box {
		width: 160px;
		background-color: var(--el-bg-color);
		border: 1px solid var(--el-border-color-extra-light);
		box-shadow:
			0 4px 6px -1px rgba(0, 0, 0, 0.1),
			0 2px 4px -2px rgba(0, 0, 0, 0.1);
		border-radius: 6px;
		position: absolute;
		top: 0;
		padding: 5px;

		&.is-append {
			right: calc(-100% - 5px);
			top: -5px;
		}

		& > div {
			display: flex;
			align-items: center;
			font-size: 12px;
			cursor: pointer;
			padding: 5px 15px;
			color: var(--el-text-color-primary);
			position: relative;
			border-radius: 4px;

			span {
				flex: 1;
				user-select: none;
			}

			&:hover {
				background-color: var(--el-fill-color-lighter);
			}

			i {
				&:first-child {
					margin-right: 5px;
				}

				&:last-child {
					margin-left: 5px;
				}
			}

			&.is-active {
				background-color: #f7f7f7;
				color: #000;
			}

			&.is-ellipsis {
				span {
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}

			&.is-disabled {
				span {
					color: #ccc;

					&:hover {
						color: #ccc;
					}
				}
			}
		}
	}

	&__target {
		position: relative;

		&::after {
			content: "";
			display: block;
			position: absolute;
			left: 0;
			top: 0;
			height: 100%;
			width: 100%;
			background-color: rgba(0, 0, 0, 0.05);
		}
	}
}

@media only screen and (max-width: 768px) {
	.el-table {
		&__body {
			&-wrapper {
				&::-webkit-scrollbar {
					height: 6px;
					width: 6px;
				}
			}
		}
	}

	.cl-search-key {
		width: 100%;
		margin-right: 0 !important;

		&__wrap {
			width: 100% !important;
		}
	}
}

.cl-error-message {
	display: flex;
	align-items: center;
	height: 32px;
	padding: 0 10px;
	font-size: 14px;
	color: var(--el-color-danger);
	border: 1px solid var(--el-color-danger);
	border-radius: 4px;
	box-sizing: border-box;
	user-select: none;
}
