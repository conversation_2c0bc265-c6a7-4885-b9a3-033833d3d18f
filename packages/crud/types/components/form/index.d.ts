declare const _default: import("vue").DefineComponent<
	import("vue").ExtractPropTypes<{
		name: StringConstructor;
		inner: BooleanConstructor;
		inline: BooleanConstructor;
		enablePlugin: {
			type: BooleanConstructor;
			default: boolean;
		};
	}>,
	() => any,
	{},
	{},
	{},
	import("vue").ComponentOptionsMixin,
	import("vue").ComponentOptionsMixin,
	{},
	string,
	import("vue").PublicProps,
	Readonly<
		import("vue").ExtractPropTypes<{
			name: StringConstructor;
			inner: BooleanConstructor;
			inline: BooleanConstructor;
			enablePlugin: {
				type: BooleanConstructor;
				default: boolean;
			};
		}>
	> &
		Readonly<{}>,
	{
		inline: boolean;
		inner: boolean;
		enablePlugin: boolean;
	},
	{},
	{},
	{},
	string,
	import("vue").ComponentProvideOptions,
	true,
	{},
	any
>;
export default _default;
