import { createApp } from "vue";
import App from "./App.vue";
import { bootstrap } from "./cool";
import "./style.css";

const app = createApp(App);
function reportErrorToMonitoringSystem(err: any) {
	(window as any).microApp?.dispatch({
		msg: err
	});
}
// 启动
bootstrap(app)
	.then(() => {
		// 全局错误处理器
		// app.config.errorHandler = (err, vm, info) => {
		// 	reportErrorToMonitoringSystem(
		// 		`Vue 全局错误捕获:${err},错误信息:${info},发生错误的组件:${vm}`
		// 	);
		// };
		// // 捕获未处理的 Promise 错误
		// window.addEventListener("unhandledrejection", event => {
		// 	reportErrorToMonitoringSystem(`未捕获的 Promise 错误:${JSON.stringify(event)}`);
		// });

		// // 捕获全局 JavaScript 错误
		// window.onerror = (message, source, lineno, colno, error) => {
		// 	reportErrorToMonitoringSystem(
		// 		`全局 JavaScript 错误捕获:${message},source:${source},lineno:${lineno},colno:${colno},error:${error}`
		// 	);
		// };
		app.mount("#app");
	})
	.catch(err => {
		console.error("COOL-ADMIN 启动失败", err);
	});
