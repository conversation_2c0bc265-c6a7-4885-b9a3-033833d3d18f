<template>
	<div class="page-login">
		<el-container class="box" v-loading="loading">
			<!-- <div class="logo">

			</div> -->

			<div class="form">
				<el-form label-position="top" class="form" :disabled="saving">
					<el-form-item label="用户名">
						<input
							v-model="form.username"
							placeholder="请输入用户名"
							maxlength="20"
							type="text"
							:readonly="readonly"
							autocomplete="off"
							@focus="readonly = false"
						/>
					</el-form-item>

					<el-form-item label="密码">
						<input
							v-model="form.password"
							type="password"
							placeholder="请输入密码"
							maxlength="40"
							autocomplete="off"
						/>
					</el-form-item>

					<el-form-item label="验证码">
						<div class="row">
							<input
								v-model="form.verifyCode"
								placeholder="图片验证码"
								maxlength="4"
								@keyup.enter="toLogin"
							/>

							<pic-captcha
								:ref="setRefs('picCaptcha')"
								v-model="form.captchaId"
								@change="
									() => {
										form.verifyCode = '';
									}
								"
							/>
						</div>
					</el-form-item>

					<div class="op">
						<el-button type="primary" :loading="saving" @click="toLogin"
							>登录</el-button
						>
					</div>
				</el-form>
			</div>
		</el-container>

		<div class="bg">
			<cl-svg name="bg"></cl-svg>
		</div>
	</div>
</template>

<script lang="ts" name="login" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useCool } from '/@/cool';
import { useBase } from '/$/base';
import PicCaptcha from './components/pic-captcha.vue';
import { storage } from '/@/cool/utils';

const { refs, setRefs, router, service } = useCool();
const { user, app, org } = useBase();

// 状态
const loading = ref(false);

// 避免自动填充
const readonly = ref(true);

// 表单数据
const form = reactive({
	username: storage.get('username') || '',
	password: '',
	captchaId: '',
	verifyCode: ''
});
const saving = ref(false);
const isTokenLogin = ref(true);

function getRedirectPath() {
	// 从 query 中获取重定向地址
	const redirect = router.currentRoute.value.query.redirect as string;
	const redirect_uri = router.currentRoute.value.query.redirect_uri as string;

	// 如果有OIDC重定向地址，优先使用
	if (redirect_uri) {
		return redirect_uri;
	}

	// 如果有普通重定向地址且不是登录页面，则使用该地址
	if (redirect && !redirect.includes('/login')) {
		return redirect;
	}

	// 否则默认跳转到首页
	return '/home';
}

function handleOIDCRedirect(token: string) {
	// 检查是否是OIDC重定向
	const redirect_uri = router.currentRoute.value.query.redirect_uri as string;
	const error = router.currentRoute.value.query.error as string;

	console.log('AdminLogin: handleOIDCRedirect called with token:', token);
	console.log('AdminLogin: redirect_uri:', redirect_uri);
	console.log('AdminLogin: error:', error);

	if (redirect_uri) {
		// 构建重定向URL，携带token参数
		const url = new URL(redirect_uri);
		url.searchParams.set('token', token);

		// 如果有错误信息，也传递过去
		if (error) {
			url.searchParams.set('login_error', error);
		}

		console.log('AdminLogin: OIDC redirect to:', url.toString());
		window.location.href = url.toString();
		return true;
	}

	console.log('AdminLogin: No OIDC redirect_uri found');
	return false;
}

// 登录
async function toLogin() {
	if (!form.username) {
		return ElMessage.error('用户名不能为空');
	}

	if (!form.password) {
		return ElMessage.error('密码不能为空');
	}

	if (!form.verifyCode) {
		return ElMessage.error('图片验证码不能为空');
	}

	saving.value = true;

	try {
		// 登录
		const res = await service.base.open.login(form);
		// 	try{

		//const res=await service.base.open.tokenlogin({token:`Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiLmm7nmlY_nkZ4iLCJwYXNzd29yZCI6MTExMzQ1NDU1MCwiZXhwIjoxNzI0NzU2Mjg2fQ.IvmTZGVmXMIpYpq7X6FiRPX8uV4gxDRKKSpKV0snHH0`})
		// const res=await service.base.open.tokenlogin({token:`Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsInBhc3N3b3JkIjotMzE4Mjk2MDg0LCJleHAiOjE3MjQ3NjU0NjV9.pFeOa4GuXRi-C81hyLBtLJ01n9BzngpG-BlRQrIt2fs`})
		user.setToken({
			expire: res.expire,
			token: res.token,
			refreshExpire: res.refreshExpire,
			refreshToken: res.refreshToken
		});
		// 	console.log("res",res);
		// }catch(err){
		// 	ElMessage.error(JSON.stringify(err.message))
		// 	saving.value =false
		// 	return
		// }

		user.setUserPromission(res.permissions);
		// token 事件
		await Promise.all(app.events.hasToken.map(e => e()));

		// 设置缓存
		storage.set('username', form.username);

		// 检查是否是OIDC重定向，如果是则处理OIDC重定向
		console.log('AdminLogin: Login successful, checking OIDC redirect...');
		console.log('AdminLogin: Current query:', router.currentRoute.value.query);
		console.log('AdminLogin: Token:', res.token);

		if (!handleOIDCRedirect(res.token)) {
			// 不是OIDC重定向，使用普通重定向逻辑
			console.log(
				'AdminLogin: No OIDC redirect, using normal redirect to:',
				getRedirectPath()
			);
			router.push(getRedirectPath());
		}
	} catch (err: any) {
		refs.picCaptcha.refresh();
		ElMessage.error(err.message);
	}

	saving.value = false;
}

// 页面加载时检查OIDC重定向
onMounted(() => {
	console.log('AdminLogin: Page mounted');
	console.log('AdminLogin: Current route:', router.currentRoute.value);
	console.log('AdminLogin: Current query:', router.currentRoute.value.query);

	const redirect_uri = router.currentRoute.value.query.redirect_uri as string;
	const error = router.currentRoute.value.query.error as string;

	if (redirect_uri) {
		console.log('AdminLogin: OIDC redirect detected:', redirect_uri);
		if (error) {
			console.log('AdminLogin: OIDC error:', error);
			const errorMessages = {
				invalid_token: 'Token已过期',
				no_account: '账户信息无效',
				session_expired: '授权会话已过期，请重新登录'
			};
			ElMessage.error(`登录错误: ${errorMessages[error] || error}`);
		}

		// 检查用户是否已经登录
		if (user.token && !storage.isExpired('token')) {
			console.log('AdminLogin: User already logged in, redirecting with existing token');
			// 用户已登录，直接使用现有token重定向
			handleOIDCRedirect(user.token);
			return;
		}

		// 用户未登录，显示提示信息
		if (!error) {
			ElMessage.info('请登录以继续授权流程');
		}
	} else {
		console.log('AdminLogin: No OIDC redirect detected');
	}
});
</script>

<style lang="scss" scoped>
$color: #2c3142;

.page-login {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	width: 100%;
	position: relative;
	background-color: #fff;
	color: $color;

	.bg {
		position: absolute;
		left: 0;
		top: 0;
		height: 100%;
		width: 90%;
		pointer-events: none;
		transform: rotate(180deg) scaleY(-1);

		.cl-svg {
			height: 100%;
			width: 100%;
			fill: $color;
		}
	}

	.copyright {
		position: absolute;
		bottom: 15px;
		left: 0;
		text-align: center;
		width: 100%;
		color: #666;
		font-size: 14px;
	}

	.box {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 100%;
		width: 50%;
		position: absolute;
		right: 0;
		top: 0;
		z-index: 9;

		.logo {
			height: 50px;
			margin-bottom: 20px;
			display: flex;
			align-items: center;

			img {
				height: 46px;
				background-color: $color;
				border-radius: 50px;
				border: 3px solid $color;
				margin-right: 10px;
			}

			span {
				display: inline-block;
				font-size: 38px;
				font-weight: bold;
				line-height: 1;
				letter-spacing: 3px;

				&:nth-child(6) {
					animation: dou 1s infinite linear;
				}
			}
		}

		.desc {
			font-size: 15px;
			letter-spacing: 1px;
			margin-bottom: 50px;
		}

		.form {
			width: 300px;

			:deep(.el-form) {
				.el-form-item {
					margin-bottom: 20px;
				}

				.el-form-item__label {
					padding-left: 5px;
				}

				input {
					height: 45px;
					width: 100%;
					box-sizing: border-box;
					font-size: 17px;
					border: 0;
					border-radius: 0;
					background-color: #f8f8f8;
					padding: 0 15px;
					border-radius: 6px;
					position: relative;

					&:-webkit-autofill {
						box-shadow: none;
						-webkit-box-shadow: 0 0 0 1000px #f8f8f8 inset;
						box-shadow: 0 0 0 1000px #f8f8f8 inset;
					}

					&::placeholder {
						font-size: 14px;
					}
				}

				.row {
					display: flex;
					align-items: center;
					width: 100%;
					position: relative;

					.pic-captcha {
						position: absolute;
						right: 0;
						top: 0;
					}
				}
			}
		}

		.op {
			display: flex;
			justify-content: center;
			margin-top: 40px;

			:deep(.el-button) {
				height: 45px;
				width: 100%;
				font-size: 15px;
				border-radius: 6px;
				letter-spacing: 1px;
			}
		}
	}
}

@media screen and (max-width: 1024px) {
	.page-login {
		.box {
			width: 100%;
		}
	}
}
</style>
