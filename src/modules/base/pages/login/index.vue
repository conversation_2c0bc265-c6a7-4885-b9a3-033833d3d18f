<template>
	<el-container class="box" v-loading="loading">
		<div class="page-login">
			<!-- <div class="logo">

			</div> -->

			<div class="bg">
				<cl-svg name="bg"></cl-svg>
			</div>
		</div>
	</el-container>
</template>

<script lang="ts" name="login" setup>
import { ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useCool } from '/@/cool';
import { useBase } from '/$/base';
import { storage } from '/@/cool/utils';
import { clearDictDB } from '/@/modules/luru/utils/indexDB';
const { refs, router, service } = useCool();
const { user, app } = useBase();
// 状态
const loading = ref(false);
window.onerror = (message, source, lineno, colno, error) => {
	//console.error("全局 JavaScript 错误捕获:", message, source, lineno, colno, error);
	(window as any).microApp.dispatch({
		msg: `11全局 JavaScript 错误捕获:${message},source:${source},lineno:${lineno},colno:${colno},error:${error}`
	});
};

const isTokenLogin = ref(true);

function getRedirectPath() {
	// 从 query 中获取重定向地址
	const redirect = router.currentRoute.value.query.redirect as string;
	const redirect_uri = router.currentRoute.value.query.redirect_uri as string;

	// 如果有OIDC重定向地址，优先使用
	if (redirect_uri) {
		return redirect_uri;
	}

	// 如果有普通重定向地址且不是登录页面，则使用该地址
	if (redirect && !redirect.includes('/login')) {
		return redirect;
	}

	// 否则默认跳转到首页
	return '/home';
}

function handleOIDCRedirect(token: string) {
	// 检查是否是OIDC重定向
	const redirect_uri = router.currentRoute.value.query.redirect_uri as string;
	const error = router.currentRoute.value.query.error as string;

	console.log('MainLogin: handleOIDCRedirect called with token:', token);
	console.log('MainLogin: redirect_uri:', redirect_uri);
	console.log('MainLogin: error:', error);

	if (redirect_uri) {
		// 构建重定向URL，携带token参数
		const url = new URL(redirect_uri);
		url.searchParams.set('token', token);

		// 如果有错误信息，也传递过去
		if (error) {
			url.searchParams.set('login_error', error);
		}

		console.log('MainLogin: OIDC redirect to:', url.toString());
		window.location.href = url.toString();
		return true;
	}

	console.log('MainLogin: No OIDC redirect_uri found');
	return false;
}
// 登录
//onMounted(async () => {
setTimeout(async () => {
	//user.clear()

	// 检查是否是OIDC重定向
	const redirect_uri = router.currentRoute.value.query.redirect_uri as string;
	const error = router.currentRoute.value.query.error as string;

	if (redirect_uri) {
		console.log('OIDC redirect detected:', redirect_uri);
		if (error) {
			console.log('OIDC error:', error);
			const errorMessages = {
				invalid_token: 'Token已过期',
				no_account: '账户信息无效',
				session_expired: '授权会话已过期，请重新登录'
			};
			ElMessage.error(`登录错误: ${errorMessages[error] || error}`);
		}

		// 检查用户是否已经登录
		if (user.token && !storage.isExpired('token')) {
			console.log('User already logged in, redirecting with existing token');
			// 用户已登录，直接使用现有token重定向
			if (handleOIDCRedirect(user.token)) {
				return;
			}
		} else {
			// 用户未登录，直接重定向到adminLogin进行手动登录
			console.log('User not logged in, redirecting to adminLogin for OIDC flow');
			loading.value = false;
			router.push({
				path: '/adminLogin',
				query: router.currentRoute.value.query
			});
			return;
		}
	}

	(window as any).microApp.dispatch({ msg: '到达子应用' });
	const biToken = (window as any).microApp.getData();
	if (isTokenLogin.value && biToken) {
		clearDictDB();
		loading.value = true;
		try {
			isTokenLogin.value = false;
			(window as any).microApp.dispatch({ msg: '开始请求' });
			// 登录
			//const biToken = (window as any).microApp.getData();
			//(window as any).microApp.dispatch({msg:biToken.token})
			//await service.base.open.login({username:"cmr",password:"xxxxxx",captchaId:"kkk",verifyCode:"mmm"})
			try {
				//await service.base.open.login(form).then(user.setToken);
				const res = await service.base.open.tokenlogin({
					token: biToken.token.replace('Bearer ', '')
				}); //user.setToken({expire:res.expire,token:res.token,refreshExpire:res.refreshExpire,refreshToken:res.refreshToken})
				console.log('res', res);
				user.setToken({
					expire: res.expire,
					token: res.token,
					refreshExpire: res.refreshExpire,
					refreshToken: res.refreshToken
				});
				storage.set('username', biToken.jqId);
				user.setUserPromission(res.permissions);
				await Promise.all(app.events.hasToken.map(e => e()));

				//user.setToken({expire:res.expire,token:res.token,refreshExpire:res.refreshExpire,refreshToken:res.refreshToken});

				// token 事件

				// 设置缓存
				//storage.set("userPromission", res.projectPermissions);
				(window as any).microApp.dispatch({ msg: `获得LLLL${JSON.stringify(user.info)}` });
				//user.setUserPromission(res.projectPermissions);
				//console.log("res",res)
				//storage.set("username", biToken.username);
				//storage.set("username", biToken.jqId);
				console.log(user.info);
				(window as any).microApp.dispatch({
					msg: `获得user.info${JSON.stringify(user.info)}`
				});

				// 检查是否是OIDC重定向，如果是则处理OIDC重定向
				if (!handleOIDCRedirect(res.token)) {
					// 不是OIDC重定向，使用普通重定向逻辑
					router.push(getRedirectPath());
				}
			} catch (err: any) {
				ElMessageBox.confirm(err.message, '提示', {
					type: 'warning',
					confirmButtonText: '确认'
				}).then(() => {
					router.push('/404');
				});
				return;
			}
		} catch (err: any) {
			refs.value.captcha.refresh();
			ElMessage.error(err.message);
		}
		loading.value = false;
	} else {
		// 没有微前端token，检查是否是OIDC重定向
		const redirect_uri = router.currentRoute.value.query.redirect_uri as string;
		const error = router.currentRoute.value.query.error as string;

		if (redirect_uri) {
			loading.value = false;
			console.log('OIDC redirect without microApp token:', redirect_uri);

			// 再次检查用户是否已经登录（可能在setTimeout期间登录状态发生变化）
			if (user.token && !storage.isExpired('token')) {
				console.log('User logged in during timeout, redirecting with existing token');
				handleOIDCRedirect(user.token);
				return;
			}

			if (error) {
				const errorMessages = {
					invalid_token: 'Token已过期',
					no_account: '账户信息无效',
					session_expired: '授权会话已过期，请重新登录'
				};
				ElMessage.error(`需要重新登录: ${errorMessages[error] || error}`);
			} else {
				ElMessage.info('请登录以继续授权流程');
			}
			// 这里可以显示登录表单让用户手动登录
			// 或者重定向到adminLogin页面
			console.log('Redirecting to adminLogin for manual login');
			router.push({
				path: '/adminLogin',
				query: router.currentRoute.value.query
			});
		} else {
			loading.value = false;
		}
	}
}, 500);
//})
</script>

<style lang="scss" scoped>
$color: #2c3142;

.page-login {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	width: 100%;
	position: relative;
	background-color: #fff;
	color: $color;

	.bg {
		position: absolute;
		left: 0;
		top: 0;
		height: 100%;
		width: 90%;
		pointer-events: none;
		transform: rotate(180deg) scaleY(-1);

		.cl-svg {
			height: 100%;
			width: 100%;
		}
	}

	.copyright {
		position: absolute;
		bottom: 15px;
		left: 0;
		text-align: center;
		width: 100%;
		color: var(--el-color-info);
		font-size: 14px;
		user-select: none;
	}

	.box {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 100%;
		width: 50%;
		position: absolute;
		right: 0;
		top: 0;
		z-index: 9;

		.logo {
			height: 50px;
			margin-bottom: 20px;
			display: flex;
			align-items: center;
			user-select: none;

			.icon {
				border-radius: 8px;
				padding: 5px;
				margin-right: 10px;
				background-color: $color;

				img {
					height: 36px;
				}
			}

			span {
				font-size: 38px;
				font-weight: bold;
				line-height: 1;
				letter-spacing: 3px;
			}
		}

		.desc {
			font-size: 15px;
			letter-spacing: 1px;
			margin-bottom: 50px;
			user-select: none;
			max-width: 80%;
			text-align: center;
		}

		.form {
			width: 300px;

			:deep(.el-form) {
				.el-form-item {
					margin-bottom: 20px;
				}

				.el-form-item__label {
					color: var(--el-color-info);
					padding-left: 5px;
					user-select: none;
				}

				.el-input {
					box-sizing: border-box;
					font-size: 15px;
					border: 0;
					border-radius: 0;
					background-color: #f8f8f8;
					padding: 0 5px;
					border-radius: 8px;
					position: relative;

					&__wrapper {
						box-shadow: none;
						background-color: transparent;
					}

					&__inner {
						height: 45px;
						color: #333;
					}

					&:-webkit-autofill {
						-webkit-box-shadow: 0 0 0 1000px #f8f8f8 inset;
						box-shadow: 0 0 0 1000px #f8f8f8 inset;
					}
				}
			}

			:deep(.pic-captcha) {
				position: absolute;
				right: -5px;
				top: 0;
			}
		}

		.op {
			display: flex;
			justify-content: center;
			margin-top: 40px;

			:deep(.el-button) {
				height: 45px;
				width: 100%;
				font-size: 16px;
				border-radius: 8px;
				letter-spacing: 1px;
			}
		}
	}
}

@media screen and (max-width: 1024px) {
	.page-login {
		.box {
			width: 100%;
		}
	}
}
</style>
