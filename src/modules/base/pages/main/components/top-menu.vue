<template>
	<div class="top-menu">
		<!-- 分组菜单 -->
		<div class="top-menu__groups">
			<el-dropdown
				v-for="(item, index) in list"
				:key="item.id"
				@command="onMenuSelect"
				trigger="click"
				popper-class="top-menu-dropdown"
			>
				<div class="top-menu__group-item" :class="{ 'is-active': index == active }">
					<cl-svg class="mr-2" :name="item.icon" :size="16" v-if="item.icon" />
					<span class="text-[12px] tracking-wider whitespace-nowrap">{{
						item.meta?.label
					}}</span>
					<el-icon class="ml-1"><arrow-down /></el-icon>
				</div>
				<template #dropdown>
					<el-dropdown-menu>
						<template
							v-for="(menuItem, menuIndex) in groupMenusMap[index]"
							:key="menuItem.id"
						>
							<!-- 直接菜单项 -->
							<el-dropdown-item
								v-if="menuItem.type === 1"
								:command="menuItem.meta?.isHome ? '/' : menuItem.path"
							>
								<cl-svg :name="menuItem.icon" :size="14" v-if="menuItem.icon" />
								<span class="ml-2">{{ menuItem.meta?.label }}</span>
							</el-dropdown-item>

							<!-- 分组菜单 -->
							<template v-else-if="menuItem.type === 0 && menuItem.children?.length">
								<!-- 分组标题 -->
								<div
									class="dropdown-group-title"
									:class="{ 'with-divider': menuIndex > 0 }"
								>
									<cl-svg :name="menuItem.icon" :size="12" v-if="menuItem.icon" />
									<span>{{ menuItem.meta?.label }}</span>
								</div>
								<!-- 分组子菜单 -->
								<el-dropdown-item
									v-for="childItem in menuItem.children.filter(c => c.isShow)"
									:key="childItem.id"
									:command="childItem.meta?.isHome ? '/' : childItem.path"
									class="dropdown-group-item"
								>
									<cl-svg
										:name="childItem.icon"
										:size="14"
										v-if="childItem.icon"
									/>
									<span class="ml-2">{{ childItem.meta?.label }}</span>
								</el-dropdown-item>
							</template>
						</template>

						<!-- 如果没有菜单项，显示提示 -->
						<el-dropdown-item
							v-if="!groupMenusMap[index] || groupMenusMap[index].length === 0"
							disabled
						>
							暂无菜单项
						</el-dropdown-item>
					</el-dropdown-menu>
				</template>
			</el-dropdown>
		</div>

		<!-- 移除菜单列表显示 -->
		<div v-if="false" class="top-menu__list">
			<el-menu
				mode="horizontal"
				:default-active="route.path"
				background-color="transparent"
				@select="onMenuSelect"
				class="top-menu__el-menu"
			>
				<template v-for="menuItem in []" :key="menuItem.id">
					<el-sub-menu
						v-if="menuItem.type === 0 && menuItem.children?.length"
						:index="String(menuItem.id)"
						popper-class="top-menu__submenu-popper"
					>
						<template #title>
							<cl-svg :name="menuItem.icon" :size="16" v-if="menuItem.icon" />
							<span class="ml-2">{{ menuItem.meta?.label }}</span>
						</template>
						<el-menu-item
							v-for="childItem in menuItem.children.filter(c => c.isShow)"
							:key="childItem.id"
							:index="childItem.meta?.isHome ? '/' : childItem.path"
						>
							<cl-svg :name="childItem.icon" :size="14" v-if="childItem.icon" />
							<span class="ml-2">{{ childItem.meta?.label }}</span>
						</el-menu-item>
					</el-sub-menu>
					<el-menu-item
						v-else-if="menuItem.type === 1"
						:index="menuItem.meta?.isHome ? '/' : menuItem.path"
					>
						<cl-svg :name="menuItem.icon" :size="16" v-if="menuItem.icon" />
						<span class="ml-2">{{ menuItem.meta?.label }}</span>
					</el-menu-item>
				</template>
			</el-menu>
		</div>
	</div>
</template>

<script lang="ts" setup>
defineOptions({
	name: 'top-menu'
});

import { computed, ref, watch } from 'vue';
import { useBase } from '/$/base';
import { useCool } from '/@/cool';
import { ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
import { ArrowDown } from '@element-plus/icons-vue';

const { router, route } = useCool();
const { menu } = useBase();
const { t } = useI18n();

// 选中的分组索引
const active = ref(0);

// 分组列表
const list = computed(() => {
	return menu.group.filter(e => e.isShow);
});

// 获取所有分组的菜单映射
const groupMenusMap = computed(() => {
	const map: Record<number, Menu.Item[]> = {};
	list.value.forEach((group, index) => {
		if (group && group.children && Array.isArray(group.children)) {
			map[index] = group.children.filter(item => item && item.isShow);
		} else {
			map[index] = [];
		}
	});
	return map;
});

// 获取指定分组的菜单
function getGroupMenus(groupIndex: number) {
	return groupMenusMap.value[groupIndex] || [];
}

// 选择分组
function selectGroup(index: number) {
	if (index == active.value) {
		return false;
	}

	// 设置该分组下的菜单
	menu.setMenu(index);

	// 更新激活状态
	active.value = index;
}

// 菜单选择事件
function onMenuSelect(url: string) {
	if (url != route.path) {
		router.push(url);
	}
}

// 刷新激活状态
function refresh() {
	let index = 0;

	function deep(e: Menu.Item, i: number) {
		switch (e.type) {
			case 0:
				if (e.children) {
					e.children.forEach(child => {
						deep(child, i);
					});
				}
				break;
			case 1:
				if (route.path.includes(e.path)) {
					index = i;
				}
				break;
			default:
				break;
		}
	}

	// 遍历所有分组
	list.value.forEach(deep);

	// 确认选择
	active.value = index;

	// 设置该分组下的菜单
	menu.setMenu(index);
}

// 监听变化 - 简化监听条件
watch(
	() => route.path,
	() => {
		refresh();
	},
	{
		immediate: true
	}
);
</script>

<style lang="scss" scoped>
.top-menu {
	display: flex;
	width: 100%;
	overflow: hidden;

	&__groups {
		display: flex;
		align-items: center;
		gap: 1px;
		overflow-x: auto;
		overflow-y: hidden;
		min-width: 0;
		flex: 1;
		padding: 2px 0;

		/* 自定义滚动条样式 */
		&::-webkit-scrollbar {
			height: 4px;
		}

		&::-webkit-scrollbar-track {
			background: transparent;
			border-radius: 2px;
		}

		&::-webkit-scrollbar-thumb {
			background: var(--el-color-primary-light-7);
			border-radius: 2px;
			transition: all 0.3s ease;

			&:hover {
				background: var(--el-color-primary-light-5);
			}
		}

		/* 确保内容不换行 */
		white-space: nowrap;
	}

	&__group-item {
		display: flex;
		align-items: center;
		height: 30px;
		padding: 0 10px;
		color: var(--el-text-color-regular);
		border-radius: 6px;
		cursor: pointer;
		transition: all 0.3s;
		white-space: nowrap;
		font-size: 13px;
		font-weight: 500;
		border: 1px solid transparent;
		flex-shrink: 0;

		&:hover {
			background-color: var(--el-color-primary-light-9);
			color: var(--el-color-primary);
			border-color: var(--el-color-primary-light-5);
		}

		&.is-active {
			color: var(--el-color-primary);
			background-color: var(--el-color-primary-light-9);
			border-color: var(--el-color-primary-light-8);
		}
	}

	&__list {
		background-color: var(--el-bg-color);
		overflow-x: auto;
		overflow-y: hidden;
		padding: 0 16px;
		min-height: 40px;
		border-bottom: 1px solid var(--el-border-color-extra-light);
	}

	&__el-menu {
		border-bottom: none;
		min-width: max-content;

		:deep(.el-menu-item),
		:deep(.el-sub-menu__title) {
			height: 40px;
			line-height: 40px;
			border-bottom: none;
			padding: 0 16px;
			margin: 0 4px;
			border-radius: 4px;

			&:hover {
				background-color: var(--el-color-primary-light-9);
			}

			&.is-active {
				color: var(--el-color-primary);
				background-color: var(--el-color-primary-light-9);
				border-bottom: 2px solid var(--el-color-primary);
			}
		}

		:deep(.el-sub-menu) {
			&.is-active > .el-sub-menu__title {
				color: var(--el-color-primary);
				background-color: var(--el-color-primary-light-9);
			}
		}
	}
}
</style>

<style lang="scss">
.top-menu-dropdown {
	z-index: 9999 !important;

	.el-dropdown-menu {
		border: 1px solid var(--el-border-color-light);
		border-radius: 8px;
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
		padding: 4px;
		min-width: 160px;

		.el-dropdown-menu__item {
			border-radius: 4px;
			padding: 8px 12px;
			margin: 2px 0;

			&:hover {
				background-color: var(--el-color-primary-light-9);
				color: var(--el-color-primary);
			}

			&.dropdown-group-item {
				padding-left: 24px; // 增加缩进
				font-size: 13px;
			}

			.cl-svg {
				margin-right: 8px;
			}
		}

		.dropdown-group-title {
			padding: 6px 12px 2px 12px;
			font-size: 12px;
			font-weight: 600;
			color: var(--el-text-color-secondary);
			display: flex;
			align-items: center;
			background-color: var(--el-fill-color-extra-light);
			margin: 2px 0;
			border-radius: 4px;

			&.with-divider {
				margin-top: 8px;
				position: relative;

				&::before {
					content: '';
					position: absolute;
					top: -4px;
					left: 6px;
					right: 6px;
					height: 1px;
					background-color: var(--el-border-color-extra-light);
				}
			}

			.cl-svg {
				margin-right: 6px;
			}
		}
	}
}

.top-menu__submenu-popper {
	.el-menu {
		border: 1px solid var(--el-border-color-light);
		border-radius: 6px;
		box-shadow: var(--el-box-shadow-light);

		.el-menu-item {
			&:hover {
				background-color: var(--el-color-primary-light-9);
			}
		}
	}
}
</style>
