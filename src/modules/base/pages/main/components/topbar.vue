<template>
	<div class="app-topbar">
		<div class="app-topbar__content">
			<!-- 左侧区域 -->
			<div class="app-topbar__left">
				<!-- 路由导航（非分组模式） -->
				<route-nav v-if="!app.info.menu.isGroup" />
				<!-- 菜单分组（分组模式） -->
				<top-menu v-else class="app-topbar__menu-groups" />
			</div>

			<!-- 右侧工具栏 -->
			<div class="app-topbar__actions">
				<!-- 工具栏 -->
				<ul class="app-topbar__tools" v-if="isroute()">
					<li>
						<input
							v-model="org.name"
							disabled
							style="height: 30px; width: 300px"
							class="input-style"
						/>
					</li>
					<li>
						<el-button type="primary" @click="Switchzuzhi()">切换组织</el-button>
					</li>
				</ul>

				<el-popover placement="bottom" :width="390">
					<template #reference>
						<el-badge
							:value="messages.messagesList.totalCount"
							:hidden="!messages.messagesList.totalCount"
							style="margin-right: 10px"
						>
							<cl-svg name="icon-notice" :size="16" />
						</el-badge>
					</template>
					<div>
						<div class="message-container">
							<div class="message-header">
								<el-tabs v-model="activeName" type="border-card">
									<el-tab-pane name="notice" :label="`通知`">
										<template #label>
											<span class="custom-tab-label">
												通知
												<el-badge
													v-if="
														messages.messagesList.announcements.length >
														0
													"
													:value="
														messages.messagesList.announcements.length
													"
													type="danger"
												/>
											</span>
										</template>
										<div class="message-content">
											<el-scrollbar max-height="300px">
												<template
													v-if="
														messages.messagesList.announcements.length
													"
												>
													<div
														v-for="notice in messages.messagesList
															.announcements"
														:key="notice.id"
													>
														<div class="message-item">
															<div class="item-left">
																<el-icon
																	:size="20"
																	:color="
																		getNoticeIconColor(
																			notice.type
																		)
																	"
																>
																	<component
																		:is="
																			getNoticeIcon(
																				notice.type
																			)
																		"
																	/>
																</el-icon>
															</div>

															<div
																class="item-content"
																@click="viewNoticeDetail(notice)"
															>
																<div class="content-header">
																	<span class="title">{{
																		notice.title
																	}}</span>
																	<el-tag
																		size="small"
																		:type="
																			getNoticeTypeTag(
																				notice.noticeType
																			)
																		"
																	>
																		{{
																			getNoticeType(
																				notice.noticeType
																			)
																		}}
																	</el-tag>
																</div>

																<div class="content-body">
																	<div
																		class="preview-text"
																		:class="{
																			expanded:
																				notice.isExpanded
																		}"
																	>
																		<template
																			v-if="
																				!notice.isExpanded
																			"
																		>
																			{{
																				getPreviewContent(
																					notice.content
																				)
																			}}
																		</template>
																		<template v-else>
																			<div
																				v-html="
																					notice.content
																				"
																			></div>
																		</template>
																	</div>
																	<el-button
																		type="primary"
																		link
																		@click.stop="
																			toggleExpand(notice)
																		"
																	>
																		{{
																			notice.isExpanded
																				? '收起'
																				: getPreviewContent(
																							notice.content
																					  ).includes(
																							'...'
																					  )
																					? '展开'
																					: ''
																		}}
																	</el-button>
																</div>
															</div>

															<div class="item-right">
																<div
																	v-if="
																		!notice.isRead &&
																		allowNoticeType.includes(
																			notice.noticeType
																		)
																	"
																>
																	<el-tooltip
																		content="标记已读"
																		placement="top"
																	>
																		<el-button
																			type="primary"
																			link
																			@click.stop="
																				markRead(notice.id)
																			"
																		>
																			<el-icon
																				><check
																			/></el-icon>
																		</el-button>
																	</el-tooltip>
																</div>
																<div v-else>
																	<el-tooltip
																		content="请去处理，处理完成会自动已读"
																		placement="top"
																	>
																		<el-button type="info" link>
																			<el-icon
																				><paperclip
																			/></el-icon>
																		</el-button>
																	</el-tooltip>
																</div>
															</div>
														</div>
													</div>
												</template>
												<el-empty v-else description="暂无公告" />
											</el-scrollbar>

											<div class="message-footer">
												<el-button
													v-if="
														messages.messagesList.announcements.length
													"
													type="primary"
													link
													@click="markAllRead"
												>
													<el-icon><check /></el-icon>
													全部已读
												</el-button>
												<el-button
													type="primary"
													size="small"
													@click="viewAllNotices"
												>
													查看全部
												</el-button>
											</div>
										</div>
									</el-tab-pane>

									<el-tab-pane name="todo" :label="`待办`">
										<template #label>
											<span class="custom-tab-label">
												待办
												<el-badge
													v-if="messages.messagesList.todos.length > 0"
													:value="messages.messagesList.todos.length"
													type="danger"
												/>
											</span>
										</template>
										<div
											class="todo-list"
											v-if="messages.messagesList.todos.length"
										>
											<div class="todo-item">
												<div
													style="
														display: flex;
														justify-content: space-between;
													"
												>
													<div class="item-left">
														<el-icon :size="20" color="#67C23A">
															<list />
														</el-icon>
													</div>
													<div class="item-content">
														<span class="todo-title">我的待办</span>
													</div>
													<!-- <div class="item-right">
												<el-badge
													:value="messages.messagesList.todos.length"
													type="danger"
												/>
											</div> -->
												</div>
												<div style="float: right">
													<el-button
														type="primary"
														plain
														round
														size="small"
														@click="goTask"
													>
														去处理
													</el-button>
												</div>
											</div>
										</div>
										<el-empty v-else description="暂无待办" />
									</el-tab-pane>
								</el-tabs>
							</div>
						</div>
					</div>
				</el-popover>
				<!-- 用户信息 -->
				<template v-if="user.info">
					<el-dropdown
						hide-on-click
						popper-class="app-topbar__user-popper"
						:popper-options="{}"
						@command="onCommand"
					>
						<div class="app-topbar__user">
							<el-text class="mr-[10px]">{{ user.info.username }}</el-text>
							<cl-avatar :size="26" :src="user.info.headImg" />
						</div>

						<template #dropdown>
							<div class="user">
								<cl-avatar :size="34" :src="user.info.headImg" />

								<div class="det">
									<el-text size="small" tag="p">{{ user.info.username }}</el-text>
								</div>
							</div>

							<el-dropdown-menu>
								<el-dropdown-item command="exit">
									<cl-svg name="exit" />
									<span>更新权限</span>
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</template>
			</div>
		</div>
	</div>
</template>

<script lang="ts" name="app-topbar" setup>
import { ref, computed, markRaw, onMounted, reactive } from 'vue';
import { isFunction, orderBy } from 'lodash-es';
import { useBase } from '/$/base';
import { module, useCool } from '/@/cool';
import { useI18n } from 'vue-i18n';
import RouteNav from './route-nav.vue';
import AMenu from './amenu.vue';
import TopMenu from './top-menu.vue';

import { ElMessageBox, ElMessage } from 'element-plus';
import {
	List,
	Monitor,
	Bell,
	Check,
	Message,
	Warning,
	Calendar,
	InfoFilled,
	Paperclip,
	Menu
} from '@element-plus/icons-vue';
import { useStore } from '../../../store/index';
import { noticeTypeList } from '/@/modules/message/utils/noticTypeList';
// import { formatDistance } from "date-fns";
// import { zhCN } from "date-fns/locale";

const { route, router, service, browser, mitt } = useCool();
const { user, app, org, process } = useBase();

const unCount = ref(1);
const { messages } = useStore();
const newInfoList = ref([
	{
		title: '系统更新',
		icon: Monitor,
		count: 1
	},
	{
		title: '我的待办',
		icon: List,
		count: 1
	}
]);
const notices = ref<any[]>([]);
const todoList = ref<any[]>([]);
const activeName = ref('notice');
const allowNoticeType = ref<string[]>(['announcement', 'terminated', 'completed', 'started']);
//const messagesList = computed(() => messages.messagesList);

// 命令事件
async function onCommand(name: string) {
	switch (name) {
		case 'my':
			router.push('/my/info');
			break;
		case 'exit':
			ElMessageBox.confirm('确认更新权限吗', '提示', {
				type: 'warning'
			})
				.then(async () => {
					await service.base.comm.logout();
					user.logout();
					org.name = undefined;
					org.code = undefined;
					//router.push("/login");
				})
				.catch(() => null);
			break;
	}
}

// 跳转
const Switchzuzhi = async () => {
	router.push({
		path: '/zdf/switchzz'
	});
};

function openNewInfo(title: string) {
	if (title === '系统更新') {
		// router.push("/system/update");
	} else if (title === '我的待办') {
		// router.push("/my/todo");
	}
}
function isroute() {
	const route_path = route.path;
	if (
		route_path.includes('/zdf') ||
		route_path.includes('/kucunView') ||
		route_path.includes('/yszx')
	) {
		return true;
	} else {
		return false;
	}
}

// 工具栏
const toolbar = reactive({
	list: [] as any[],

	async init() {
		const arr = orderBy(
			module.list.filter(e => e.enable !== false && !!e.toolbar).map(e => e.toolbar),
			'order'
		);

		this.list = await Promise.all(
			arr
				.filter(e => e?.component)
				.map(async e => {
					if (e) {
						const c = await (isFunction(e.component) ? e.component() : e.component);

						return {
							...e,
							component: markRaw(c.default || c)
						};
					}
				})
		);
	}
});

function goTask() {
	console.log('跳转待办页面');
	router.push('/todoTask');
}

// 工具栏组件
const toolbarComponents = computed(() => {
	return toolbar.list.filter(e => {
		if (browser.isMini) {
			return e?.h5 ?? true;
		}

		return e?.pc ?? true;
	});
});

// 获取公告列表
const getNotices = async () => {
	return new Promise(async (resolve, reject) => {
		try {
			await messages.getMessagesList();
			// 为每条通知添加展开状态标记
			messages.messagesList.announcements.forEach((notice: any) => {
				notice.isExpanded = notice.isExpanded || false;
			});
			resolve(true);
			// if (info.value) {
			// 	window.location.reload();
			// }
		} catch (error) {
			console.error('获取公告列表失败:', error);
			reject(error);
		}
	});
};

// 获取公告类型对应的tag类型
const getNoticeTypeTag = (type: string) => {
	const typeMap: Record<string, string> = {
		announcement: 'info',
		overdue: 'danger',
		todo: 'warning'
	};
	return typeMap[type] || 'info';
};

// 获取公告类型
const getNoticeType = (type: string) => {
	return noticeTypeList.find(item => item.value === type)?.label || '通知';
};

// 格式化预览内容
const getPreviewContent = (content: string) => {
	const div = document.createElement('div');
	div.innerHTML = content;
	const text = div.textContent || div.innerText;
	return text.length > 40 ? text.slice(0, 40) + '...' : text;
};

const getNoticeContent = (content: string) => {
	const div = document.createElement('div');
	div.innerHTML = content;
	const text = div.textContent || div.innerText;
	return text;
};

// 格式化时间
// const formatTime = (time: string) => {
// 	return formatDistance(new Date(time), new Date(), {
// 		addSuffix: true,
// 		locale: zhCN
// 	});
// };

// 标记单个公告已读
const markRead = async (id: number) => {
	try {
		// await service.messageInfo.messageInfo.read(id)
		await service.messages.notification.read({ ids: [id] });
		await getNotices();
	} catch (error) {
		console.error('标记已读失败:', error);
	}
};

// 标记所有公告已读
const markAllRead = async () => {
	try {
		const allIds = messages.messagesList.announcements
			.filter((notice: any) => allowNoticeType.value.includes(notice.noticeType))
			.map((notice: any) => notice.id);
		console.log('allIds', allIds);
		// await service.messageInfo.messageInfo.readAll()
		if (allIds && allIds[0]) {
			await service.messages.notification.read({ ids: allIds });
			const res = await getNotices();
			console.log('res', res);
		}

		const ids = messages.messagesList.announcements.filter((notice: any) => {
			return allowNoticeType.value.includes(notice.noticeType);
		});
		console.log('ids', ids);
		if (ids.length == 0 && messages.messagesList.announcements.length > 0) {
			ElMessage.error('请去处理剩余通知，处理完成后会自动消失');
		}
		// if (res) {
		// 	window.location.reload();
		// }
		//
	} catch (error) {
		console.error('标记全部已读失败:', error);
	}
};

// 点击公告跳转
const viewNoticeDetail = async (notice: any) => {
	console.log('查看公告详情', notice);

	// 定义要跳转的路由和参数
	let targetPath = '';
	let targetState = {};

	//久悬
	if (notice.noticeType === 'overdue') {
		targetPath = '/myTask';
		targetState = { instanceId: notice.businessData.instanceId };

		// 添加路由判断
		if (route.path === targetPath) {
			router.replace({
				path: targetPath,
				state: targetState,
				force: true
			});
			setTimeout(() => {
				mitt.emit('view.refresh');
			}, 200);
		} else {
			router.push({
				path: targetPath,
				state: { instanceId: targetState.instanceId },
				replace: true
			});
		}
	} else if (notice.noticeType === 'started') {
		//发起流程
		targetPath = '/myTask';
		targetState = { instanceId: notice.businessData.instanceId };

		// 添加路由判断
		if (
			route.path === targetPath
			// &&
			// router.options.history.state.instanceId == notice.businessData.instanceId
		) {
			router.replace({
				path: targetPath,
				state: targetState,
				force: true
			});

			setTimeout(() => {
				mitt.emit('view.refresh');
			}, 200);
			//	console.log('state', router.options.history.state);
		} else {
			router.push({
				path: targetPath,
				state: { instanceId: targetState.instanceId },
				replace: true
			});
		}
	} else if (
		notice.noticeType === 'urge' ||
		notice.noticeType === 'transfer' ||
		notice.noticeType === 'add_sign'
	) {
		//催办、转办、加签

		targetPath = '/todoTask';
		targetState = {
			taskId: notice.taskId,
			instanceId: notice.businessData.instanceId
		};

		// 添加路由判断
		if (route.path === targetPath) {
			router.replace({
				path: targetPath,
				state: targetState,
				force: true
			});
			console.log('当前路由与目标路由完全相同');
			setTimeout(() => {
				mitt.emit('view.refresh');
			}, 200);
		} else {
			router.push({
				path: targetPath,
				state: targetState,
				replace: true
			});
		}
	} else if (notice.noticeType === 'completed') {
		//流程已完成
		targetPath = '/myTask';
		// 添加路由判断
		if (route.path === targetPath) {
			router.replace({
				path: targetPath,
				state: targetState,
				force: true
			});
			setTimeout(() => {
				mitt.emit('view.refresh');
			}, 200);
		} else {
			router.push({
				path: targetPath,
				state: { instanceId: notice.businessData.instanceId },
				replace: true
			});
		}
	} else if (notice.noticeType === 'data_correction') {
		try {
			if (Number(notice.businessData.formId)) {
				const res = await service.cloud.db.list({ id: notice.businessData.formId });
				if (res && res.length > 0) {
					console.log('res查看跳转', res[0]);

					if (res[0].tableType == 'system') {
						targetPath = '/dataView';
						targetState = {
							id: notice.businessData.formId,
							rowIds: JSON.stringify(notice.businessData.rowIds),
							ruleId: notice.businessData.ruleId,
							tableType: res[0].tableType
						};
					} else {
						targetPath = '/luruTable/data';
						targetState = {
							id: res[0].id,
							name: res[0].name,
							type: '查看',
							checkDataIds: JSON.stringify(notice.businessData.rowIds),
							ruleId: notice.businessData.ruleId
						};
					}
					console.log('targetState', router.options.history.state);
					const currentState = router.options.history.state;
					if (route.path === targetPath) {
						router.replace({
							path: targetPath,
							state: targetState,
							force: true
						});
						setTimeout(() => {
							mitt.emit('view.refresh');
						}, 200);
					} else {
						router.push({
							path: targetPath,
							state: targetState,
							replace: true
						});
					}
				} else {
					ElMessage.error('暂无数据');
					return;
				}
			} else {
				targetPath = '/dataView';
				console.log('process.list', process.list);
				if (route.path === targetPath) {
					router.replace({
						path: '/dataView',
						state: {
							id: notice.businessData.formId,
							rowIds: JSON.stringify(notice.businessData.rowIds),
							ruleId: notice.businessData.ruleId,
							tableType: 'data'
						},
						force: true
					});

					setTimeout(() => {
						mitt.emit('view.refresh');
					}, 200);
					//window.location.reload();
				} else {
					router.push({
						path: '/dataView',
						state: {
							id: notice.businessData.formId,
							rowIds: JSON.stringify(notice.businessData.rowIds),
							ruleId: notice.businessData.ruleId,
							tableType: 'data'
						}
					});
				}
				console.log('router.options.history.state', router.options.history.state);
			}
		} catch (error) {
			console.error('获取数据失败:', error);
			ElMessage.error('获取数据失败' + error.message);
			return;
		}
	} else {
		targetPath = '/messageRead';
	}

	// 判断当前路由是否与目标路由完全相同（包括路径和参数）
	//
};

// 查看全部公告
const viewAllNotices = () => {
	// 路由跳转到公告列表页
	router.push('/messageRead');
};

// 新增清空消息方法
const clearAllNotices = () => {
	// 实现清空消息的逻辑
};

// 获取通知图标
const getNoticeIcon = (type: string) => {
	const iconMap: Record<string, any> = {
		announcement: InfoFilled,
		overdue: Warning,
		todo: List
	};
	return iconMap[type] || Message;
};

// 获取通知图标颜色
const getNoticeIconColor = (type: string) => {
	const colorMap: Record<string, string> = {
		announcement: '#409EFF', // 蓝色
		overdue: '#F56C6C', // 红色
		todo: '#67C23A' // 绿色
	};
	return colorMap[type] || '#409EFF';
};

// 添加展开/收起功能
function toggleExpand(notice: any) {
	notice.isExpanded = !notice.isExpanded;
}

onMounted(() => {
	toolbar.init();
	getNotices();
});
</script>

<style lang="scss" scoped>
.app-topbar {
	height: 42px;
	background-color: var(--el-bg-color);
	box-sizing: border-box;
	flex-shrink: 0;
	position: relative;
	z-index: 100;

	&__content {
		display: flex;
		align-items: center;
		height: 100%;
		justify-content: space-between;
		padding: 0 8px;
	}

	&__left {
		display: flex;
		align-items: center;
		flex: 1;
		min-width: 0;
		overflow: hidden;
	}

	&__menu-groups {
		flex: 1;
		min-width: 0;
	}

	&__actions {
		display: flex;
		align-items: center;
		gap: 10px;
	}

	&__tools {
		display: flex;
		margin-right: 10px;

		& > li {
			display: flex;
			justify-content: center;
			align-items: center;
			list-style: none;
			height: 45px;
			cursor: pointer;
			margin-left: 10px;
		}
	}

	&__user {
		display: flex;
		align-items: center;
		outline: none;
		cursor: pointer;
		white-space: nowrap;
		padding: 5px 5px 5px 10px;
		border-radius: 6px;

		&:hover {
			background-color: var(--el-fill-color-light);
		}
	}

	:deep(.cl-comm__icon) {
		&:hover {
			border-color: var(--el-color-primary);
			background-color: transparent;
		}
	}
}
.newInfo {
	height: 35px;
	line-height: 35px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	&_item {
		margin-left: 10px;
		display: flex;
		align-items: center;
	}
	&_title {
		margin-left: 6px;
		color: #333;
	}
	&_count {
		margin-right: 15px;
		color: #e6191c;
		font-weight: bold;
	}
	&:hover {
		background-color: #f0f2f5;
	}
}
.message-container {
	.message-header {
		:deep(.el-tabs__header) {
			margin-bottom: 10px;
		}
	}

	.message-content {
		padding: 0 10px;
	}

	.message-item {
		position: relative;
		display: flex;
		align-items: flex-start;
		padding: 12px;
		border-bottom: 1px solid #ebeef5;
		transition: all 0.3s;

		&:hover {
			background-color: #f5f7fa;
		}

		&.message-unread {
			//background-color: #f5f5f5;

			&:hover {
				background-color: #e6f1fc;
			}
		}

		.item-left {
			flex: 0 0 auto;
			padding: 4px 12px 0 0;
		}

		.item-content {
			flex: 1;
			min-width: 0;
			margin: 0 12px;

			.content-header {
				display: flex;
				align-items: center;
				gap: 8px;
				margin-bottom: 6px;

				.title {
					font-weight: 500;
					color: #303133;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}

			.content-body {
				overflow: visible;
				.preview-text {
					color: #606266;
					font-size: 13px;
					line-height: 1.5;

					// 未展开时的样式
					&:not(.expanded) {
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
						overflow: hidden;
					}

					// 展开时的样式
					&.expanded {
						max-height: none;
						overflow: visible;
						-webkit-line-clamp: unset;

						:deep(img) {
							max-width: 100%;
							height: auto;
							margin: 8px 0;
						}

						:deep(p) {
							margin: 8px 0;
						}
					}
				}
			}
		}

		.item-right {
			flex: 0 0 auto;
			display: flex;
			align-items: center;
		}
	}

	.message-footer {
		display: flex;
		justify-content: space-between;
		padding: 12px 0;
		border-top: 1px solid #ebeef5;
		margin-top: 10px;
	}

	.todo-list {
		.todo-item {
			display: flex;
			align-items: center;
			padding: 12px;
			justify-content: space-between;
			cursor: pointer;
			transition: all 0.3s;

			&:hover {
				background-color: #f5f7fa;
			}

			.todo-title {
				font-size: 14px;
				color: #303133;
			}
		}
	}
}

.notice-preview {
	padding: 8px;

	.preview-title {
		margin: 0 0 12px;
		padding-bottom: 10px;
		font-size: 16px;
		font-weight: bold;
		color: #303133;
		border-bottom: 1px solid #ebeef5;
		position: relative;
	}

	.preview-info {
		display: flex;
		align-items: center;
		gap: 10px;
		margin-bottom: 16px;
		padding: 0 4px;

		.preview-time {
			color: #909399;
			font-size: 13px;
		}
	}

	.preview-content {
		padding: 12px;
		font-size: 14px;
		line-height: 1.6;
		color: #606266;
		max-height: 300px;
		overflow-y: auto;
		background-color: #f8f9fa;
		border-radius: 4px;

		:deep(p) {
			margin: 8px 0;
		}

		:deep(img) {
			max-width: 100%;
			height: auto;
			border-radius: 4px;
			margin: 8px 0;
		}

		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-thumb {
			background-color: #dcdfe6;
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background-color: #f0f2f5;
			border-radius: 3px;
		}
	}
}

// 修改 el-popover 的默认样式
:deep(.el-popover.el-popper) {
	padding: 16px;
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
	border-radius: 8px;
}
</style>

<style lang="scss">
.app-topbar__user-popper {
	.el-dropdown-menu__item {
		padding: 6px 12px;
		font-size: 12px;
	}

	.user {
		display: flex;
		align-items: center;
		padding: 10px 10px;
		width: 200px;
		border-bottom: 1px solid var(--el-color-info-light-9);

		.det {
			margin-left: 10px;
			flex: 1;
			font-size: 12px;
		}
	}

	.cl-svg {
		margin-right: 8px;
		font-size: 16px;
	}
}
</style>

<style lang="scss">
.app-topbar__user-popper {
	.el-dropdown-menu__item {
		padding: 6px 12px;
		font-size: 12px;
	}

	.user {
		display: flex;
		align-items: center;
		padding: 10px 10px;
		width: 200px;
		border-bottom: 1px solid var(--el-color-info-light-9);

		.det {
			margin-left: 10px;
			flex: 1;
			font-size: 12px;
		}
	}

	.cl-svg {
		margin-right: 8px;
		font-size: 16px;
	}
}
</style>
