<template>
	<div class="app-layout" :class="{ 'is-full': app.isFull }">
		<div class="app-layout__header">
			<!-- 顶部工具栏 -->
			<topbar />
		</div>

		<div class="app-layout__content">
			<process />
			<views />
		</div>
	</div>
</template>

<script lang="ts" setup>
defineOptions({
	name: 'app-layout'
});

import { useBase } from '/$/base';
import Topbar from './components/topbar.vue';
import TopMenu from './components/top-menu.vue';
import process from './components/process.vue';
import Views from './components/views.vue';

const { app } = useBase();
</script>

<style lang="scss" scoped>
.app-global {
	position: absolute;
	left: 0;
	top: 0;
}

.app-layout {
	display: flex;
	flex-direction: column;
	background-color: var(--bg-color);
	height: 100%;
	width: 100%;
	overflow: hidden;

	&__header {
		flex-shrink: 0;
		width: 100%;
		display: flex;
		flex-direction: column;
	}

	&__content {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;
	}

	&.is-full {
		:deep(.app-topbar) {
			padding: 0;
			height: 0;
			overflow: hidden;
		}

		.app-layout__content {
			height: 100%;
		}
	}
}
</style>
