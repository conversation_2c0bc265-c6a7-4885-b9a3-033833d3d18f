import { useAppStore } from "./app";
import { useMenuStore } from "./menu";
import { useProcessStore } from "./process";
import { useUserStore } from "./user";
import { useCompany } from "./xm";
import { useMessagesStore } from "./messages";

export function useStore() {
	const app = useAppStore();
	const menu = useMenuStore();
	const process = useProcessStore();
	const user = useUserStore();
	const org = useCompany();
	const messages = useMessagesStore();

	return {
		app,
		menu,
		process,
		user,
		org,
		messages
	};
}
