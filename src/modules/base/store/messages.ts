import { defineStore } from 'pinia';
import { ref } from 'vue';
import { storage } from '/@/cool/utils';
import { service, router } from '/@/cool';
import { config } from '/@/config';

// 本地缓存
// const data = storage.info();

export const useMessagesStore = defineStore('messages', function () {
	const messagesList = ref({
		announcements: [],
		todos: [],
		totalCount: 0
	});
	async function getMessagesList() {
		const res = await service.messages.comm.unread();
		messagesList.value = res;
		return messagesList.value;
	}
	return {
		messagesList,
		getMessagesList
	};
});
