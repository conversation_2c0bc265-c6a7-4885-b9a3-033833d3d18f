import { defineStore } from 'pinia';
import { ref } from 'vue';
import { storage } from '/@/cool/utils';
import { service, router } from '/@/cool';
import { config, isDev } from '/@/config';
import { PermissionInfo } from '/@/modules/luru/utils/constants';
import { clearDictDB } from '/@/modules/luru/utils/indexDB';

// 本地缓存
const data = storage.info();

export const useUserStore = defineStore('user', function () {
	// 标识
	const token = ref<string>(data.token);

	// 设置标识
	function setToken(data: {
		token: string;
		expire: number;
		refreshToken: string;
		refreshExpire: number;
	}) {
		// 请求的唯一标识
		token.value = data.token;
		storage.set('token', data.token, data.expire);

		// 刷新 token 的唯一标识
		storage.set('refreshToken', data.refreshToken, data.refreshExpire);
	}

	// 刷新标识
	async function refreshToken(): Promise<string> {
		return new Promise((resolve, reject) => {
			service.base.open
				.refreshToken({
					refreshToken: storage.get('refreshToken')
				})
				.then(res => {
					setToken(res);
					resolve(res.token);
				})
				.catch(err => {
					logout();
					reject(err);
				});
		});
	}

	// 用户信息
	const info = ref<Eps.BaseSysUserEntity | null>(data.userInfo);
	const userPromission: any = ref(data.userPromission);
	// 设置用户信息
	function set(value: any) {
		info.value = value;
		storage.set('userInfo', value);
	}
	function splitPermission(permissions: any) {
		const result = {};
		const allFunctions = [
			PermissionInfo.LURU,
			PermissionInfo.SHENHE,
			PermissionInfo.SHEJI,
			PermissionInfo.DICT,
			PermissionInfo.CHAKAN
		];

		permissions.forEach(permission => {
			const { projectCode, 表单权限代码: formCode, 功能权限代码: functionCode } = permission;

			// 如果功能权限代码为ALL，则拆分为所有功能
			const functionsToProcess = functionCode === 'ALL' ? allFunctions : [functionCode];

			functionsToProcess.forEach(func => {
				if (!result[func]) {
					result[func] = {};
				}

				if (func === '设计') {
					result[func] = { ALL: ['ALL'] };
				} else {
					if (!result[func][formCode]) {
						result[func][formCode] = [];
					}

					if (projectCode === 'ALL') {
						result[func][formCode] = ['ALL'];
					} else if (!result[func][formCode].includes('ALL')) {
						result[func][formCode].push(projectCode);
					}
				}
			});
		});

		return result;
	}
	function setUserPromission(value: any) {
		userPromission.value = splitPermission(value);
		storage.set('userPromission', userPromission.value);
	}
	// 清除用户
	function clear() {
		storage.remove('userInfo');
		storage.remove('token');
		storage.remove('refreshToken');
		storage.remove('userPromission');
		token.value = '';
		info.value = null;
		userPromission.value = null;
	}

	// 退出
	async function logout() {
		clear();
		clearDictDB();
		//router.clear();
		if (!isDev) {
			router.push('/login');
		} else {
			router.push('/adminlogin');
		}
	}

	// 获取用户信息
	async function get() {
		return service.base.comm.person().then(res => {
			set(res);
			return res;
		});
	}
	async function getUserPromission() {
		return userPromission.value;
	}

	return {
		token,
		info,
		userPromission,
		get,
		set,
		setUserPromission,
		getUserPromission,
		logout,
		clear,
		setToken,
		refreshToken
	};
});
