import { defineStore } from 'pinia';
import { ref } from 'vue';
import { storage } from '/@/cool/utils';

const data = storage.info();

export const useCompany = defineStore('company', function () {
	const last_user = ref(data.last_user);

	const setlastUser = (selecteduser: any) => {
		storage.set('last_user', selecteduser);
		last_user.value = selecteduser;
	};

	const name = ref(data.orgName);

	const setOrg = (selectedOrg: any) => {
		storage.set('orgName', selectedOrg);
		name.value = selectedOrg;
	};
	const code = ref(data.orgCode);

	const setCode = (selectedOrg: any) => {
		storage.set('orgCode', selectedOrg);
		code.value = selectedOrg;
	};

	// 创建项目组织空对象
	const listOrg = ref<any[]>(data.listOrg);

	const setListorg = (selectObject: any) => {
		storage.set('listOrg', selectObject);
		listOrg.value = selectObject;
	};

	// 创建招待费货物空对象
	const listhw = ref<any[]>(data.listhw);

	const setListhw = (selectObject: any) => {
		storage.set('listhw', selectObject);
		listhw.value = selectObject;
	};

	// 创建招待费保管部门空对象
	const listpart = ref<any[]>(data.listpart);

	const setListpart = (selectObject: any) => {
		storage.set('listpart', selectObject);
		listpart.value = selectObject;
	};

	// 创建预算控制大类空对象
	const listyskzdl = ref<any[]>(data.listyskzdl);

	const setListyskzdl = (selectObject: any) => {
		storage.set('listyskzdl', selectObject);
		listyskzdl.value = selectObject;
	};

	// 创建经费类别空对象
	const listjflb = ref<any[]>(data.listjflb);

	const setListjflb = (selectObject: any) => {
		storage.set('listjflb', selectObject);
		listjflb.value = selectObject;
	};

	return {
		last_user,
		setlastUser,
		name,
		setOrg,
		code,
		setCode,
		listOrg,
		setListorg,
		listhw,
		setListhw,
		listpart,
		setListpart,
		listyskzdl,
		setListyskzdl,
		listjflb,
		setListjflb
	};
});
