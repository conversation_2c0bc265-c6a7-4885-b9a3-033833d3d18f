<template>
	<div class="chatbot-container">
		<!-- 加载状态 -->
		<div v-if="loading" v-loading="true" :element-loading-text="$t('拼命加载中')" element-loading-background="rgba(0, 0, 0, 0.8)" class="loading-container">
		</div>

		<!-- 错误状态 -->
		<div v-else-if="error" class="error-container">
			<el-alert
				:title="$t('加载失败')"
				:description="error.message"
				type="error"
				show-icon
			/>
		</div>

		<!-- 空状态 -->
		<div v-else-if="chatbots.length === 0" class="empty-container">
			<el-empty :description="$t('暂无聊天机器人配置')">
				<template #description>
					<div class="empty-description">
						<p>{{ $t('暂无聊天机器人配置') }}</p>
						<p class="empty-hint">{{ $t('请在系统参数中配置key为chatbot的参数') }}</p>
					</div>
				</template>
			</el-empty>
		</div>

		<!-- 聊天机器人界面 -->
		<div v-else class="chatbot-layout">
			<!-- 左侧机器人列表 -->
			<div class="chatbot-sidebar">
				<div class="bot-list">
					<div
						v-for="(chatbot, index) in chatbots"
						:key="index"
						class="bot-item"
						:class="{ active: activeBot === index }"
						@click="switchBot(index)"
					>
						<div class="bot-name">{{ chatbot.botname }}</div>
						<div class="bot-status" :class="{ online: activeBot === index }"></div>
					</div>
				</div>
			</div>

			<!-- 右侧iframe区域 -->
			<div class="chatbot-main">
				<div class="iframe-container">
					<iframe
						v-if="currentBot"
						:key="activeBot"
						:src="currentBot.iframe_url"
						class="chatbot-iframe"
						frameborder="0"
						allow="microphone"
					/>
					<div v-else class="no-selection">
						<el-empty :description="$t('请选择聊天机器人')" />
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
defineOptions({
	name: 'chatbot-iframe'
});

import { ElMessage, ElAlert, ElEmpty } from 'element-plus';
import { onMounted, ref, computed } from 'vue';
import { useCool } from '/@/cool';
import { useI18n } from 'vue-i18n';
import { useBase } from '/$/base';

const { t } = useI18n();
const { service } = useCool();
const { user } = useBase();

// 聊天机器人配置
interface ChatbotConfig {
	botname: string;
	iframe_url: string;
}

// 响应式数据
const chatbots = ref<ChatbotConfig[]>([]);
const loading = ref(false);
const error = ref<Error | null>(null);
const activeBot = ref<number>(0); // 当前选中的机器人索引

// 计算当前选中的机器人
const currentBot = computed(() => {
	return chatbots.value[activeBot.value] || null;
});

// 切换机器人
function switchBot(index: number) {
	console.log(`切换到第${index + 1}个机器人:`, chatbots.value[index]);
	activeBot.value = index;
	console.log('当前激活的机器人:', currentBot.value);
}

// 压缩并编码为Base64
async function compressAndEncodeBase64(input: string | number): Promise<string> {
	const uint8Array = new TextEncoder().encode(String(input));
	const compressedStream = new Response(
		new Blob([uint8Array])
			.stream()
			.pipeThrough(new CompressionStream('gzip'))
	).arrayBuffer();
	const compressedUint8Array = new Uint8Array(await compressedStream);
	return btoa(String.fromCharCode(...compressedUint8Array));
}

// 处理iframe URL，添加用户ID、开始日期和JWT参数
async function processIframeUrl(baseUrl: string): Promise<string> {
	try {
		// 获取当前用户ID，如果没有则使用默认值
		const currentUserId = user.info?.username || 240199;
		const userId = await compressAndEncodeBase64(currentUserId);

		// 获取用户JWT token
		const jwtToken = user.get;

		// 检查URL是否已经包含参数
		const separator = baseUrl.includes('?') ? '&' : '?';

		const finalUrl = `${baseUrl}${separator}jqId=${encodeURIComponent(userId)}&jwt=${encodeURIComponent(jwtToken)}`;

		// 调试信息
		console.log('=== Chatbot iframe 参数调试 ===');
		console.log('原始URL:', baseUrl);
		console.log('当前用户ID:', currentUserId);
		console.log('压缩编码后的用户ID:', userId);
		console.log('JWT Token:', jwtToken ? `${jwtToken.substring(0, 20)}...` : '无');
		console.log('最终URL:', finalUrl);
		console.log('=== 调试信息结束 ===');

		return finalUrl;
	} catch (err) {
		console.error('处理iframe URL失败:', err);
		return baseUrl; // 如果处理失败，返回原始URL
	}
}

// 加载聊天机器人配置
async function loadChatbotConfig() {
	try {
		loading.value = true;
		error.value = null;

		console.log('开始加载聊天机器人配置...');

		// 调用后端API获取chatbot参数
		const res = await service.base.sys.param.page({ keyName: 'chatbot' });

		console.log('后端返回的配置数据:', res);

		if (res.list?.[0]?.data) {
			const configData = JSON.parse(res.list[0].data);
			console.log('解析后的配置数据:', configData);

			// 确保configData是数组格式
			const chatbotList = Array.isArray(configData) ? configData : [];
			console.log('聊天机器人列表:', chatbotList);

			// 处理每个chatbot的iframe URL
			const processedChatbots = await Promise.all(
				chatbotList.map(async (bot: ChatbotConfig, index: number) => {
					console.log(`处理第${index + 1}个机器人:`, bot);
					const processedBot = {
						...bot,
						iframe_url: await processIframeUrl(bot.iframe_url)
					};
					console.log(`处理后的第${index + 1}个机器人:`, processedBot);
					return processedBot;
				})
			);

			chatbots.value = processedChatbots;
			console.log('最终的聊天机器人配置:', processedChatbots);
		} else {
			console.log('未找到聊天机器人配置数据');
			chatbots.value = [];
		}
	} catch (err) {
		console.error('加载聊天机器人配置失败:', err);
		error.value = err as Error;
		ElMessage.error(t('加载聊天机器人配置失败'));
	} finally {
		loading.value = false;
	}
}

// 组件挂载时加载配置
onMounted(() => {
	loadChatbotConfig();

	// 防止页面自动滚动到底部
	window.scrollTo(0, 0);
	document.documentElement.scrollTop = 0;
	document.body.scrollTop = 0;
});
</script>

<style lang="scss" scoped>
// 重置样式，防止意外滚动
* {
	box-sizing: border-box;
}

.chatbot-container {
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.loading-container,
.error-container,
.empty-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	padding: 20px;
}

.empty-description {
	text-align: center;

	p {
		margin: 8px 0;
		color: #909399;
	}

	.empty-hint {
		font-size: 12px;
		color: #c0c4cc;
	}
}

.chatbot-layout {
	display: flex;
	height: 100%;
	width: 100%;
	overflow: hidden; // 防止布局溢出
}

// 左侧边栏
.chatbot-sidebar {
	width: 240px;
	border-right: 1px solid #e4e7ed;
	background-color: #f8f9fa;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	flex-shrink: 0;
}

.sidebar-header {
	padding: 16px 20px;
	border-bottom: 1px solid #e4e7ed;
	background-color: #fff;
	flex-shrink: 0; // 防止头部被压缩

	h3 {
		margin: 0;
		font-size: 16px;
		font-weight: 500;
		color: #303133;
	}
}

.bot-list {
	flex: 1;
	overflow-y: auto;
	padding: 8px 0;
	min-height: 0; // 关键：确保flex子项能够收缩
}

.bot-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px 20px;
	margin: 2px 8px;
	border-radius: 6px;
	cursor: pointer;
	transition: all 0.2s ease;

	&:hover {
		background-color: #f0f2f5;
	}

	&.active {
		background-color: #e6f7ff;
		border: 1px solid #91d5ff;
		color: #1890ff;
	}
}

.bot-name {
	font-size: 14px;
	font-weight: 500;
}

.bot-status {
	width: 8px;
	height: 8px;
	border-radius: 50%;
	background-color: #d9d9d9;

	&.online {
		background-color: #52c41a;
	}
}

// 右侧主区域
.chatbot-main {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	min-width: 0;
	position: relative;
}

.main-header {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 56px;
	padding: 16px 20px;
	border-bottom: 1px solid #e4e7ed;
	background-color: #fff;
	z-index: 1;
	display: flex;
	align-items: center;

	h3 {
		margin: 0;
		font-size: 16px;
		font-weight: 500;
		color: #303133;
	}
}

.iframe-container {
	position: absolute;
	top: 56px;
	left: 0;
	right: 0;
	bottom: 0;
	overflow: hidden;
}

.chatbot-iframe {
	width: 100%;
	height: 100%;
	border: none;
}

.no-selection {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

// 响应式设计
@media (max-width: 768px) {
	.chatbot-layout {
		flex-direction: column;
	}

	.chatbot-sidebar {
		width: 100%;
		height: 200px;
		border-right: none;
		border-bottom: 1px solid #e4e7ed;
	}

	.bot-list {
		display: flex;
		flex-direction: row;
		overflow-x: auto;
		overflow-y: hidden;
		padding: 8px;
	}

	.bot-item {
		min-width: 120px;
		margin: 0 4px;
		flex-shrink: 0;
	}

	.chatbot-main {
		flex: 1;
		min-height: 500px;
	}
}
</style>
