<template>
	<cl-view-group ref="ViewGroup">
		<template #item-name="{ item }"> {{ item.name }} - {{ item.key }} </template>

		<template #right>
			<cl-crud ref="Crud">
				<cl-row>
					<!-- 刷新按钮 -->

					<!-- 新增按钮 -->
					<!-- <div v-if="dictType.source == '录入数据'">11</div> -->
					<cl-add-btn />
					<el-button @click="refreshDict">刷新</el-button>
					<cl-flex1 />
					<!-- 关键字搜索 -->
					<cl-search-key :placeholder="$t('搜索名称')" />
				</cl-row>

				<cl-row>
					<!-- 数据表格 -->
					<cl-table
						ref="Table"
						row-key="id"
						lazy
						:load="load"
						:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
						:data="paginatedData"
					>
						<template #slot-btn="{ scope }">
							<el-button
								v-permission="service.dict.info.permission.add"
								text
								bg
								type="success"
								@click="append(scope.row)"
								>新增</el-button
							>
							<el-button
								text
								bg
								type="primary"
								v-permission="service.dict.info.permission.add"
								@click="updateStatus(scope.row)"
								>{{ scope.row.status == 1 ? '禁用' : '启用' }}</el-button
							>
						</template>
					</cl-table>
				</cl-row>

				<cl-row>
					<cl-flex1 />
					<!-- 添加自定义分页组件 -->
					<el-pagination
						v-model:current-page="currentPage"
						v-model:page-size="pageSize"
						:total="totalRootItems"
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
						:page-sizes="[10, 20, 50, 100]"
						layout="total, sizes, prev, pager, next, jumper"
					/>
				</cl-row>

				<!-- 新增、编辑 -->
				<cl-upsert ref="Upsert">
					<template #slot-value="{ scope }">
						<div>
							<el-input
								v-model="scope.value"
								placeholder="请填写代码"
								clearable
								type="textarea"
								:rows="4"
								class="mb-2"
							/>
							<div class="op"></div>
						</div>
					</template>
				</cl-upsert>
			</cl-crud>
		</template>
	</cl-view-group>
</template>

<script lang="ts" setup>
defineOptions({
	name: 'dict-list'
});

import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { computed, ref } from 'vue';
import { deepTree, mergeArrays } from '/@/cool/utils';
import { cloneDeep } from 'lodash-es';
import { useDict } from '../index';
import { useViewGroup } from '/@/plugins/view';
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus';
import { getDictFromIndexDB, saveDictToIndexDB, clearDictDB } from '/@/modules/luru/utils/indexDB';
import { Plugins } from '/#/crud';
import { useI18n } from 'vue-i18n';
const { service } = useCool();
const { dict } = useDict();
const { t } = useI18n();
const typeId = ref();
const dictType = ref();
const loading = ref(false);

// 在组件顶部声明一个 ref 来存储树数据
const treeData: any = ref([]);

const currentPage = ref(1);
const pageSize = ref(20);
const allData = ref([]);
const paginatedData: any = ref([]);
const isDetailAdd = ref(false);
const totalRootItems = computed(() => allData.value.length);

function getTableData() {
	const startIndex = (currentPage.value - 1) * pageSize.value;
	const endIndex = startIndex + pageSize.value;
	paginatedData.value = allData.value.slice(startIndex, endIndex).map(item => ({
		...item,
		children: [] // 保留完整的子树结构
	}));
}
async function updateStatus(row: any) {
	ElMessageBox.confirm(`确定要${row.status == 0 ? '启用' : '禁用'}该字典吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(async () => {
		await service.dict.info.update({
			id: row.id,
			status: row.status == 0 ? 1 : 0
		});
		refreshDict();
	});
}

// const paginatedData = computed(() => {
// 	console.log(
// 		'返回的table',
// 		allData.value.slice(startIndex, endIndex).map(item => ({
// 			...item,
// 			children: null // 保留完整的子树结构
// 		}))
// 	);
// 	return;
// 	// return allData.value.slice(startIndex, endIndex).map(item => ({
// 	// 	...item,
// 	// 	children: item.children // 保留完整的子树结构
// 	// }));
// });
const load = (row, treeNode, resolve) => {
	//let data: any = row.children;
	const children = findChildrenById(allData.value, row.id);

	setTimeout(() => {
		resolve(children);
	}, 50);
};
const findChildrenById = (data: any[], targetId: string | number): any[] => {
	// 深度优先搜索
	for (const item of data) {
		if (item.id === targetId) {
			return item.children || [];
		}
		if (item.children && item.children.length) {
			const result = findChildrenById(item.children, targetId);
			if (result.length) {
				return result;
			}
		}
	}
	return [];
};
const handleSizeChange = (val: number) => {
	pageSize.value = val;
	currentPage.value = 1;
	getTableData();
};

const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	getTableData();
};

const { ViewGroup } = useViewGroup({
	label: '类型',
	title: '字典列表',
	service: service.dict.type,
	onSelect(item) {
		dictType.value = item;
		typeId.value = item.id;
		refresh({
			typeId: item.id,
			page: 1,
			prop: 'orderNum',
			order: 'desc'
		});
	},
	onEdit() {
		return {
			width: '500px',
			props: {
				labelWidth: '80px'
			},
			items: [
				{
					label: '名称',
					prop: 'name',
					component: {
						name: 'el-input',
						props: {
							maxlength: 20
						}
					},
					required: true
				},
				{
					label: 'Key',
					prop: 'key',
					component: {
						name: 'el-input',
						props: {
							maxlength: 20
						}
					},
					required: true
				},
				{
					label: '父节点',
					prop: 'parentId',
					component: {
						name: 'el-tree-select',
						props: {
							data: computed(() => {
								const data = cloneDeep(ViewGroup.value.list);
								function deep(d: any, f: boolean) {
									console.log('d', d, 'data', data);
									if (d.id && d.id == Upsert.value?.getForm('id')) {
										f = true;
									}

									if (f) {
										d.disabled = true;
									}

									if (d.children) {
										d.children.forEach((e: any) => {
											deep(e, f);
										});
									}
								}
								deep({ children: data }, false);
								return data;
							}),
							props: {
								label: 'name',
								value: 'id',
								children: 'children',
								disabled: 'disabled'
							},
							clearable: true,
							filterable: true,
							'default-expand-all': false,
							'check-strictly': true
						}
					},
					required: true
				},
				{
					label: '类型',
					prop: 'type',
					component: {
						name: 'el-select',
						props: {
							clearable: true, // 可清除
							filterable: true
						},
						options: [
							{ label: '普通字典', value: 'dict' },
							{ label: '系统字典', value: 'system' }
						]
					},
					required: true
				},
				{
					label: '字典显示类型',
					prop: 'dictLoadType',
					component: {
						name: 'el-select',
						props: {
							clearable: true, // 可清除
							filterable: true
						},
						options: [
							{ label: '下拉型字典(下来选项展示数据)', value: 'local' },
							{ label: '搜索型字典(直接搜索数据)', value: 'remotr' }
						]
					},
					required: true
				}
			]
		};
	}
});
async function refreshDict() {
	await clearDictDB();
	Crud.value?.refresh();
}

// cl-upsert
const Upsert = useUpsert({
	dialog: {
		width: '800px'
	},
	props: {
		labelWidth: '100px'
	},
	items: [
		e => {
			console.log('ViewGroup.value.list', ViewGroup.value.list);
			console.log('e', e);
			if (!isDetailAdd.value) {
				treeData.value = allData.value;
			}
			return {
				label: '上级节点',
				prop: 'parentId',
				component: {
					name: 'el-tree-select',
					props: {
						data: treeData,
						props: {
							label: 'name',
							value: 'id',
							children: 'children',
							disabled: 'disabled'
						},
						clearable: true,
						filterable: true,
						'default-expand-all': false,
						'check-strictly': true,
						'render-after-expand': false
					}
				}
			};
		},
		{
			label: '名称',
			prop: 'name',
			required: true,
			component: { name: 'el-input' }
		},
		{
			label: '代码',
			prop: 'value',
			required: true,
			component: { name: 'slot-value' }
		},
		{
			label: '备注',
			prop: 'remark',
			component: { name: 'el-input', props: { type: 'textarea', rows: 4 } }
		},
		{
			label: '启用状态',
			prop: 'status',
			required: true,
			component: {
				name: 'el-switch',
				props: {
					activeValue: 1,
					inactiveValue: 0
				}
			},
			value: 1 // 设置默认值为1
		}
	],
	onSubmit(data, { done, close, next }) {
		console.log('data提交', data, treeData.value);
		data.source = '录入系统';
		if (data.parentId) {
			const newRes = findObjectById(allData.value, data.parentId);
			console.log('newRes', newRes);
			const newtypeId = newRes.typeId;
			const res = findObjectById(ViewGroup.value.list, newtypeId);
			console.log('res', res);
			if (res.children && res.children.length > 0) {
				data.typeId = res.children[0].id;
			} else {
				ElMessage.warning('该字典类型下没有子节点,请先在左侧对应字典类型下新增子节点');

				close();
				return;
			}
		} else {
			data.parentId = null;
		}
		console.log('data!!!!!', data);
		next({
			...data,
			typeId: data.parentId ? data.typeId : ViewGroup.value?.selected?.id
		});
		done();
		isDetailAdd.value = false;
	},
	plugins: [Plugins.Form.setFocus('name')]
});
function findObjectById(arr, id) {
	// 遍历数组中的每个对象
	for (const obj of arr) {
		// 如果当前对象的 id 匹配，直接返回
		if (obj.id === id) {
			return obj;
		}
		// 如果当前对象有 children，递归查找
		if (obj.children && obj.children.length > 0) {
			const result = findObjectById(obj.children, id);
			// 如果在子级中找到匹配的对象，返回结果
			if (result) {
				return result;
			}
		}
	}
	// 如果没有找到，返回 undefined
	return undefined;
}
// cl-table
const Table = useTable({
	contextMenu: [
		'refresh',
		row => {
			return {
				label: '新增',
				hidden: !service.dict.info._permission?.add || row.source == '主数据',
				callback(done) {
					append(row);
					done();
				}
			};
		},
		'order-asc',
		'order-desc'
	],
	columns: [
		{ label: '名称', prop: 'name', align: 'left', minWidth: 240 },
		{ label: '代码', prop: 'value', minWidth: 150, showOverflowTooltip: true },
		{ label: '备注', prop: 'remark', showOverflowTooltip: true, minWidth: 150 },
		// 启用状态显示成el-switch，不可编辑
		{
			label: '启用状态',
			prop: 'status',
			showOverflowTooltip: true,
			minWidth: 60,
			dict: [
				{
					label: '启用',
					value: 1,
					type: 'success'
				},
				{
					label: '禁用',
					value: 0,
					type: 'danger'
				}
			]
		},
		// { label: "创建时间", prop: "createTime", sortable: "custom", minWidth: 170 },
		{ label: '更新时间', prop: 'updateTime', sortable: 'custom', minWidth: 120 },
		{ label: '字典来源', prop: 'source', sortable: 'custom', minWidth: 100 },
		{
			type: 'op',
			width: 180,
			buttons({ scope }) {
				if (scope.row.type == 'system') {
					return [];
				}
				if (scope.row.source == '录入系统') {
					return ['edit', 'slot-btn'];
				}
				return ['slot-btn'];
			}
		}
	],
	plugins: [Plugins.Table.toTree()]
});

// cl-crud
const Crud = useCrud({
	service: service.dict.info,
	async onRefresh(params, { render }) {
		try {
			//loading.value = true;
			// 添加 loading 状态
			// const loading = ElLoading.service({
			// 	lock: true,
			// 	text: '加载中...'
			// });

			let dictAllData;
			if (params.keyWord) {
				// 关键词搜索时只获取匹配数据
				const res = await service.dict.info.list({
					...params,
					page: undefined,
					size: undefined
				});
				dictAllData = res;
			} else {
				// 分批加载数据
				const typeIdArray = params.typeId ? [params.typeId] : [];
				console.log('getindexDB数据');
				dictAllData = {};
				dictAllData = cloneDeep(await getDictFromIndexDB(typeIdArray));
				console.log('dictAllData', dictAllData);
				if (!dictAllData) {
					dictAllData = await service.dict.info.getAllChildren({
						...params,
						page: undefined,
						size: undefined
					});
					console.log('保存到indexDB1', dictAllData);
					// 异步保存到 IndexDB
					saveDictToIndexDB(typeIdArray, dictAllData).catch(console.error);
					console.log('保存到indexDB1成功');
				}
			}
			console.log('dictAllData', dictAllData);
			// 使用 Web Worker 处理数据转换
			const reqall = cloneDeep(mergeArrays(dictAllData));
			allData.value = deepTree(reqall, params.sort);
			console.log('allData', allData.value);
			getTableData();
			render(paginatedData.value);

			loading.value = false;
			//render(getTableData());
			console.log('加载完成', loading.value);
			//loading.close();
		} catch (error) {
			ElMessage.error('加载数据失败');
			console.error(error);
			loading.value = false;
		}
	}
});

// 刷新
async function refresh(params?: any) {
	// console.log("刷新")
	Crud.value?.refresh(params);
	// try {
	// 	// 异步获取数据
	// 	console.log("ViewGroup.value?.selected",ViewGroup.value?.selected)
	// 	const rawData = await service.dict.info.list({
	// 		typeId: ViewGroup.value?.selected?.parentId
	// 	});

	// 	// 将原始数据转换为树结构
	// 	const tree = deepTree(rawData);

	// 	// 克隆树数据以避免修改原始数据
	// 	const data = cloneDeep(tree);

	// 	// 应用禁用逻辑
	// 	function deep(d: any, f: boolean) {
	// 		if (d.id && d.id == Upsert.value?.getForm("id")) {
	// 			f = true;
	// 		}

	// 		if (f) {
	// 			d.disabled = true;
	// 		}

	// 		if (d.children) {
	// 			d.children.forEach((e: any) => {
	// 				deep(e, f);
	// 			});
	// 		}
	// 	}

	// 	deep({ children: data }, false);

	// 	// 更新 ref
	// 	treeData.value = data;
	// } catch (error) {
	// 	console.error('获取树数据失败:', error);
	// 	treeData.value = []; // 出错时设置为空数组
	// }
}

// 行点击展开
// function onRowClick(row: any, column: any) {
// 	if (column?.property && row.children) {
// 		Table.value?.toggleRowExpansion(row);
// 	}
// }

// 追加子集
async function append(row: any) {
	const res = findObjectById(ViewGroup.value.list, row.typeId);

	console.log('res', res);
	if (!res.children || res.children.length == 0) {
		ElMessage.warning('该字典类型下没有子节点,请先在左侧对应字典类型下新增子节点');
		return;
	}
	isDetailAdd.value = true;

	treeData.value = [
		{
			name: row.name,
			id: row.id
		}
	];
	Crud.value?.rowAppend({
		parentId: row.id,
		typeId: res.children[0].id,
		orderNum: 1,
		status: 1 // 设置默认状态为1
	});
}
</script>
