import { useStore } from "../store/index";
import { cloneDeep } from "lodash-es";
const flowFormStore = useStore();

export const pageApi = params => {
	return new Promise(async (resolve, reject) => {
		let data: any = await flowFormStore.formFlowStore.getFormFlowUserList();

		//console.log("得到data", data);
		if (params.keyWord) {
			data = data.filter(item => {
				return item.username.includes(params.keyWord);
			});
		}
		//console.log("查询data", data);
		// 计算分页数据
		const page = params.page || 1;
		const size = params.size || 10;
		const start = (page - 1) * size;
		const end = start + size;
		const pageData = data.slice(start, end);

		resolve({
			total: data.length,
			rows: pageData
		});
	});
};
