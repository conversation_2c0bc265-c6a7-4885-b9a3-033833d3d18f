import { useStore } from "../store/index";
const flowStore = useStore();
export const pageApi = params => {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			if (params.flowName) {
				const data = flowStore.flowStore
					.getFlowData()
					.filter(item => item.flowName.includes(params.flowName));
				resolve({
					total: data.length,
					rows: data
				});
			} else {
				resolve({
					total: flowStore.flowStore.getFlowData().length,
					rows: flowStore.flowStore.getFlowData()
				});
			}
		}, 200);
	});
};
export const delApi = params => {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			console.log("delApi-params", params);
			flowStore.flowStore.delFlowData(params);
			resolve({});
		}, 200);
	});
};
export const publishApi = params => {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			flowStore.flowStore.publishFlowData(params);
			resolve({});
		}, 200);
	});
};
export const addApi = params => {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			flowStore.flowStore.addFlowData(params);
			resolve({});
		}, 200);
	});
};
export const editApi = params => {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			flowStore.flowStore.editFlowData(params);
			// const index = flowData.findIndex(item => item.id === params.id);
			// flowData[index] = params;
			resolve({});
		}, 200);
	});
};
export const listApi = () => {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			resolve(flowStore.flowStore.getFlowData());
		}, 200);
	});
};
export const detailApi = params => {
	return new Promise((resolve, reject) => {
		console.log("detailApi-params", params);
		const data = flowStore.flowStore.getFlowData().find(item => item.id === params.id);
		console.log("detailApi", data);
		resolve({ data: data });
	});
};
export const saveConfigApi = params => {
	return new Promise((resolve, reject) => {
		setTimeout(() => {
			flowStore.flowStore.updateFlowData(params);
			resolve({});
		}, 200);
	});
};
