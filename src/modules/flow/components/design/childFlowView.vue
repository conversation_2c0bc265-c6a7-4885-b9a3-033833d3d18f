<template>
	<el-dialog v-model="dialogVisible" title="选择流程" width="80%">
		<div class="child-flow-container">
			<!-- 搜索框 -->
			<div class="search-box">
				<el-input
					v-model="searchKeyword"
					placeholder="请输入流程名称搜索"
					prefix-icon="Search"
					clearable
					@input="handleSearch"
				/>
			</div>

			<!-- 流程列表 -->
			<div class="flow-grid">
				<div
					v-for="flow in currentPageFlows"
					:key="flow.id"
					class="flow-item"
					:class="{ selected: selectedFlow?.id === flow.id }"
					@click="selectFlow(flow)"
					@mouseenter="handleMouseEnter(flow)"
					@mouseleave="handleMouseLeave"
				>
					<!-- 流程缩略图 -->
					<div class="flow-thumbnail">
						<div :style="{ transform: 'scale(' + 20 / 100 + ')' }">
							<flow-render
								v-if="flow.flowJson"
								v-model="flow.flowJson"
								v-bind="flow.flowJson"
								:readonly="true"
								class="flow-preview"
							></flow-render>
						</div>
						<div class="flow-thumbnail-placeholder">
							<i class="el-icon-share"></i>
						</div>
						<div class="flow-actions">
							<el-button
								type="primary"
								size="small"
								@click.stop="topreviewFlow(flow.flowJson)"
							>
								查看
							</el-button>
						</div>
					</div>

					<!-- 流程名称 -->
					<div class="flow-name" :title="flow.name">{{ flow.name }}</div>
				</div>
			</div>

			<!-- 分页 -->
			<div class="pagination-container">
				<el-pagination
					v-model:current-page="currentPage"
					v-model:page-size="pageSize"
					:total="filteredFlows.length"
					:page-sizes="[12, 24, 36, 48]"
					layout="total, sizes, prev, pager, next"
				/>
			</div>
		</div>

		<!-- 底部按钮 -->
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="confirmSelection" :disabled="!selectedFlow">
					确定
				</el-button>
			</span>
		</template>

		<!-- 预览弹窗 -->
		<el-dialog v-model="previewVisible" title="流程预览" width="90%" append-to-body>
			<div class="preview-container">
				<flow-render
					v-if="previewFlow"
					v-model="previewFlow"
					v-bind="previewFlow"
					:readonly="true"
				></flow-render>
			</div>
		</el-dialog>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import FlowRender from "./nodes/render.vue";
import { useCool } from "/@/cool";
import { ElMessage } from "element-plus";

const { service } = useCool();
const dialogVisible = ref(false);
const previewVisible = ref(false);
const selectedFlow = ref(null);
const previewFlow = ref(null);
const currentPage = ref(1);
const pageSize = ref(12);

// 模拟流程数据
const flowList = ref([]);

const searchKeyword = ref("");

// 根据搜索关键词过滤流程
const filteredFlows = computed(() => {
	if (!searchKeyword.value) return flowList.value;
	return flowList.value.filter(flow =>
		flow.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
	);
});

// 当前页的流程数据
const currentPageFlows = computed(() => {
	const start = (currentPage.value - 1) * pageSize.value;
	const end = start + pageSize.value;
	return filteredFlows.value.slice(start, end);
});

// 搜索处理
const handleSearch = () => {
	// 可以在这里添加实时搜索逻辑
};

// 选择流程
const selectFlow = flow => {
	selectedFlow.value = flow;
};

// 预览流程
const topreviewFlow = flow => {
	console.log("flow", flow);
	previewFlow.value = flow;
	previewVisible.value = true;
};

// 确认选择
const confirmSelection = () => {
	console.log("选择的子流程", selectedFlow.value);
	if (selectedFlow.value) {
		emit("selectFlow", selectedFlow.value.flowJson, selectedFlow.value.name);
		dialogVisible.value = false;
	}
};

// 打开对话框
const openDialog = async () => {
	dialogVisible.value = true;
	selectedFlow.value = null;
	try {
		const res: any = await service.flow.flow.list();
		flowList.value = res;
	} catch (error) {
		console.error(error);
		ElMessage.error("错误" + error.message);
	}
};

defineExpose({
	openDialog
});

const emit = defineEmits(["selectFlow"]);
</script>

<style scoped>
.child-flow-container {
	padding: 20px;
}

.search-box {
	margin-bottom: 20px;
}

.flow-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
	gap: 20px;
}

.flow-item {
	border: 1px solid #e4e7ed;
	border-radius: 4px;
	padding: 10px;
	cursor: pointer;
	transition: all 0.3s;
	position: relative;
}

.flow-item:hover {
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	border-color: #409eff;
}

.flow-item.selected {
	border-color: #409eff;
	box-shadow: 0 0 8px rgba(64, 158, 255, 0.6);
}

.flow-preview {
	transform: scale(0.1);
	transform-origin: center center;
	width: 100%;
	height: 100%;
}

.flow-thumbnail {
	width: 100%;
	height: 120px;
	background-color: #f5f7fa;
	border-radius: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 8px;
	overflow: hidden;
	position: relative;
}

.flow-thumbnail img {
	max-width: 100%;
	max-height: 100%;
	object-fit: contain;
}

.flow-thumbnail-placeholder {
	color: #909399;
	font-size: 24px;
}

.flow-name {
	font-size: 14px;
	color: #606266;
	text-align: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.flow-actions {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	display: none;
	background-color: rgba(0, 0, 0, 0.5);
	padding: 8px;
	border-radius: 4px;
}

.flow-item:hover .flow-actions {
	display: flex;
	gap: 8px;
}

.pagination-container {
	margin-top: 20px;
	display: flex;
	justify-content: center;
}

.preview-container {
	height: 70vh;
	overflow: auto;
	background-color: var(--global-flow-background-color);
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
}
</style>
