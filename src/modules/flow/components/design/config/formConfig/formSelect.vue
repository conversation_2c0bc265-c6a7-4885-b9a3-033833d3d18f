<template>
	<el-dialog
		v-model="dialogVisible"
		title="表单配置"
		width="65%"
		:before-close="handleClose"
		class="form-select-dialog"
		@open="handleOpen"
	>
		<div class="form-container">
			<el-form
				ref="formRef"
				:model="formData"
				:rules="rules"
				label-position="left"
				class="config-form"
			>
				<!-- 表单选择部分 -->
				<div class="section form-section">
					<div class="section-header">
						<div class="section-header-left">
							<div class="title-wrapper">
								<div>
									<el-icon class="title-icon"><document-add /></el-icon>
									<span class="section-title">选择关联表单</span>
								</div>
							</div>
							<div>
								<p class="section-desc">
									<el-icon><info-filled /></el-icon>
									请选择需要关联的表单，用于后续的字段映射配置
								</p>
							</div>
						</div>
					</div>
					<div class="select-wrapper">
						<el-select
							v-model="formData.externalFormId"
							placeholder="请选择要关联的表单"
							class="form-select"
							@change="handleFormChange"
						>
							<el-option
								v-for="item in formList"
								:key="item.id"
								:label="item.name"
								:value="item.id"
							>
								<div class="form-option">
									<el-icon><tickets /></el-icon>
									<span>{{ item.name }}</span>
								</div>
							</el-option>
						</el-select>
					</div>
				</div>

				<!-- 条件列映射部分 -->
				<div class="section mapping-section">
					<div class="section-header">
						<div class="section-header-left">
							<div class="title-wrapper">
								<el-icon class="title-icon"><connection /></el-icon>
								<span class="section-title">条件列映射配置</span>
							</div>
							<p class="section-desc">
								<el-icon><link /></el-icon>
								配置主表与权限人表之间的字段映射关系
							</p>
						</div>
						<div class="section-header-right">
							<el-button
								type="primary"
								plain
								@click="addConditionColumn"
								class="add-button"
							>
								<el-icon><plus /></el-icon>
								添加映射关系
							</el-button>
						</div>
					</div>

					<div class="mapping-list">
						<div
							v-for="(field, index) in formData.formFields"
							:key="'condition-' + index"
							class="mapping-item"
						>
							<div class="mapping-content">
								<div class="select-group">
									<span class="select-label">
										<el-icon><grid /></el-icon>
										主表字段
									</span>
									<el-select
										v-model="formData.formFields[index]"
										placeholder="选择主表字段"
										class="mapping-select"
									>
										<el-option
											v-for="option in primaryTableCols"
											:key="option.field"
											:label="option.label"
											:value="option.field"
										/>
									</el-select>
								</div>

								<div class="mapping-arrow">
									<el-icon><right /></el-icon>
								</div>

								<div class="select-group">
									<span class="select-label">
										<el-icon><grid /></el-icon>
										权限人表字段
									</span>
									<el-select
										v-model="formData.externalFields[index]"
										placeholder="选择权限人表字段"
										class="mapping-select"
									>
										<el-option
											v-for="option in externalFormFields"
											:key="option.field"
											:label="option.label"
											:value="option.field"
										/>
									</el-select>
								</div>

								<el-button
									class="delete-btn"
									@click="removeConditionColumn(index)"
									link
									type="danger"
								>
									<el-icon><delete /></el-icon>
								</el-button>
							</div>
						</div>
					</div>
				</div>

				<!-- 权限人列映射部分 -->
				<div class="section auth-section">
					<div class="section-header">
						<div class="section-header-left">
							<div class="title-wrapper">
								<el-icon class="title-icon"><user-filled /></el-icon>
								<span class="section-title">权限人配置</span>
							</div>
							<p class="section-desc">
								<el-icon><key /></el-icon>
								配置流程节点的权限人字段映射关系
							</p>
						</div>
					</div>

					<div class="mapping-list">
						<div
							v-for="(field, index) in formData.candidateUserField"
							:key="'value-' + index"
							class="mapping-item auth-mapping-item"
						>
							<div class="mapping-content">
								<div class="field-name">
									<el-icon><user /></el-icon>
									<span>权限人</span>
								</div>
								<div class="mapping-arrow">
									<el-icon><right /></el-icon>
								</div>
								<el-select
									v-model="formData.candidateUserField[index]"
									placeholder="选择表单字段"
									class="mapping-select"
								>
									<el-option
										v-for="option in externalFormFields"
										:key="option.field"
										:label="option.label"
										:value="option.field"
									/>
								</el-select>
							</div>
						</div>
					</div>
				</div>
			</el-form>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="submitSelectData">确定</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, defineExpose, defineEmits, nextTick, toRefs } from 'vue';
import { ElMessage } from 'element-plus';
import { useCool } from '/@/cool';
import { useStore } from '/@/modules/flow/store';
import {
	DocumentAdd,
	InfoFilled,
	Search,
	Connection,
	Link,
	Grid,
	UserFilled,
	Key,
	User,
	Right,
	Delete,
	Plus,
	Tickets
} from '@element-plus/icons-vue';
import { changeRules } from '/@/modules/luru/utils/luruTable';

const { formFlowStore } = useStore();
const { service } = useCool();
const dialogVisible = ref(false);
const formList: any = ref([]);
const formRef = ref();

// 表单验证规则
const rules = {
	externalFormId: [{ required: true, message: '请选择关联表单', trigger: 'change' }],
	formFields: [
		{
			type: 'array',
			required: true,
			message: '请至少添加一个条件列映射',
			trigger: 'change',
			validator: (rule: any, value: any[], callback: any) => {
				if (!value || value.length === 0) {
					callback(new Error('请至少添加一个条件列映射'));
					return;
				}
				// 检查是否所有映射都已选择字段
				const hasEmptyField = value.some((field, index) => {
					return !field || !formData.value.externalFields[index];
				});
				if (hasEmptyField) {
					callback(new Error('请完成所有条件列的字段映射'));
					return;
				}
				callback();
			}
		}
	],
	candidateUserField: [
		{
			type: 'array',
			required: true,
			message: '请选择权限人字段',
			trigger: 'change',
			validator: (rule: any, value: any[], callback: any) => {
				if (!value || value.length === 0) {
					callback(new Error('请选择权限人字段'));
					return;
				}
				// 检查是否所有权限人字段都已选择
				const hasEmptyField = value.some(field => !field);
				if (hasEmptyField) {
					callback(new Error('请完成权限人字段的选择'));
					return;
				}
				callback();
			}
		}
	]
};

const handleClose = () => {
	dialogVisible.value = false;
};
const formData: any = ref({
	externalFormName: '',
	externalFormId: '',
	formFields: [''],
	externalFields: [''],
	candidateUserField: ['']
});
const externalFormFields: any = ref([]);
const primaryTableCols: any = ref([]);
const props = defineProps({
	data: {
		type: Object,
		default: () => ({})
	}
});
const { data } = toRefs(props);
const handleFormChange = value => {
	const data = formList.value.find(item => item.id === value);
	console.log('找到表单', data);
	if (data) {
		const colList = changeRules(data.colInfo).filter(item => item.selected);
		externalFormFields.value = [];
		colList.map(item => {
			if (item.hasDict) {
				if (item.isProject) {
					externalFormFields.value.push({
						field: 'projectCode',
						label: item.showName
					});
				} else {
					externalFormFields.value.push({
						field: `${item.name}代码`,
						label: item.showName
					});
				}
			} else {
				externalFormFields.value.push({
					field: item.name,
					label: item.showName
				});
			}
		});

		// 重置映射字段
		formData.value.formFields = [''];
		formData.value.externalFields = [''];
	}
};
const handleOpen = () => {
	console.log('handleOpen');
	if (data.value.externalFormName) {
		formData.value = data.value;
	}
};
function addConditionColumn() {
	if (!formData.value.externalFormId) {
		ElMessage.warning('请先选择关联表单');
		return;
	}
	formData.value.formFields.push('');
	formData.value.externalFields.push('');
}
const open = async record => {
	try {
		const res = await service.cloud.db.list();
		if (res) {
			formList.value = res;
			let cols = [];
			if (record) {
				console.log('已传值', record);
				cols = res.find(item => item.id === record.formId)?.colInfo;
			} else {
				cols = res.find(
					item => item.id === formFlowStore.getFormFlowData().form_id
				)?.colInfo;
			}
			const colList = changeRules(cols).filter(item => item.selected);
			primaryTableCols.value = [];
			colList.map(item => {
				if (item.hasDict) {
					if (item.isProject) {
						primaryTableCols.value.push({
							field: 'projectCode',
							label: item.showName
						});
					} else {
						primaryTableCols.value.push({
							field: `${item.name}代码`,
							label: item.showName
						});
					}
				} else {
					primaryTableCols.value.push({
						field: item.name,
						label: item.showName
					});
				}
			});
			// 确保数据都准备好后，再打开抽屉
			nextTick(() => {
				dialogVisible.value = true;
			});
		}
	} catch (error) {
		console.error('加载表单列表失败:', error);
		ElMessage.error('加载表单列表失败');
	}
};

const emits = defineEmits(['formSelectData']);
const submitSelectData = async () => {
	try {
		// 表单验证
		await formRef.value.validate();

		// 设置表单名称
		formData.value.externalFormName = formList.value.find(
			item => item.id === formData.value.externalFormId
		)?.name;
		console.log('formData.value', formData.value);
		// 发射事件
		emits('formSelectData', formData.value);

		// 关闭对话框
		dialogVisible.value = false;
	} catch (error) {
		// 验证失败时的错误提示
		if (error.message) {
			ElMessage.error(error.message);
		} else {
			ElMessage.error('请检查表单填写是否完整');
		}
	}
};

// 添加删除条件列的方法
const removeConditionColumn = (index: number) => {
	if (formData.value.formFields.length <= 1) {
		ElMessage.warning('至少保留一个条件列映射');
		return;
	}
	formData.value.formFields.splice(index, 1);
	formData.value.externalFields.splice(index, 1);
};

defineExpose({ open });
</script>

<style scoped lang="scss">
.form-container {
	padding: 0 20px;
	max-height: 75vh;
	overflow-y: auto;
}

.section {
	margin-bottom: 32px;
	padding: 24px;
	border-radius: 8px;
	transition: all 0.3s ease;
}

.form-section {
	background: #f0f2f5;
	//	background: var(--el-color-primary-light-9);
	border: 1px solid var(--el-color-primary-light-7);
}

.mapping-section {
	background: #f0f2f5;
	//background: var(--el-color-success-light-9);
	border: 1px solid var(--el-color-success-light-7);
}

.auth-section {
	background: #f0f2f5;
	//background: var(--el-color-warning-light-9);
	border: 1px solid var(--el-color-warning-light-7);
}

.section-header {
	margin-bottom: 20px;
	display: flex;
	justify-content: space-between;
}

.title-wrapper {
	display: flex;
	align-items: center;
	gap: 8px;
	margin-bottom: 8px;
}

.title-icon {
	font-size: 20px;
	color: var(--el-color-primary);
}

.section-title {
	font-size: 16px;
	font-weight: 500;
	color: var(--el-text-color-primary);
}

.section-desc {
	margin: 0;
	font-size: 13px;
	color: var(--el-text-color-secondary);
	display: flex;
	align-items: center;
	gap: 4px;
}

.select-wrapper {
	position: relative;
	display: flex;
	align-items: center;
}

.select-prefix-icon {
	position: absolute;
	left: 10px;
	color: var(--el-text-color-secondary);
	z-index: 1;
}

.form-select {
	width: 100%;
	max-width: 500px;
}

.mapping-list {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.mapping-item {
	padding: 20px;
	background: var(--el-bg-color-overlay);
	border: 1px solid var(--el-border-color-light);
	border-radius: 8px;
	transition: all 0.3s ease;
}

.mapping-item:hover {
	border-color: var(--el-border-color-darker);
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.mapping-content {
	display: flex;
	align-items: center;
	gap: 16px;
}

.select-group {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.select-label {
	font-size: 12px;
	color: var(--el-text-color-secondary);
	display: flex;
	align-items: center;
	gap: 4px;
}

.mapping-select {
	width: 220px;
}

.mapping-arrow {
	color: var(--el-text-color-secondary);
	display: flex;
	align-items: center;
	font-size: 20px;
}

.field-name {
	min-width: 120px;
	color: var(--el-text-color-regular);
	font-size: 14px;
	display: flex;
	align-items: center;
	gap: 8px;
}

.form-option {
	display: flex;
	align-items: center;
	gap: 8px;
}

.add-button {
	margin-top: 12px;
	display: flex;
	align-items: center;
	gap: 4px;
}

.delete-btn {
	margin-left: auto;
	font-size: 18px;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
	padding-top: 16px;
}

:deep(.el-select .el-input__wrapper) {
	box-shadow: 0 0 0 1px var(--el-border-color) inset;
}

:deep(.el-select .el-input__wrapper:hover) {
	box-shadow: 0 0 0 1px var(--el-border-color-darker) inset;
}

/* 自定义滚动条 */
.form-container::-webkit-scrollbar {
	width: 6px;
}

.form-container::-webkit-scrollbar-thumb {
	background-color: var(--el-border-color);
	border-radius: 3px;
}

.form-container::-webkit-scrollbar-track {
	background-color: var(--el-fill-color-lighter);
}
</style>
