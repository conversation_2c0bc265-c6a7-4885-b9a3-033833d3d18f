<template>
	<el-dialog v-model="dialogVisible" title="表单配置" width="50%" :before-close="handleClose">
		<div style="display: flex; flex-direction: column; gap: 20px">
			<!-- 条件列配置 -->
			<div class="config-section">
				<div class="section-header">
					<h3>条件列配置</h3>
					<el-button type="primary" size="small" @click="addConditionColumn">
						添加条件列
					</el-button>
				</div>
				<div v-for="(item, index) in conditionColumns" :key="index" class="column-item">
					<el-input v-model="item.name" placeholder="请输入列名" />
					<el-button type="danger" size="small" @click="removeConditionColumn(index)">
						删除
					</el-button>
				</div>
			</div>

			<!-- 值列配置 -->
			<div class="config-section">
				<div class="section-header">
					<h3>权限人列配置</h3>
				</div>
				<div v-for="(item, index) in valueColumns" :key="index" class="column-item">
					<el-input v-model="item.name" placeholder="请输入列名" />
				</div>
			</div>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-space>
					<el-button @click="dialogVisible = false">取消</el-button>
					<el-button type="primary" @click="submitSelectData()"> 确定 </el-button>
				</el-space>
			</div>
		</template>
	</el-dialog>
</template>
<script setup lang="ts">
import { ref, defineExpose, defineEmits } from "vue";
import { ElMessage } from "element-plus";

const dialogVisible = ref(false);
const handleClose = () => {
	dialogVisible.value = false;
};

const open = record => {
	//tableReload();
	dialogVisible.value = true;
};

const emits = defineEmits(["formSelectData"]);

// 条件列数据
const conditionColumns = ref([{ name: "" }]);

// 值列数据
const valueColumns = ref([{ name: "" }]);

// 添加条件列
const addConditionColumn = () => {
	conditionColumns.value.push({ name: "" });
};

// 删除条件列
const removeConditionColumn = (index: number) => {
	if (conditionColumns.value.length > 1) {
		conditionColumns.value.splice(index, 1);
	} else {
		ElMessage.warning("至少保留一个条件列");
	}
};

// 添加值列
const addValueColumn = () => {
	valueColumns.value.push({ name: "" });
};

// 删除值列
const removeValueColumn = (index: number) => {
	if (valueColumns.value.length > 1) {
		valueColumns.value.splice(index, 1);
	} else {
		ElMessage.warning("至少保留一个值列");
	}
};

// 修改提交方法
const submitSelectData = () => {
	// 验证条件列
	const emptyConditionColumn = conditionColumns.value.find(column => !column.name.trim());
	if (emptyConditionColumn !== undefined) {
		ElMessage.warning("条件列名称不能为空");
		return;
	}

	// 验证值列
	const emptyValueColumn = valueColumns.value.find(column => !column.name.trim());
	if (emptyValueColumn !== undefined) {
		ElMessage.warning("权限人列名称不能为空");
		return;
	}
	const obj = {
		conditionColumns: conditionColumns.value.map(item => item.name),
		valueColumns: valueColumns.value.map(item => item.name)
	};
	emits("formSelectData", obj);
	console.log("submitSelectData", obj, conditionColumns.value, valueColumns.value);
	dialogVisible.value = false;
};
defineExpose({ open });
</script>

<style scoped>
.config-section {
	background-color: #f5f7fa;
	padding: 16px;
	border-radius: 8px;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16px;
}

.section-header h3 {
	margin: 0;
	color: #303133;
}

.column-item {
	display: flex;
	gap: 10px;
	margin-bottom: 10px;
	align-items: center;
}

.column-item .el-input {
	flex: 1;
}
</style>
