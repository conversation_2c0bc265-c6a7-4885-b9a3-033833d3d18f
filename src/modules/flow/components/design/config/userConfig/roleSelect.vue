<template>
	<el-dialog v-model="dialogVisible" title="" width="50%" :before-close="handleClose" top="20px">
		<div style="display: flex; flex-direction: column; gap: 10px">
			<el-card :body-style="{ 'padding-bottom': '0px' }">
				<el-form
					:inline="true"
					:model="formInline"
					class="demo-form-inline"
					label-suffix=":"
				>
					<el-form-item label="角色编码">
						<el-input v-model="queryData.label" placeholder="角色编码" clearable />
					</el-form-item>
					<el-form-item label="角色名称">
						<el-input v-model="queryData.name" placeholder="角色名称" clearable />
					</el-form-item>
					<el-form-item>
						<el-button type="primary" @click="tableReload()" plain>查询</el-button>
					</el-form-item>
				</el-form>
			</el-card>

			<el-card>
				<el-table
					:data="tableData"
					:border="true"
					style="width: 100%; height: calc(240px + 10vh)"
					@selection-change="handleSelectionChange"
					v-if="tableData.length > 0"
				>
					<el-table-column type="selection" width="55" />
					<el-table-column prop="label" label="角色编码" />
					<el-table-column prop="name" label="角色名称" />
					<el-table-column prop="type" label="类型" />

					<el-table-column prop="createTime" label="创建时间"> </el-table-column>
				</el-table>

				<el-empty description="无数据" v-else></el-empty>

				<div style="padding: 5px 0"></div>
				<el-row>
					<el-col :span="24">
						<el-pagination
							background
							layout="prev, pager, next"
							:total="pagination.total"
							v-model:page-size="pagination.size"
							v-model:current-page="pagination.current"
							@change="pageTable"
						/>
					</el-col>
				</el-row>
			</el-card>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<el-space>
					<el-button @click="dialogVisible = false">取消</el-button>
					<el-button type="primary" @click="submitSelectData()"> 确定 </el-button>
				</el-space>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, defineExpose, defineEmits } from "vue";
import { useCool } from "/@/cool";
//import { pageApi } from "/@/modules/flow/api/role";
const { service } = useCool();

const queryData = ref({
	label: "",
	name: ""
	//roleStatus: ""
});

const dialogVisible = ref(false);
const tableData = ref([]);

// 选择的角色
const tableSelect = ref([]);
const handleSelectionChange = rows => {
	tableSelect.value = rows;
};

const pagination = ref({
	size: 10,
	current: 1,
	total: 0
});
const pageTable = () => {
	tableReload();
};
const tableReload = () => {
	const param: any = {};
	Object.assign(param, queryData.value);
	param.page = pagination.value.current;
	param.size = pagination.value.size;
	service.base.sys.role.page(param).then((res: any) => {
		console.log("返回的角色", res);
		pagination.value.total = res.pagination.total;
		tableData.value = res.list;
	});
};

const open = record => {
	tableReload();
	dialogVisible.value = true;
};

const emits = defineEmits(["roleSelectData"]);
const submitSelectData = () => {
	emits("roleSelectData", tableSelect.value);
	dialogVisible.value = false;
};

defineExpose({ open });
</script>

<style scoped></style>
