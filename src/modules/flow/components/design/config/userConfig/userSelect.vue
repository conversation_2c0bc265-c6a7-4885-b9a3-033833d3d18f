<template>
	<el-dialog
		v-model="dialogVisible2"
		:title="title"
		top="20px"
		width="70vw"
		destroy-on-close
		:before-close="handleClose"
	>
		<div style="display: flex; flex-direction: column; gap: 10px">
			<el-card :body-style="{ 'padding-bottom': '0px' }">
				<el-form
					:inline="true"
					:model="formInline"
					class="demo-form-inline"
					label-suffix=":"
				>
					<el-form-item label="用户名" prop="username">
						<el-input v-model="queryData.username" placeholder="用户名" />
					</el-form-item>
					<el-form-item>
						<el-button type="primary" @click="tableReload()" plain>查询</el-button>
					</el-form-item>
				</el-form>
			</el-card>
			<el-row style="gap: 10px; flex-wrap: nowrap">
				<el-card style="flex: 1 1 auto">
					<!-- <el-row class="row-bg" justify="space-between" style="margin-bottom: 16px">
						<span>
							<el-button type="primary" @click="openForm()">新增</el-button>
						</span>
						<span> </span>
					</el-row> -->
					<el-table
						:data="tableData"
						:border="true"
						style="width: 100%; height: calc(240px + 10vh)"
						@selection-change="handleSelectionChange"
						v-if="tableData.length > 0"
					>
						<el-table-column type="selection" width="55">
							<template #default="scope" v-if="mutiSelect === false">
								<el-checkbox
									v-model="scope.row._checked"
									@click="singleSelect(scope.row)"
								/>
							</template>
						</el-table-column>
						<el-table-column prop="username" label="用户名" />
						<el-table-column prop="name" label="昵称" />
					</el-table>

					<el-empty description="无数据" v-else></el-empty>

					<div style="padding: 5px 0"></div>
					<el-row>
						<el-col :span="24">
							<el-pagination
								background
								layout="prev, pager, next"
								:total="pagination.total"
								v-model:page-size="pagination.size"
								v-model:current-page="pagination.current"
								@change="pageTable"
							/>
						</el-col>
					</el-row>
				</el-card>
			</el-row>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<el-space>
					<el-button @click="dialogVisible2 = false">取消</el-button>
					<el-button type="primary" @click="submitSelectData()"> 确定 </el-button>
				</el-space>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { defineEmits, ref, onMounted, defineExpose } from "vue";
import { useCool } from "/@/cool";
import { useStore } from "/@/modules/flow/store";

const { formFlowStore } = useStore();
import { pageApi, deleteApi } from "/@/modules/flow/api/formFlow";
//import { treeApi } from "~/api/sys/dept";
const { service } = useCool();
const queryData = ref({
	username: ""
});
const tableData = ref([]);
const mutiSelect = ref(true);
const dialogVisible2 = ref(false);
const selectIds = ref([]);

const pagination = ref({
	size: 10,
	current: 1,
	total: 0
});

onMounted(async () => {
	// treeApi().then(res => {
	// 	deptList.value = res.rows;
	// });
	//const data = await service.base.comm.getAllUsers();
	tableReload();
});

const tableReload = () => {
	const param: any = {};
	//Object.assign(param, queryData.value);
	param.page = pagination.value.current;
	param.size = pagination.value.size;
	if (queryData.value.username) {
		param.keyWord = queryData.value.username.trim();
	}
	console.log("param", param);
	//param.deptId = currentNodeKey.value;
	pageApi(param).then(res => {
		console.log("页数据res", res);
		pagination.value.total = res.total;
		tableData.value = res.rows;
		if (selectIds.value && selectIds.value.length > 0) {
			const data = tableData.value.filter(item => selectIds.value.includes(item.id));
			data.forEach(item => {
				if (tableSelect.value.find(selectItem => selectItem.id === item.id)) {
					item._checked = true;
				} else {
					const hasData = tableSelect.value.filter(r => r.id == item.id);
					if (hasData.length == 0) tableSelect.value.push(item);
				}
			});

			console.log("tableSelect", data, tableSelect.value);
		}
	});
};
// 办理
// const handleClick = row => {
// 	const data = {
// 		id: row.id,
// 		message: "ok",
// 		skipType: "PASS",
// 		variable: "这是一个未知数"
// 	};
// 	skipApi(data).then(res => {
// 		alert(res.msg);
// 	});
// };
const pageTable = () => {
	tableReload();
};
const changeUserStatus = (value, row) => {
	// if(value == '1'){
	//     enableApi([{id: row.id}]).then(()=> {
	//     }).catch(e=> {
	//         row.roleStatus = '0';
	//     })
	// }else{
	//     disableApi([{id: row.id}]).then(()=> {
	//     }).catch(e=> {
	//         row.roleStatus = '1';
	//     })
	// }
};

const openForm = record => {
	formRef.value.openForm(Object.assign({}, record));
};

// 部门配置
const deptList = ref([]);
const currentNodeKey = ref(null);
const defaultProps = ref({
	label: "name"
});

// 点击部门，查询部门下的人员
const handleNodeClick = curr => {
	if (currentNodeKey.value == curr.id) {
		currentNodeKey.value = null;
	} else {
		currentNodeKey.value = curr.id;
	}
	tableReload();
};

const tableSelect = ref([]);
const handleSelectionChange = (rows, v) => {
	if (mutiSelect.value) {
		//tableSelect.value = [];
		rows.map(item => {
			const hasData = tableSelect.value.filter(r => r.id == item.id);
			if (hasData.length == 0) tableSelect.value.push(item);
		});
	}
};
const singleSelect = row => {
	tableData.value.forEach((element, index, array) => {
		if (element.id !== row.id) {
			element._checked = false;
		}
		// 执行操作
	});
	tableSelect.value[0] = row;
};

const open = (ids?: any) => {
	// if (typeof type === "boolean" || typeof type === Boolean) {
	// 	mutiSelect.value = type;
	// } else {
	mutiSelect.value = true;
	// }
	selectIds.value = ids;
	tableSelect.value = [];
	tableReload();
	dialogVisible2.value = true;
};

const emits = defineEmits(["userSelectData"]);
const submitSelectData = () => {
	console.log("tableSelect", tableSelect.value, mutiSelect.value);
	emits("userSelectData", tableSelect.value, mutiSelect.value);
	dialogVisible2.value = false;
};

defineExpose({ open });
</script>

<style lang="scss" scoped></style>
