<template>
	<el-dialog v-model="dialogVisible" top="10vh" :title="title" width="900px">
		<el-form
			ref="ruleFormRef"
			:model="formData"
			:rules="rules"
			label-width="120px"
			class="flow-design-form"
		>
			<!-- 基本信息部分 -->
			<div class="section-card">
				<div class="section-header">
					<el-icon><document /></el-icon>
					<span class="section-title">基本信息</span>
				</div>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="流程名称" prop="flow_name">
							<el-input v-model="formData.flow_name" placeholder="请输入流程名称">
								<template #prefix>
									<el-icon><edit /></el-icon>
								</template>
							</el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="流程版本" prop="flow_version">
							<el-input
								v-model="formData.flow_version"
								disabled
								placeholder="请输入流程版本"
							>
								<template #prefix>
									<el-icon><info-filled /></el-icon>
								</template>
							</el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</div>

			<!-- 负责人配置部分 -->
			<div class="section-card">
				<div class="section-header">
					<el-icon><user /></el-icon>
					<span class="section-title">负责人配置</span>
				</div>
				<el-form-item label="流程负责人" prop="flow_managers">
					<div class="manager-container">
						<el-button
							type="primary"
							@click="addManager"
							size="small"
							class="add-button"
						>
							<el-icon><plus /></el-icon>添加负责人
						</el-button>
						<div class="tags-wrapper">
							<el-tag
								v-for="item in formData.flow_managers"
								:key="item.id"
								closable
								:effect="'light'"
								class="manager-tag"
								@close="selectClose(item.id)"
							>
								<el-icon><user /></el-icon>
								{{ item.name }}
							</el-tag>
						</div>
					</div>
				</el-form-item>
			</div>

			<div class="section-card">
				<div class="section-header">
					<el-icon><bell /></el-icon>
					<span class="section-title">流程久悬通知</span>
					<el-tooltip content="流程卡住时，多久通知流程发起人" placement="top">
						<el-icon><info-filled /></el-icon>
					</el-tooltip>
				</div>
				<el-form-item label="通知时间配置" prop="flowUnDoneNoticeDay">
					<el-input-number
						v-model="formData.flowUnDoneNoticeDay"
						:min="1"
						:max="999"
						style="width: 200px; margin-bottom: 10px"
					/>
					<span class="unit-text" style="margin-bottom: 4px">天</span>
				</el-form-item>
			</div>

			<!-- 流程描述部分 -->
			<div class="section-card">
				<div class="section-header">
					<el-icon><document /></el-icon>
					<span class="section-title">流程描述</span>
				</div>
				<el-form-item label="流程描述" prop="flow_description">
					<el-input
						v-model="formData.flow_description"
						type="textarea"
						:rows="3"
						placeholder="请输入流程描述"
						show-word-limit
						maxlength="200"
					/>
				</el-form-item>
			</div>
		</el-form>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button
					type="primary"
					v-if="formData.flow_id"
					@click="submitForm(ruleFormRef, '保存')"
				>
					保存
				</el-button>
				<el-button type="primary" @click="submitForm(ruleFormRef, '保存并设计流程')">
					保存并设计流程
				</el-button>
			</div>
		</template>
		<user-select ref="userSelectRef" @userSelectData="userSelectData"></user-select>
		<role-select ref="roleSelectRef" @roleSelectData="roleSelectData"></role-select>
		<form-select ref="formSelectRef" @formSelectData="formSelectData"></form-select>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, toRefs, reactive, defineEmits, defineExpose } from 'vue';
import iconSelect from '../../components/icon/index.vue';
import UserSelect from '/@/modules/flow/components/design/config/userConfig/userSelect.vue';
import RoleSelect from '/@/modules/flow/components/design/config/userConfig/roleSelect.vue';
import FormSelect from '/@/modules/flow/components/design/config/formConfig/formSelect.vue';
import { useCool } from '/@/cool';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useStore } from '../../store';
import {
	Document,
	Edit,
	InfoFilled,
	User,
	Setting,
	Plus,
	UserFilled,
	Avatar,
	List,
	Bell,
	Timer,
	Calendar,
	AlarmClock
} from '@element-plus/icons-vue';
import { cloneDeep } from 'lodash-es';
import { changeRules } from '/@/modules/luru/utils/luruTable';
const { service, router } = useCool();
const { formFlowStore } = useStore();
const iconSelectRef = ref();
const userSelectRef = ref();
const roleSelectRef = ref();
const formSelectRef = ref();

const dialogVisible = ref(false);

const title = ref('新增');
const rowData = ref({});
const fieldList = ref([]);
const userType = ref('流程管理员');
const formData = ref({
	form_id: '',
	flow_managers: [],
	form_name: '',
	flow_id: '',
	flow_name: '',
	flow_version: 1,
	flow_description: '',
	flow_active: true,
	flowJson: null,
	flowUnDoneNoticeDay: 1
});

const ruleFormRef = ref();

// 处理 自定义表单的校验 blur 无效问题
const tips = ref('');

const rules = reactive({
	flow_name: [{ required: true, message: '必填', trigger: 'blur' }],
	flow_managers: [{ required: true, message: '必填', trigger: 'blur' }],
	flowUnDoneNoticeDay: [{ required: true, message: '必填', trigger: 'blur' }]
});

const openForm = async record => {
	//if (record.flow_name) {
	console.log('record', record);
	if (record.flow_name) {
		formData.value = record;
		formData.value.flow_active = record.flow_active ? true : false;
		title.value = '编辑';
		if (record.flow_managers && record.flow_managers.length > 0) {
			formData.value.flow_managers = record.flow_managers;
		} else {
			formData.value.flow_managers = [];
		}
		console.log('编辑', formData.value);
	} else {
		const res = await service.flow.process.unbindFlow({ formId: record.form_id });

		console.log('解绑流程', res);
		resetFormData();
		if (res.length > 0) {
			formData.value.flow_version = Number(res[0].version) + 1;
			console.log('formData.value.flow_version', formData.value.flow_version);
		}
		formData.value.form_name = record.form_name;
		formData.value.form_id = record.form_id;
		console.log('新增', formData.value);
		title.value = '新增';
	}
	let colInfo = [];
	if (record.colInfo) {
		colInfo = record.colInfo;
	} else {
		const res = await service.cloud.db.page({
			page: 1,
			id: formData.value.form_id
		});
		colInfo = res.list[0].colInfo;
	}
	console.log('colInfo', colInfo);
	const colList = changeRules(colInfo);
	// const res = await service.cloud.db.page({
	// 	page: 1,
	// 	id: formData.value.form_id
	// });
	console.log('colList', colList);
	fieldList.value = colList.map(item => {
		return {
			value: item.name,
			label: item.showName
		};
	});
	dialogVisible.value = true;
};
function resetFormData() {
	formData.value = {
		form_id: '',
		flow_managers: [],
		form_name: '',
		flow_id: '',
		flow_name: '',
		flow_version: 1,
		flow_description: '',
		flow_active: true,
		flowJson: null,
		flowUnDoneNoticeDay: 1
	};
}

// 选择角色
const selectRole = () => {
	roleSelectRef.value.open();
};

// 选择用户
const selectUser = () => {
	userType.value = '处理人';
	userSelectRef.value.open();
};

// 选择表单
const selectForm = () => {
	formSelectRef.value.open({ formId: formData.value.form_id });
};

const closeForm = () => {
	dialogVisible.value = false;
	//emits('tableReload');
};
defineExpose({
	openForm,
	closeForm
});

const selectClose = id => {
	formData.value.flow_managers = formData.value.flow_managers.filter(item => item.id !== id);
};

const addManager = () => {
	userType.value = '流程管理员';
	userSelectRef.value.open();
};

const emits = defineEmits(['tableReload']);

const submitForm = async (formEl, type) => {
	console.log('submitForm-type', formData.value);
	if (!formEl) return;

	await formEl.validate((valid, fields) => {
		if (valid) {
			if (!formData.value.flow_version) {
				formData.value.flow_version = 1;
			}
			formFlowStore.addFormFlowData(formData.value);
			dialogVisible.value = false;
			console.log('submit!', formData.value);
			if (type === '保存') {
				console.log('submit!编辑', formData.value);

				service.flow.flow
					.update({
						id: Number(formData.value.flow_id),
						name: formData.value.flow_name,
						managers: formData.value.flow_managers,
						formId: formData.value.form_id,
						version: formData.value.flow_version,
						description: formData.value.flow_description,
						active: true,
						flowUnDoneNoticeDay: formData.value.flowUnDoneNoticeDay
					})
					.then(res => {
						dialogVisible.value = false;
						emits('tableReload');
						ElMessage.success('保存成功', res);

						console.log(res);
					})
					.catch(e => {
						console.log(e);

						ElMessage.error(e.message);
						dialogVisible.value = false;
						//ElMessage.error("保存失败", e);
					});
			} else {
				console.log('保存设计formData.value', formData.value);
				if (formData.value.flow_id) {
					// ElMessageBox.confirm(
					// 	'重新设计流程，会导致当前正在审批的流程终止，确定要重新设计流程吗？',
					// 	'提示',
					// 	{
					// 		confirmButtonText: '确定',
					// 		cancelButtonText: '取消',
					// 		type: 'warning'
					// 	}
					// ).then(() => {
					//console.lo;
					dialogVisible.value = false;
					setTimeout(() => {
						router.push({
							path: '/flowDesign',
							state: {
								flow_id: formData.value.flow_id,
								flow_managers: JSON.stringify(formData.value.flow_managers),
								flow_name: formData.value.flow_name,
								flow_version: formData.value.flow_version,
								flow_description: formData.value.flow_description,
								flow_active: true,
								form_id: formData.value.form_id,
								flowUnDoneNoticeDay: formData.value.flowUnDoneNoticeDay
								//	flowJson: formData.value.flowJson
							}
						});
					}, 200);
					// });
				} else {
					dialogVisible.value = false;
					console.log('保存设计并关闭', dialogVisible.value, formData.value);
					setTimeout(() => {
						router.push({
							path: '/flowDesign',
							state: {
								flow_id: formData.value.flow_id,
								flow_managers: JSON.stringify(formData.value.flow_managers),
								flow_name: formData.value.flow_name,
								flow_version: formData.value.flow_version,
								flow_description: formData.value.flow_description,
								flow_active: true,
								form_id: formData.value.form_id,
								flowUnDoneNoticeDay: formData.value.flowUnDoneNoticeDay
								//	flowJson: formData.value.flowJson
							}
						});
					}, 200);
				}
			}
		} else {
			console.log('error submit!', fields);
		}
	});
};
</script>

<style scoped>
.flow-design-form {
	padding: 4px;
}

.section-card {
	background-color: var(--el-bg-color);
	border-radius: 8px;
	padding: 4px;
	margin-bottom: 8px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.section-header {
	display: flex;
	align-items: center;
	margin-bottom: 16px;
	padding-bottom: 8px;
	border-bottom: 1px solid var(--el-border-color-lighter);
}

.section-header .el-icon {
	margin-right: 6px;
	font-size: 16px;
	color: var(--el-color-primary);
}

.section-title {
	font-size: 15px;
	font-weight: 600;
	color: var(--el-text-color-primary);
}

.manager-container {
	background-color: var(--el-fill-color-lighter);
	border-radius: 6px;
	padding: 12px;
	display: flex;
	align-items: baseline;
}
.field-config-section,
.urge-config-section {
	background-color: var(--el-fill-color-lighter);
	border-radius: 6px;
	padding: 6px;
}

.add-button {
	margin-bottom: 12px;
}

.field-config-card {
	background-color: white;
	border-radius: 6px;
	padding: 12px;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
}

.field-select {
	width: 100%;
}

.source-options {
	display: flex;
	gap: 12px;
	flex-wrap: wrap;
}

.source-item {
	flex: 1;
	min-width: 200px;
	border: 1px solid var(--el-border-color-lighter);
	border-radius: 4px;
	padding: 8px;
	background-color: var(--el-fill-color-lighter);
}

.source-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 6px;
}

.source-header .el-checkbox {
	display: flex;
	align-items: center;
}

.source-header .el-checkbox .el-icon {
	margin-right: 4px;
}

.tags-wrapper {
	display: flex;
	flex-wrap: wrap;
	gap: 4px;
	margin-top: 6px;
}

.manager-tag {
	display: flex;
	align-items: center;
	gap: 4px;
	margin: 2px;
}

.urge-config-section {
	display: flex;
	align-items: center;
	gap: 12px;
	flex-wrap: wrap;
}

.urge-select {
	width: 200px;
}

.urge-details {
	display: flex;
	align-items: center;
	gap: 8px;
	flex-wrap: wrap;
}

.period-select,
.day-select {
	width: 100px;
}

.unit-text {
	margin-left: 4px;
	color: var(--el-text-color-regular);
}

.dialog-footer {
	padding-top: 16px;
	text-align: right;
	border-top: 1px solid var(--el-border-color-lighter);
}

:deep(.el-dialog__body) {
	padding: 0;
}

:deep(.el-form-item__label) {
	font-weight: 500;
}

:deep(.el-form-item) {
	margin-bottom: 12px;
}

:deep(.el-select-dropdown__item) {
	display: flex;
	align-items: center;
}

:deep(.el-checkbox__label) {
	display: flex;
	align-items: center;
}

:deep(.el-tag) {
	display: flex;
	align-items: center;
	gap: 4px;
	margin: 2px;
}

:deep(.el-input__prefix) {
	display: flex;
	align-items: center;
}

:deep(.el-form-item:last-child) {
	margin-bottom: 0;
}

:deep(.el-dialog__header) {
	padding: 16px;
	margin-right: 0;
}

:deep(.el-dialog__footer) {
	padding: 12px 16px;
}
</style>
