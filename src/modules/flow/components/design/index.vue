<template>
	<flow-index :flowData="data.flowJson" :readonly="readonly" @nodeClick="nodeClick"></flow-index>
	<setting ref="settingRef"></setting>
	<formFlowDrawer :key="Date.now()" ref="formFlowDrawerRef"></formFlowDrawer>
</template>

<script setup lang="ts">
import { defineProps, ref, toRefs, watch } from "vue";
import FlowIndex from "./flowIndex.vue";
import setting from "./setting.vue";
import { backNodeList } from "/@/modules/flow/utils/flow";
import formFlowDrawer from "/@/modules/flow/components/formFlow/formFlowDrawer.vue";
const props = defineProps({
	data: Object,
	readonly: {
		type: Boolean,
		default: false
	},
	formColumns: {
		type: Array,
		default: () => []
	},
	isForm: {
		type: Boolean,
		default: false
	},
	formId: {
		type: String,
		default: ""
	}
});
const settingRef = ref();
const formFlowDrawerRef = ref();
const currNodeId = ref(null);
const currNode = ref();
const { data, readonly } = toRefs(props);
const backNodes = ref([]);

watch(
	() => data.value.flowJson,
	newV => {
		if (newV) {
			backNodes.value = backNodeList(newV);
		}
	},
	{
		immediate: true,
		deep: true
	}
);
//节点点击事件
const nodeClick = node => {
	node.properties = node.properties || {};
	node.properties.formColumns = props.formColumns;
	if (readonly.value) {
		return;
	}
	console.log("nodeClick", node, " backNodes.value", backNodes.value);
	currNodeId.value = node.nodeId;
	currNode.value = node; // getNode(flowData.value, nodeId);
	//打开配置项
	console.log("props.isForm", data?.value, data?.value?.formId);
	if (props.isForm) {
		formFlowDrawerRef.value.openHandler(node, props.formId);
	} else {
		settingRef.value.openHandler(node, backNodes.value);
	}
};
</script>

<style lang="scss" scoped></style>
