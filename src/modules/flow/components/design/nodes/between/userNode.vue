<!-- 一个节点  包含 面板 + 连线 -->
<template>
	<div style="display: flex">
		<node
			v-bind="$attrs"
			defaultValue="请设置"
			:backgroundImage="backgroundImage"
			:class="getCheckStatus($attrs)"
		>
		</node>
		<!-- 		<el-popover
			:width="300"
			trigger="hover"
			:show-arrow="true"
			:hide-after="0"
			placement="left-start"
			v-if="$attrs.readonly"
		>
			<template #reference>
				<node
					v-bind="$attrs"
					defaultValue="请设置"
					:class="showClass"
					:backgroundImage="backgroundImage"
				>
				</node>
			</template>

			<div class="notice-preview">
				<h3 class="preview-title">
					{{ $attrs.name }}
				</h3>
				<div class="preview-info">
					<span class="preview-time">{{ $attrs.name }}</span>
				</div>
			</div>
		</el-popover> -->

		<!-- <span style="width: 60px" v-show="$attrs?.checkStatus" :class="getCheckStatus($attrs)">{{
			$attrs?.checkStatus
		}}</span> -->
	</div>
</template>

<script setup lang="ts">
import { defineProps, ref, toRefs, watch } from "vue";
import Node from "../node.vue";

const props = defineProps({
	// nodeData:{},
	bodyClass: {
		type: String,
		default: ""
	}
});
// const {nodeData} = toRefs(props);
const backgroundImage = ref("linear-gradient(to right, #a5a5a5 0%,  #dadada  100%)");
const warnClass = ref("");
const getCheckStatus = (node: any) => {
	if (node && node.checkStatus) {
		if (node.checkStatus == "已审核") {
			//statusClass.value = "flow-status-completed";
			backgroundImage.value = "linear-gradient(to right, #67b26f, #45a04b)";
			return "checkedNode1";
		} else {
			backgroundImage.value = "linear-gradient(to right, #fbbd48, #f89e28)";
			return "checkingNode1";
		}
	}
	return false;
};
</script>

<style scoped>
.flow-single-node {
	padding-left: var(--golbal-flow-node-padding);
	padding-right: var(--golbal-flow-node-padding);
	position: relative;
	justify-content: center;
	margin-top: 15px;
	display: grid;
	z-index: 1;
	background-color: var(--global-flow-background-color);
}

.flow-single-node::before {
	content: "";
	position: absolute;
	top: -16px;
	left: 50%;
	-webkit-transform: translateX(-50%);
	transform: translate(-50%);
	width: 0;
	height: 4px;
	border-style: solid;
	border-width: 12px 6px 0px;
	border-color: var(--global-flow-line-color) transparent transparent;
	background: var(--global-flow-background-color);
}
/* 已审核状态 */
/* .checkedNode {
	padding: 5px;
	background: linear-gradient(145deg, #67b26f, #45a04b);
	border-radius: 10px;
	margin-bottom: 8px;
	color: white;
	font-size: 14px;
	font-weight: 600;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}
.checkedNode:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.checkingNode {
	padding: 5px;
	width: 60px;
	height: 36px;
	border-radius: 10px;
	background: linear-gradient(145deg, #fbbd48, #f89e28); 
	color: white;
	font-size: 14px;
	font-weight: 600;
	margin-bottom: 8px;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
} */
.checkingNode:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}
.notice-preview {
	padding: 8px;

	.preview-title {
		margin: 0 0 12px;
		padding-bottom: 10px;
		font-size: 16px;
		font-weight: bold;
		color: #303133;
		border-bottom: 1px solid #ebeef5;
		position: relative;
	}

	.preview-info {
		display: flex;
		align-items: center;
		gap: 10px;
		margin-bottom: 16px;
		padding: 0 4px;

		.preview-time {
			color: #909399;
			font-size: 13px;
		}
	}

	.preview-content {
		padding: 12px;
		font-size: 14px;
		line-height: 1.6;
		color: #606266;
		max-height: 300px;
		overflow-y: auto;
		background-color: #f8f9fa;
		border-radius: 4px;

		:deep(p) {
			margin: 8px 0;
		}

		:deep(img) {
			max-width: 100%;
			height: auto;
			border-radius: 4px;
			margin: 8px 0;
		}

		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-thumb {
			background-color: #dcdfe6;
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background-color: #f0f2f5;
			border-radius: 3px;
		}
	}
}
</style>
