<template>
	<start-node v-bind="$attrs" v-if="$attrs.nodeType === 'start'"></start-node>
	<between-box v-bind="$attrs" v-else-if="$attrs.nodeType === 'between'"></between-box>
	<serial-box v-bind="$attrs" v-else-if="$attrs.nodeType === 'serial'"></serial-box>
	<serial-node v-bind="$attrs" v-else-if="$attrs.nodeType === 'serial-node'"></serial-node>
	<parallel-box v-bind="$attrs" v-else-if="$attrs.nodeType === 'parallel'"></parallel-box>
	<end-flow v-else-if="$attrs.nodeType === 'end'"></end-flow>
	<div v-else>不存在的类别{{ $attrs.nodeType }}</div>
	<drender
		v-if="childNode"
		v-model="props.childNode"
		v-bind="{ ...$attrs, ...childNode }"
	></drender>
</template>

<script setup lang="ts">
import StartNode from "./startNode.vue";
import EndFlow from "./endNode.vue";
import SerialBox from "./serial/serialBox.vue";
import SerialNode from "./serial/serialNode.vue";
import BetweenBox from "./between/betweenBox.vue";
import ParallelBox from "./parallel/parallelBox.vue";
import Drender from "./render.vue";
import { defineProps, ref } from "vue";

const props = defineProps({
	childNode: []
});
</script>

<style lang="scss" scoped></style>
