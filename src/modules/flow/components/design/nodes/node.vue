<template>
	<div class="flw-node-single">
		<span v-show="$attrs?.checkStatus" :class="getCheckStatus($attrs)">{{
			$attrs?.checkStatus
		}}</span>
		<panelView
			:class="(panelClass + ' ' + statusClass, { 'form-custom-node': hasFormCustom($attrs) })"
			v-bind="$attrs"
		>
		</panelView>
		<lineView v-bind="$attrs"></lineView>
	</div>
</template>

<script setup lang="ts">
import panelView from './panel.vue';
import lineView from './line.vue';
import { defineProps, watch, ref, useAttrs } from 'vue';
import { updateCheckStatus } from '../../../utils/flow';
const props = defineProps({
	panelClass: {
		type: String,
		default: ''
	},
	//节点状态
	nodeStatus: {
		type: Array
	}
});
const statusClass = ref('');

watch(
	() => props.nodeStatus,
	(newv, oldv) => {
		if (newv) {
			if (newv.includes('1')) {
				statusClass.value = 'flow-status-progress';
			} else if (newv.includes('2')) {
				statusClass.value = 'flow-status-completed';
			} else if (newv.includes('9')) {
				statusClass.value = 'flow-status-back';
			} else if (newv.includes('99')) {
				statusClass.value = 'flow-status-reject';
			} else {
				statusClass.value = '';
			}
		}
	},
	{ immediate: true, deep: true }
);
const getCheckStatus = (node: any) => {
	if (node && node.checkStatus) {
		if (node.checkStatus == '已审核') {
			//	statusClass.value = "flow-status-completed";
			return 'checkedNode';
		} else if (node.checkStatus == '审核中') {
			return 'checkingNode';
		} else if (node.nodeType == 'start') {
			return 'checkedNode';
		}
	}
	return 'unCheckedNode';
};
const hasFormCustom = (node: any) => {
	if (
		node &&
		node.properties?.combination?.form?.length > 0 &&
		!node.properties?.combination?.form[0].name.formId
	) {
		console.log('找到了', node.nodeName);

		return true;
	}
	return false;
};
</script>

<style scoped>
.flow-status-back {
	border: 2px solid RGB(255, 105, 164);
	padding: 1px;
}
.flow-status-reject {
	border: 2px solid #f25643;
	padding: 1px;
}
.flow-status-completed {
	border: 2px solid #67b26f;
	padding: 1px;
}
.flow-status-progress {
	border: 2px solid #315efb;
	padding: 1px;
}

/* 已审核状态 */
.checkedNode {
	padding: 10px;
	background: linear-gradient(145deg, #67b26f, #45a04b);
	border-radius: 10px;
	margin-bottom: 8px;
	color: white;
	font-size: 14px;
	font-weight: 600;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}
.checkedNode:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}
.unCheckedNode {
	padding: 10px;
	background: linear-gradient(145deg, #dadada, #dadada);
	border-radius: 10px;
	margin-bottom: 8px;
	color: white;
	font-size: 14px;
	font-weight: 600;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}
.checkingNode {
	padding: 10px;
	border-radius: 10px;
	background: linear-gradient(145deg, #fbbd48, #f89e28); /* 渐变色 */
	color: white;
	font-size: 14px;
	font-weight: 600;
	margin-bottom: 8px;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}
.checkingNode:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.flw-node-single {
	padding-left: var(--golbal-flow-node-padding);
	padding-right: var(--golbal-flow-node-padding);
	z-index: 1;
}

/* 表单自定义提示 */
/* .form-custom-node::after {
	content: "表单自定义";
	position: absolute;
	top: -20px;
	right: -10px;
	background-color: rgb(188, 8, 8);
	color: white;
	padding: 2px 8px;
	border-radius: 10px;
	font-size: 12px;
	transform: scale(0.8);
} */
</style>
