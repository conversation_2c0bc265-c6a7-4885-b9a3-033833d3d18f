<!-- 分支选择盒子 （由分支选择组组成）-->
<template>
	<!-- 最外层框 -->
	<div class="flow-serial-box" :style="{ 'margin-top': $attrs.readonly ? '0' : '10px' }">
		<parallel-group v-bind="$attrs"></parallel-group>
		<div class="flow-serial-box-footer">
			<line-view v-bind="$attrs"></line-view>
		</div>
	</div>
</template>

<script setup lang="ts">
import { defineProps, toRefs, useAttrs } from "vue";
import LineView from "../line.vue";
import ParallelGroup from "./parallelGroup.vue";
const attrs = useAttrs();
const props = defineProps({});
</script>

<style scoped>
.flow-serial-box {
	width: 100%;
	/* margin-top: 10px; */
	display: flex;
	flex-direction: column;
	min-width: 0;
	flex-shrink: 0;
	/* align-content: center; */
	/* flex-wrap: nowrap; */
	/* align-items: center; */
	position: relative;
}
.flow-serial-box-wrap {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	align-content: center;
	justify-content: center;
}
.flow-serial-box-footer {
	display: flex;
	width: 100%;
	flex-wrap: nowrap;
	justify-content: center;
	background-color: var(--global-flow-background-color);
}
</style>
