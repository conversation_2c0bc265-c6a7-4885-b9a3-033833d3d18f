<template>
	<startNode v-bind="$attrs" v-if="$attrs.nodeType === 'start'"></startNode>
	<between-box v-bind="$attrs" v-else-if="$attrs.nodeType === 'between'"></between-box>
	<serial-box v-bind="$attrs" v-else-if="$attrs.nodeType === 'serial'"></serial-box>
	<serial-node
		v-bind="$attrs"
		v-else-if="$attrs.nodeType === 'serial-node'"
		@mouseenter="showTooltip"
		@mouseleave="hideTooltip"
	></serial-node>
	<parallel-box v-bind="$attrs" v-else-if="$attrs.nodeType === 'parallel'"></parallel-box>
	<endFlow v-else-if="$attrs.nodeType === 'end'"></endFlow>

	<div v-else>不存在的类别{{ $attrs.nodeType }}</div>
	<render
		v-if="childNode"
		v-model="props.childNode"
		v-bind="{ ...childNode }"
		@addNodes="$attrs.onAddNodes"
		@nodeClick="$attrs.onNodeClick"
		@removeNodes="$attrs.onRemoveNodes"
		:readonly="$attrs.readonly"
	></render>
</template>

<script setup lang="ts">
import { ref } from "vue";
import startNode from "./startNode.vue";
import endFlow from "./endNode.vue";
import serialBox from "./serial/serialBox.vue";
import serialNode from "./serial/serialNode.vue";
import betweenBox from "./between/betweenBox.vue";
import parallelBox from "./parallel/parallelBox.vue";
import render from "./render.vue";
import { defineProps } from "vue";

const props = defineProps({
	childNode: {},
	reviewers: {
		type: Array,
		default: () => []
	}
});

const showTooltipFlag = ref(false);
const tooltipStyle = ref({
	left: "0px",
	top: "0px"
});

const showTooltip = (event: MouseEvent) => {
	showTooltipFlag.value = true;
	tooltipStyle.value = {
		left: `${event.clientX + 10}px`,
		top: `${event.clientY + 10}px`
	};
};

const hideTooltip = () => {
	showTooltipFlag.value = false;
};
</script>

<style>
.flow-success {
	border-color: 1px solid #67c23a !important;
}
.flow-render {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100%;
}

.node-tooltip {
	position: fixed;
	background: white;
	border: 1px solid #dcdfe6;
	padding: 10px;
	border-radius: 4px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	z-index: 9999;
}

.tooltip-title {
	font-weight: bold;
	margin-bottom: 5px;
}
</style>
