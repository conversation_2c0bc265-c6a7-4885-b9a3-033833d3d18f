<!-- 一个节点  包含 面板 + 连线 -->
<template>
	<div class="flow-start-node">
		<node v-bind="$attrs" :backgroundImage="backgroundImage" :enableDestory="false"> </node>
	</div>
</template>

<script setup lang="ts">
import { defineProps, ref, useAttrs } from "vue";
import Node from "./node.vue";
const attrs = useAttrs();
defineProps({
	bodyClass: {
		type: String,
		default: ""
	}
});

const backgroundImage = ref("linear-gradient(to right, #67b26f, #45a04b)");
</script>

<style scoped>
.flow-start-node {
	margin-top: 35px;
	justify-content: center;
	display: grid;
}
</style>
