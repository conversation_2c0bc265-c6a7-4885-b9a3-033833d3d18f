<template>
	<el-dialog
		v-model="dialogVisible"
		title="流程详情"
		:fullscreen="isMobile"
		:width="isMobile ? '100%' : '80%'"
		@close="hanldeClose"
		:destroy-on-close="true"
		:class="{ 'mobile-dialog': isMobile }"
	>
		<el-tabs
			:tab-position="isMobile ? 'top' : 'left'"
			v-model="tabsModel"
			class="flow-tabs"
			:class="{ 'mobile-tabs': isMobile }"
		>
			<el-tab-pane label="表单" name="form" class="tab-content">
				<form-show
					:data="currFormJson"
					:formOptions="formOptions"
					:formColInfo="formColInfo"
					:isTodo="isTodo"
					:buttons="buttons"
					@buttonClick="handleButtonClick"
				></form-show>
			</el-tab-pane>
			<el-tab-pane label="流程" name="flow" class="tab-content flow-content">
				<flow-index :readonly="true" :flowData="currFlowData"></flow-index>
			</el-tab-pane>
			<el-tab-pane label="详情" name="detail" class="tab-content">
				<el-button type="primary" @click="getHisList" class="refresh-btn">刷新</el-button>
				<el-table
					:data="hisList"
					border
					style="width: 100%"
					:class="{ 'mobile-table': isMobile }"
				>
					<el-table-column prop="currentNodeName" label="节点名称" />
					<el-table-column prop="approvers" label="审批人" />
					<el-table-column label="操作类型" prop="result" />
					<el-table-column prop="targetUsers" label="操作对象" />
					<el-table-column prop="comment" label="审批意见" />
					<el-table-column prop="nodeRatioType" label="节点类型" width="90px" />
					<el-table-column prop="nodeRatio" label="节点比例" width="90px" />
					<el-table-column prop="createTime" label="完成时间" />
				</el-table>
			</el-tab-pane>
		</el-tabs>
	</el-dialog>
	<user-select ref="userSelectRef" @userSelectData="userSelectData"></user-select>
	<!-- 审批操作弹窗 -->
	<el-dialog
		v-model="btnDialogVisible"
		:title="getDialogTitle"
		width="500px"
		destroy-on-close
		draggable
	>
		<div class="approval-dialog">
			<!-- 加签/转办用户选择 -->
			<template v-if="['signAdd', 'signRedu', 'transfer'].includes(btnType)">
				<div class="user-select-section">
					<el-form ref="formRef" :model="formData" label-width="80px">
						<!-- 加签类型选择 -->
						<!-- <el-form-item v-if="btnType === 'signAdd'" label="加签类型">
							<el-radio-group v-model="formData.signType">
								<el-radio label="before">前加签</el-radio>
								<el-radio label="after">后加签</el-radio>
								<el-radio label="parallel">并行加签</el-radio>
							</el-radio-group>
						</el-form-item> -->

						<!-- 用户选择 -->
						<el-form-item
							v-if="btnType != 'signRedu'"
							:label="currentAction === 'transfer' ? '转办给' : '选择用户'"
							required
						>
							<div class="user-select">
								<el-button type="primary" plain @click="addUser" size="small"
									>选择用户</el-button
								>
								<div v-if="selectedUsers.length > 0" style="margin-left: 10px">
									<el-tag
										closable
										@close="selectClose(item.id)"
										v-for="item in selectedUsers"
										:key="item.id"
									>
										{{ item.name }}
									</el-tag>
								</div>
							</div>
						</el-form-item>

						<el-form-item v-if="btnType === 'signRedu'" label="选择用户">
							<el-select
								v-model="formData.selectedUsers"
								filterable
								remote
								multiple
								:loading="loading"
								placeholder="请选择用户"
							>
								<el-option
									v-for="item in userOptions"
									:key="item.id"
									:label="item.name"
									:value="item.id"
								>
									<div class="user-option">
										<span class="user-info">
											<span class="user-name">{{ item.name }}</span>
										</span>
									</div>
								</el-option>
							</el-select>
						</el-form-item>
					</el-form>
				</div>
			</template>

			<!-- 回退节点选择 -->
			<template v-if="btnType === 'back'">
				<div class="back-node-section">
					<el-form ref="formRef" :model="formData" label-width="80px">
						<el-form-item label="回退至" required>
							<el-select v-model="formData.targetNode" placeholder="请选择回退节点">
								<el-option
									v-for="node in backNodes"
									:key="node.nodeId"
									:label="node.nodeName"
									:value="node.nodeId"
								>
									<div class="node-option">
										<el-icon><position /></el-icon>
										<span>{{ node.nodeName }}</span>
									</div>
								</el-option>
							</el-select>
						</el-form-item>
					</el-form>
				</div>
			</template>

			<!-- 审批意见 -->
			<div class="comment-section">
				<el-form-item label="审批意见">
					<el-input
						v-model="formData.comment"
						type="textarea"
						:rows="3"
						placeholder="请输入审批意见"
						show-word-limit
						maxlength="500"
					/>
				</el-form-item>
			</div>
		</div>

		<!-- 弹窗底部按钮 -->
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="btnDialogVisible = false">取消</el-button>
				<el-button type="primary" @click="handlerSubmit" :loading="submitting">
					确认
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="flowDetail">
import FlowIndex from '../components/design/flowIndex.vue';
import formShow from './formShow.vue';
import { useCool } from '/@/cool';
import { useBase } from '/$/base';
//import { hisListApi } from "~/api/flow/flowTask.js";
import { updateCheckStatus, findNodeById, buildChildNodeChain } from '../utils/flow';
import { onMounted, defineEmits, ref, defineExpose, computed, toRefs } from 'vue';
// import { openCustomForm } from "./common.js";
import { getFlowColor, getFlowStatus } from '../utils/flowInfo';
import { ElMessage } from 'element-plus';
import userSelect from '/@/modules/flow/components/design/config/userConfig/userSelect.vue';
import { Position } from '@element-plus/icons-vue';

const { service, router } = useCool();
const { user } = useBase();
const flowParam = ref();
const dialogVisible = ref(false);
const btnDialogVisible = ref(false);
const buttons = ref([]);
const btnType = ref('');
// onMounted(()=>{
//     openApprovalForm(hisTask.value);
// })
// 当前的流程定义id
const currentDefId = ref();
const currFormJson = ref({});
const currFlowData = ref([]);
const variable = ref({});
const hisList: any = ref([]);
const comment: any = ref('');
const userSelectRef = ref();
const selectedUsers = ref([]);
// 表单组件判断
const formComponent = ref(null);
// 动态组件ref
const dynFormRef = ref(null);

const tabsModel = ref('form');

const currentAction = ref('');
const loading = ref(false);
const submitting = ref(false);
const instanceId = ref('');
const taskId = ref('');
const isTodo = ref(false);
// 模拟回退节点数据
const backNodes = ref([
	// ... 更多节点数据
]);

const formOptions = ref({});
const formColInfo = ref([]);

const btnMap = {
	aggren: '同意',
	approve: '同意',
	reject: '驳回',
	back: '回退',
	addSign: '加签',
	signAdd: '加签',
	signRedu: '减签',
	transfer: '转办',
	start: '开始',
	removeSign: '减签',
	rollback: '回退'
};
const newFlowJson = ref([]);
const formData = ref({
	signType: 'before',
	selectedUsers: [],
	targetNode: '',
	comment: btnMap[btnType.value]
});
const nodeRatioType = {
	1: '或签',
	2: '票签',
	3: '会签',
	start: '开始'
};
// 减签用户数据
const userOptions = ref([]);
const open = async (params, info) => {
	tabsModel.value = 'form';
	//openApprovalForm(params);
	if (info == '办理') isTodo.value = true;
	btnDialogVisible.value = false;
	btnType.value = '';
	if (params.taskId) taskId.value = params.taskId;
	const res = await service.flow.process.predict({ instanceId: params.instanceId });
	console.log('predict返回内容', res);
	newFlowJson.value = buildChildNodeChain(res);
	console.log('newFlowJson.value', newFlowJson.value);
	console.log('params', params);
	instanceId.value = params.instanceId;
	await getHisList();
	dialogVisible.value = true;
};
const selectClose = id => {
	selectedUsers.value = selectedUsers.value.filter(item => item.id !== id);
};
async function getHisList() {
	hisList.value = [];
	const res = await service.flow.flowInstance.list({
		id: instanceId.value
	});
	console.log('res', res);

	if (res && res.length) {
		res[0].flowJson = newFlowJson.value;
		flowParam.value = res[0];
		variable.value = res[0].formData;
		currFlowData.value =
			typeof res[0].flowJson === 'string' ? JSON.parse(res[0].flowJson) : res[0].flowJson;
		console.log('currFlowData.value', currFlowData.value);
		if (res[0].formData) {
			const formRes = await service.cloud.db.list({ id: res[0].formId });
			console.log('formRes', formRes);
			formColInfo.value = formRes[0].colInfo;
			formOptions.value = formRes[0].tableInfo;
			currFormJson.value = res[0].formData;
		}
		console.log(' params.nodeId', res[0].currentNodeId);
		const currentNode = findNodeById(currFlowData.value, res[0].currentNodeId);
		console.log('currentNode', currentNode);
		buttons.value = currentNode?.properties?.buttons?.filter(item => item.checked) || [];
		console.log('buttons', buttons.value);
		const history = res[0].approvalHistory;
		console.log('history', history);
		userOptions.value = [];
		res[0].currentApprovers.map((item: any) => {
			userOptions.value.push(...item.approvers);
		});
		userOptions.value = userOptions.value.filter(item => item.name != user.info?.username);
		hisList.value = [];
		console.log('currentApprovers', userOptions.value);
		//const userOptions = currentApprovzrs;
		console.log('item', res[0]);
		history.map(e => {
			console.log('currFlowData.value,', currFlowData.value, e, e.nodeId);
			const nodeData = findNodeById(currFlowData.value, e.nodeId);
			console.log('节点nodeData', nodeData, e);
			hisList.value.push({
				currentNodeId: e.nodeId,
				currentNodeName: nodeData.nodeName,
				result: btnMap[e.action],
				nodeRatioType: nodeRatioType[nodeData.properties.nodeRatioType],
				nodeRatio:
					nodeData.properties.nodeRatioType == 2
						? nodeData.properties.nodeRatio + '%'
						: '',
				//status: "completed",
				comment: e.comment,
				approvers: e.userName,
				createTime: e.createTime,
				targetUsers: e.targetUsers?.map(item => item.name)
			});
		});

		console.log('修改节点状态', currFlowData.value);
	}
	console.log('hisList.value', hisList.value);
	if (isTodo.value) {
		currFlowData.value = updateCheckStatus(
			currFlowData.value,
			hisList.value,
			flowParam.value.currentNodeId
		);
	} else {
		currFlowData.value = updateCheckStatus(
			currFlowData.value,
			hisList.value,
			flowParam.value.currentNodeId,
			'查看'
		);
	}
}
const addUser = () => {
	userSelectRef.value.open();
};

// function findNodeById(node, targetNodeId) {
// 	// 如果当前节点的 nodeId 匹配目标 nodeId，返回当前节点
// 	if (node.nodeId === targetNodeId) {
// 		return node;
// 	}

// 	// 递归检查 childNode
// 	if (node.childNode) {
// 		const result = findNodeById(node.childNode, targetNodeId);
// 		if (result) {
// 			return result;
// 		}
// 	}

// 	// 递归检查 conditionNodes（如果有）
// 	if (node.conditionNodes) {
// 		for (const conditionNode of node.conditionNodes) {
// 			const result = findNodeById(conditionNode, targetNodeId);
// 			if (result) {
// 				return result;
// 			}
// 		}
// 	}

// 	// 如果没有找到匹配的节点，返回 null
// 	return null;
// }

const userSelectData = data => {
	if (btnType.value == 'transfer' && data.length > 1) {
		ElMessage.info('转办只能选择一位用户');
		selectedUsers.value = data[0];
	} else {
		selectedUsers.value = data;
	}

	console.log('userSelectData', data, selectedUsers.value);
};

async function handlerSubmit() {
	console.log('comment', comment.value);
	btnDialogVisible.value = false;
	try {
		switch (btnType.value) {
			case 'aggren':
				console.log('同意', flowParam.value);

				await service.flow.process.approve({
					// instanceId: flowParam.value.id,
					// nodeId: flowParam.value.nodeId,
					taskId: taskId.value,
					comment: formData.value.comment ? formData.value.comment : btnMap[btnType.value]
				});

				ElMessage.success('审批成功');
				hanldeClose();
				break;
			case 'reject':
				console.log('驳回');
				await service.flow.process.reject({
					// instanceId: flowParam.value.id,
					// nodeId: flowParam.value.nodeId,
					taskId: taskId.value,
					comment: formData.value.comment ? formData.value.comment : btnMap[btnType.value]
				});
				ElMessage.success('驳回成功');
				hanldeClose();
				break;
			case 'back':
				await service.flow.process.rollback({
					taskId: taskId.value,
					targetNodeId: formData.value.targetNode,
					comment: formData.value.comment ? formData.value.comment : btnMap[btnType.value]
				});
				ElMessage.success('退回成功');
				hanldeClose();
				break;
			case 'transfer':
				console.log('转交', selectedUsers, flowParam.value);
				if (selectedUsers.value[0]) {
					await service.flow.process.transfer({
						taskId: taskId.value,
						toUser: selectedUsers.value[0].id,
						comment: formData.value.comment
							? formData.value.comment
							: btnMap[btnType.value]
					});
					hanldeClose();
				} else {
					ElMessage.error('请选择转交人');
				}
				ElMessage.success('转交成功');
				break;
			case 'signAdd':
				console.log('formdataooo', formData.value, selectedUsers.value);
				const otherUsersId = selectedUsers.value.map(item => item.id);
				await service.flow.process.addSign({
					taskId: taskId.value,
					userIds: otherUsersId,
					comment: formData.value.comment ? formData.value.comment : btnMap[btnType.value]
				});
				hanldeClose();
				//	userSelectRef.value.open();
				console.log('加签');
				break;
			case 'signRedu':
				console.log('formdataooo', formData.value, selectedUsers.value);
				await service.flow.process.removeSign({
					taskId: taskId.value,
					userIds: formData.value.selectedUsers,
					comment: formData.value.comment ? formData.value.comment : btnMap[btnType.value]
				});
				hanldeClose();
				console.log('减签');
				break;
			default:
				ElMessage.error('审批失败');
				break;
		}
	} catch (err) {
		ElMessage.error('审批失败:' + JSON.stringify(err.message));
	}
}

const handleButtonClick = async btn => {
	btnType.value = btn.type;
	switch (btn.type) {
		case 'back':
			const res = await service.flow.process.rollbackNodes({
				taskId: taskId.value
			});
			backNodes.value = res;
			console.log('退回res', res);
			formData.value.comment = btnMap[btnType.value];
			btnDialogVisible.value = true;
			selectedUsers.value = [];
			break;
		// case "signRedu":
		// 	const instanceRes = await service.flow.flowInstance.page({
		// 		id: instanceId.value
		// 	});
		// 	userOptions.value = [];
		// 	instanceRes.list[0].currentApprovers.map((item: any) => {
		// 		if (item.approvers.name != user.info?.username) {
		// 			userOptions.value.push(...item.approvers);
		// 		}
		// 	});
		// 	formData.value.selectedUsers = [];
		// 	btnDialogVisible.value = true;
		// 	break;

		default:
			formData.value.comment = btnMap[btnType.value];
			formData.value.selectedUsers = '';
			selectedUsers.value = [];
			btnDialogVisible.value = true;
			break;
	}
};

// const openApprovalForm = async def => {
// 	formComponent.value = openCustomForm(def);
// };
const emits = defineEmits(['close']);

const hanldeClose = () => {
	dialogVisible.value = false;
	emits('close');
};

// 弹窗标题
const getDialogTitle = computed(() => {
	const titleMap = {
		signAdd: '加签',
		signRedu: '减签',
		transfer: '转办',
		back: '回退'
	};
	return titleMap[currentAction.value] || '审批操作';
});

defineExpose({ open });

// 优化移动端检测逻辑
const isMobile = computed(() => {
	console.log('宽度', window.matchMedia('(max-width: 768px)'));
	if (typeof window === 'undefined') return false;

	// 使用 matchMedia 来检测屏幕宽度
	const mediaQuery = window.matchMedia('(max-width: 768px)');
	return mediaQuery.matches;
});

// 添加窗口大小变化监听
onMounted(() => {
	window.addEventListener('resize', () => {
		console.log('重新计算');
		// 如果 isMobile 是一个响应式变量，应该通过重新赋值来触发更新
		isMobile.value = window.innerWidth <= 768; // 假设这是判断移动设备的逻辑
	});
});
</script>

<style lang="scss" scoped>
// 全屏对话框基础样式
:deep(.el-dialog) {
	margin: 0 !important;
	height: 100vh;
	display: flex;
	flex-direction: column;

	.el-dialog__header {
		padding: 12px 15px;
		margin: 0;
		border-bottom: 1px solid var(--el-border-color-lighter);
	}

	.el-dialog__body {
		flex: 1;
		padding: 0;
		overflow: hidden;
		height: calc(100vh - 53px); // 减去header高度
	}
}

// 移动端特定样式
.mobile-dialog {
	:deep(.el-tabs) {
		height: 100%;

		.el-tabs__content {
			height: calc(100% - 40px); // 减去tabs导航的高度
			overflow: auto;
		}
	}

	.tab-content {
		height: 100%;
		padding: 10px;
		overflow: auto;
		-webkit-overflow-scrolling: touch; // 增加iOS滚动惯性

		&.flow-content {
			padding: 0;
		}
	}

	// 优化表格在移动端的显示
	:deep(.el-table) {
		width: 100% !important;

		.el-table__body-wrapper {
			overflow-x: auto;
		}
	}
}

// 修改tabs样式
.flow-tabs {
	height: 100%;

	&.mobile-tabs {
		:deep(.el-tabs__header) {
			margin-bottom: 10px;
		}

		:deep(.el-tabs__nav) {
			width: 100%;
			display: flex;
			justify-content: space-around;
		}

		:deep(.el-tabs__item) {
			flex: 1;
			text-align: center;
		}
	}
}

// 表单内容区域样式
.tab-content {
	box-sizing: border-box;

	&.flow-content {
		background-color: var(--global-flow-background-color);
	}
}

// 确保按钮和其他操作区域正确显示
.refresh-btn {
	margin: 10px;
}

// 优化表单显示
:deep(.el-form) {
	padding: 10px;
}

// 优化弹窗内的滚动区域
.approval-dialog {
	padding: 10px;
	height: 100%;
	overflow: auto;
}

.user-select {
	display: flex;
	flex-direction: row;
	align-items: center;
}
.user-select-section,
.back-node-section,
.comment-section {
	margin-bottom: 20px;
}

.user-option {
	display: flex;
	align-items: center;
	gap: 8px;
}

.user-info {
	display: flex;
	flex-direction: column;
}

.user-name {
	font-size: 14px;
	color: var(--el-text-color-primary);
}

.user-dept {
	font-size: 12px;
	color: var(--el-text-color-secondary);
}

.node-option {
	display: flex;
	align-items: center;
	gap: 8px;
}

:deep(.el-select) {
	width: 100%;
}

:deep(.el-radio-group) {
	display: flex;
	gap: 16px;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
	padding-top: 20px;
}

// 优化样式选择器优先级
.flow-dialog {
	&.mobile-dialog {
		:deep(.el-dialog) {
			margin: 0 !important;
			height: 100vh !important;
			width: 100% !important;
			max-width: 100% !important;
		}
	}
}
</style>
