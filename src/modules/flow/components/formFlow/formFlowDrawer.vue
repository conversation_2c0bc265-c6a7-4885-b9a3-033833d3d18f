<template>
	<el-drawer
		:key="nodeData?.nodeId"
		v-model="drawer"
		:destroy-on-close="true"
		@close="closeHandler"
		title="自定义表单"
		:direction="direction"
	>
		<div class="form-flow-container">
			<el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
				<!-- 表单选择 -->
				<div class="section-title">
					<el-icon><document /></el-icon>
					表单选择
				</div>
				<el-form-item label="关联表单" prop="externalFormId">
					<el-select
						v-model="formData.externalFormId"
						placeholder="请选择关联表单"
						style="width: 100%"
						@change="handleFormChange"
					>
						<el-option
							v-for="item in formList"
							:key="item.id"
							:label="item.name"
							:value="item.id"
						/>
					</el-select>
				</el-form-item>

				<!-- 条件列映射 -->
				<template
					v-if="formData.externalFormId && nodeData?.properties?.combination?.form[0]"
				>
					<div class="section-title">
						<el-icon><connection /></el-icon>
						条件列映射
					</div>
					<div class="mapping-card">
						<el-alert type="info" :closable="false" show-icon>
							请将流程节点中的条件列与表单字段进行映射:主表->权限人表
						</el-alert>

						<div
							v-for="(field, index) in nodeData.properties.combination.form[0].name
								.conditionColumns"
							:key="'condition-' + index"
							class="mapping-item"
						>
							<el-form-item
								:label="field + '：'"
								label-width="80px"
								:rules="[
									{
										validator: (rule, value, callback) => {
											if (!formData.formField[index]) {
												callback(new Error('请选择主表字段'));
											} else if (!formData.externalFields[index]) {
												callback(new Error('请选择权限人表单字段'));
											} else {
												callback();
											}
										},
										trigger: 'change'
									}
								]"
								:prop="'externalFields.' + index"
							>
								<div class="mapping-row">
									<span class="field-label"
										><el-select
											v-model="formData.formField[index]"
											placeholder="请选择主表字段"
											style="width: 120px"
										>
											<el-option
												v-for="option in primaryTableCols"
												:key="option.field"
												:label="option.label"
												:value="option.field"
											/> </el-select
									></span>
									<el-icon class="mapping-arrow"><right /></el-icon>
									<el-select
										v-model="formData.externalFields[index]"
										placeholder="请选择权限人表单字段"
										style="width: 120px"
									>
										<el-option
											v-for="option in externalFormFields"
											:key="option.field"
											:label="option.label"
											:value="option.field"
										/>
									</el-select>
								</div>
							</el-form-item>
						</div>
					</div>

					<!-- 权限人列映射 -->
					<div class="section-title">
						<el-icon><user /></el-icon>
						权限人列映射
					</div>
					<div class="mapping-card">
						<el-alert type="info" :closable="false" show-icon>
							请将流程节点中的权限人列与表单字段进行映射
						</el-alert>

						<div
							v-for="(field, index) in nodeData.properties.combination.form[0].name
								.valueColumns"
							:key="'value-' + index"
							class="mapping-item"
						>
							<el-form-item
								:label="'权限人列' + '：'"
								:prop="'candidateUserField.' + index"
								:rules="[
									{ required: true, message: '请选择映射字段', trigger: 'change' }
								]"
							>
								<div class="mapping-row">
									<span class="field-label">{{ field }}</span>
									<el-icon class="mapping-arrow"><right /></el-icon>
									<el-select
										v-model="formData.candidateUserField[index]"
										placeholder="请选择表单字段"
										style="width: 120px"
									>
										<el-option
											v-for="option in externalFormFields"
											:key="option.field"
											:label="option.label"
											:value="option.field"
										/>
									</el-select>
								</div>
							</el-form-item>
						</div>
					</div>
				</template>
			</el-form>
		</div>

		<!-- <template #footer>
			<div class="drawer-footer">
				<el-button @click="closeDrawer">取消</el-button>
				<el-button type="primary" @click="submitData">确定</el-button>
			</div>
		</template> -->
	</el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from "vue";
import { Document, Connection, Right, InfoFilled, User } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useCool } from "/@/cool";
import { useStore } from "../../store/index";

const { service } = useCool();
const formListStore = useStore();
const drawer = ref(false);
const direction = ref("rtl");
const formRef = ref();
const nodeData = ref();
const primaryTableCols = ref([]);
const primaryTable = ref();
const primaryTableId = ref("");
// 表单数据
const formData: any = ref({
	externalFormId: "",
	formField: [],
	externalFields: [],
	candidateUserField: []
});

// 模拟表单列表数据
const formList: any = ref([]);

// 模拟表单字段数据
const externalFormFields: any = ref([]);

// 表单验证规则
const rules = {
	externalFormId: [{ required: true, message: "请选择关联表单", trigger: "change" }],
	"formField.*": [{ required: true, message: "请选择主表字段", trigger: "change" }],
	"externalFields.*": [{ required: true, message: "请选择权限人表单字段", trigger: "change" }],
	"candidateUserField.*": [{ required: true, message: "请选择表单字段", trigger: "change" }]
};

// 处理表单选择变化
const handleFormChange = value => {
	const data = formList.value.find(item => item.id === value);
	console.log("找到表单", data);
	externalFormFields.value = data.colInfo.map(item => ({
		field: item.name,
		label: item.showName
	}));
	// 模拟获取表单字段

	// 初始化映射数组
	if (nodeData.value?.properties?.combination?.form[0]) {
		const { conditionColumns, valueColumns } =
			nodeData.value.properties.combination.form[0].name;

		// 初始化条件列映射
		formData.value.externalFields = new Array(conditionColumns.length).fill("");
		formData.value.formField = Array.from({ length: conditionColumns.length }, () => "");
		// 初始化权限人列映射
		formData.value.candidateUserField = new Array(valueColumns.length).fill("");
		console.log("formData.externalFields", formData.value.externalFields);
		console.log("formData.candidateUserField", formData.value.candidateUserField);
	}
};

const closeHandler = async () => {
	if (!formRef.value) return;

	try {
		await formRef.value.validate();
		const data = formList.value.find(item => item.id === formData.value.externalFormId);

		if (nodeData.value?.properties?.combination?.form[0]) {
			nodeData.value.properties.combination.form[0].name = {
				...nodeData.value.properties.combination.form[0].name,
				externalFields: formData.value.externalFields,
				formField: formData.value.formField,
				candidateUserField: formData.value.candidateUserField,
				formName: data.name,
				externalFormId: data.id
			};
		}

		drawer.value = false;
	} catch (error) {
		ElMessage.error("请完善表单信息");
	}
};

const submitData = async () => {
	if (!formRef.value) return;

	try {
		await formRef.value.validate();
		// 验证通过，提交数据
		console.log("提交的数据：", formData);
		drawer.value = false;
	} catch (error) {
		ElMessage.error("请完善表单信息");
	}
};

const openHandler = async (node, formId) => {
	nodeData.value = null;
	// 先重置数据
	formData.value = {
		externalFormId: "",
		externalFields: [],
		formField: [],
		candidateUserField: []
	};
	primaryTableId.value = formId;
	console.log("formId", formId);
	primaryTable.value = formListStore.formListStore.detailFormListData(formId);
	console.log("primaryTable", primaryTable.value);
	primaryTableCols.value = primaryTable.value.colInfo.map(item => {
		return {
			field: item.name,
			label: item.showName
		};
	});
	console.log("primaryTable", primaryTable);
	// 设置节点数据
	nodeData.value = node;
	console.log("nodeData", nodeData.value);
	const formNode = node.properties.combination.form;
	if (!formNode || formNode.length === 0) {
		await ElMessageBox.alert("该节点没有表单需要关联，请选择表单自定义节点");
		return;
	}
	console.log("已存在formNode", formNode, formData.value);
	// 如果有已存在的表单配置，则加载它
	if (formNode.length > 0 && formNode[0].name.externalFormId) {
		console.log("已存在formNode-1", formNode[0].name);
		formData.value = {
			externalFormId: formNode[0].name.externalFormId,
			externalFields: formNode[0].name.externalFields || [],
			formField: formNode[0].name.formField || [],
			candidateUserField: formNode[0].name.candidateUserField || []
		};
	}

	// 加载表单列表
	try {
		const res = await service.cloud.db.list();
		if (res) {
			formList.value = res;
			// 确保数据都准备好后，再打开抽屉
			nextTick(() => {
				drawer.value = true;
			});
		}
	} catch (error) {
		console.error("加载表单列表失败:", error);
		ElMessage.error("加载表单列表失败");
	}
};

defineExpose({
	openHandler
});
</script>

<style scoped scss>
/* .form-flow-container {
	padding: 10px;
} */

.section-title {
	font-size: 16px;
	font-weight: bold;
	margin: 20px 0;
	padding-left: 10px;
	border-left: 4px solid var(--el-color-primary);
	display: flex;
	align-items: center;
	gap: 8px;
}

.mapping-card {
	background: var(--el-fill-color-blank);
	border-radius: 8px;
	padding: 20px;
	margin-top: 16px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
	.mapping-info {
		color: rgb(175, 175, 175);
		font-size: 14px;
	}
}

.mapping-item {
	margin-top: 20px;
	padding-bottom: 16px;
	border-bottom: 1px dashed var(--el-border-color-lighter);
}

.mapping-item:last-child {
	border-bottom: none;
}

.mapping-row {
	display: flex;
	align-items: center;
	gap: 12px;
}

.field-label {
	color: rgb(100, 100, 100);
	font-size: 14px;
}

.mapping-arrow {
	color: var(--el-color-primary);
}

.info-icon {
	color: var(--el-color-info);
	cursor: pointer;
}

.drawer-footer {
	padding: 10px 20px;
	text-align: right;
}

:deep(.el-alert) {
	margin-bottom: 20px;
}

:deep(.el-form-item:last-child) {
	margin-bottom: 0;
}
</style>
