<template>
	<el-drawer
		:key="nodeData?.nodeId"
		v-model="drawer"
		@open="openDrawer"
		@close="closeHandler"
		:direction="direction"
		destroy-on-close
		:force-render="false"
	>
		<div class="form-flow-container">
			<el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
				<!-- 基础配置区域 -->
				<div class="section-title">基础配置</div>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="表单名称" prop="formName">
							<el-select
								v-model="formData.formName"
								placeholder="请选择表单"
								@change="handleFormChange"
								style="width: 100%"
							>
								<el-option
									v-for="item in formOptions"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="关联流程" prop="flowId">
							<el-select
								v-model="formData.flowId"
								placeholder="请选择流程"
								@change="handleFlowChange"
								style="width: 100%"
							>
								<el-option
									v-for="item in flowOptions"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>

				<el-form-item label="启用状态">
					<el-switch v-model="formData.status" active-text="启用" inactive-text="禁用" />
				</el-form-item>

				<!-- 映射配置区域 -->
				<template v-if="formData.formName && formData.flowId">
					<div class="section-title">条件字段映射</div>
					<div class="mapping-container">
						<el-form-item
							v-for="(condition, index) in flowConditions"
							:key="index"
							:label="condition.label"
							:prop="'conditionMappings.' + index + '.fieldId'"
						>
							<div class="mapping-row">
								<el-select
									v-model="formData.conditionMappings[index].fieldId"
									placeholder="请选择表单字段"
									style="width: 80%"
								>
									<el-option
										v-for="field in formFields"
										:key="field.value"
										:label="field.label"
										:value="field.value"
									/>
								</el-select>
								<el-tooltip :content="condition.description" placement="top">
									<el-icon class="info-icon"><info-filled /></el-icon>
								</el-tooltip>
							</div>
						</el-form-item>
					</div>

					<div class="section-title">审批人员映射</div>
					<div class="mapping-container">
						<el-form-item
							v-for="(role, index) in flowRoles"
							:key="index"
							:label="role.label"
							:prop="'roleMappings.' + index + '.fieldId'"
						>
							<div class="mapping-row">
								<el-select
									v-model="formData.roleMappings[index].fieldId"
									placeholder="请选择人员字段"
									style="width: 80%"
								>
									<el-option
										v-for="field in userFields"
										:key="field.value"
										:label="field.label"
										:value="field.value"
									/>
								</el-select>
								<el-tag size="small" type="info">{{ role.type }}</el-tag>
							</div>
						</el-form-item>
					</div>
				</template>
			</el-form>
		</div>
		<template #footer>
			<el-button @click="handleClose">取消</el-button>
			<el-button type="primary" @click="submitData">确定</el-button>
		</template>
	</el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, defineExpose, defineEmits } from "vue";
import { InfoFilled } from "@element-plus/icons-vue";
import { useStore } from "../../store";

const { formFlowStore } = useStore();
const formRef = ref();
const drawer = ref(false);
const direction = ref("rtl");
const nodeData = ref();

const emit = defineEmits(["submitData"]);
const openDrawer = () => {
	console.log(nodeData.value);
};
// 表单数据
const formData = reactive({
	formName: "",
	flowId: "",
	status: true,
	conditionMappings: [],
	roleMappings: []
});

// 表单选项
const formOptions = ref([
	{ label: "请假申请表", value: "leave_form" },
	{ label: "报销申请表", value: "expense_form" }
]);

// 流程选项
const flowOptions = ref([
	{ label: "请假审批流程", value: "leave_flow" },
	{ label: "报销审批流程", value: "expense_flow" }
]);

// 表单字段
const formFields = ref([]);
const userFields = ref([]);

// 流程条件和角色
const flowConditions = ref([]);
const flowRoles = ref([]);

// 表单验证规则
const rules = {
	formName: [{ required: true, message: "请选择表单", trigger: "change" }],
	flowId: [{ required: true, message: "请选择流程", trigger: "change" }]
};

// 处理表单变化
const handleFormChange = value => {
	// 模拟获取表单字段
	formFields.value = [
		{ label: "申请金额", value: "amount" },
		{ label: "申请日期", value: "apply_date" }
	];
	userFields.value = [
		{ label: "部门主管", value: "department_manager" },
		{ label: "直接领导", value: "direct_leader" }
	];
	resetMappings();
};

// 处理流程变化
const handleFlowChange = value => {
	if (value === "leave_flow") {
		flowConditions.value = [
			{ label: "请假天数", value: "leave_days", description: "大于3天需要总监审批" },
			{ label: "请假类型", value: "leave_type", description: "病假需要提供证明" }
		];
		flowRoles.value = [
			{ label: "直接主管", value: "direct_manager", type: "一级审批" },
			{ label: "部门总监", value: "director", type: "二级审批" }
		];
	} else if (value === "expense_flow") {
		flowConditions.value = [
			{ label: "报销金额", value: "expense_amount", description: "大于5000需要财务审批" }
		];
		flowRoles.value = [
			{ label: "部门主管", value: "department_head", type: "审批人" },
			{ label: "财务人员", value: "finance", type: "复核人" }
		];
	}
	resetMappings();
};

// 重置映射关系
const resetMappings = () => {
	formData.conditionMappings = flowConditions.value.map(item => ({
		conditionId: item.value,
		fieldId: ""
	}));
	formData.roleMappings = flowRoles.value.map(item => ({
		roleId: item.value,
		fieldId: ""
	}));
};

const submitData = async () => {
	if (!formRef.value) return;
	await formRef.value.validate((valid: boolean) => {
		if (valid) {
			emit("submitData", formData);
			handleClose();
		}
	});
};

const handleClose = () => {
	drawer.value = false;
};
const openHandler = (currNode, backNodes) => {
	nodeData.value = currNode;
	backNodeList.value = backNodes;

	fieldsParse(currNode.properties);
	drawer.value = true;
};
const closeHandler = () => {
	// 配置验证和保存
	// settingRef.value
	// 	.formConfig()
	// 	.then(formValue => {
	// 		nodeData.value.properties = formValue;
	// 		nodeData.value.value = formValue.value;
	// 	})
	// 	.catch(error => {
	// 		console.log(error);
	// 	});

	drawer.value = false;
};
defineExpose({
	openHandler,
	closeHandler
});
</script>

<style scoped>
.form-flow-container {
	padding: 0 20px;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	margin: 20px 0;
	padding-left: 10px;
	border-left: 4px solid var(--el-color-primary);
}

.mapping-container {
	padding: 16px;
	background: var(--el-fill-color-lighter);
	border-radius: 4px;
	margin-bottom: 20px;
}

.mapping-row {
	display: flex;
	align-items: center;
	gap: 12px;
}

.info-icon {
	color: var(--el-color-info);
	cursor: pointer;
}

:deep(.el-form-item__label) {
	font-weight: 500;
}

:deep(.el-tag) {
	margin-left: 8px;
}
</style>
