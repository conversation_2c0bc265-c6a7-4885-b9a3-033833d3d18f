<template>
	<el-card class="data-card" shadow="hover">
		<template #header>
			<div class="card-header">
				<span class="title">{{ formOptions?.formName || '测试' }}</span>
				<!-- <el-tag :type="getStatusType(data['审核状态'])" effect="dark" class="status-tag">
					{{ getStatusText(data["审核状态"]) }}
				</el-tag> -->
			</div>
		</template>

		<div class="content-wrapper">
			<formCreate
				:rule="formColInfo"
				:option="formOptions"
				v-model="formData"
				v-model:api="fApi"
			></formCreate>
		</div>

		<!-- 按钮操作区 -->
		<div class="action-wrapper" :class="{ 'mobile-action-wrapper': isMobile }" v-if="isTodo">
			<el-button
				v-for="btn in buttons"
				:key="btn.type"
				plain
				:class="{ 'mobile-button': isMobile }"
				:type="getButtonType(btn.type)"
				@click="handleButtonClick(btn)"
			>
				<!-- <template #icon>
					<el-icon>
						<component :is="getButtonIcon(btn.type)" />
					</el-icon>
				</template> -->

				{{ btn.text }}
			</el-button>
		</div>
	</el-card>
</template>

<script setup lang="ts">
import { computed, onMounted, toRefs, ref, watch, nextTick } from 'vue';
import { Picture, Check, Close, Back, Plus, Minus, Share } from '@element-plus/icons-vue';
import { useCool } from '/@/cool';
import formCreate from '@form-create/element-ui';
import FcDesigner from '@form-create/designer';

const formData = ref({});
const fApi = ref();

formCreate.component('table-form', FcDesigner.tableForm);

const { service, router } = useCool();
const props = defineProps({
	data: {
		type: Object,
		required: true
	},
	formOptions: {
		type: Object
	},
	formColInfo: {
		type: Array
	},
	buttons: {
		type: Array,
		required: true
	},
	isTodo: {
		type: Boolean
	}
});
const { isTodo, data, buttons, formOptions, formColInfo } = toRefs(props);
watch(
	() => formColInfo,
	newVal => {
		if (newVal) {
			const mappedData = {};

			// 遍历规则找到所有需要映射的字段

			traverseRules(formColInfo.value, mappedData);

			// 更新表单数据
			formData.value = mappedData;

			// 确保表单更新
			nextTick(() => {
				if (fApi.value) {
					Object.entries(mappedData).forEach(([field, value]) => {
						fApi.value.setValue(field, value);
					});
				}
			});
		}
	}
);

onMounted(() => {
	console.log(
		'formShow',
		data.value,
		'formColInfo.value',
		formColInfo?.value,
		'formOptions',
		formOptions?.value,
		'buttons',
		buttons.value
	);
	const mappedData = {};
	// 遍历规则找到所有需要映射的字段
	traverseRules(formColInfo?.value, mappedData);
	// 更新表单数据
	formData.value = mappedData;
	//console.log('formData_mappedData', mappedData);
	// 确保表单更新
	nextTick(() => {
		if (fApi.value) {
			Object.entries(mappedData).forEach(([field, value]) => {
				fApi.value.setValue(field, value);
			});
		}
	});
});
function traverseRules(items, mappedData) {
	//console.log('mappedData', mappedData);
	items.forEach(item => {
		// 处理普通字段
		//	console.log('遍历字段', item);
		if (item.props) {
			item.props.disabled = true;
		}

		if (item.field && item.type !== 'tableForm') {
			const fieldName = item.props?.fieldName || item.title;
			if (fieldName && data.value[fieldName] !== undefined) {
				// 处理带有字典的select类型
				if (
					(item.type === 'select' || item.type == 'elTreeSelect') &&
					item.props?.hasDict?.includes('hasDict')
				) {
					if (item.props.isProject) {
						if (data.value[fieldName] && data.value['projectCode']) {
							console.log('mappedData', mappedData, item.field);
							mappedData[item.field] =
								`${data.value[fieldName]}&${data.value['projectCode']}`;
						}
					} else {
						const codeField = `${fieldName}代码`;
						if (data.value[fieldName] && data.value[codeField]) {
							mappedData[item.field] =
								`${data.value[fieldName]}&${data.value[codeField]}`;
						}
					}
				} else {
					// 处理其他类型字段
					mappedData[item.field] = data.value[fieldName];
				}
			}
		}

		// 处理表格表单类型
		if (item.type === 'tableForm') {
			const fieldName = item.props?.fieldName;
			if (fieldName && Array.isArray(data.value[fieldName])) {
				const tableData = data.value[fieldName].map(row => {
					const mappedRow = {};
					// 遍历表格的列定义
					item.props.columns.forEach(col => {
						const colField = col.rule[0].field;
						const colLabel = col.label;
						// 使用列的label作为key来获取数据
						if (row[colLabel] !== undefined) {
							mappedRow[colField] = row[colLabel];
						}
					});
					return mappedRow;
				});
				mappedData[item.field] = tableData;
			}
		}

		// 递归处理子项
		if (item.children) {
			traverseRules(item.children, mappedData);
		}
	});
}

// 需要排除的字段
const excludeFields = [
	'id',
	'审核人',
	'审核时间',
	'审核说明',
	'审核状态',
	'tenantId'
	//	"项目名称" // 因为已经在标题中显示
];

// 图片字段名称（可以根据实际需求修改）
const imageFields = ['image', 'photo', 'picture', '图片', '照片', '影像'];

// 判断是否为图片字段
const isImageField = (key: string) => {
	// 通过字段名判断
	if (imageFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
		return true;
	}
	// 通过值的类型判断（如果值是图片URL或图片URL数组）
	const value = data.value[key];
	if (typeof value === 'string' && isImageUrl(value)) {
		return true;
	}
	if (Array.isArray(value) && value.every(item => typeof item === 'string' && isImageUrl(item))) {
		return true;
	}
	return false;
};

// 简单判断是否为图片URL
const isImageUrl = (url: string) => {
	if (!url) return false;
	return /\.(jpg|jpeg|png|gif|webp)$/i.test(url) || url.startsWith('data:image/');
};

// 过滤后的数据
const filteredData = computed(() => {
	const result = {};
	Object.entries(data.value).forEach(([key, value]) => {
		if (!excludeFields.includes(key)) {
			result[key] = value;
		}
	});
	return result;
});

// 获取显示值（处理 null 和 undefined）
const getDisplayValue = (key: string) => {
	return data.value[key] || data.value?.projectCode || '';
};

// 格式化标签名
const formatLabel = (label: string) => {
	return label;
};

// 格式化值
const formatValue = (value: any) => {
	if (value === null || value === undefined) {
		return '暂无数据';
	}
	return value;
};

// 获取状态类型
const getStatusType = (status: number) => {
	const statusMap = {
		1: 'success',
		2: 'warning',
		3: 'info',
		4: 'danger'
	};
	return statusMap[status] || 'info';
};

// 获取状态文本
const getStatusText = (status: number) => {
	const statusMap = {
		1: '已通过',
		0: '审核中',
		2: '已驳回'
	};
	return statusMap[status] || '未知状态';
};

// 获取按钮类型
const getButtonType = (type: string) => {
	const typeMap = {
		aggren: 'success',
		reject: 'danger',
		back: 'warning',
		signAdd: 'primary',
		signRedu: 'info',
		transfer: 'default'
	};
	return typeMap[type] || 'default';
};

// 获取按钮图标
const getButtonIcon = (type: string) => {
	const iconMap = {
		aggren: Check,
		reject: Close,
		back: Back,
		signAdd: Plus,
		signRedu: Minus,
		transfer: Share
	};
	return iconMap[type];
};

// 按钮点击处理
const emit = defineEmits(['buttonClick']);
const handleButtonClick = btn => {
	emit('buttonClick', btn);
};

// 添加移动端检测
const isMobile = computed(() => {
	return window.innerWidth <= 768;
});
</script>

<style scoped>
.data-card {
	margin: v-bind('isMobile ? "10px" : "20px"');
	border-radius: 8px;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.title {
	font-size: v-bind('isMobile ? "16px" : "18px"');
	font-weight: bold;
	color: #303133;
}

.status-tag {
	margin-left: 12px;
}

.content-wrapper {
	margin-top: v-bind('isMobile ? "10px" : "16px"');
}

:deep(.el-descriptions) {
	padding: v-bind('isMobile ? "4px" : "8px"');
}

:deep(.el-descriptions__cell) {
	padding: v-bind('isMobile ? "8px" : "12px 16px"');
}

:deep(.el-descriptions__label) {
	width: v-bind('isMobile ? "80px" : "120px"');
	font-weight: bold;
	font-size: v-bind('isMobile ? "14px" : "inherit"');
}

.mobile-cell :deep(.el-descriptions__content) {
	word-break: break-all;
}

.value-text {
	font-size: v-bind('isMobile ? "14px" : "inherit"');
}

.image-container {
	display: flex;
	flex-wrap: wrap;
	gap: v-bind('isMobile ? "4px" : "8px"');
}

.mobile-image-container {
	justify-content: flex-start;
}

.image-item {
	width: 100px;
	height: 100px;
}

.mobile-image-item {
	width: 80px;
	height: 80px;
}

:deep(.el-image) {
	width: 100%;
	height: 100%;
	border-radius: 4px;
	overflow: hidden;
}

.mobile-image-item :deep(.el-image) {
	width: 80px;
	height: 80px;
}

.image-error {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f5f7fa;
	color: #909399;
}

:deep(.el-icon) {
	font-size: 24px;
}

.action-wrapper {
	margin-top: 24px;
	padding-top: 24px;
	border-top: 1px solid var(--el-border-color-lighter);
	display: flex;
	gap: 12px;
	justify-content: center;
	flex-wrap: wrap;
}

.mobile-action-wrapper {
	margin-top: 16px;
	padding-top: 16px;
	gap: 8px;
}

:deep(.el-button) {
	padding: 8px 20px;
	min-width: 100px;
}

.mobile-button {
	padding: 6px 12px !important;
	min-width: 80px !important;
	font-size: 14px;
}

@media screen and (max-width: 768px) {
	:deep(.el-card__header) {
		padding: 12px;
	}

	:deep(.el-card__body) {
		padding: 12px;
	}
}

.table-scroll-container {
	width: 100%;
	overflow: auto;
	-webkit-overflow-scrolling: touch; /* 增加 iOS 滚动流畅度 */
}

.mobile-scroll {
	/* 设置最小宽度确保可以横向滚动 */
	min-width: 100%;
	/* 添加滚动条样式 */
	scrollbar-width: thin;
	scrollbar-color: #909399 #f4f4f5;
}

/* 自定义滚动条样式 */
.mobile-scroll::-webkit-scrollbar {
	height: 6px;
}

.mobile-scroll::-webkit-scrollbar-track {
	background: #f4f4f5;
	border-radius: 3px;
}

.mobile-scroll::-webkit-scrollbar-thumb {
	background: #909399;
	border-radius: 3px;
}

.description-table {
	/* 确保表格在移动端有足够的最小宽度 */
	min-width: v-bind('isMobile ? "300px" : "auto"');
}

/* 调整移动端下的描述列表样式 */
.mobile-cell :deep(.el-descriptions__label) {
	min-width: 100px; /* 确保标签有最小宽度 */
	white-space: nowrap; /* 防止标签换行 */
}

.mobile-cell :deep(.el-descriptions__content) {
	min-width: 150px; /* 确保内容有最小宽度 */
}

/* 优化移动端下的内容显示 */
:deep(.el-descriptions__body) {
	width: 100%;
}

:deep(.el-descriptions__table) {
	width: 100%;
	table-layout: fixed;
}

@media screen and (max-width: 768px) {
	.content-wrapper {
		margin: 0;
		width: 100%;
	}

	:deep(.el-descriptions__cell) {
		white-space: normal; /* 允许内容换行 */
	}

	:deep(.el-descriptions__label) {
		line-height: 1.4;
	}

	:deep(.el-descriptions__content) {
		line-height: 1.4;
	}
	/* 添加横向滚动提示 */
	.mobile-scroll::after {
		content: '';
		position: absolute;
		right: 0;
		top: 0;
		bottom: 0;
		width: 30px;
		background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.8));
		pointer-events: none;
		opacity: 0;
		transition: opacity 0.3s;
	}

	.mobile-scroll:not(:hover)::after {
		opacity: 1;
	}
}
</style>
