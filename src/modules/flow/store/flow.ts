import { defineStore } from 'pinia';
import { ref } from 'vue';
import { uuid } from '/@/modules/flow/utils/flow';
import { storage } from '/@/cool/utils';

export const useFlow = defineStore('flowStore', function () {
	const flowData = ref(
		storage.get('flow_data') || [
			{
				id: '1852714097983586305',
				//tenantId: "1",
				createUser: '1',
				updateUser: '1',
				createTime: '2024-11-02 22:07:17',
				updateTime: '2025-01-17 16:37:56',
				//flowCode: "leave",
				flowName: '测试流程',
				flowVersion: '1',
				//分类
				//categoryId: "1811200484240461826",
				//categoryName: "财务",
				defId: '1852714563165454338',
				icon: 'Avatar',
				flowJson:
					'{"nodeId":"801e3c73-e665-4f6f-8ea7-d4de0927214c","nodeType":"start","nodeName":"开始节点","value":"所有人","properties":{},"childNode":{"nodeId":"f1c63412-5710-446d-a5ac-f441f37a22c2","nodeName":"审批人1","nodeType":"between","value":"admin","type":"between","properties":{"nodeRatioType":"1","backType":"1","nodeRatio":0,"permissions":["user"],"combination":{"role":[],"user":[{"id":"1814307624207470594","name":"seven"}],"form":[]},"emptyApprove":{"type":"AUTO","value":[]},"value":"seven","buttons":[{"type":"aggren","checked":true,"text":"同意"},{"type":"reject","checked":true,"text":"拒绝"},{"type":"back","checked":false,"text":"回退"}],"backTypeNode":"801e3c73-e665-4f6f-8ea7-d4de0927214c"},"childNode":{"nodeId":"3483a491-0765-4b54-8c35-dbacb29e5bbf","nodeType":"serial","nodeName":"分支选择","conditionNodes":[{"nodeId":"b1d40308-9bab-4aaa-a1b0-7a362e24844f","nodeType":"serial-node","type":"serial-node","nodeName":"条件","sortNum":0,"value":"days 大于等于 3","properties":{"conditions":{"simple":true,"group":"and","simpleData":[{"key":"days","cond":"ge","value":"3","next":"and"}]},"value":"days 大于等于 3"},"levelValue":"优先级1","childNode":{"nodeId":"f36a09c6-08ab-4027-85f9-db1f8a55f57c","nodeType":"parallel","nodeName":"并行分支","properties":{},"conditionNodes":[{"nodeId":"a4095f0e-1950-4d4c-b886-4a3035b645c2","nodeName":"审批人1","nodeType":"between","type":"between","properties":{"nodeRatioType":"1","backType":"1","nodeRatio":0,"permissions":["user"],"combination":{"role":[],"user":[{"id":"1","name":"超管"}],"form":[]},"emptyApprove":{"type":"AUTO","value":[]},"value":"超管","buttons":[{"type":"aggren","checked":true,"text":"同意"},{"type":"reject","checked":true,"text":"拒绝"},{"type":"back","checked":false,"text":"回退"},{"type":"transfer","checked":false,"text":"转办"},{"type":"depute","checked":false,"text":"委派"},{"type":"signAdd","checked":false,"text":"加签"},{"type":"signRedu","checked":false,"text":"减签"}],"backTypeNode":"801e3c73-e665-4f6f-8ea7-d4de0927214c"},"value":"超管"},{"nodeId":"091944fb-4f6f-4eeb-82d9-1ea3da22bd52","nodeName":"审批人2","nodeType":"between","type":"between","properties":{"nodeRatioType":"1","backType":"1","nodeRatio":0,"permissions":["user"],"combination":{"role":[],"user":[{"id":"2","name":"测试"}],"form":[]},"emptyApprove":{"type":"AUTO","value":[]},"value":"测试","buttons":[{"type":"aggren","checked":true,"text":"同意"},{"type":"reject","checked":true,"text":"拒绝"},{"type":"back","checked":false,"text":"回退"},{"type":"transfer","checked":false,"text":"转办"},{"type":"depute","checked":false,"text":"委派"},{"type":"signAdd","checked":false,"text":"加签"},{"type":"signRedu","checked":false,"text":"减签"}],"backTypeNode":"801e3c73-e665-4f6f-8ea7-d4de0927214c"},"value":"测试"}]}},{"nodeId":"1f678933-91b3-43c2-814c-26722a25d8f4","nodeType":"serial-node","type":"serial-node","nodeName":"条件","value":"其他条件默认走此流程","sortNum":1,"default":true,"properties":{},"levelValue":"优先级2","enableDestory":true,"childNode":{"nodeId":"32610c7f-21f1-43c5-821b-f65e3fcb917b","nodeName":"审批人1","nodeType":"between","value":"测试","type":"between","properties":{"nodeRatioType":"1","backType":"1","nodeRatio":0,"permissions":["user"],"combination":{"role":[],"user":[{"id":"2","name":"测试"}],"form":[]},"emptyApprove":{"type":"AUTO","value":[]},"value":"测试","buttons":[{"type":"aggren","checked":true,"text":"同意"},{"type":"reject","checked":true,"text":"拒绝"},{"type":"back","checked":false,"text":"回退"},{"type":"transfer","checked":false,"text":"转办"},{"type":"depute","checked":false,"text":"委派"},{"type":"signAdd","checked":false,"text":"加签"},{"type":"signRedu","checked":false,"text":"减签"}],"backTypeNode":"801e3c73-e665-4f6f-8ea7-d4de0927214c"}}}],"childNode":{"nodeId":"fa13aeeb-f794-43c8-a2aa-27da340b9696","nodeType":"end","nodeName":"结束","properties":{}}}}}',
				formJson:
					'{"schemas":[{"label":"表单","type":"form","componentProps":{"layout":"horizontal","name":"default","labelWidth":100,"labelLayout":"fixed","labelCol":{"span":5},"wrapperCol":{"span":19},"colon":true,"labelAlign":"right","labelPlacement":"left"},"children":[],"id":"root"}],"script":"const { defineExpose, find } = epic;\\n\\nfunction test (){\\n    console.log(\'test\')\\n}\\n\\n// 通过defineExpose暴露的函数或者属性\\ndefineExpose({\\n test\\n})"}',
				status: '1',
				sortNum: null,
				formCustom: 'Y',
				formPath: 'leave.vue',
				managerId: '1',
				managerName: '超管',
				createUserNick: '超管',
				updateUserNick: '超管'
			}
		]
	);

	const addFlowData = (data: any) => {
		console.log('add', data);
		data.status = '1';
		data.categoryId = 'xxxx';
		data.categoryName = '财务';
		data.createTime = new Date().toLocaleTimeString();
		data.createUser = '1';
		data.formCustom = 'Y';
		data.formPath = 'leave.vue';
		data.createUserNick = 'admin';
		data.defId = data.defId || '';
		data.flowVersion = data.flowVersion || '1';
		data.id = uuid();
		flowData.value.push(data);
		console.log('dictCols存入pinia', data);
		storage.set('flow_data', flowData.value);
	};
	const getFlowData = () => {
		return flowData.value;
	};
	const editFlowData = (data: any) => {
		const flowItem = flowData.value.find((item: any) => item.id == data.id);
		const keys = Object.keys(data);
		keys.forEach((key: any) => {
			flowItem[key] = data[key];
		});
		flowData.value = flowData.value.filter((item: any) => item.id != data.id);
		flowData.value.push(flowItem);
		storage.set('flow_data', flowData.value);
	};
	const updateFlowData = (data: any) => {
		console.log('updateFlowData', data);
		flowData.value.map((item: any) => {
			if (item.id == data.id) {
				item.flowJson = data.flowJson;
				console.log('item.flowJson', item.flowJson);
				item.form_json = data.form_json;
			}
		});
		console.log('更新流程设计后', flowData.value);
		storage.set('flow_data', flowData.value);
	};
	const delFlowData = (data: any) => {
		flowData.value = flowData.value.filter((item: any) => item.id != data.id);
		storage.set('flow_data', flowData.value);
	};
	const publishFlowData = (data: any) => {
		console.log('publishFlowData', data);
		if (data.id) {
			flowData.value.map((item: any) => {
				if (item.id == data.id) {
					item.flowVersion = item.flowVersion + 1;
				}
			});
		}
		storage.set('flow_data', flowData.value);
	};
	return {
		//dictCols,
		addFlowData,
		updateFlowData,
		editFlowData,
		getFlowData,
		delFlowData,
		publishFlowData
	};
});
