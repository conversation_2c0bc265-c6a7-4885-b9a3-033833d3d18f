import { defineStore } from 'pinia';
import { ref } from 'vue';
import { uuid } from '/@/modules/flow/utils/flow';
import { storage } from '/@/cool/utils';

import { useCool } from '/@/cool';
export const useFormFlow = defineStore('formFlowStore', function () {
	const { service } = useCool();
	const formFlowData = ref(storage.get('form_flow_data') || []);
	const formFlowUserList = ref(storage.get('form_flow_user_list') || []);
	const addFormFlowData = (data: any) => {
		//	console.log("添加addFormFlowData", data);
		formFlowData.value = data;

		storage.set('form_flow_data', formFlowData.value);
	};
	//const save
	const getFormFlowUserList = async () => {
		return new Promise(async (resolve, reject) => {
			//console.log("获取formFlowUserList", formFlowUserList.value);
			if (formFlowUserList.value.length === 0) {
				const res = await service.base.comm.getAllUsers();
				formFlowUserList.value = res;
				//console.log("获取formFlowUserList11", formFlowUserList.value);
			}
			resolve(formFlowUserList.value);
		});
	};
	const getFormFlowData = () => {
		return formFlowData.value;
	};
	const editFormFlowData = async (data: any) => {
		const index = formFlowData.value.findIndex((item: any) => item.id === data.id);
		if (index === -1) {
			console.error('未找到对应的流程表单数据，id:', data.id);
			return;
		}
		console.log('编辑流程表单-editFormFlowData', data, index);
		const updatedData = formFlowData.value.map((item, i) =>
			i === index ? { ...item, ...data } : item
		);
		formFlowData.value = updatedData;
		try {
			await storage.set('form_flow_data', updatedData);
		} catch (error) {
			console.error('存储数据失败:', error);
		}
	};
	const deleteFormFlowData = (id: string) => {
		formFlowData.value = formFlowData.value.filter((item: any) => item.id !== id);
		storage.set('form_flow_data', formFlowData.value);
	};
	const detailFormFlowData = (id: string) => {
		return formFlowData.value.find((item: any) => item.id === id);
	};
	return {
		formFlowData,
		addFormFlowData,
		getFormFlowData,
		editFormFlowData,
		deleteFormFlowData,
		detailFormFlowData,
		getFormFlowUserList
	};
});
