import { defineStore } from "pinia";
import { ref } from "vue";
import { uuid } from "/@/modules/flow/utils/flow";
import { storage } from "/@/cool/utils";

export const useFormListData = defineStore("formListData", function () {
	const formListData = ref(
		storage.get("form_list_data") || [
			{
				id: 1774,
				createTime: "2025-02-19 14:35:08",
				name: "测试表单156",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('测试表单156')\nexport class 测试表单156Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 200 })\n            projectCode: string;\n @Column({ comment: '合同', length: 200, nullable: true })\n            合同: string;\n @Column({ comment: '合同代码', length: 200, nullable: true })\n            合同代码: string;\n @Column({ comment: ' 数值',nullable: true,precision: 12})\n               数值: number;\n @Column({ comment: '备注', length: 150, nullable: true })\n            备注: string;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true
					},
					{
						name: "合同",
						type: "dict",
						dictId: 399,
						isNull: true,
						hasDict: true,
						dictName: "合同字典",
						disabled: true,
						showName: "合同",
						dictField: "198/202/32/399"
					},
					{
						name: "数值",
						type: "number",
						isNull: true,
						hasDict: false,
						showName: "数值"
					},
					{
						name: "备注",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "备注",
						stringType: "rangeLength",
						stringLength: [0, 150]
					}
				],
				tableInfo: {
					allowEdit: false,
					autoincrement: false
				},
				tableName: "func_测试表单156",
				tableType: "user",
				formTypeId: 4,
				status: 1
			},
			{
				id: 1767,
				createTime: "2025-01-22 11:14:21",
				name: "权限人审核表",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('权限人审核表')\nexport class 权限人审核表Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 200 })\n            projectCode: string;\n @Column({ comment: '部门', length: 200, nullable: true })\n            部门: string;\n @Column({ comment: '部门代码', length: 200, nullable: true })\n            部门代码: string;\n @Column({ comment: '权限人', length: 200, nullable: true })\n            权限人: string;\n @Column({ comment: '权限人代码', length: 200, nullable: true })\n            权限人代码: string;\n @Column({ comment: '备注', length: 150, nullable: true })\n            备注: string;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true
					},
					{
						name: "部门",
						type: "dict",
						dictId: 199,
						isNull: true,
						hasDict: true,
						dictName: "部门",
						disabled: true,
						showName: "部门",
						dictField: "198/204/21/199"
					},
					{
						name: "权限人",
						type: "dict",
						dictId: 200,
						isNull: true,
						hasDict: true,
						dictName: "人员字典",
						disabled: true,
						showName: "权限人",
						dictField: "198/203/200"
					},
					{
						name: "备注",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "备注",
						stringType: "rangeLength",
						stringLength: [0, 150]
					}
				],
				tableInfo: {
					allowEdit: false
				},
				tableName: "func_权限人审核表",
				tableType: "user",
				formTypeId: 1,
				status: 1
			},
			{
				id: 191,
				createTime: "2024-10-25 08:20:47",
				name: "借款与垫款情况",
				readme: "本表每月采集一次",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('借款与垫款情况')\n@Index([\"采集月份\",\"单位代码\"], { unique: true })\nexport class 借款与垫款情况Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 200 })\n            projectCode: string;\n @Column({ comment: '单位简称', length: 1500, nullable: true })\n            单位简称: string;\n @Column({ comment:  '采集月份', type: 'date' })\n                采集月份: Date;\n @Column({ comment:  '借款',nullable: true,type:'decimal', precision: 12, scale:2})\n               借款: number;\n @Column({ comment:  '本部垫款',nullable: true,type:'decimal', precision: 12, scale:2})\n               本部垫款: number;\n @Column({\n\t\t\t\ttype: \"decimal\",\n\t\t\t\tgeneratedType: \"VIRTUAL\",\n\t\t\t\tasExpression: `借款 +本部垫款`,\n\t\t\t\tprecision: 12,\n\t\t\t\tscale: 2\n\t\t\t\t})\n\t\t\t\t合计: number;\n @Column({ comment: ' 序号',nullable: true,precision: 12})\n               序号: number;\n @Column({ comment: '单位', length: 200 })\n            单位: string;\n @Column({ comment: '单位代码', length: 150 })\n            单位代码: string;\n @Column({ comment: '备注', length: 1500, nullable: true })\n            备注: string;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						isPrimaryKey: false,
						typeDisabled: true
					},
					{
						name: "单位简称",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "单位简称",
						stringType: "anyLength",
						relatedTable: 175,
						stringLength: null,
						typeDisabled: true,
						relatedColType: "string",
						relatedShowName: "公司简称",
						relatedTableCol: "公司简称"
					},
					{
						name: "采集月份",
						type: "date",
						hasDict: false,
						dateType: "YYYY-MM",
						showName: "采集月份",
						isPrimaryCol: true,
						typeDisabled: true
					},
					{
						name: "借款",
						type: "number",
						isNull: true,
						hasDict: false,
						decimals: "2",
						showName: "借款",
						typeDisabled: true
					},
					{
						name: "本部垫款",
						type: "number",
						isNull: true,
						hasDict: false,
						decimals: "2",
						showName: "本部垫款",
						typeDisabled: true
					},
					{
						name: "合计",
						type: "calculatedCol",
						sqlStr: "﻿`借款`﻿ +`本部垫款`﻿ ﻿﻿",
						hasDict: false,
						decimals: "2",
						showName: "合计",
						typeDisabled: true,
						calculateType: "number"
					},
					{
						name: "序号",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "序号",
						stringType: "rangeLength",
						relatedTable: 175,
						stringLength: null,
						typeDisabled: true,
						relatedColType: "number",
						relatedShowName: "顺序",
						relatedTableCol: "顺序"
					},
					{
						name: "单位",
						type: "dict",
						dictId: 42,
						hasDict: true,
						dictName: "项目（核算）",
						disabled: true,
						showName: "单位",
						dictField: "198/204/42",
						isPrimaryCol: true,
						isPrimaryKey: true,
						typeDisabled: true,
						relatedTables: [
							{
								relatedTable: 175,
								relatedTableCol: "单位",
								relatedColisProject: true
							}
						]
					},
					{
						name: "备注",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "备注",
						stringType: "anyLength",
						typeDisabled: true
					}
				],
				tableInfo: {
					allowEdit: true
				},
				tableName: "func_借款与垫款情况",
				tableType: "user",
				formTypeId: 4,
				status: 1
			},
			{
				id: 172,
				createTime: "2024-10-23 16:30:53",
				name: "票证",
				readme: "本表每月更新一次",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('票证')\n@Index([\"projectCode\",\"票据类型代码\",\"标签种类代码\",\"采集月份\"], { unique: true })\nexport class 票证Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 150 })\n            projectCode: string;\n @Column({ comment: '票据类型', length: 200 })\n            票据类型: string;\n @Column({ comment: '票据类型代码', length: 150 })\n            票据类型代码: string;\n @Column({ comment: '标签种类', length: 200 })\n            标签种类: string;\n @Column({ comment: '标签种类代码', length: 150 })\n            标签种类代码: string;\n @Column({ comment:  '金额',nullable: true,type:'decimal', precision: 12, scale:2})\n               金额: number;\n @Column({ comment:  '采集月份', type: 'date' })\n                采集月份: Date;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						isPrimaryCol: true,
						isPrimaryKey: true,
						typeDisabled: true
					},
					{
						name: "票据类型",
						type: "dict",
						dictId: 388,
						hasDict: true,
						dictName: "票据类型",
						disabled: true,
						showName: "票据类型",
						dictField: "198/209/388",
						isPrimaryCol: true,
						isPrimaryKey: true,
						typeDisabled: true
					},
					{
						name: "标签种类",
						type: "dict",
						dictId: 389,
						hasDict: true,
						dictName: "标签种类",
						disabled: true,
						showName: "标签种类",
						dictField: "198/209/389",
						isPrimaryCol: true,
						isPrimaryKey: true,
						typeDisabled: true
					},
					{
						name: "金额",
						type: "number",
						isNull: true,
						hasDict: false,
						decimals: "2",
						showName: "金额",
						typeDisabled: true
					},
					{
						name: "采集月份",
						type: "date",
						hasDict: false,
						dateType: "YYYY-MM",
						showName: "采集月份",
						isPrimaryCol: true,
						isPrimaryKey: true,
						typeDisabled: true
					}
				],
				tableInfo: {
					allowEdit: true
				},
				tableName: "func_票证",
				tableType: "user",
				formTypeId: 4,
				status: 1
			},
			{
				id: 171,
				createTime: "2024-10-23 16:16:55",
				name: "授信",
				readme: "本表每月采集一次",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('授信')\n@Index([\"projectCode\",\"授信银行代码\",\"采集月份\"], { unique: true })\nexport class 授信Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 150 })\n            projectCode: string;\n @Column({ comment: '授信银行', length: 200 })\n            授信银行: string;\n @Column({ comment: '授信银行代码', length: 150 })\n            授信银行代码: string;\n @Column({ comment:  '授信额度',nullable: true,type:'decimal', precision: 12, scale:2})\n               授信额度: number;\n @Column({ comment:  '已使用额度',nullable: true,type:'decimal', precision: 12, scale:2})\n               已使用额度: number;\n @Column({\n\t\t\t\ttype: \"decimal\",\n\t\t\t\tgeneratedType: \"VIRTUAL\",\n\t\t\t\tasExpression: `授信额度 -已使用额度`,\n\t\t\t\tprecision: 12,\n\t\t\t\tscale: 2\n\t\t\t\t})\n\t\t\t\t可用额度: number;\n @Column({ comment:  '采集月份', type: 'date' })\n                采集月份: Date;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						isPrimaryCol: true,
						isPrimaryKey: true,
						typeDisabled: true
					},
					{
						name: "授信银行",
						type: "dict",
						dictId: 390,
						hasDict: true,
						dictName: "银行简称",
						disabled: true,
						showName: "授信银行",
						dictField: "198/209/390",
						isPrimaryCol: true,
						isPrimaryKey: true,
						typeDisabled: true
					},
					{
						name: "授信额度",
						type: "number",
						isNull: true,
						hasDict: false,
						decimals: "2",
						showName: "授信额度",
						typeDisabled: true
					},
					{
						name: "已使用额度",
						type: "number",
						isNull: true,
						hasDict: false,
						decimals: "2",
						showName: "已使用额度",
						typeDisabled: true
					},
					{
						name: "可用额度",
						type: "calculatedCol",
						sqlStr: "﻿`授信额度`﻿ -`已使用额度`﻿ ﻿﻿",
						hasDict: false,
						decimals: "2",
						showName: "可用额度",
						typeDisabled: true,
						calculateType: "number"
					},
					{
						name: "采集月份",
						type: "date",
						hasDict: false,
						dateType: "YYYY-MM",
						showName: "采集月份",
						isPrimaryCol: true,
						isPrimaryKey: true,
						typeDisabled: true
					}
				],
				tableInfo: {
					allowEdit: true
				},
				tableName: "func_授信",
				tableType: "user",
				formTypeId: 4,
				status: 1
			},
			{
				id: 1766,
				createTime: "2025-01-15 14:28:05",
				name: "客商信息补充",
				readme: "本表用于采集客商社会统一信用代码、客商属性",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('客商信息补充')\n@Index([\"客商名称代码\"], { unique: true })\nexport class 客商信息补充Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 200 })\n            projectCode: string;\n @Column({ comment: '客商名称', length: 200 })\n            客商名称: string;\n @Column({ comment: '客商名称代码', length: 200 })\n            客商名称代码: string;\n @Column({ comment: '纳税人识别号', length: 200 })\n            纳税人识别号: string;\n @Column({ comment: '客商属性', length: 200 })\n            客商属性: string;\n @Column({ comment: '客商属性代码', length: 200 })\n            客商属性代码: string;\n @Column({ comment: '备注', length: 150, nullable: true })\n            备注: string;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true
					},
					{
						name: "客商名称",
						type: "dict",
						dictId: 39,
						isNull: false,
						hasDict: true,
						dictName: "客商",
						disabled: true,
						showName: "客商名称",
						dictField: "198/201/39",
						isPrimaryCol: true
					},
					{
						name: "纳税人识别号",
						type: "string",
						isNull: false,
						hasDict: false,
						showName: "纳税人识别号",
						regularStr: "^[A-Z0-9]{18}$|^[A-Z0-9]{20}$",
						stringType: "regularStr",
						stringLength: 200
					},
					{
						name: "客商属性",
						type: "dict",
						dictId: 410,
						isLeaf: true,
						isNull: false,
						hasDict: true,
						dictName: "单位类型二级",
						disabled: true,
						parentId: 403,
						showName: "客商属性",
						dictField: "198/209/403/410",
						parentName: "单位类型一级"
					},
					{
						name: "备注",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "备注",
						stringType: "rangeLength",
						stringLength: [0, 150]
					}
				],
				tableInfo: {
					allowEdit: true
				},
				tableName: "func_客商信息补充",
				tableType: "user",
				formTypeId: 3,
				status: 1
			},
			{
				id: 1749,
				createTime: "2024-11-27 10:55:38",
				name: "询证函详情",
				readme: null,
				content:
					"import { BaseEntity } from '@cool-midway/core';\r\nimport { Column, Entity, Index } from 'typeorm';\r\n\r\n/**\r\n * 描述\r\n */\r\n@Entity('xzh_detail')\r\n@Index('idx_xzh_detail_status', ['询证函单号', '确权状态'])\r\nexport class XzhDetailEntity extends BaseEntity {\r\n  @Column({ comment: '询证函单号' })\r\n  询证函单号: string;\r\n  @Column({ comment: '所属项目代码' })\r\n  projectCode: string;\r\n  @Column({ comment: '所属项目名称' })\r\n  所属项目名称: string;\r\n  @Column({ comment: '客商名称代码' })\r\n  客商名称代码: string;\r\n  @Column({ comment: '科目' })\r\n  科目: string;\r\n  @Column({ comment: '客商名称' })\r\n  客商名称: string;\r\n  @Column({ comment: '凭证日期' })\r\n  凭证日期: string;\r\n  @Column({ comment: '凭证类别' })\r\n  凭证类别: string;\r\n  @Column({ comment: '凭证号' })\r\n  凭证号: string;\r\n  @Column({ comment: '合同代码' })\r\n  合同代码: string;\r\n  @Column({ comment: '合同名称' })\r\n  合同名称: string;\r\n  @Column({ comment: '账龄' })\r\n  账龄: number;\r\n  @Column({ comment: '账期' })\r\n  账期: string;\r\n  @Column({ comment: '责任人', nullable: true })\r\n  责任人: string;\r\n  @Column({\r\n    comment: '待确权金额',\r\n    type: 'decimal',\r\n    precision: 16,\r\n    scale: 2,\r\n  })\r\n  待确权金额: number;\r\n\r\n  @Column({\r\n    comment: '已确权金额',\r\n    type: 'decimal',\r\n    precision: 16,\r\n    scale: 2,\r\n  })\r\n  已确权金额: number;\r\n  @Column({ comment: '合同函证影像', nullable: true })\r\n  合同函证影像: string;\r\n  @Column({ comment: '确权状态', default: 0 })\r\n  确权状态: number;\r\n  @Column({ comment: '回函确认状态', default: 0 })\r\n  回函确认状态: number;\r\n  @Column({ comment: '备注', nullable: true })\r\n  备注: string;\r\n  @Column({ comment: '确权人', nullable: true })\r\n  确权人: string;\r\n}",
				colInfo: [
					{
						name: "所属项目名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "所属项目名称",
						dictField: "",
						isProject: true,
						typeDisabled: true
					},
					{
						name: "询证函单号",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "询证函单号",
						isNotShow: false,
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "客商名称",
						type: "dict",
						dictId: 39,
						isNull: true,
						hasDict: true,
						dictName: "客商",
						disabled: true,
						showName: "客商名称",
						dictField: "198/201/39",
						isNotShow: false,
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "账龄",
						type: "number",
						isNull: true,
						hasDict: false,
						showName: "账龄",
						isNotShow: false,
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "账期",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "账期",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "合同名称",
						type: "dict",
						dictId: 32,
						isNull: true,
						hasDict: true,
						dictName: "合同字典",
						disabled: true,
						showName: "合同名称",
						dictField: "198/202/399/32",
						isNotShow: false,
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "责任人",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "责任人",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "待确权金额",
						type: "number",
						isNull: true,
						hasDict: false,
						decimals: "2",
						showName: "待确权金额",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "已确权金额",
						type: "number",
						isNull: true,
						hasDict: false,
						decimals: "2",
						showName: "已确权金额",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "确权状态",
						type: "number",
						isNull: true,
						hasDict: false,
						showName: "确权状态",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "回函确认状态",
						type: "number",
						isNull: true,
						hasDict: false,
						showName: "回函确认状态",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "确权人",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "确权人",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "合同函证影像",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "合同函证影像",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "凭证日期",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "凭证日期",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "凭证类别",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "凭证类别",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "凭证号",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "凭证号",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "科目",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "科目",
						isNotShow: false,
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "备注",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "备注",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					}
				],
				tableInfo: {
					allowEdit: false
				},
				tableName: "func_xzh_detail",
				tableType: "only_view",
				formTypeId: 3,
				status: 1
			},
			{
				id: 1748,
				createTime: "2024-11-26 20:51:31",
				name: "询证函",
				readme: null,
				content:
					"import { BaseEntity } from '@cool-midway/core';\r\nimport { Column, Entity, Index } from 'typeorm';\r\n\r\n/**\r\n * 描述\r\n */\r\n@Entity('xzh_record')\r\n@Index('idx_xzh_record_status', ['询证函单号', '回函确认状态'])\r\nexport class XzhEntity extends BaseEntity {\r\n  @Column({ comment: '询证函单号' })\r\n  询证函单号: string;\r\n\r\n  @Column({ comment: '应收账款会计期' })\r\n  应收账款会计期: string;\r\n\r\n  @Column({ comment: '所属项目代码' })\r\n  projectCode: string;\r\n\r\n  @Column({ comment: '所属项目名称' })\r\n  所属项目名称: string;\r\n\r\n  @Column({ comment: '客商名称代码' })\r\n  客商名称代码: string;\r\n\r\n  @Column({ comment: '客商名称' })\r\n  客商名称: string;\r\n  @Column({\r\n    comment: '应收账款总金额',\r\n    type: 'decimal',\r\n    precision: 16,\r\n    scale: 2,\r\n    default: 0,\r\n  })\r\n  应收账款总金额: number;\r\n\r\n  @Column({\r\n    comment: '已确权总金额',\r\n    type: 'decimal',\r\n    precision: 16,\r\n    scale: 2,\r\n    default: 0,\r\n  })\r\n  已确权总金额: number;\r\n  @Column({\r\n    comment: '未回函总金额',\r\n    type: 'decimal',\r\n    precision: 16,\r\n    scale: 2,\r\n    default: 0,\r\n    nullable: true,\r\n  })\r\n  未回函总金额: number;\r\n  @Column({\r\n    comment: '已回函总金额',\r\n    type: 'decimal',\r\n    precision: 16,\r\n    scale: 2,\r\n    default: 0,\r\n  })\r\n  已回函总金额: number;\r\n\r\n  @Column({ comment: '责任人', nullable: true })\r\n  责任人: string;\r\n  @Column({ comment: '责任人邮箱', nullable: true })\r\n  责任人邮箱: string;\r\n  @Column({ comment: '责任人手机号码', nullable: true })\r\n  责任人手机号码: string;\r\n  @Column({ comment: '客商联系人', length: 100, nullable: true })\r\n  客商联系人: string;\r\n  @Column({ comment: '客商联系人邮箱', length: 100, nullable: true })\r\n  客商联系人邮箱: string;\r\n  @Column({ comment: '客商联系人手机号码', length: 100, nullable: true })\r\n  客商联系人手机号码: string;\r\n  @Column({ comment: '发函状态', default: 0 })\r\n  发函状态: number;\r\n\r\n  @Column({ comment: '询证函的电子原件', nullable: true })\r\n  询证函的电子原件: string;\r\n\r\n  @Column({ comment: '预计发函时间', nullable: true })\r\n  预计发函时间: Date;\r\n\r\n  @Column({ comment: '实际发函时间', nullable: true })\r\n  实际发函时间: Date;\r\n\r\n  @Column({ comment: '回函时间', nullable: true })\r\n  回函时间: Date;\r\n\r\n  @Column({ comment: '回函人邮箱', nullable: true })\r\n  回函人邮箱: string;\r\n\r\n  @Column({ comment: '回函确认状态', default: 0 })\r\n  回函确认状态: number;\r\n\r\n  @Column({ comment: '回函附件', nullable: true })\r\n  回函附件: string;\r\n}",
				colInfo: [
					{
						name: "所属项目名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "所属项目名称",
						dictField: "",
						isNotShow: false,
						isProject: true,
						typeDisabled: true,
						isNotShowCode: false
					},
					{
						name: "询证函单号",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "询证函单号",
						isNotShow: false,
						stringType: "rangeLength",
						stringLength: [0, 150],
						typeDisabled: true
					},
					{
						name: "客商名称",
						type: "dict",
						dictId: 39,
						isNull: true,
						hasDict: true,
						dictName: "客商",
						disabled: true,
						showName: "客商名称",
						dictField: "198/201/39",
						isNotShow: false,
						stringLength: null,
						typeDisabled: true,
						isNotShowCode: false
					},
					{
						name: "应收账款会计期",
						type: "string",
						isNull: true,
						hasDict: false,
						dateType: "YYYY-MM",
						showName: "应收账款会计期",
						stringType: "rangeLength",
						stringLength: [0, 150],
						typeDisabled: true
					},
					{
						name: "应收账款总金额",
						type: "number",
						isNull: true,
						hasDict: false,
						decimals: "2",
						showName: "应收账款总金额",
						isNotShow: false,
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "已确权总金额",
						type: "number",
						isNull: true,
						hasDict: false,
						decimals: "2",
						showName: "已确权总金额",
						isNotShow: false,
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "未回函总金额",
						type: "number",
						isNull: true,
						hasDict: false,
						decimals: "2",
						showName: "未回函总金额",
						isNotShow: false,
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "已回函总金额",
						type: "number",
						isNull: true,
						hasDict: false,
						decimals: "2",
						showName: "已回函总金额",
						isNotShow: false,
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "责任人",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "责任人",
						isNotShow: false,
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "责任人邮箱",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "责任人邮箱",
						isNotShow: false,
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "责任人手机号码",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "责任人手机号码",
						isNotShow: false,
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "发函状态",
						type: "number",
						isNull: true,
						hasDict: false,
						showName: "发函状态",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "询证函的电子原件",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "询证函的电子原件",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "预计发函时间",
						type: "date",
						isNull: true,
						hasDict: false,
						dateType: "YYYY-MM-DD HH:mm:ss",
						showName: "预计发函时间",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "实际发函时间",
						type: "date",
						isNull: true,
						hasDict: false,
						dateType: "YYYY-MM-DD HH:mm:ss",
						showName: "实际发函时间",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "回函时间",
						type: "date",
						isNull: true,
						hasDict: false,
						dateType: "YYYY-MM-DD HH:mm:ss",
						showName: "回函时间",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "回函人邮箱",
						type: "string",
						isNull: true,
						hasDict: false,
						dateType: "YYYY-MM-DD HH:mm:ss",
						showName: "回函人邮箱",
						stringType: "rangeLength",
						stringLength: [0, 150],
						typeDisabled: true
					},
					{
						name: "回函确认状态",
						type: "number",
						isNull: true,
						hasDict: false,
						showName: "回函确认状态",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "回函附件",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "回函附件",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "客商联系人",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "客商联系人",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "客商联系人邮箱",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "客商联系人邮箱",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "客商联系人手机号码",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "客商联系人手机号码",
						isNotShow: false,
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					}
				],
				tableInfo: {
					allowEdit: false
				},
				tableName: "func_xzh_record",
				tableType: "only_view",
				formTypeId: 3,
				status: 1
			},
			{
				id: 1743,
				createTime: "2024-11-13 13:22:46",
				name: "询证函预留联系信息",
				readme: "本表各单位暂定只维护一人",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('询证函预留联系信息')\n@Index([\"projectCode\"], { unique: true })\nexport class 询证函预留联系信息Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 150 })\n            projectCode: string;\n @Column({ comment: '财务联系人', length: 200 })\n            财务联系人: string;\n @Column({ comment: '财务联系人代码', length: 200 })\n            财务联系人代码: string;\n @Column({ comment: '联系电话', length: 200 })\n            联系电话: string;\n @Column({ comment: '联系地址', length: 200 })\n            联系地址: string;\n @Column({ comment: '备注', length: 200, nullable: true })\n            备注: string;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						isNull: false,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						isPrimaryCol: true,
						typeDisabled: true
					},
					{
						name: "财务联系人",
						type: "dict",
						dictId: 200,
						isNull: false,
						hasDict: true,
						dictName: "人员字典",
						disabled: true,
						showName: "财务联系人",
						dictField: "198/203/200",
						typeDisabled: true
					},
					{
						name: "联系电话",
						type: "string",
						isNull: false,
						hasDict: false,
						showName: "联系电话",
						regularStr:
							"^(?:\\+?86)?(?:\\(?\\d{1,4}\\)?[- ]?)?\\d{7,8}(?:\\d{1,4})?$|^1[3-9]\\d{9}$",
						stringType: "regularStr",
						stringLength: 200,
						typeDisabled: true
					},
					{
						name: "联系地址",
						type: "string",
						isNull: false,
						hasDict: false,
						showName: "联系地址",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "备注",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "备注",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					}
				],
				tableInfo: {
					allowEdit: false
				},
				tableName: "func_询证函预留联系信息",
				tableType: "user",
				formTypeId: 3,
				status: 1
			},
			{
				id: 174,
				createTime: "2024-09-29 15:27:19",
				name: "发函策略表维护(询证)",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\r\nimport { Index, Column, Entity } from 'typeorm';\r\n\r\n/**\r\n * 描述\r\n */\r\n@Entity('send_strategy')\r\n@Index(['projectCode'], { unique: true })\r\nexport class SendStrategyEntity extends BaseEntity {\r\n  @Column({ comment: '录入人' })\r\n  录入人: string;\r\n  @Column({ comment: '审核人', nullable: true })\r\n  审核人: string;\r\n  @Column({ comment: '审核时间', nullable: true })\r\n  审核时间: Date;\r\n  @Column({ comment: '审核状态', default: 0 })\r\n  审核状态: number;\r\n  @Column({ comment: '审核说明', nullable: true })\r\n  审核说明: string;\r\n  @Column({ comment: '项目代码' })\r\n  projectCode: string;\r\n  @Column({ comment: '项目名称' })\r\n  项目名称: string;\r\n  @Column({ comment: '客商名称' })\r\n  客商名称: string;\r\n  @Column({ comment: '客商名称代码' })\r\n  客商名称代码: string;\r\n  @Column({ comment: '发函策略名称代码' })\r\n  发函策略代码: string;\r\n  @Column({ comment: '发函策略名称' })\r\n  发函策略: string;\r\n  @Column({ comment: '发函策略周期', nullable: true })\r\n  发函策略周期: string;\r\n  @Column({ comment: '发函策略周期代码', nullable: true })\r\n  发函策略周期代码: string;\r\n  @Column({ comment: '发函策略周期间隔', nullable: true })\r\n  发函策略周期间隔: number;\r\n  @Column({ comment: '发函策略时间' })\r\n  发函策略时间: Date;\r\n  @Column({ comment: '最新编辑人', default: 'admin' })\r\n  最新编辑人: string;\r\n}\r\n",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						isPrimaryKey: true,
						typeDisabled: true
					},
					{
						name: "客商名称",
						type: "dict",
						dictId: 39,
						hasDict: true,
						dictName: "客商",
						disabled: true,
						showName: "客商名称",
						dictField: "198/201/39",
						typeDisabled: true
					},
					{
						name: "发函策略",
						type: "dict",
						dictId: 392,
						hasDict: true,
						dictName: "发函策略",
						disabled: true,
						showName: "发函策略",
						dictField: "198/201/392",
						typeDisabled: true
					},
					{
						name: "发函策略周期",
						type: "dict",
						dictId: 391,
						isNull: true,
						hasDict: true,
						dictName: "周期",
						disabled: true,
						showName: "发函策略周期",
						dictField: "198/201/391",
						isPrimaryKey: false,
						typeDisabled: true
					},
					{
						name: "发函策略周期间隔",
						type: "number",
						isNull: true,
						hasDict: false,
						decimals: "0",
						showName: "发函策略周期间隔",
						typeDisabled: true
					},
					{
						name: "发函策略时间",
						type: "date",
						hasDict: false,
						dateType: "YYYY-MM-DD HH:mm:ss",
						showName: "发函策略时间",
						typeDisabled: true
					}
				],
				tableInfo: {
					allowEdit: true
				},
				tableName: "func_send_strategy",
				tableType: "only_add",
				formTypeId: 3,
				status: 1
			},
			{
				id: 173,
				createTime: "2024-09-29 15:14:30",
				name: "客商采集表维护(询证)",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\r\nimport { Column, Entity, Index } from 'typeorm';\r\n/**\r\n * 描述\r\n */\r\n@Entity('customer_contacts')\r\nexport class CustomerContactsEntity extends BaseEntity {\r\n@Column({ comment: '录入人',nullable: true})\r\n    录入人: string;\r\n @Column({ comment:  '项目名称',length:100 })\r\n            项目名称: string;\r\n @Column({ comment:  'projectCode',length:100 })\r\n            projectCode: string;\r\n @Column({ comment:  '客商名称',length:100 })\r\n            客商名称: string;\r\n @Column({ comment:  '客商名称代码',length:100 })\r\n            客商名称代码: string;\r\n @Column({ comment:  '对接人',length:100 ,nullable: true})\r\n            对接人: string;\r\n @Column({ comment:  '对接人代码',length:100,nullable: true })\r\n            对接人代码: string;\r\n @Column({ comment:  '对接人邮箱',length:100 ,nullable: true})\r\n            对接人邮箱: string;\r\n @Column({ comment:  '对接人手机号码',length:100 ,nullable: true})\r\n            对接人手机号码: string;\r\n @Column({ comment:  '客商联系人',length:100 ,nullable: true})\r\n            客商联系人: string;\r\n @Column({ comment:  '客商联系人邮箱',length:100 ,nullable: true})\r\n            客商联系人邮箱: string;\r\n @Column({ comment:  '客商联系人手机号码',length:100 ,nullable: true})\r\n            客商联系人手机号码: string;\r\n @Column({ comment: '最新编辑人', default:'admin'})\r\n    最新编辑人: string;\r\n@Column({ comment: '审核状态', default: 0})\r\n    审核状态: number;\r\n @Column({ comment: '审核时间', nullable: true })\r\n    审核时间: Date;\r\n @Column({ comment: '审核人', nullable: true })\r\n    审核人: string;\r\n @Column({ comment: '审核说明', nullable: true })\r\n    审核说明: string;\r\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						typeDisabled: true
					},
					{
						name: "客商名称",
						type: "dict",
						dictId: 39,
						hasDict: true,
						dictName: "客商",
						disabled: true,
						showName: "客商名称",
						dictField: "198/201/39",
						typeDisabled: true
					},
					{
						name: "对接人",
						type: "dict",
						dictId: 200,
						isNull: true,
						hasDict: true,
						dictName: "人员字典",
						disabled: true,
						showName: "对接人",
						dictField: "198/203/200",
						typeDisabled: true
					},
					{
						name: "对接人邮箱",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "对接人邮箱",
						typeDisabled: true
					},
					{
						name: "对接人手机号码",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "对接人手机号码",
						typeDisabled: true
					},
					{
						name: "客商联系人",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "客商联系人",
						typeDisabled: true
					},
					{
						name: "客商联系人邮箱",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "客商联系人邮箱",
						typeDisabled: true
					},
					{
						name: "客商联系人手机号码",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "客商联系人手机号码",
						typeDisabled: true
					}
				],
				tableInfo: {
					allowEdit: false
				},
				tableName: "func_customer_contacts",
				tableType: "only_add",
				formTypeId: 3,
				status: 1
			},
			{
				id: 1765,
				createTime: "2024-12-19 15:18:16",
				name: "pzlb",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('pzlb')\n@Index([\"projectCode\"], { unique: true })\nexport class pzlbEntity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 150 })\n            projectCode: string;\n @Column({ comment: '凭证类别', length: 150 })\n            凭证类别: string;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						isNull: false,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						isPrimaryCol: true,
						isPrimaryKey: true
					},
					{
						name: "凭证类别",
						type: "string",
						isNull: false,
						hasDict: false,
						showName: "凭证类别",
						stringType: "rangeLength",
						stringLength: [0, 150]
					}
				],
				tableInfo: {
					allowEdit: false
				},
				tableName: "func_pzlb",
				tableType: "user",
				formTypeId: 1,
				status: 1
			},
			{
				id: 1756,
				createTime: "2024-12-10 13:39:45",
				name: "组织机构映射表",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('组织机构映射表')\n@Index([\"projectCode\"], { unique: true })\nexport class 组织机构映射表Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 150 })\n            projectCode: string;\n @Column({ comment: '组织机构', length: 200 })\n            组织机构: string;\n @Column({ comment: '组织机构代码', length: 200 })\n            组织机构代码: string;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						isNull: false,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						isPrimaryCol: true,
						isPrimaryKey: false
					},
					{
						name: "组织机构",
						type: "dict",
						dictId: 382,
						isNull: false,
						hasDict: true,
						dictName: "组织",
						disabled: true,
						showName: "组织机构",
						dictField: "198/204/382",
						stringLength: null
					}
				],
				tableInfo: {
					allowEdit: true
				},
				tableName: "func_组织机构映射表",
				tableType: "user",
				formTypeId: 1,
				status: 1
			},
			{
				id: 1746,
				createTime: "2024-11-19 13:04:21",
				name: "合同编号核心字与部门映射表",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('合同编号核心字与部门映射表')\n@Index([\"合同编号规则\",\"部门id\",\"部门名称\"], { unique: true })\nexport class 合同编号核心字与部门映射表Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 200 })\n            projectCode: string;\n @Column({ comment: '合同编号规则', length: 150 })\n            合同编号规则: string;\n @Column({ comment: '部门id', length: 150 })\n            部门id: string;\n @Column({ comment: '部门名称', length: 150 })\n            部门名称: string;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true
					},
					{
						name: "合同编号规则",
						type: "string",
						isNull: false,
						hasDict: false,
						showName: "合同编号规则",
						stringType: "rangeLength",
						isPrimaryCol: true,
						stringLength: [0, 150]
					},
					{
						name: "部门id",
						type: "string",
						isNull: false,
						hasDict: false,
						showName: "部门id",
						stringType: "rangeLength",
						isPrimaryCol: true,
						stringLength: null
					},
					{
						name: "部门名称",
						type: "string",
						isNull: false,
						hasDict: false,
						showName: "部门名称",
						stringType: "rangeLength",
						isPrimaryCol: true,
						stringLength: null
					}
				],
				tableInfo: {
					allowEdit: true
				},
				tableName: "func_合同编号核心字与部门映射表",
				tableType: "user",
				formTypeId: 1,
				status: 1
			},
			{
				id: 1745,
				createTime: "2024-11-18 08:46:04",
				name: "子分公司在集团部门树形下的名称映射",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('子分公司在集团部门树形下的名称映射')\n@Index([\"projectCode\"], { unique: true })\nexport class 子分公司在集团部门树形下的名称映射Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 150 })\n            projectCode: string;\n @Column({ comment: '部门代码', length: 150 })\n            部门代码: string;\n @Column({ comment: '部门名称', length: 200 })\n            部门名称: string;\n @Column({ comment: '子分公司项目代码', length: 200 })\n            子分公司项目代码: string;\n @Column({ comment: '子分公司项目名称', length: 200 })\n            子分公司项目名称: string;\n @Column({ comment: '备注', length: 200, nullable: true })\n            备注: string;\n @Column({ comment: '集团公司项目代码', length: 200 })\n            集团公司项目代码: string;\n @Column({ comment: '集团公司项目名称', length: 200 })\n            集团公司项目名称: string;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						isNull: false,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						isPrimaryCol: true,
						typeDisabled: true
					},
					{
						name: "部门代码",
						type: "string",
						isNull: false,
						hasDict: false,
						showName: "部门代码",
						stringType: "rangeLength",
						stringLength: [0, 150],
						typeDisabled: true
					},
					{
						name: "部门名称",
						type: "string",
						isNull: false,
						hasDict: false,
						showName: "部门名称",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "子分公司项目代码",
						type: "string",
						isNull: false,
						hasDict: false,
						showName: "子分公司项目代码",
						stringType: "rangeLength",
						isPrimaryKey: false,
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "子分公司项目名称",
						type: "string",
						isNull: false,
						hasDict: false,
						showName: "子分公司项目名称",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "备注",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "备注",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "集团公司项目代码",
						type: "string",
						isNull: false,
						hasDict: false,
						showName: "集团公司项目代码",
						stringType: "rangeLength",
						stringLength: null
					},
					{
						name: "集团公司项目名称",
						type: "string",
						isNull: false,
						hasDict: false,
						showName: "集团公司项目名称",
						stringType: "rangeLength",
						stringLength: null
					}
				],
				tableInfo: {
					allowEdit: true
				},
				tableName: "func_子分公司在集团部门树形下的名称映射",
				tableType: "user",
				formTypeId: 1,
				status: 1
			},
			{
				id: 1744,
				createTime: "2024-11-15 13:47:17",
				name: "纳税人识别号采集表",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('纳税人识别号采集表')\n@Index([\"projectCode\"], { unique: true })\nexport class 纳税人识别号采集表Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 150 })\n            projectCode: string;\n @Column({ comment: '纳税人识别号', length: 200 })\n            纳税人识别号: string;\n @Column({ comment: '备注', length: 200, nullable: true })\n            备注: string;\n @Column({ comment: '公司名称', length: 200 })\n            公司名称: string;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						isNull: false,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						isPrimaryCol: true,
						typeDisabled: true
					},
					{
						name: "纳税人识别号",
						type: "string",
						isNull: false,
						hasDict: false,
						showName: "纳税人识别号",
						regularStr: "^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$",
						stringType: "regularStr",
						stringLength: 200,
						typeDisabled: true
					},
					{
						name: "备注",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "备注",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "公司名称",
						type: "string",
						isNull: false,
						hasDict: false,
						showName: "公司名称",
						regularStr:
							"^[A-Za-z0-9()（）\\u4E00-\\u9FA5]+(?:[-_' .&()（）\\u4E00-\\u9FA5A-Za-z0-9]+)*$",
						stringType: "regularStr",
						stringLength: 200
					}
				],
				tableInfo: {
					allowEdit: true
				},
				tableName: "func_纳税人识别号采集表",
				tableType: "user",
				formTypeId: 1,
				status: 1
			},
			{
				id: 1742,
				createTime: "2024-11-12 21:10:24",
				name: "演示表",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('演示表')\nexport class 演示表Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 150 })\n            projectCode: string;\n @Column({ comment: '合同项目', length: 200 })\n            合同项目: string;\n @Column({ comment: '合同项目代码', length: 150 })\n            合同项目代码: string;\n @Column({ comment: '合同类型', length: 100, nullable: true })\n            合同类型: string;\n @Column({ comment: '领域', length: 100, nullable: true })\n            领域: string;\n @Column({ comment: '合同状态', length: 100, nullable: true })\n            合同状态: string;\n @Column({ comment: '项目所在市', length: 1500, nullable: true })\n            项目所在市: string;\n @Column({ comment: '登记人邮箱', length: 200, nullable: true })\n            登记人邮箱: string;\n @Column({ comment:  '金额',nullable: true,type:'decimal', precision: 12, scale:2})\n               金额: number;\n @Column({ comment: ' 单价',nullable: true,precision: 12})\n               单价: number;\n @Column({\n\t\t\t\t\ttype: \"int\",\n\t\t\t\t\tgeneratedType: \"VIRTUAL\",\n\t\t\t\t\tasExpression: `金额 *单价`,\n\t\t\t\t})\n\t\t\t\t总价: number;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						isNull: false,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						isPrimaryKey: true,
						typeDisabled: true,
						relatedTables: [
							{
								relatedTable: 196,
								relatedTableCol: "项目名称"
							}
						]
					},
					{
						name: "合同项目",
						type: "dict",
						dictId: 32,
						isNull: false,
						hasDict: true,
						dictName: "合同字典",
						disabled: true,
						showName: "合同项目",
						dictField: "198/202/32",
						isNotShow: true,
						isPrimaryKey: true,
						typeDisabled: true,
						relatedTables: [
							{
								relatedTable: 196,
								relatedTableCol: "合同项目名称"
							}
						]
					},
					{
						name: "合同类型",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "合同类型",
						isNotShow: true,
						stringType: "valueLength",
						relatedTable: 196,
						stringLength: 100,
						typeDisabled: true,
						relatedColType: "string",
						relatedShowName: "合同类型",
						relatedTableCol: "合同类型"
					},
					{
						name: "领域",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "领域",
						stringType: "valueLength",
						relatedTable: 196,
						stringLength: 100,
						typeDisabled: true,
						relatedColType: "string",
						relatedShowName: "领域",
						relatedTableCol: "领域"
					},
					{
						name: "合同状态",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "合同状态",
						stringType: "valueLength",
						relatedTable: 196,
						stringLength: 100,
						typeDisabled: true,
						relatedColType: "string",
						relatedShowName: "合同状态",
						relatedTableCol: "合同状态"
					},
					{
						name: "项目所在市",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "项目所在市",
						stringType: "anyLength",
						relatedTable: 196,
						stringLength: null,
						typeDisabled: true,
						relatedColType: "string",
						relatedShowName: "项目所在市",
						relatedTableCol: "项目所在市"
					},
					{
						name: "登记人邮箱",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "登记人邮箱",
						regularStr:
							"^[A-Za-z0-9\\u4e00-\\u9fa5]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$",
						stringType: "regularStr",
						stringLength: 200,
						typeDisabled: true
					},
					{
						name: "金额",
						type: "number",
						isNull: true,
						hasDict: false,
						decimals: "2",
						showName: "金额",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "单价",
						type: "number",
						isNull: true,
						hasDict: false,
						showName: "单价",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "总价",
						type: "calculatedCol",
						isNull: true,
						sqlStr: "﻿`金额`﻿ *`单价`﻿ ﻿﻿",
						hasDict: false,
						showName: "总价",
						stringLength: null,
						typeDisabled: true,
						calculateType: "number"
					}
				],
				tableInfo: {
					allowEdit: false
				},
				tableName: "func_演示表",
				tableType: "user",
				formTypeId: 1,
				status: 1
			},
			{
				id: 1740,
				createTime: "2024-11-12 13:23:48",
				name: "应收账款存量压降目标采集表",
				readme: "本表每年采集一次",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('应收账款存量压降目标采集表')\n@Index([\"projectCode\",\"年\"], { unique: true })\nexport class 应收账款存量压降目标采集表Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 150 })\n            projectCode: string;\n @Column({ comment: '公司简称', length: 1500, nullable: true })\n            公司简称: string;\n @Column({ comment:  '年', type: 'date' })\n                年: Date;\n @Column({ comment:  '存量压降目标',nullable: true,type:'decimal', precision: 12, scale:2})\n               存量压降目标: number;\n @Column({ comment: '备注', length: 200, nullable: true })\n            备注: string;\n @Column({ comment: ' 序号',nullable: true,precision: 12})\n               序号: number;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						isNull: false,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						isPrimaryCol: true,
						isPrimaryKey: true,
						relatedTables: [
							{
								relatedTable: 175,
								relatedTableCol: "单位"
							}
						]
					},
					{
						name: "公司简称",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "公司简称",
						stringType: "anyLength",
						isPrimaryCol: false,
						relatedTable: 175,
						stringLength: null,
						relatedColType: "string",
						relatedShowName: "公司简称",
						relatedTableCol: "公司简称"
					},
					{
						name: "年",
						type: "date",
						isNull: false,
						hasDict: false,
						dateType: "YYYY",
						showName: "年",
						isPrimaryCol: true,
						stringLength: null
					},
					{
						name: "存量压降目标",
						type: "number",
						isNull: true,
						hasDict: false,
						decimals: "2",
						showName: "存量压降目标",
						stringLength: null
					},
					{
						name: "备注",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "备注",
						stringType: "rangeLength",
						stringLength: null
					},
					{
						name: "序号",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "序号",
						stringType: "rangeLength",
						relatedTable: 175,
						stringLength: null,
						relatedColType: "number",
						relatedShowName: "顺序",
						relatedTableCol: "顺序"
					}
				],
				tableInfo: {
					allowEdit: false
				},
				tableName: "func_应收账款存量压降目标采集表",
				tableType: "user",
				formTypeId: 1,
				status: 1
			},
			{
				id: 1704,
				createTime: "2024-11-05 13:51:35",
				name: "总包项目收入利润调整表",
				readme: "本表每月采集一次",
				content:
					"import { BaseEntity } from '@cool-midway/core';\r\nimport { Column, Entity, Index } from 'typeorm';\r\n/**\r\n * 描述\r\n */\r\n@Entity('总包项目收入利润调整表')\r\n@Index([\"projectCode\",\"类别代码\",\"采集月份\"], { unique: true })\r\nexport class 总包项目收入利润调整表Entity extends BaseEntity {\r\n@Column({ comment: '录入人',nullable: true})\r\n    录入人: string;\r\n @Column({ comment: '项目名称', length: 200 })\r\n            项目名称: string;\r\n @Column({ comment: 'projectCode', length: 150 })\r\n            projectCode: string;\r\n @Column({ comment: '公司简称', length: 1500, nullable: true })\r\n            公司简称: string;\r\n @Column({ comment: '类别', length: 200, nullable: true })\r\n            类别: string;\r\n @Column({ comment: '类别代码', length: 200, nullable: true })\r\n            类别代码: string;\r\n @Column({ comment:  '采集月份', type: 'date', nullable: true })\r\n                采集月份: Date;\r\n @Column({ comment:  '金额',nullable: true,type:'decimal', precision: 12, scale:2})\r\n               金额: number;\r\n @Column({ comment: '备注', length: 200, nullable: true })\r\n            备注: string;\r\n@Column({ comment: '审核状态', default: 0})\r\n    审核状态: number;\r\n @Column({ comment: '审核时间', nullable: true })\r\n    审核时间: Date;\r\n @Column({ comment: '审核人', nullable: true })\r\n    审核人: string;\r\n @Column({ comment: '审核说明', nullable: true })\r\n    审核说明: string;\r\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						isNull: false,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "公司名称",
						dictField: "",
						isProject: true,
						isPrimaryCol: true,
						isPrimaryKey: true,
						typeDisabled: true,
						relatedTables: [
							{
								relatedTable: 175,
								relatedTableCol: "单位"
							}
						]
					},
					{
						name: "公司简称",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "公司简称",
						stringType: "anyLength",
						relatedTable: 175,
						stringLength: 150,
						typeDisabled: true,
						relatedColType: "string",
						relatedShowName: "公司简称",
						relatedTableCol: "公司简称"
					},
					{
						name: "类别",
						type: "dict",
						dictId: 397,
						isLeaf: false,
						isNull: true,
						hasDict: true,
						dictName: "总包项目收入利润字典",
						disabled: true,
						showName: "类别",
						dictField: "198/209/397",
						isPrimaryCol: true,
						typeDisabled: true
					},
					{
						name: "采集月份",
						type: "date",
						isNull: true,
						hasDict: false,
						dateType: "YYYY-MM",
						showName: "采集月份",
						isPrimaryCol: true,
						typeDisabled: true
					},
					{
						name: "金额",
						type: "number",
						isNull: true,
						hasDict: false,
						decimals: "2",
						showName: "金额",
						typeDisabled: true
					},
					{
						name: "备注",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "备注",
						stringType: "valueLength",
						stringLength: 150,
						typeDisabled: true
					}
				],
				tableInfo: {
					allowEdit: false
				},
				tableName: "func_总包项目收入利润调整表",
				tableType: "user",
				formTypeId: 1,
				status: 1
			},
			{
				id: 192,
				createTime: "2024-10-30 13:50:25",
				name: "测试专用表单",
				readme: "测试单据设计、录入功能用",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('测试专用表单')\nexport class 测试专用表单Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 150 })\n            projectCode: string;\n @Column({ comment: '客商名称', length: 200 })\n            客商名称: string;\n @Column({ comment: '客商名称代码', length: 150 })\n            客商名称代码: string;\n @Column({ comment: '纳税人识别号', length: 1500, nullable: true })\n            纳税人识别号: string;\n @Column({ comment: '合同项目名称', length: 200 })\n            合同项目名称: string;\n @Column({ comment: '合同项目名称代码', length: 150 })\n            合同项目名称代码: string;\n @Column({ comment: '合同状态', length: 100, nullable: true })\n            合同状态: string;\n @Column({ comment: '领域', length: 100, nullable: true })\n            领域: string;\n @Column({ comment: '细分领域', length: 100, nullable: true })\n            细分领域: string;\n @Column({ comment: '主体院', length: 100, nullable: true })\n            主体院: string;\n @Column({ comment: '课题负责人', length: 1500, nullable: true })\n            课题负责人: string;\n @Column({ comment:  '立项金额',nullable: true,type:'decimal', precision: 12, scale:2})\n               立项金额: number;\n @Column({ comment: '研发项目类别', length: 1500, nullable: true })\n            研发项目类别: string;\n @Column({ comment: '合同金额', length: 1500, nullable: true })\n            合同金额: string;\n @Column({\n\t\t\ttype: \"varchar\",\n\t\t\tgeneratedType: \"VIRTUAL\",\n\t\t\tasExpression: `left( 纳税人识别号,10 )`,\n\t\t\t})\n\t\t\t测试金额: string;\n @Column({ comment: '姓名', length: 200 })\n            姓名: string;\n @Column({ comment: '身份证号', length: 200 })\n            身份证号: string;\n @Column({ comment: '研发项目名称', length: 200 })\n            研发项目名称: string;\n @Column({ comment: '研发项目名称代码', length: 150 })\n            研发项目名称代码: string;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						isPrimaryKey: true,
						typeDisabled: true,
						relatedTables: [
							{
								relatedTable: 196,
								relatedTableCol: "项目名称"
							}
						]
					},
					{
						name: "客商名称",
						type: "dict",
						dictId: 39,
						hasDict: true,
						dictName: "客商",
						disabled: true,
						showName: "客商名称",
						dictField: "198/201/39",
						isPrimaryKey: true,
						typeDisabled: true,
						relatedTables: [
							{
								relatedTable: 177,
								relatedTableCol: "单位名称"
							}
						]
					},
					{
						name: "纳税人识别号",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "纳税人识别号",
						stringType: "anyLength",
						relatedTable: 177,
						typeDisabled: true,
						relatedColType: "string",
						relatedShowName: "纳税人识别号",
						relatedTableCol: "纳税人识别号"
					},
					{
						name: "合同项目名称",
						type: "dict",
						dictId: 32,
						hasDict: true,
						dictName: "合同字典",
						disabled: true,
						showName: "合同项目名称",
						dictField: "198/202/32",
						isPrimaryKey: true,
						typeDisabled: true,
						relatedTables: [
							{
								relatedTable: 196,
								relatedTableCol: "合同项目名称"
							}
						]
					},
					{
						name: "合同状态",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "合同状态",
						stringType: "valueLength",
						relatedTable: 196,
						stringLength: 100,
						typeDisabled: true,
						relatedColType: "string",
						relatedShowName: "合同状态",
						relatedTableCol: "合同状态"
					},
					{
						name: "领域",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "领域",
						stringType: "valueLength",
						relatedTable: 196,
						stringLength: 100,
						typeDisabled: true,
						relatedColType: "string",
						relatedShowName: "领域",
						relatedTableCol: "领域"
					},
					{
						name: "细分领域",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "细分领域",
						stringType: "valueLength",
						relatedTable: 196,
						stringLength: 100,
						typeDisabled: true,
						relatedColType: "string",
						relatedShowName: "领域细分",
						relatedTableCol: "领域细分"
					},
					{
						name: "主体院",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "主体院",
						stringType: "valueLength",
						relatedTable: 196,
						stringLength: 100,
						typeDisabled: true,
						relatedColType: "string",
						relatedShowName: "主体院",
						relatedTableCol: "主体院"
					},
					{
						name: "课题负责人",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "课题负责人",
						stringType: "anyLength",
						relatedTable: 176,
						typeDisabled: true,
						relatedColType: "string",
						relatedShowName: "课题负责人",
						relatedTableCol: "课题负责人"
					},
					{
						name: "立项金额",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						decimals: "2",
						showName: "立项金额",
						stringType: "anyLength",
						relatedTable: 176,
						typeDisabled: true,
						relatedColType: "number",
						relatedShowName: "立项金额",
						relatedTableCol: "立项金额"
					},
					{
						name: "研发项目类别",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "研发项目类别",
						stringType: "anyLength",
						relatedTable: 176,
						typeDisabled: true,
						relatedColType: "string",
						relatedShowName: "研发项目类别",
						relatedTableCol: "研发项目类别"
					},
					{
						name: "合同金额",
						type: "relatedCol",
						isNull: true,
						hasDict: false,
						showName: "合同金额",
						stringType: "anyLength",
						relatedTable: 177,
						typeDisabled: true,
						relatedColType: "string",
						relatedShowName: "注册资金",
						relatedTableCol: "注册资金"
					},
					{
						name: "测试金额",
						type: "calculatedCol",
						sqlStr: "﻿﻿left﻿( `纳税人识别号`,10 ) ﻿﻿",
						hasDict: false,
						decimals: "2",
						showName: "测试金额",
						typeDisabled: true,
						calculateType: "string"
					},
					{
						name: "姓名",
						type: "string",
						hasDict: false,
						showName: "姓名",
						stringType: "rangeLength",
						stringLength: null,
						typeDisabled: true
					},
					{
						name: "身份证号",
						type: "string",
						hasDict: false,
						showName: "身份证号",
						regularStr:
							"^\\d{6}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$",
						stringType: "regularStr",
						typeDisabled: true
					},
					{
						name: "研发项目名称",
						type: "dict",
						dictId: 33,
						isLeaf: true,
						isNull: false,
						hasDict: true,
						dictName: "研发项目",
						disabled: true,
						parentId: 33,
						showName: "研发项目名称",
						dictField: "198/210/33",
						parentName: "研发项目",
						isPrimaryKey: true,
						typeDisabled: true,
						relatedTables: [
							{
								relatedTable: 176,
								relatedTableCol: "研发项目名称"
							}
						]
					}
				],
				tableInfo: {
					allowEdit: false
				},
				tableName: "func_测试专用表单",
				tableType: "user",
				formTypeId: 1,
				status: 3
			},
			{
				id: 170,
				createTime: "2024-10-08 09:25:22",
				name: "基本运营指标目标值",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('基本运营指标目标值')\n@Index([\"基本营运指标代码\",\"报表单位代码\",\"年\"], { unique: true })\nexport class 基本运营指标目标值Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 200 })\n            projectCode: string;\n @Column({ comment: '基本营运指标', length: 200 })\n            基本营运指标: string;\n @Column({ comment: '基本营运指标代码', length: 200 })\n            基本营运指标代码: string;\n @Column({ comment: '报表单位', length: 200 })\n            报表单位: string;\n @Column({ comment: '报表单位代码', length: 200 })\n            报表单位代码: string;\n @Column({ comment:  '年', type: 'date' })\n                年: Date;\n @Column({ comment:  '目标值',type:'decimal', precision: 12, scale:2})\n               目标值: number;\n @Column({ comment:  '分值',type:'decimal', precision: 12, scale:2})\n               分值: number;\n @Column({ comment:  '插值法与归零的临界值',type:'decimal', precision: 12, scale:2})\n               插值法与归零的临界值: number;\n @Column({ comment: '备注', length: 1500, nullable: true })\n            备注: string;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						typeDisabled: true
					},
					{
						name: "基本营运指标",
						type: "dict",
						dictId: 374,
						hasDict: true,
						dictName: "管控类小类",
						disabled: true,
						showName: "基本营运指标",
						dictField: "198/209/40/374",
						isPrimaryCol: true,
						typeDisabled: true
					},
					{
						name: "报表单位",
						type: "dict",
						dictId: 41,
						hasDict: true,
						dictName: "项目（报表）",
						disabled: true,
						showName: "报表单位",
						dictField: "198/204/41",
						isPrimaryCol: true,
						typeDisabled: true
					},
					{
						name: "年",
						type: "date",
						hasDict: false,
						dateType: "YYYY",
						showName: "年",
						isPrimaryCol: true,
						typeDisabled: true
					},
					{
						name: "目标值",
						type: "number",
						hasDict: false,
						decimals: "2",
						showName: "目标值",
						typeDisabled: true
					},
					{
						name: "分值",
						type: "number",
						hasDict: false,
						decimals: "2",
						showName: "分值",
						typeDisabled: true
					},
					{
						name: "插值法与归零的临界值",
						type: "number",
						hasDict: false,
						decimals: "2",
						showName: "插值法与归零的临界值",
						typeDisabled: true
					},
					{
						name: "备注",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "备注",
						stringType: "anyLength",
						stringLength: null,
						typeDisabled: true
					}
				],
				tableInfo: {
					allowEdit: false
				},
				tableName: "func_基本运营指标目标值",
				tableType: "user",
				formTypeId: 1,
				status: 1
			},
			{
				id: 118,
				createTime: "2024-09-14 10:36:49",
				name: "三金采集表",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('三金采集表')\nexport class 三金采集表Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '单位全称', length: 200 })\n            单位全称: string;\n @Column({ comment: 'projectCode', length: 200 })\n            projectCode: string;\n @Column({ comment: '部门全称', length: 200 })\n            部门全称: string;\n @Column({ comment: '部门全称代码', length: 200 })\n            部门全称代码: string;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "单位全称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "单位全称",
						dictField: "",
						isProject: true,
						typeDisabled: true
					},
					{
						name: "部门全称",
						type: "dict",
						dictId: 378,
						isLeaf: true,
						hasDict: true,
						dictName: "二级部门",
						disabled: true,
						parentId: 199,
						showName: "部门全称",
						dictField: "198/204/21/199/378",
						parentName: "部门",
						typeDisabled: true
					}
				],
				tableInfo: {
					allowEdit: false
				},
				tableName: "func_三金采集表",
				tableType: "user",
				formTypeId: 1,
				status: 1
			},
			{
				id: 111,
				createTime: "2024-09-13 11:02:45",
				name: "指标表",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('指标表')\nexport class 指标表Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 200 })\n            projectCode: string;\n @Column({ comment: '指标大类', length: 200 })\n            指标大类: string;\n @Column({ comment: '指标大类代码', length: 200 })\n            指标大类代码: string;\n @Column({ comment: '指标名称', length: 200 })\n            指标名称: string;\n @Column({ comment: '指标名称代码', length: 200 })\n            指标名称代码: string;\n @Column({ comment:  '年', type: 'date' })\n                年: Date;\n @Column({ comment:  '数值',type:'decimal', precision: 12, scale:2})\n               数值: number;\n @Column({ comment: '备注', length: 1500, nullable: true })\n            备注: string;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						typeDisabled: true
					},
					{
						name: "指标大类",
						type: "dict",
						dictId: 40,
						hasDict: true,
						dictName: "管控类大类",
						disabled: true,
						showName: "指标大类",
						dictField: "198/209/40",
						typeDisabled: true
					},
					{
						name: "指标名称",
						type: "dict",
						dictId: 374,
						hasDict: true,
						dictName: "管控类小类",
						disabled: true,
						showName: "指标名称",
						dictField: "198/209/40/374",
						typeDisabled: true
					},
					{
						name: "年",
						type: "date",
						hasDict: false,
						dateType: "YYYY",
						showName: "年",
						typeDisabled: true
					},
					{
						name: "数值",
						type: "number",
						hasDict: false,
						decimals: "2",
						showName: "数值",
						typeDisabled: true
					},
					{
						name: "备注",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "备注",
						stringType: "anyLength",
						typeDisabled: true
					}
				],
				tableInfo: {
					allowEdit: false
				},
				tableName: "func_指标表",
				tableType: "user",
				formTypeId: 1,
				status: 1
			},
			{
				id: 112,
				createTime: "2024-09-12 11:28:27",
				name: "所得税税率表",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('所得税税率表')\n@Index([\"项目名称_报表代码\",\"税目大类代码\",\"税收小类代码\"], { unique: true })\nexport class 所得税税率表Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '项目名称', length: 200 })\n            项目名称: string;\n @Column({ comment: 'projectCode', length: 200 })\n            projectCode: string;\n @Column({ comment: '项目名称_报表', length: 200 })\n            项目名称_报表: string;\n @Column({ comment: '项目名称_报表代码', length: 200 })\n            项目名称_报表代码: string;\n @Column({ comment: '税目大类', length: 200 })\n            税目大类: string;\n @Column({ comment: '税目大类代码', length: 200 })\n            税目大类代码: string;\n @Column({ comment: '税收小类', length: 200 })\n            税收小类: string;\n @Column({ comment: '税收小类代码', length: 200 })\n            税收小类代码: string;\n @Column({ comment:  '税率',type:'decimal', precision: 12, scale:2})\n               税率: number;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "项目名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "项目名称",
						dictField: "",
						isProject: true,
						typeDisabled: true
					},
					{
						name: "项目名称_报表",
						type: "dict",
						dictId: 41,
						hasDict: true,
						dictName: "项目（报表）",
						disabled: true,
						showName: "项目名称_报表",
						dictField: "198/204/41",
						isPrimaryCol: true,
						typeDisabled: true
					},
					{
						name: "税目大类",
						type: "dict",
						dictId: 43,
						hasDict: true,
						dictName: "一级税收类别",
						disabled: true,
						showName: "税目大类",
						dictField: "198/207/43",
						isPrimaryCol: true,
						typeDisabled: true
					},
					{
						name: "税收小类",
						type: "dict",
						dictId: 371,
						hasDict: true,
						dictName: "二级税收类别",
						disabled: true,
						showName: "税收小类",
						dictField: "198/207/43/371",
						isPrimaryCol: true,
						typeDisabled: true
					},
					{
						name: "税率",
						type: "number",
						hasDict: false,
						decimals: 2,
						showName: "税率",
						isPercent: true,
						typeDisabled: true
					}
				],
				tableInfo: {
					allowEdit: false
				},
				tableName: "func_所得税税率表",
				tableType: "user",
				formTypeId: 1,
				status: 1
			},
			{
				id: 108,
				createTime: "2024-09-12 11:11:17",
				name: "财务专用章",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\r\nimport { Column, Entity,Index } from 'typeorm';\r\n\r\n/**\r\n * 描述\r\n */\r\n@Entity('财务专用章')\r\n@Index([\"projectCode\",\"印章类型\"], { unique: true })\r\nexport class 财务专用章Entity extends BaseEntity {\r\n @Column({ comment: '录入人',nullable: true})\r\n    录入人: string;\r\n @Column({ comment: 'xxx',length:500 })\r\n            单位名称: string;\r\n @Column({ comment: 'xxx',length:200 })\r\n            projectCode: string;\r\n @Column({ comment: 'xxx',length:500 })\r\n            印章类型: string;\r\n @Column({ comment: 'xxx',length:200 })\r\n            印章类型代码: string;\r\n @Column({ comment: 'xxx',length:200 })\r\n            印章影像: string;\r\n @Column({ comment: 'xxx',length:1000 ,nullable: true})\r\n            备注: string;\r\n@Column({ comment: '审核状态', default: 0})\r\n    审核状态: number;\r\n @Column({ comment: '审核时间', nullable: true })\r\n    审核时间: Date;\r\n @Column({ comment: '审核人', nullable: true })\r\n    审核人: string;\r\n @Column({ comment: '审核说明', nullable: true })\r\n    审核说明: string;\r\n}",
				colInfo: [
					{
						name: "单位名称",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "单位名称",
						dictField: "",
						isProject: true,
						isPrimaryCol: true
					},
					{
						name: "印章类型",
						type: "dict",
						dictId: 38,
						hasDict: true,
						dictName: "印章类型",
						disabled: true,
						showName: "印章类型",
						dictField: "198/208/38",
						isPrimaryCol: true
					},
					{
						name: "印章影像",
						type: "picture",
						hasDict: false,
						showName: "印章影像"
					},
					{
						name: "备注",
						type: "string",
						isNull: true,
						hasDict: false,
						showName: "备注"
					}
				],
				tableInfo: {
					allowEdit: true
				},
				tableName: "func_财务专用章",
				tableType: "only_add",
				formTypeId: 1,
				status: 1
			},
			{
				id: 105,
				createTime: "2024-09-11 11:14:52",
				name: "坏账计提比例",
				readme: "",
				content:
					"import { BaseEntity } from '@cool-midway/core';\nimport { Column, Entity, Index } from 'typeorm';\n/**\n * 描述\n */\n@Entity('坏账计提比例')\n@Index([\"projectCode\",\"客商类型代码\",\"账龄代码\"], { unique: true })\nexport class 坏账计提比例Entity extends BaseEntity {\n@Column({ comment: '录入人',nullable: true})\n    录入人: string;\n @Column({ comment: '坏账计提比例', length: 200 })\n            坏账计提比例: string;\n @Column({ comment: 'projectCode', length: 150 })\n            projectCode: string;\n @Column({ comment: '客商类型', length: 200 })\n            客商类型: string;\n @Column({ comment: '客商类型代码', length: 200 })\n            客商类型代码: string;\n @Column({ comment: '账龄', length: 200 })\n            账龄: string;\n @Column({ comment: '账龄代码', length: 200 })\n            账龄代码: string;\n @Column({ comment:  '比例',type:'decimal', precision: 12, scale:2})\n               比例: number;\n@Column({ comment: '审核状态', default: 0})\n    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })\n    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })\n    审核人: string;\n @Column({ comment: '审核说明', nullable: true })\n    审核说明: string;\n}",
				colInfo: [
					{
						name: "坏账计提比例",
						type: "dict",
						dictId: 21,
						hasDict: true,
						dictName: "项目表",
						disabled: true,
						showName: "坏账计提比例",
						dictField: "",
						isProject: true,
						isPrimaryCol: true,
						typeDisabled: true
					},
					{
						name: "客商类型",
						type: "dict",
						dictId: 410,
						isLeaf: true,
						isNull: false,
						hasDict: true,
						dictName: "单位类型二级",
						disabled: true,
						parentId: 403,
						showName: "客商类型",
						dictField: "198/209/403/410",
						parentName: "单位类型一级",
						isPrimaryCol: true,
						typeDisabled: true
					},
					{
						name: "账龄",
						type: "dict",
						dictId: 31,
						hasDict: true,
						dictName: "账龄",
						disabled: true,
						showName: "账龄",
						dictField: "198/209/31",
						isPrimaryCol: true,
						typeDisabled: true
					},
					{
						name: "比例",
						type: "number",
						hasDict: false,
						decimals: "2",
						showName: "比例",
						typeDisabled: true
					}
				],
				tableInfo: {
					allowEdit: true
				},
				tableName: "func_坏账计提比例",
				tableType: "user",
				formTypeId: 1,
				status: 1
			}
		]
	);
	const getFormListData = () => {
		return formListData.value;
	};
	const detailFormListData = (id: string) => {
		console.log("id", id);
		const data = formListData.value.find((item: any) => item.id == id);
		console.log("data", data);
		return data;
	};
	const editFormListData = (data: any) => {
		const index = formListData.value.findIndex((item: any) => item.id == data.id);
		console.log("index", index, data);
		if (index === -1) {
			console.error("未找到对应的流程表单数据，id:", data.id);
			return;
		}
		if (data.flow) {
			formListData.value[index].flow = data.flow;
			formListData.value[index].flowID = data.flow.id;
			formListData.value[index].flowName = data.flow.flowName;
		}
		if (data.flowJson) {
			formListData.value[index].flow.flowJson = data.flowJson;
		}
		storage.set("form_list_data", formListData.value);
	};
	const delFlowFormListData = (id: string) => {
		const index = formListData.value.findIndex((item: any) => item.id == id);
		console.log("index", index);
		if (index !== -1) {
			delete formListData.value[index].flow;
			delete formListData.value[index].flowID;
			delete formListData.value[index].flowName;
			storage.set("form_list_data", formListData.value);
		}
	};
	return {
		formListData,
		getFormListData,
		editFormListData,
		detailFormListData,
		delFlowFormListData
	};
});
