import { useFlow } from "./flow";
import { useFormFlow } from "./formFlow";
import { useMyTask } from "./myTask";
import { useFormListData } from "./formList";
export function useStore() {
	const flowStore = useFlow();
	const formFlowStore = useFormFlow();
	const myTaskStore = useMyTask();
	const formListStore = useFormListData();
	return {
		flowStore,
		formFlowStore,
		myTaskStore,
		formListStore
	};
}
