import { defineStore } from "pinia";
import { ref } from "vue";
import { storage } from "/@/cool/utils";

export const useMyTask = defineStore("myTaskStore", function () {
	const myTaskData = ref(
		storage.get("my_task_data") || [
			{
				id: "1881237082605658114",
				createTime: "2025-01-20 15:07:27",
				updateTime: "2025-01-20 15:07:27",
				tenantId: "1",
				delFlag: "0",
				definitionId: "1880202021202616322",
				flowName: "流程1",
				flowCode: "leave",
				version: "2",
				businessId: "1",
				nodeType: 1,
				nodeCode: "f1c63412-5710-446d-a5ac-f441f37a22c2",
				nodeName: "审批人1",
				variable:
					'{"@type":"java.util.LinkedHashMap","defId":"1880202021202616322","formData":{"@type":"java.util.LinkedHashMap","type":"2","times":["2024-12-31T16:00:00.000Z","2025-01-17T16:00:00.000Z"],"days":9,"reason":"","id":"ecc8f093-a244-42e8-8807-42d75f0c4a0b"}}',
				flowStatus: 1,
				createBy: "1",
				initiatorName: "超管",
				formCustom: "Y",
				formPath: "leave.vue",
				ext: null,
				formData: {
					type: "2",
					times: ["2024-12-31T16:00:00.000Z", "2025-01-17T16:00:00.000Z"],
					days: 9,
					reason: "",
					id: "ecc8f093-a244-42e8-8807-42d75f0c4a0b"
				},
				flowJson:
					'{"nodeId":"801e3c73-e665-4f6f-8ea7-d4de0927214c","nodeType":"start","nodeName":"开始节点","value":"所有人","properties":{},"childNode":{"nodeId":"f1c63412-5710-446d-a5ac-f441f37a22c2","nodeName":"审批人1","nodeType":"between","value":"admin","type":"between","properties":{"nodeRatioType":"1","backType":"1","nodeRatio":0,"permissions":["user"],"combination":{"role":[],"user":[{"id":"1814307624207470594","name":"seven"}]},"emptyApprove":{"type":"AUTO","value":[]},"value":"seven","buttons":[{"type":"aggren","checked":true,"text":"同意"},{"type":"reject","checked":true,"text":"拒绝"},{"type":"back","checked":false,"text":"回退"},{"type":"transfer","checked":false,"text":"转办"},{"type":"depute","checked":false,"text":"委派"},{"type":"signAdd","checked":false,"text":"加签"},{"type":"signRedu","checked":false,"text":"减签"}],"backTypeNode":"801e3c73-e665-4f6f-8ea7-d4de0927214c"},"childNode":{"nodeId":"3483a491-0765-4b54-8c35-dbacb29e5bbf","nodeType":"serial","nodeName":"分支选择","conditionNodes":[{"nodeId":"b1d40308-9bab-4aaa-a1b0-7a362e24844f","nodeType":"serial-node","type":"serial-node","nodeName":"条件","sortNum":0,"value":"days 大于等于 3","properties":{"conditions":{"simple":true,"group":"and","simpleData":[{"key":"days","cond":"ge","value":"3","next":"and"}]},"value":"days 大于等于 3"},"levelValue":"优先级1","childNode":{"nodeId":"f36a09c6-08ab-4027-85f9-db1f8a55f57c","nodeType":"parallel","nodeName":"并行分支","properties":{},"conditionNodes":[{"nodeId":"a4095f0e-1950-4d4c-b886-4a3035b645c2","nodeName":"审批人1","nodeType":"between","type":"between","properties":{"nodeRatioType":"1","backType":"1","nodeRatio":0,"permissions":["user"],"combination":{"role":[],"user":[{"id":"1","name":"超管"}]},"emptyApprove":{"type":"AUTO","value":[]},"value":"超管","buttons":[{"type":"aggren","checked":true,"text":"同意"},{"type":"reject","checked":true,"text":"拒绝"},{"type":"back","checked":false,"text":"回退"},{"type":"transfer","checked":false,"text":"转办"},{"type":"depute","checked":false,"text":"委派"},{"type":"signAdd","checked":false,"text":"加签"},{"type":"signRedu","checked":false,"text":"减签"}],"backTypeNode":"801e3c73-e665-4f6f-8ea7-d4de0927214c"},"value":"超管","nodeStatus":[]},{"nodeId":"091944fb-4f6f-4eeb-82d9-1ea3da22bd52","nodeName":"审批人2","nodeType":"between","type":"between","properties":{"nodeRatioType":"1","backType":"1","nodeRatio":0,"permissions":["user"],"combination":{"role":[],"user":[{"id":"2","name":"测试"}]},"emptyApprove":{"type":"AUTO","value":[]},"value":"测试","buttons":[{"type":"aggren","checked":true,"text":"同意"},{"type":"reject","checked":true,"text":"拒绝"},{"type":"back","checked":false,"text":"回退"},{"type":"transfer","checked":false,"text":"转办"},{"type":"depute","checked":false,"text":"委派"},{"type":"signAdd","checked":false,"text":"加签"},{"type":"signRedu","checked":false,"text":"减签"}],"backTypeNode":"801e3c73-e665-4f6f-8ea7-d4de0927214c"},"value":"测试","nodeStatus":[]}],"nodeStatus":[]},"nodeStatus":[]},{"nodeId":"1f678933-91b3-43c2-814c-26722a25d8f4","nodeType":"serial-node","type":"serial-node","nodeName":"条件","value":"其他条件默认走此流程","sortNum":1,"default":true,"properties":{},"levelValue":"优先级2","enableDestory":true,"childNode":{"nodeId":"32610c7f-21f1-43c5-821b-f65e3fcb917b","nodeName":"审批人1","nodeType":"between","value":"测试","type":"between","properties":{"nodeRatioType":"1","backType":"1","nodeRatio":0,"permissions":["user"],"combination":{"role":[],"user":[{"id":"2","name":"测试"}]},"emptyApprove":{"type":"AUTO","value":[]},"value":"测试","buttons":[{"type":"aggren","checked":true,"text":"同意"},{"type":"reject","checked":true,"text":"拒绝"},{"type":"back","checked":false,"text":"回退"},{"type":"transfer","checked":false,"text":"转办"},{"type":"depute","checked":false,"text":"委派"},{"type":"signAdd","checked":false,"text":"加签"},{"type":"signRedu","checked":false,"text":"减签"}],"backTypeNode":"801e3c73-e665-4f6f-8ea7-d4de0927214c"},"nodeStatus":[]},"nodeStatus":[]}],"childNode":{"nodeId":"fa13aeeb-f794-43c8-a2aa-27da340b9696","nodeType":"end","nodeName":"结束","properties":{},"nodeStatus":[]},"nodeStatus":[]},"nodeStatus":["1"]},"nodeStatus":["2"]}',
				formJson:
					'{"schemas":[{"label":"表单","type":"form","componentProps":{"layout":"horizontal","name":"default","labelWidth":100,"labelLayout":"fixed","labelCol":{"span":5},"wrapperCol":{"span":19},"colon":true,"labelAlign":"right","labelPlacement":"left"},"children":[],"id":"root"}],"script":"const { defineExpose, find } = epic;\\n\\nfunction test (){\\n    console.log(\'test\')\\n}\\n\\n// 通过defineExpose暴露的函数或者属性\\ndefineExpose({\\n test\\n})"}',
				assigneeList: [
					{
						id: "1814307624207470594",
						name: "曹敏瑞",
						instanceId: "1881237082605658114"
					}
				],
				assigneeStr: "曹敏瑞"
			},
			{
				id: "1881531340595318785",
				createTime: "2025-01-21 10:36:43",
				updateTime: "2025-01-21 10:36:43",
				tenantId: "1",
				delFlag: "0",
				definitionId: "1869320627067998209",
				flowName: "yzctest",
				flowCode: "yzctest",
				version: "1",
				businessId: "1",
				nodeType: 2,
				nodeCode: "4cc506aa-68f3-4c83-b1a3-bfab825fda26",
				nodeName: "结束",
				variable:
					'{"@type":"java.util.LinkedHashMap","defId":"1869320627067998209","formData":{"@type":"java.util.LinkedHashMap","checkbox_wh93a5hd":["\\u9009\\u98791"],"slider_mphwg5ip":55,"select_qgih4c4v":"\\u9009\\u98791"},"WarmListenerParam":""}',
				flowStatus: 8,
				createBy: "1",
				initiatorName: "超管",
				formCustom: "N",
				formPath: "",
				ext: null,
				formData: {
					checkbox_wh93a5hd: ["选项1"],
					slider_mphwg5ip: 55,
					select_qgih4c4v: "选项1"
				},
				flowJson:
					'{"nodeId":"a0838968-ae79-4129-9a59-4913918eba55","nodeType":"start","nodeName":"开始节点","value":"所有人","properties":{},"childNode":{"nodeId":"8aca225d-c539-46c0-86da-91d84f109150","nodeType":"between","nodeName":"审批人","value":"","properties":{"nodeRatioType":"1","backType":"1","nodeRatio":0,"permissions":["role"],"combination":{"role":[],"user":[]},"emptyApprove":{"type":"AUTO","value":[]},"value":"","formPath":"","listeners":[],"buttons":[{"type":"aggren","checked":true,"text":"同意"},{"type":"reject","checked":true,"text":"拒绝"},{"type":"back","checked":false,"text":"回退"},{"type":"transfer","checked":false,"text":"转办"},{"type":"depute","checked":false,"text":"委派"},{"type":"signAdd","checked":false,"text":"加签"},{"type":"signRedu","checked":false,"text":"减签"}],"formColumns":[],"fieldSetting":[],"backTypeNode":"a0838968-ae79-4129-9a59-4913918eba55"},"childNode":{"nodeId":"4cc506aa-68f3-4c83-b1a3-bfab825fda26","nodeType":"end","nodeName":"结束","properties":{},"nodeStatus":[]},"nodeStatus":[]},"nodeStatus":["2"]}',
				formJson:
					'{"schemas":[{"label":"表单","type":"form","componentProps":{"layout":"horizontal","name":"default","labelWidth":100,"labelLayout":"fixed","labelCol":{"span":5},"wrapperCol":{"span":19},"colon":true,"labelAlign":"right","labelPlacement":"left"},"children":[{"label":"栅格布局","type":"row","children":[{"type":"col","children":[{"label":"文本域","type":"textarea","field":"textarea_qt43fe8g","input":true,"componentProps":{"placeholder":"请输入","type":"textarea"},"id":"textarea_qt43fe8g"}],"componentProps":{"span":12},"id":"col_ifvdi1ce"}],"id":"row_xcsws2i2","componentProps":{"gutter":4}},{"label":"栅格布局","type":"row","children":[{"type":"col","children":[{"label":"复选框","type":"checkbox","field":"checkbox_gt3ex69r","input":true,"componentProps":{"options":[{"label":"选项1","value":"选项1"},{"label":"选项2","value":"选项2"}]},"id":"checkbox_gt3ex69r"}],"componentProps":{"span":12},"id":"col_z54gmq6d"},{"type":"col","children":[{"label":"复选框","type":"checkbox","field":"checkbox_ffxop0dj","input":true,"componentProps":{"options":[{"label":"选项1","value":"选项1"},{"label":"选项2","value":"选项2"}]},"id":"checkbox_ffxop0dj"}],"componentProps":{"span":12},"id":"col_9ysgqxr3"},{"type":"col","children":[{"label":"复选框","type":"checkbox","field":"checkbox_wh93a5hd","input":true,"componentProps":{"options":[{"label":"选项1","value":"选项1"},{"label":"选项2","value":"选项2"}]},"id":"checkbox_wh93a5hd"}],"componentProps":{"span":12},"id":"col_npgkug2x"},{"type":"col","children":[{"label":"复选框","type":"checkbox","field":"checkbox_1x6ebcyk","input":true,"componentProps":{"options":[{"label":"选项1","value":"选项1"},{"label":"选项2","value":"选项2"}]},"id":"checkbox_1x6ebcyk"}],"componentProps":{"span":12},"id":"col_ugev00ee"},{"type":"col","children":[{"label":"复选框","type":"checkbox","field":"checkbox_sjx97x5f","input":true,"componentProps":{"options":[{"label":"选项1","value":"选项1"},{"label":"选项2","value":"选项2"}]},"id":"checkbox_sjx97x5f"}],"componentProps":{"span":12},"id":"col_jtqvs9tw"},{"type":"col","children":[{"label":"选择框","type":"select","field":"select_qgih4c4v","input":true,"componentProps":{"options":[{"label":"选项1","value":"选项1"},{"label":"选项2","value":"选项2"}],"placeholder":"请选择","size":"default","effect":"light","placement":"bottom-start"},"id":"select_qgih4c4v"}],"componentProps":{"span":12},"id":"col_au4pxbpy"}],"id":"row_jdnlfju2","componentProps":{"gutter":4}},{"label":"栅格布局","type":"row","children":[],"id":"row_xpw2jkpk"},{"label":"栅格布局","type":"row","children":[{"type":"col","children":[{"label":"上传文件","type":"upload-file","field":"upload-file_hvanxzaa","input":true,"componentProps":{"action":"https://examples.epicjs.cn/static/upload-img.json","name":"file","showFileList":true},"id":"upload-file_hvanxzaa"}],"componentProps":{"span":12},"id":"col_03pljayz"},{"type":"col","children":[{"label":"滑块","type":"slider","field":"slider_mphwg5ip","input":true,"componentProps":{"placement":"top-start","showTooltip":true},"id":"slider_mphwg5ip"}],"componentProps":{"span":12},"id":"col_focx29lt"},{"type":"col","children":[{"label":"颜色选择器","type":"color-picker","field":"color-picker_hbt2cwjr","input":true,"id":"color-picker_hbt2cwjr"}],"componentProps":{"span":12},"id":"col_ng5ud3s9"},{"type":"col","children":[],"componentProps":{"span":12},"id":"col_gj1sul77"}],"id":"row_i0fh9pxv"},{"label":"栅格布局","type":"row","children":[],"id":"row_1ajhbk28"},{"label":"栅格布局","type":"row","children":[{"type":"col","children":[{"label":"按钮","type":"button","field":"button_olicxdez","input":false,"id":"button_olicxdez","componentProps":{"type":"primary"}}],"componentProps":{"span":12},"id":"col_2w3syce9"},{"type":"col","children":[{"label":"按钮","type":"button","field":"button_u0m3apen","input":false,"id":"button_u0m3apen"}],"componentProps":{"span":12},"id":"col_aqx82thf"}],"id":"row_wc2ir8v1"}],"id":"root"}],"script":"const { defineExpose, find } = epic;\\n\\nfunction test (){\\n    console.log(\'test\')\\n}\\n\\n// 通过defineExpose暴露的函数或者属性\\ndefineExpose({\\n test\\n})"}',
				assigneeList: null,
				assigneeStr: null
			},
			{
				id: "1880076176927674369",
				createTime: "2025-01-17 10:14:25",
				updateTime: "2025-01-20 09:00:27",
				tenantId: "1",
				delFlag: "0",
				definitionId: "1875020502577369090",
				flowName: "202501021559",
				flowCode: "202501021559",
				version: "6",
				businessId: "1",
				nodeType: 1,
				nodeCode: "3fba3918-b26b-4866-98d8-01892d272623",
				nodeName: "审批人1",
				variable:
					'{"@type":"java.util.LinkedHashMap","defId":"1875020502577369090","formData":{"@type":"java.util.LinkedHashMap"},"WarmListenerParam":""}',
				flowStatus: 1,
				createBy: "1",
				initiatorName: "超管",
				formCustom: "N",
				formPath: "",
				ext: null,
				formData: {},
				flowJson:
					'{"nodeId":"b12eb1ce-948b-4f48-b3d8-0f1004c05122","nodeType":"start","nodeName":"开始节点","value":"所有人","properties":{},"childNode":{"nodeId":"00134eda-1ac3-4e63-9feb-15b5532ff04c","nodeType":"between","nodeName":"审批人","value":"超级管理员","properties":{"nodeRatioType":"1","backType":"1","nodeRatio":0,"permissions":["role"],"combination":{"role":[{"id":"1812054642967375874","roleName":"超级管理员"}],"user":[]},"emptyApprove":{"type":"MANAGER","value":[]},"value":"超级管理员","formPath":"","listeners":[],"buttons":[{"type":"aggren","checked":true,"text":"同意"},{"type":"reject","checked":true,"text":"拒绝"},{"type":"back","checked":false,"text":"回退"},{"type":"transfer","checked":false,"text":"转办"},{"type":"depute","checked":false,"text":"委派"},{"type":"signAdd","checked":false,"text":"加签"},{"type":"signRedu","checked":false,"text":"减签"}],"formColumns":[],"fieldSetting":[],"backTypeNode":"b12eb1ce-948b-4f48-b3d8-0f1004c05122"},"childNode":{"nodeId":"3fba3918-b26b-4866-98d8-01892d272623","nodeName":"审批人1","nodeType":"between","value":"timi","type":"between","properties":{"nodeRatioType":"1","backType":"1","nodeRatio":0,"permissions":["user"],"combination":{"role":[],"user":[{"id":"1859430042765021185","name":"timi"}]},"emptyApprove":{"type":"MANAGER","value":[]},"value":"timi","formPath":"","listeners":[],"buttons":[{"type":"aggren","checked":true,"text":"同意"},{"type":"reject","checked":true,"text":"拒绝"},{"type":"back","checked":false,"text":"回退"},{"type":"transfer","checked":false,"text":"转办"},{"type":"depute","checked":false,"text":"委派"},{"type":"signAdd","checked":false,"text":"加签"},{"type":"signRedu","checked":false,"text":"减签"}],"formColumns":[],"fieldSetting":[],"backTypeNode":"b12eb1ce-948b-4f48-b3d8-0f1004c05122"},"childNode":{"nodeId":"5ae67662-472f-4b21-85a5-58dcbfeae4db","nodeType":"end","nodeName":"结束","properties":{},"nodeStatus":[]},"nodeStatus":["1"]},"nodeStatus":["2"]},"nodeStatus":["2"]}',
				formJson:
					'{"schemas":[{"label":"表单","type":"form","componentProps":{"layout":"horizontal","name":"default","labelWidth":100,"labelLayout":"fixed","labelCol":{"span":5},"wrapperCol":{"span":19},"colon":true,"labelAlign":"right","labelPlacement":"left"},"children":[],"id":"root"}],"script":"const { defineExpose, find } = epic;\\n\\nfunction test (){\\n    console.log(\'test\')\\n}\\n\\n// 通过defineExpose暴露的函数或者属性\\ndefineExpose({\\n test\\n})"}',
				assigneeList: [
					{
						id: "1859430042765021185",
						name: "曹敏瑞",
						instanceId: "1880076176927674369"
					}
				],
				assigneeStr: "曹敏瑞"
			}
		]
	);

	const addMyTaskData = (data: any) => {
		myTaskData.value.push(data);
		storage.set("my_task_data", myTaskData.value);
	};
	const getMyTaskData = () => {
		return myTaskData.value;
	};
	return {
		myTaskData,
		addMyTaskData,
		getMyTaskData
	};
});
