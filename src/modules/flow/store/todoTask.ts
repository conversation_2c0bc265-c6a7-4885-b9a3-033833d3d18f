import { defineStore } from "pinia";
import { ref } from "vue";
import { storage } from "/@/cool/utils";

export const useMyTask = defineStore("myTaskStore", function () {
	const myTaskData = ref(storage.get("my_task_data") || []);

	const addMyTaskData = (data: any) => {
		myTaskData.value.push(data);
		storage.set("my_task_data", myTaskData.value);
	};
	const getMyTaskData = () => {
		return myTaskData.value;
	};
	return {
		myTaskData,
		addMyTaskData,
		getMyTaskData
	};
});
