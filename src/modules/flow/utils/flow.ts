export function uuid() {
	const s = [];
	const hexDigits = '0123456789abcdef';
	for (let i = 0; i < 36; i++) {
		s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
	}
	s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
	s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
	s[8] = s[13] = s[18] = s[23] = '-';

	const uuid = s.join('');
	return uuid;
}

export function nodeList(nodeData, list) {
	for (const i in nodeData) {
		const node = nodeData[i];
		if (
			node.nodeType == 'start' ||
			node.nodeType == 'between' || // || node.nodeType == 'parallel'  || node.nodeType == 'serial'
			node.nodeType == 'parallel-node'
		) {
			list.push({
				nodeId: node.nodeId,
				nodeName: node.nodeName,
				nodeType: node.nodeType
			});
		}

		const childNodes = node.childNodes;
		if (childNodes != null && childNodes.length > 0) {
			for (const j in childNodes) {
				nodeList(childNodes[j], list);
			}
		}
	}
}

/**
 *
 * @param {解析表单配置中的字段信息} parseFormJson
 */
export function parseFormJson(formFields) {
	const formColumns = [];
	formFields.forEach(element => {
		// 判断 input = true就行
		if (element.input) {
			formColumns.push({
				label: element.label,
				field: element.field,
				required: element.rules?.[0].required
			});
		} else {
			if (element.children) {
				return formColumns.concat(parseFormJson(element.children));
			}
		}
	});
	return formColumns;
}

export function changeNodeStatus(flowNode, hisList) {
	// 修改节点状态
	const nodeData = { ...flowNode, nodeStatus: [] };
	nodeData.nodeStatus = [];
	const hiss = hisList.filter(r => r.nodeCode == nodeData.nodeId);
	if (hiss && hiss.length > 0) {
		for (const j in hiss) {
			nodeData.nodeStatus.push(hiss[j].flowStatus);
		}
	}
	if (nodeData.childNode) {
		changeNodeStatus(nodeData.childNode, hisList);
	}

	if (nodeData.conditionNodes) {
		nodeData.conditionNodes.forEach(childNode => {
			changeNodeStatus(childNode, hisList);
		});
	}
	return nodeData;
}
export function updateCheckStatus(dataA, dataB, currentNodeId, type?) {
	// 递归遍历节点
	const updatedData = JSON.parse(JSON.stringify(dataA));
	let ischecking = false;
	// 递归遍历节点
	function traverseNode(node) {
		//if (type) {
		// 	if (dataB.some(item => item.currentNodeId === node.nodeId)) {
		// 		node.checkStatus = "已审核"; // 设置已审核节点状态为"已审核"
		// 	}
		// } else {
		if (node.nodeId === currentNodeId) {
			node.checkStatus = '审核中'; // 设置当前节点状态为"审核中"
			ischecking = true;
		} else if (dataB.some(item => item.currentNodeId === node.nodeId && !ischecking)) {
			node.checkStatus = '已审核'; // 设置已审核节点状态为"已审核"
		}
		if (node.nodeType == 'start') {
			node.checkStatus = '已提交';
		}
		//}
		// 如果有子节点，继续递归
		if (node.childNode) {
			traverseNode(node.childNode);
		}

		// 如果有条件节点，继续递归
		if (node.conditionNodes) {
			node.conditionNodes.forEach(conditionNode => {
				if (conditionNode.childNode) {
					traverseNode(conditionNode.childNode);
				}
			});
		}
	}

	// 从根节点开始遍历
	traverseNode(updatedData);

	// 返回更新后的数据
	return updatedData;
}
export function findNodeById(dataA, nodeId) {
	// 递归查找节点
	function findNode(node) {
		if (node.nodeId === nodeId) {
			return node; // 找到对应节点
		}

		// 如果有子节点，继续递归
		if (node.childNode) {
			const foundNode = findNode(node.childNode);
			if (foundNode) {
				return foundNode;
			}
		}

		// 如果有条件节点，继续递归
		if (node.conditionNodes) {
			for (const conditionNode of node.conditionNodes) {
				if (conditionNode.childNode) {
					const foundNode = findNode(conditionNode.childNode);
					if (foundNode) {
						return foundNode;
					}
				}
			}
		}

		return null; // 未找到对应节点
	}

	// 从根节点开始查找
	return findNode(dataA);
}

export function findFormNode(flowName, dataA, hasFormNode = false) {
	// 递归遍历节点
	function traverseNode(node) {
		if (node.value && node.value.includes('表单自定')) {
			node.properties.combination.form = [];
			hasFormNode = true;
		}
		if (node.nodeName) {
			node.nodeName = `${flowName}_${node.nodeName}`;
		}
		if (node.childNode) {
			// 如果有子节点，继续递归
			traverseNode(node.childNode);
		}

		// 如果有条件节点，继续递归
		if (node.conditionNodes) {
			node.conditionNodes.forEach(conditionNode => {
				if (conditionNode.childNode) {
					traverseNode(conditionNode.childNode);
				}
			});
		}
	}

	// 从根节点开始遍历
	traverseNode(dataA);

	// 返回更新后的数据
	return { node: dataA, hasFormNode };
}
export function checkFormNode(dataA, falseData = []) {
	// 递归遍历节点
	function traverseNode(node) {
		if (node.value && node.value.includes('表单自定')) {
			if (node.properties.combination.form.length > 0) {
				let isOk = true;
				console.log('node', node.properties.combination.form);
				node.properties.combination.form.map(item => {
					console.log(
						'item',
						item,
						item.name.candidateUserField,
						item.name.externalFields
					);
					if (
						item.name.candidateUserField.length > 0 &&
						item.name.externalFields.length > 0 &&
						item.name.externalFormId &&
						item.name.externalFormName &&
						item.name.formFields.length > 0
					) {
					} else {
						isOk = false;
					}
				});
				if (!isOk) {
					falseData.push(`${node.nodeId}-${node.nodeName}`);
				}
			} else {
				falseData.push(`${node.nodeId}-${node.nodeName}`);
			}
		}

		// 如果有子节点，继续递归
		if (node.childNode) {
			traverseNode(node.childNode);
		}

		// 如果有条件节点，继续递归
		if (node.conditionNodes) {
			node.conditionNodes.forEach(conditionNode => {
				if (conditionNode.childNode) {
					traverseNode(conditionNode.childNode);
				}
			});
		}
	}

	// 从根节点开始遍历
	traverseNode(dataA);

	// 返回更新后的数据
	return { node: dataA, falseData };
}
export function buildChildNodeChain(nodes) {
	if (nodes.length === 0) return null;

	// 先将nodes展平成一维数组
	const flattenNodes = nodes.reduce((acc, node) => {
		// 如果节点有conditionInfo属性，将其展平并添加到结果中
		if (node.conditionInfo) {
			return [...acc, node, ...node.conditionInfo];
		}
		return [...acc, node];
	}, []);

	// 从第一个节点开始构建
	const rootNode = { ...flattenNodes[0], childNode: null };
	let currentNode = rootNode;

	// 遍历剩余的节点，逐个添加到 childNode
	for (let i = 1; i < flattenNodes.length; i++) {
		if (flattenNodes[i].nodeType != 'serial' && flattenNodes[i].nodeType != 'serial-node') {
			const nextNode = { ...flattenNodes[i], childNode: null };
			currentNode.childNode = nextNode;
			currentNode = nextNode;
		}
	}

	return rootNode;
}
export function backNodeList(nodeData) {
	const backNodeList = [];
	return getBackNodes(nodeData, backNodeList);
}

function getBackNodes(nodeData, backNodeList) {
	const nodeType = nodeData.nodeType;

	if (nodeType == 'start' || nodeType == 'end' || nodeType == 'between') {
		backNodeList.push({
			nodeId: nodeData.nodeId,
			nodeName: nodeData.nodeName
		});
	}
	const conditionNodes = nodeData.conditionNodes;
	if (conditionNodes != null && conditionNodes.length > 0) {
		for (const i in conditionNodes) {
			getBackNodes(conditionNodes[i], backNodeList);
		}
	}
	const childNode = nodeData.childNode;
	if (childNode != null) {
		getBackNodes(childNode, backNodeList);
	}

	return backNodeList;
}
