export const getFlowStatus = str => {
	switch (str) {
		case 'pending':
			return '待审批';
		case 'running':
			return '审批中';
		case 'completed':
			return '审批完成';
		case 'terminated':
			return '审批终止';
		case 'revoked':
			return '审批撤销';
		case 5:
			return '作废';
		case 6:
			return '撤销';
		case 7:
			return '取回';
		case 8:
			return '已完成';
		case 9:
			return '已退回';
		case 10:
			return '失效';
		case 99:
			return '已拒绝';
		default:
			return '未知状态';
	}
};

export const getFlowColor = str => {
	switch (str) {
		case 'running':
			return 'RGB(65, 101, 241)';
		case 'completed':
			return 'RGB(69, 202, 80)';
		case 'terminated':
			return '#ff3131';
		case 'pending':
			return 'RGB(202, 202, 202)';
		case 'addSign':
			return 'RGB(111, 93, 68)';
		case 'start':
			return 'RGB(255, 69, 76)';
		case 'revoked':
			return 'RGB(171, 71, 60)';
		case 7:
			return 'RGB(255, 149, 0)';
		case 8:
			return 'RRGB(38, 40, 221)';
		case 9:
			return 'RGB(255, 105, 164)';
		case 10:
			return 'RGB(0, 122, 204)';
		case 99:
			return 'RGB(255, 0, 0)';
		default:
			return 'RGB(255, 0, 0)';
	}
};
