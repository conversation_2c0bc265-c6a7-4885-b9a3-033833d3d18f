<template>
	<div style="display: flex; flex-direction: column; gap: 10px">
		<el-card>
			<el-row>
				<el-col :span="24">
					<el-button
						type="primary"
						style="margin-bottom: 10px"
						@click="tableReload()"
						plain
						>刷新</el-button
					>
				</el-col>
			</el-row>
			<el-table
				v-direct-auto-height
				:data="
					tableData.slice(
						(pagination.current - 1) * pagination.size,
						pagination.current * pagination.size
					)
				"
				:border="true"
				style="width: 100%"
			>
				<el-table-column prop="taskId" label="任务ID" />
				<el-table-column prop="formData" label="审批数据ID">
					<template #default="scope">
						<el-text tag="p">{{ scope.row.formData.id }}</el-text>
					</template>
				</el-table-column>
				<el-table-column prop="flowId" label="流程编码" />
				<el-table-column prop="flowName" label="流程名称" />
				<el-table-column prop="formName" label="表单名称"> </el-table-column>
				<el-table-column prop="nodeName" label="当前节点">
					<template #default="scope">
						<el-text size="small" tag="p">{{
							findNodeById(scope.row.flowJson, scope.row.nodeId)?.nodeName
						}}</el-text>
					</template>
				</el-table-column>
				<el-table-column prop="status" label="状态">
					<template #default="scope">
						<el-tag
							:color="getFlowColor(scope.row.instanceStatus)"
							effect="dark"
							style="border-color: unset !important"
						>
							<span>{{ getFlowStatus(scope.row.instanceStatus) }}</span>
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="action" label="审批操作">
					<template #default="scope">
						<span>{{ btnMap[scope.row.action] }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="comment" label="审批意见"> </el-table-column>
				<el-table-column label="操作" width="80">
					<template #default="scope">
						<el-button link type="primary" size="small" @click="handleClick(scope.row)">
							查看
						</el-button>
					</template>
				</el-table-column>
			</el-table>

			<div style="padding: 5px 0"></div>
			<el-row>
				<el-col :span="24">
					<el-pagination
						background
						layout="prev, pager, next"
						:total="pagination.total"
						v-model:page-size="pagination.size"
						v-model:current-page="pagination.current"
						@change="pageTable"
					/>
				</el-col>
			</el-row>
		</el-card>
	</div>
	<flowdetail ref="flowDetailRef"></flowdetail>
</template>

<script setup lang="ts" name="finishedTask">
import { reactive, ref, onMounted, watch, onActivated } from 'vue';

import Flowdetail from '../components/flowDetail.vue';
import { getFlowColor, getFlowStatus } from '../utils/flowInfo';
import { useCool } from '/@/cool';
import { useBase } from '/$/base';
import { findNodeById } from '../utils/flow';
import { buildChildNodeChain } from '../utils/flow';
const { user } = useBase();
const { router, service } = useCool();
const flowDetailRef = ref(null);
const btnMap = {
	aggren: '同意',
	approve: '同意',
	reject: '驳回',
	back: '回退',
	addSign: '加签',
	signAdd: '加签',
	signRedu: '减签',
	transfer: '转办',
	start: '开始',
	removeSign: '减签',
	rollback: '回退'
};
const tableData: any = ref([]);

const pagination = ref({
	size: 20,
	current: 1,
	total: 0
});

onActivated(() => {
	if (router.options.history.state?.instanceId) {
		console.log('router.options.history.state', router.options.history.state);
		flowDetailRef.value?.open({ instanceId: router.options.history.state.instanceId }, '查看');
	}
	tableReload();
});
// onMounted(() => {
// 	tableReload();
// });
const tableReload = async () => {
	const res = await service.flow.process.completedTasks();
	console.log(res);
	res.map(e => {
		const data = e.approvalHistory.filter(item => {
			if (item.taskId) {
				return (
					item.nodeId == e.nodeId &&
					item.userName == user.info?.name &&
					item.taskId == e.taskId
				);
			} else {
				return item.nodeId == e.nodeId && item.userName == user.info?.name;
			}
		});

		//console.log("data", data, "user.info?", user.info);
		if (data && data[0] && data[0].action) {
			console.log('data', data[0]);
			e.action = data[0].action;
			e.comment = data[0].comment;
		}
		//console.log("Data", e.approvalHistory, user.info?.name, data);
	});
	console.log('res', res);
	tableData.value = res;
	pagination.value.total = res.length;
};
const pageTable = (current, size) => {
	pagination.value.current = current;
	tableReload();
};

const handleClick = async row => {
	// const res = await service.flow.process.predict({ instanceId: row.instanceId });
	// console.log("返回内容", res);
	// const newFlowJson = buildChildNodeChain(res);
	//console.log("newFlowJson拼好了", newFlowJson);
	const data = {
		// id: row.instanceId,
		// flowId: row.flowId,
		// flowName: row.flowName,
		// formId: row.formId,
		// formName: row.formName,
		//flowJson: newFlowJson, // 流程json
		// formData: row.formData, // 表单数据
		// formJson: row.formJson,
		instanceId: row.instanceId
		// nodeId: row.nodeId // 节点id
	};
	console.log(data);
	flowDetailRef.value.open(data, '查看');
};
</script>

<style lang="scss" scoped></style>
