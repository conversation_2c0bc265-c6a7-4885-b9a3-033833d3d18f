<template>
	<div style="display: flex; flex-direction: column; height: 100%; width: 100%">
		<el-card style="min-height: 65px">
			<el-row justify="space-between">
				<el-row justify="space-between" style="width: 300px" align="middle">
					<el-button @click="goBack()">返回</el-button>
					<span style="width: 180px; overflow: hidden" :title="flowData.name"
						>流程名称：{{ rowData.flow_name }}</span
					>
				</el-row>

				<el-row
					justify="space-between"
					style="width: 350px"
					align="middle"
					class="flow-config-step-num"
				>
					<!-- <template v-for="(item, index) in configSteps" :key="index">
						<span
							@click="changeStepNum(item.value)"
							:class="
								currStep == item.value
									? 'step-num-active'
									: '' + noSelectClass(item.value)
							"
						>
							<span class="step-num">{{ index + 1 }}</span> {{ item.label }}
						</span>
					</template> -->
				</el-row>
				<div style="width: 300px">
					<el-button style="margin-left: 8px; float: right" @click="saveConfig"
						>保存</el-button
					>
				</div>
			</el-row>
		</el-card>

		<div style="flex: 1; position: relative; overflow: auto" v-show="currStep === 2">
			<flow-design
				:formColumns="formColumns"
				:formId="formId"
				:data="rowData"
				:readonly="readonly"
				:isForm="isForm"
			></flow-design>
		</div>

		<!-- <el-drawer :key="currNodeData?.nodeId"
            v-model="drawer"
            @open="openDrawer"
            @close="closeHandler"
            :direction="direction"
            destroy-on-close
        >   
        <template #header>
            <el-text v-if="!drawerEdit" @click="changeTitle" tag="ins">{{ currNodeData?.nodeName }}</el-text>
            <el-input v-else  ref="drawerEditInput" v-model="currNodeData.nodeName" autofocus  @blur="drawerEditBlur" />
        </template>
           <template #default>
            <component ref="settingRef" :key="currNodeData.nodeId" :data="currNodeData"  :backNodeList="backNodeList" :is="currComponent"></component>
           </template>
          
        </el-drawer> -->
	</div>
</template>

<script setup lang="ts" name="flowDesign">
import { nextTick, onMounted, onActivated, reactive, ref, watch } from 'vue';
import FlowDesign from '/@/modules/flow/components/design/index.vue';
//import { EDesigner } from "epic-designer";
import { uuid, checkFormNode, parseFormJson } from '/@/modules/flow/utils/flow';
import { detailApi, saveConfigApi } from '/@/modules/flow/api/management';
import { useCool } from '/@/cool';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useStore } from '../store/index';
import { cloneDeep } from 'lodash-es';

const { formFlowStore } = useStore();
const formListStore = useStore();

const flowData: any = ref({});
const settingRef = ref<any>(null);
const { router, service } = useCool();
const id = ref<string | string[]>('');
const currStep = ref<number>(1);
const isForm = ref<boolean>(false);
const flowId = ref<any>(null);
const formId = ref<any>(null);
const formType = ref<string>('');

//const edDesignerRef = ref<any>(null);

const configSteps = ref([
	{
		label: '流程配置',
		value: 2
	}
]);
const formColumns: any = ref([]);
const rowData: any = ref({});
onActivated(async () => {
	console.log('设计页面onActivated');
	formColumns.value = [];
	console.log('router.options.history.state', router.options.history.state);
	rowData.value = cloneDeep(router.options.history.state);
	//	id.value = router.options.history.state.id;
	//formType.value = router.options.history.state.type;
	formId.value = router.options.history.state.form_id;
	isForm.value = formType.value?.split('_')[0] == 'form';
	console.log('isForm.value', isForm.value, id.value, router.currentRoute.value.query);
	if (rowData.value && rowData.value.flow_id) {
		//id.value = rowData.value.flow_id;
		console.log('rowData.value', rowData.value.flowJson);
		currStep.value = 2;
		rowData.value.flowJson = formFlowStore.getFormFlowData().flowJson;
		// try {
		// 	const res = await service.flow.flow.info({ id: rowData.value.flow_id });
		// 	rowData.value.flowJson = res.flowJson;
		// } catch (error) {
		// 	ElMessage.error(`获取流程失败${error}`);
		// 	console.log("error", error);
		// }
	} else {
		rowData.value.flowJson = {
			nodeId: uuid(),
			nodeType: 'start',
			nodeName: '开始节点',
			value: '所有人',
			properties: {},
			childNode: {
				nodeId: uuid(),
				nodeType: 'between',
				nodeName: '审批人',
				value: '',
				properties: {},
				childNode: {
					nodeId: uuid(),
					nodeType: 'end',
					nodeName: '结束',
					properties: {}
				}
			}
		};
		currStep.value = 2;
	}
	// if (isForm.value && formType.value?.split("_")[1] == "edit") {
	// 	flowData.value = cloneDeep(
	// 		formListStore.formListStore.detailFormListData(formId.value).flow
	// 	);
	// 	console.log("flowData.value", flowData.value);
	// 	flowData.value.flowJson =
	// 		typeof flowData.value.flowJson === "string"
	// 			? JSON.parse(flowData.value.flowJson)
	// 			: flowData.value.flowJson;
	// 	currStep.value = 2;
	// } else {
	// 	const params = id.value;
	// 	//flowData.value = {};
	// 	service.flow.flow.info({ id: params }).then(res => {
	// 		console.log("res", res);
	// 		flowData.value = cloneDeep(res);
	// 		console.log("flowData.value", flowData.value);
	// 		currStep.value = 2;
	// 		if (flowData.value?.formJson != null) {
	// 			//	edDesignerRef.value.setData(JSON.parse(flowData.value.formJson));
	// 		}
	// 		if (flowData.value?.flowJson == null || !flowData.value?.flowJson) {
	// 			flowData.value.flowJson = {
	// 				nodeId: uuid(),
	// 				nodeType: "start",
	// 				nodeName: "开始节点",
	// 				value: "所有人",
	// 				properties: {},
	// 				childNode: {
	// 					nodeId: uuid(),
	// 					nodeType: "between",
	// 					nodeName: "审批人",
	// 					value: "",
	// 					properties: {},
	// 					childNode: {
	// 						nodeId: uuid(),
	// 						nodeType: "end",
	// 						nodeName: "结束",
	// 						properties: {}
	// 					}
	// 				}
	// 			};
	// 			console.log("flowData.value.flowJson为空", flowData.value);
	// 		} else {
	// 			console.log("flowData.value.flowJson不为空", flowData.value.flowJson);
	// 			flowData.value.flowJson = JSON.parse(flowData.value.flowJson);
	// 			//flowData.value.flowJson = flowData.value.flowJson;
	// 		}
	// 	});
	// }
});

function goBack() {
	router.push({
		path: `/formFlow`
	});
	// if (isForm.value) {
	// 	console.log('isForm.value', isForm.value);
	// 	router.push({
	// 		path: `/formFlow`
	// 	});
	// } else {
	// 	router.go(-1);
	// }
}

function findFormData(node) {
	let result: any = [];

	// 检查当前节点是否有 properties 和 combination.form
	if (
		node.properties &&
		node.properties.combination &&
		node.properties.combination.form &&
		node.properties.combination.form.length > 0
	) {
		//form.name.
		result.push({
			externalFormId: node.properties.combination.form[0].name.formId,
			nodeId: node.nodeId,
			formField: node.properties.combination.form[0].name.formField,
			externalFields: node.properties.combination.form[0].name.externalFields,
			candidateUserField: node.properties.combination.form[0].name.candidateUserField
		});
	}

	// 递归检查 childNode
	if (node.childNode) {
		result = result.concat(findFormData(node.childNode));
	}

	// 递归检查 conditionNodes（如果有）
	if (node.conditionNodes) {
		node.conditionNodes.forEach(conditionNode => {
			result = result.concat(findFormData(conditionNode));
		});
	}

	return result;
}

// 发布
const saveConfig = () => {
	//const formData = edDesignerRef.value.getData();
	const formData = checkFormNode(cloneDeep(rowData.value.flowJson));
	if (formData.falseData.length > 0) {
		ElMessageBox.alert(
			`当前流程内部分表单自定节点未配置，请重新配置表单自定义节点，错误节点：${formData.falseData.join(',')}`
		);
		return;
	}
	if (rowData.value.flow_id) {
		console.log('保存时flowData.value', flowData.value);
		ElMessageBox.confirm(
			'重新设计流程，会导致当前正在审批的流程终止，确定要重新设计流程吗？',
			'提示',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}
		).then(() => {
			service.flow.flow
				.update({
					id: Number(rowData.value.flow_id),
					name: rowData.value.flow_name,
					version: rowData.value.flow_version,
					managers: JSON.parse(rowData.value.flow_managers),
					active: true,
					description: rowData.value.flow_description,
					flowJson: rowData.value.flowJson,
					flowUnDoneNoticeDay: rowData.value.flowUnDoneNoticeDay
				})
				.then(res => {
					console.log('res', res);
					ElMessage.success('保存成功');
					setTimeout(() => {
						router.push({
							path: `/designTableView`
						});
					}, 200);
				})
				.catch(err => {
					console.log('err', err);
					ElMessage.error('保存失败' + JSON.stringify(err.message));
				});
		});
	} else {
		service.flow.flow
			.add({
				formId: rowData.value.form_id,
				name: rowData.value.flow_name,
				version: rowData.value.flow_version,
				managers: JSON.parse(rowData.value.flow_managers),
				active: true,
				description: rowData.value.description,
				flowJson: rowData.value.flowJson,
				flowUnDoneNoticeDay: rowData.value.flowUnDoneNoticeDay
			})
			.then(res => {
				console.log('res', res);
				ElMessage.success('保存成功');
				setTimeout(() => {
					router.push({
						path: `/designTableView`
					});
				}, 200);
			})
			.catch(err => {
				console.log('err', err);
				ElMessage.error('保存失败' + JSON.stringify(err.message));
			});
	}
};

const drawer = ref(false);
const drawerEdit = ref(false);
const drawerEditInput = ref();

const changeTitle = () => {
	// 不这么写 多点几次后 input 聚焦无效
	drawerEdit.value = true;
	nextTick(() => {
		drawerEditInput.value.focus();
	});
};
const drawerEditBlur = () => {
	if (currNodeData.value.nodeName.length == 0) {
		currNodeData.value.nodeName = '未设置';
	}
	drawerEdit.value = false;
};

const readonly = ref();

const direction = ref('rtl');

const openDrawer = (e, e1, e2) => {
	console.log(e);
};

const currNodeData = ref();
</script>

<style scoped>
.ep-main {
	--el-main-padding: 0px;
	border-right: 1px solid var(--el-border-color);
	box-sizing: border-box;
}
.flow-config-main {
	height: 100%;
}
.main-container {
	display: flex;
	width: 100%;
	height: 100%;
}
.left {
	width: 100px;
}
.left-content {
	height: 100%;
	width: calc(100% - 500px);
}
.right-content {
	width: 500px;
	border: 1px solid green;
}

.read-Model {
	position: fixed;
	top: 177px;
	right: 110px;
}

.flow-config-step-num > span {
	padding: 2px 10px;
	cursor: pointer;
	border: 1px solid #b5c5e7;
	background-color: aliceblue;
	border-radius: 12px;
}
.flow-config-step-num .step-num {
	display: inline-flex;
	justify-content: center;
	align-items: center;
	width: 18px;
	height: 18px;
	border: 1px solid var(--el-text-color-primary);
	border-radius: 50%;
}
.flow-config-step-num > span:hover {
	cursor: pointer;
	background-color: #2049fe;
	color: #ffffff;
}
.no-select {
	cursor: no-drop !important;
}
.no-select:hover {
	cursor: no-drop !important;
}

.flow-config-step-num > .step-num-active {
	background-color: #2049fe;
	color: #ffffff;
}
</style>
