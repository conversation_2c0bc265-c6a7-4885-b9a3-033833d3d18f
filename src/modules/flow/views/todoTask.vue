<template>
	<div style="display: flex; flex-direction: column; gap: 10px">
		<el-card>
			<el-row>
				<el-col :span="24">
					<el-button
						type="primary"
						style="margin-bottom: 10px"
						@click="tableReload()"
						plain
						>刷新</el-button
					>
				</el-col>
			</el-row>
			<el-table
				v-direct-auto-height
				:data="
					tableData.slice(
						(pagination.current - 1) * pagination.size,
						pagination.current * pagination.size
					)
				"
				:border="true"
				style="width: 100%"
			>
				<el-table-column prop="formData" label="数据id">
					<template #default="scope">
						<el-text size="small" tag="p">{{ scope.row.formData.id }}</el-text>
					</template>
				</el-table-column>
				<el-table-column prop="flowId" label="流程编码" />
				<el-table-column prop="flowName" label="流程名称" />
				<el-table-column prop="formName" label="表单名称"> </el-table-column>
				<el-table-column prop="nodeName" label="当前节点">
					<template #default="scope">
						<el-text size="small" tag="p">{{
							findNodeById(scope.row.flowJson, scope.row.nodeId)?.nodeName
						}}</el-text>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="80">
					<template #default="scope">
						<el-button link type="primary" size="small" @click="handleClick(scope.row)">
							办理
						</el-button>
					</template>
				</el-table-column>
			</el-table>

			<div style="padding: 5px 0"></div>
			<el-row>
				<el-col :span="24">
					<el-pagination
						background
						layout="prev, pager, next"
						:total="pagination.total"
						v-model:page-size="pagination.size"
						v-model:current-page="pagination.current"
						@change="pageTable"
					/>
				</el-col>
			</el-row>
		</el-card>
	</div>
	<flowDetail ref="flowDetailRef" @close="tableReload"></flowDetail>
</template>

<script setup lang="ts" name="todoTask">
import { reactive, ref, onMounted, onActivated, watch } from 'vue';

import flowDetail from '../components/flowDetail.vue';
import { getFlowColor, getFlowStatus } from '../utils/flowInfo';
import { useMyTask } from '../store/myTask';
import { useCool } from '/@/cool';
import { useStore } from '/@/modules/base/store/index';
import { cloneDeep } from 'lodash-es';
import { findNodeById } from '../utils/flow';

const { messages } = useStore();
const { service, router } = useCool();
const flowDetailRef = ref(null);
const queryData = ref({
	flowCode: '',
	flowName: '',
	category: ''
});
const dialogVisible = ref(false);

const tableData = ref([]);
const pagination = ref({
	size: 20,
	current: 1,
	total: 0
});

const pageTable = (current, size) => {
	pagination.value.current = current;
	tableReload();
};

onActivated(async () => {
	console.log('router.options.history.state', router.options.history.state);
	await tableReload();

	// 判断当前路由是否需要打开详情
	const { instanceId, taskId } = router.options.history.state;
	console.log('instanceId', instanceId);
	console.log('taskId', taskId);
	if (instanceId && taskId) {
		flowDetailRef.value?.open(
			{
				instanceId,
				taskId
			},
			'办理'
		);
	}
});

const tableReload = async () => {
	const param = {};
	const res = await service.flow.process.tasks(param);
	console.log('todo', res);
	tableData.value = res;
	pagination.value.total = res.length;
	messages.getMessagesList();
};

// 办理
const handleClick = async row => {
	// const res = await service.flow.process.predict({ instanceId: row.instanceId });
	// console.log("返回内容", res);
	// const newFlowJson = buildChildNodeChain(res);
	// console.log("newFlowJson拼好了", newFlowJson);
	const data = {
		taskId: row.id,
		// flowId: row.flowId,
		// flowName: row.flowName,
		// formId: row.formId,
		// formName: row.formName,
		// flowJson: newFlowJson, // 流程json
		// formData: row.formData, // 表单数据
		instanceId: row.instanceId
		//nodeId: row.nodeId // 节点id
	};
	console.log(data);
	flowDetailRef.value?.open(data, '办理');
	// let data = {
	//     id: row.id,
	//     message: "ok",
	//     skipType: "PASS",
	//     variable: "这是一个未知数"
	// }
	// skipApi(data).then(res=> {
	//     alert(res.msg);
	// })
};
</script>

<style lang="scss" scoped></style>
