<template>
	<cl-crud ref="Crud">
		<cl-row>
			<cl-refresh-btn />
			<cl-add-btn />
			<cl-multi-delete-btn />
			<cl-flex1 />
			<cl-search-key />
		</cl-row>

		<el-row>
			<cl-table ref="Table">
				<template #column-flowName="{ scope }">
					<template v-if="scope.row.flowName">
						<el-tag
							v-for="(item, index) in scope.row.flowName.split(',')"
							:key="index"
							disable-transitions
							size="small"
							effect="plain"
							style="margin: 2px"
							>{{ item }}
						</el-tag>
					</template>
				</template>
			</cl-table>
		</el-row>

		<el-row>
			<cl-flex1 />
			<cl-pagination />
		</el-row>

		<cl-upsert ref="Upsert"></cl-upsert>
	</cl-crud>
</template>

<script lang="ts" setup>
import { useTable, useUpsert, useCrud } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { reactive, ref } from "vue";

const { service } = useCool();
const bidata = ref("");

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.zhongtie.email_user
	},
	(app) => {
		app.refresh();
	}
);

// cl-upsert 配置
const Upsert = useUpsert({
	dialog: {
		width: "800px"
	},

	items: [
		{
			prop: "name",
			label: "姓名",
			span: 10,
			required: true,
			component: {
				name: "el-input"
			}
		},
		{
			prop: "email",
			label: "邮箱",
			span: 14,
			required: true,
			component: {
				name: "el-input"
			}
		},
		{
			prop: "flowIdList",
			label: "流程",
			value: [],
			// required: true,
			component: {
				name: "el-select",
				options: [],
				props: {
					multiple: true,
					// "multiple-limit": 3
					"multiple-limit": 0
				}
			}
		}
	],

	onSubmit(data, { next }) {
		next({
			...data
		});
	},

	async onOpen() {
		const list_flow = await service.zhongtie.email_flow.list();
		// 设置项目列表
		Upsert.value?.setOptions(
			"flowIdList",
			list_flow.map((e) => {
				return {
					label: e.name || "",
					value: e.id
				};
			})
		);
	}
});

// cl-table 配置
const Table = useTable({
	columns: [
		{
			type: "selection",
			width: 60
		},
		{
			prop: "name",
			label: "姓名",
			minWidth: 40
		},
		{
			prop: "email",
			label: "邮箱",
			minWidth: 100
		},
		// 添加流程名称
		{
			prop: "flowName",
			label: "流程",
			headerAlign: "center",
			minWidth: 200
		},
		{
			label: "操作",
			type: "op",
			buttons: ["edit", "delete"]
		}
	]
});
</script>
