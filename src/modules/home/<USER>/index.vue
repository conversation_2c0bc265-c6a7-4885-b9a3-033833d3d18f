<template>
	<div class="home-page">
		<!-- 导航菜单 -->
		<nav class="main-nav">
			<div class="nav-container">
				<div class="logo">中铁上海院数据采集与分享平台</div>
				<ul class="nav-items">
					<li :class="{ active: currentView === 'home' }" @click="switchView('home')">
						数据驾驶舱
					</li>
					<li :class="{ active: currentView === 'news' }" @click="switchView('news')">
						行业洞见
					</li>
					<li>智能财务中心</li>
					<li>协作中心</li>
					<li>联系我们</li>
				</ul>
			</div>
		</nav>

		<!-- 轮播背景图片区域 -->
		<div class="content-area">
			<div v-if="currentView === 'home'" class="home-content" data-animate="home-content">
				<!-- 这里放首页原有的内容 -->

				<el-carousel height="600px" :interval="3000" arrow="always" class="hero-section">
					<el-carousel-item v-for="(item, index) in carouselItems" :key="index">
						<div class="carousel-content">
							<img :src="item.bg" class="carousel-bg" alt="背景图片" />
							<div class="hero-content">
								<h1>{{ item.title }}</h1>
								<h2>{{ item.subtitle }}</h2>
								<p class="chinese-text">{{ item.description }}</p>
							</div>
						</div>
					</el-carousel-item>
				</el-carousel>

				<!-- 主要内容区域 -->
				<div class="content-section" data-animate="features">
					<div class="features-grid">
						<!-- 函证情况 -->
						<div
							class="feature-card animate-card"
							@click="handleClick('函证情况')"
							@mousemove="handleMouseMove"
							@mouseenter="handleCardEnter"
							@mouseleave="handleCardLeave"
							style="animation-delay: 0.1s"
						>
							<div class="card-ripple-overlay"></div>
							<div class="card-glow"></div>
							<el-icon :size="50" class="feature-icon">
								<document />
							</el-icon>
							<h3>函证情况</h3>
							<div class="card-hover-effect">
								<span class="card-arrow">→</span>
							</div>
						</div>

						<!-- 应收应付抵债情况 -->
						<div
							class="feature-card animate-card"
							@click="handleClick('应收应付抵债情况')"
							@mousemove="handleMouseMove"
							@mouseenter="handleCardEnter"
							@mouseleave="handleCardLeave"
							style="animation-delay: 0.2s"
						>
							<div class="card-ripple-overlay"></div>
							<div class="card-glow"></div>
							<el-icon :size="50" class="feature-icon">
								<money />
							</el-icon>
							<h3>应收应付抵债情况</h3>
							<div class="card-hover-effect">
								<span class="card-arrow">→</span>
							</div>
						</div>

						<!-- 三金压降情况 -->
						<div
							class="feature-card animate-card"
							@click="handleClick('三金压降情况')"
							@mousemove="handleMouseMove"
							@mouseenter="handleCardEnter"
							@mouseleave="handleCardLeave"
							style="animation-delay: 0.3s"
						>
							<div class="card-ripple-overlay"></div>
							<div class="card-glow"></div>
							<el-icon :size="50" class="feature-icon">
								<trend-charts />
							</el-icon>
							<h3>三金压降情况</h3>
							<div class="card-hover-effect">
								<span class="card-arrow">→</span>
							</div>
						</div>

						<!-- 坏账情况 -->
						<div
							class="feature-card animate-card"
							@click="handleClick('坏账情况')"
							@mousemove="handleMouseMove"
							@mouseenter="handleCardEnter"
							@mouseleave="handleCardLeave"
							style="animation-delay: 0.4s"
						>
							<div class="card-ripple-overlay"></div>
							<div class="card-glow"></div>
							<el-icon :size="50" class="feature-icon">
								<warning />
							</el-icon>
							<h3>坏账情况</h3>
							<div class="card-hover-effect">
								<span class="card-arrow">→</span>
							</div>
						</div>
						<div
							class="feature-card animate-card"
							@click="handleClick('数据治理')"
							@mousemove="handleMouseMove"
							@mouseenter="handleCardEnter"
							@mouseleave="handleCardLeave"
							style="animation-delay: 0.5s"
						>
							<div class="card-ripple-overlay"></div>
							<div class="card-glow"></div>
							<el-icon :size="50" class="feature-icon">
								<question-filled />
							</el-icon>
							<h3>数据治理</h3>
							<div class="card-hover-effect">
								<span class="card-arrow">→</span>
							</div>
						</div>
					</div>
				</div>

				<!-- 替换通知区域的内容 -->
				<div class="notice-section" data-animate="notice">
					<h2 class="section-title">新闻动态</h2>
					<div class="notice-container">
						<!-- 左侧装饰区域 -->
						<div class="notice-left">
							<div class="notice-icon-bg">
								<el-icon class="big-icon"><bell /></el-icon>
							</div>
							<div class="notice-stats">
								<div class="stat-item">
									<!-- <span class="stat-num">2024</span>
							<span class="stat-label">督导年度</span> -->
								</div>
								<div class="stat-item">
									<!-- <span class="stat-num">1000w+</span>
							<span class="stat-label">重点项目</span> -->
								</div>
							</div>
						</div>

						<!-- 右侧内容区域 -->
						<div class="notice-right">
							<div class="notice-header">
								<h2 class="notice-title">
									<el-icon><document /></el-icon>
									关于落实"三金"压控督导方案的通知
								</h2>
								<!-- <div class="notice-meta">
									<span class="notice-number">铁建财资网发〔2024〕454号</span>

								</div> -->
							</div>

							<div class="notice-body">
								<div class="notice-intro">
									<el-icon class="quote-icon"><info-filled /></el-icon>
									<p>
										根据股份公司《关于加强"三金"压控督导工作的通知》（铁建财资网发〔2024〕454号）及集团公司《关于开展2024-2025年"三金"压控专项行动的通知》（中铁上海院财〔2024〕31号）的要求，进一步达到穿透式管控的要求，确保各类任务得到有效执行并取得实质性进展。
									</p>
								</div>

								<div class="notice-content">
									<div class="content-item">
										<h3>
											<el-icon><circle-check-filled /></el-icon>督导范围
										</h3>
										<p>
											对集团公司所属子分公司，工程总承包事业部（以下简称各单位）的“三金”情况全面督导，其中重难点项目（应收未收大于1000万元）应重点跟踪督导。
										</p>
									</div>

									<div class="content-item">
										<h3>
											<el-icon><circle-check-filled /></el-icon>督导组织机构
										</h3>
										<p>
											集团公司成立"三金"压控专项行动领导小组，集团公司主要领导任组长，总会计师任常务副组长，其他领导班子成员任副组长，财务部、经营部等相关部门负责人为组员，领导小组下设办公室，办公室设立在集团公司财务部。
										</p>
									</div>

									<div class="content-item">
										<h3>
											<el-icon><circle-check-filled /></el-icon>督导时间及方式
										</h3>
										<div class="time-info">
											<el-icon><timer /></el-icon>
											<span>督导时间：2024年9-12月</span>
										</div>
										<p>
											督导方式：采取线上视频会议以及现场督导相结合的督导方式，为节约成本，尽量利用大监督，工作调研等机会对各单位进行现场督导。
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 在 footer-section 之前添加新的区域 -->
				<div class="showcase-section" data-animate="showcase">
					<h2 class="section-title">张榜揭榜</h2>
					<div class="showcase-grid">
						<!-- 第一个展示项 -->
						<div
							class="showcase-item animate-showcase"
							@click="handleClick('库存管理')"
							@mousemove="handleMouseMove"
							style="animation-delay: 0.1s"
						>
							<div class="image-placeholder">
								<img
									:src="bang1"
									alt="网站横幅"
									style="width: 100%; height: 100%; object-fit: cover"
								/>
								<div class="image-overlay">
									<span class="view-more">查看详情</span>
								</div>
							</div>
							<h3 class="item-title">库存管理</h3>
							<p class="item-desc">优化库存管理流程，提升管理效率</p>
						</div>

						<!-- 第二个展示项 -->
						<div
							class="showcase-item animate-showcase"
							@click="handleClick('资金流向分析')"
							@mousemove="handleMouseMove"
							style="animation-delay: 0.2s"
						>
							<div class="image-placeholder">
								<img
									:src="bang2"
									alt="网站横幅"
									style="width: 100%; height: 100%; object-fit: cover"
								/>
								<div class="image-overlay">
									<span class="view-more">查看详情</span>
								</div>
							</div>
							<h3 class="item-title">资金流向分析</h3>
							<p class="item-desc">精准把控资金流向，降低经营风险</p>
						</div>

						<!-- 第三个展示项 -->
						<div
							class="showcase-item animate-showcase"
							@click="handleClick('预算执行')"
							@mousemove="handleMouseMove"
							style="animation-delay: 0.3s"
						>
							<div class="image-placeholder">
								<img
									:src="bang3"
									alt="网站横幅"
									style="width: 100%; height: 100%; object-fit: cover"
								/>
								<div class="image-overlay">
									<span class="view-more">查看详情</span>
								</div>
							</div>
							<h3 class="item-title">预算执行监控</h3>
							<p class="item-desc">实时监控预算执行，确保合理使用</p>
						</div>

						<!-- 第四个展示项 -->
						<div
							class="showcase-item animate-showcase"
							@mousemove="handleMouseMove"
							style="animation-delay: 0.4s"
						>
							<div class="image-placeholder">
								<img
									:src="bang4"
									alt="网站横幅"
									style="width: 100%; height: 100%; object-fit: cover"
								/>
								<div class="image-overlay">
									<span class="view-more">查看详情</span>
								</div>
							</div>
							<h3 class="item-title">风险预警机制</h3>
							<p class="item-desc">建立全面风险预警，保障企业安全</p>
						</div>
					</div>
				</div>

				<!-- 底部标语 -->
				<div class="footer-section">
					<div class="footer-bottom">
						<div class="slogan">去相信，去证明，梦想一触即发</div>
						<div class="copyright">
							© {{ new Date().getFullYear() }} 中铁上海院数据采集与分享平台 版权所有
						</div>
					</div>
				</div>
			</div>

			<!-- 新闻动态内容 -->
			<template v-else-if="currentView === 'news'">
				<news-content v-if="!currentNewsId" @view-detail="handleViewDetail" />
				<news-detail v-else :id="currentNewsId" @back="handleNewsBack" />
			</template>
		</div>
	</div>
</template>

<script lang="ts" setup>
import {
	Document,
	Money,
	TrendCharts,
	Warning,
	Bell,
	InfoFilled,
	QuestionFilled,
	CircleCheckFilled,
	Timer,
	Location,
	Phone,
	Message
} from '@element-plus/icons-vue';

import { useCool } from '/@/cool';
import { useBase } from '/$/base';
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import bg from '/bg2.jpg';
import bg2 from '/bg1.jpg';
import bg3 from '/bgt2.jpg';
import bg4 from '/bgt1.jpg';
import bang1 from '/bang1.jpg';
import bang2 from '/bang2.jpg';
import bang3 from '/bang3.jpg';
import bang4 from '/bang4.jpg';
import newsContent from '../components/newsContent.vue';
import newsDetail from '../components/newsDetail.vue';

const { user, org } = useBase();
const { service, router } = useCool();

const username = user.info?.username;
const treeOptions = ref<TreeNode[]>([]);

const currentView = ref('home');
const currentNewsId = ref<number | null>(null);

// 动画相关状态
const animatedElements = ref<Map<string, boolean>>(new Map());

// 创建 Intersection Observer
let observer: IntersectionObserver | null = null;

// 动画工具函数

// 初始化 Intersection Observer
function initScrollAnimations() {
	observer = new IntersectionObserver(
		entries => {
			entries.forEach(entry => {
				const elementId = entry.target.getAttribute('data-animate');
				if (elementId && entry.isIntersecting && !animatedElements.value.get(elementId)) {
					animatedElements.value.set(elementId, true);
					entry.target.classList.add('animate-in');
				}
			});
		},
		{
			threshold: 0.1,
			rootMargin: '0px 0px -50px 0px'
		}
	);

	// 观察所有带有 data-animate 属性的元素
	nextTick(() => {
		const animateElements = document.querySelectorAll('[data-animate]');
		animateElements.forEach(el => observer?.observe(el));
	});
}

// 打字机效果
function typeWriter(element: HTMLElement, text: string, speed: number = 50) {
	element.textContent = '';
	let i = 0;
	const timer = setInterval(() => {
		if (i < text.length) {
			element.textContent += text.charAt(i);
			i++;
		} else {
			clearInterval(timer);
		}
	}, speed);
}

// 鼠标移动处理
function handleMouseMove(event: MouseEvent) {
	const target = event.currentTarget as HTMLElement;
	const rect = target.getBoundingClientRect();
	const x = ((event.clientX - rect.left) / rect.width) * 100;
	const y = ((event.clientY - rect.top) / rect.height) * 100;

	target.style.setProperty('--mouse-x', `${x}%`);
	target.style.setProperty('--mouse-y', `${y}%`);
}

// 卡片进入处理
function handleCardEnter(event: MouseEvent) {
	const target = event.currentTarget as HTMLElement;
	const rippleOverlay = target.querySelector('.card-ripple-overlay') as HTMLElement;

	if (rippleOverlay) {
		rippleOverlay.style.setProperty('--ripple-start-time', Date.now().toString());
		rippleOverlay.classList.add('ripple-active');
	}
}

// 卡片离开处理
function handleCardLeave(event: MouseEvent) {
	const target = event.currentTarget as HTMLElement;
	const rippleOverlay = target.querySelector('.card-ripple-overlay') as HTMLElement;

	if (rippleOverlay) {
		rippleOverlay.classList.remove('ripple-active');
	}
}

interface TreeNode {
	id: number;
	name: string;
	value: string;
	children: TreeNode[];
}

// 递归获取树形数据
async function getChildNodes(parentId: number | undefined, childrens): Promise<TreeNode[]> {
	const result: TreeNode[] = [];
	for (const child of childrens) {
		if (child.parentId == parentId) {
			result.push({
				id: child.id,
				name: child.name,
				value: child.value,
				children: await getChildNodes(child.id, childrens)
			});
		}
	}
	return result;
}

function switchView(view: string) {
	currentView.value = view;
	// 切换视图时重置新闻ID
	if (view !== 'news') {
		currentNewsId.value = null;
	}
}

function handleClick(params: string) {
	switch (params) {
		case '函证情况':
			router.push('/hz');
			break;
		case '应收应付抵债情况':
			router.push('/ysyf');
			break;
		case '三金压降情况':
			router.push('/sjyj');
			break;
		case '坏账情况':
			console.log('跳转');
			router.push('/huaizhang');
			break;
		case '数据治理':
			router.push('/ycdj');
			break;
		case '资金流向分析':
			router.push('/zjlx');
			break;
		case '预算执行':
			router.push('/yszx');
			break;
		case '库存管理':
			router.push('/kucunView');
			break;
	}
}

function handleViewDetail(newsId: number) {
	currentNewsId.value = newsId;
}

function handleNewsBack() {
	currentNewsId.value = null;
}

// 初始化树形数据
async function initTreeData(childrens, parentId_list) {
	// 获取根节点
	// const parentId_list = await service.dict.info.list({ typeId: 21, name: org.name });

	// 直接获取根节点的子节点作为顶层节点
	let allChildren: TreeNode[] = [];
	for (const root of parentId_list) {
		const children = await getChildNodes(root.id, childrens);
		allChildren = [...allChildren, ...children];
	}
	// const children = await getChildNodes(parentId_id, childrens);
	// allChildren = [...allChildren, ...children];

	treeOptions.value = allChildren;
}

// 定义轮播数据
const carouselItems = [
	{
		bg: bg,
		title: '降存遏增 分类管控',
		subtitle: '遵循全面精细化管理思路',
		description: '强化风险管理，细化需求，建立长效机制，全面降低"三金"规模，提升企业资产质量。'
	},
	{
		bg: bg2,
		title: '数字赋能 智慧管理',
		subtitle: '打造智能化业务平台',
		description: '运用数字化手段，提升管理效率，实现业务全流程可视化监控。'
	},
	{
		bg: bg3,
		title: '创新驱动 持续发展',
		subtitle: '推进管理模式升级',
		description: '以创新为动力，优化管理流程，提升企业核心竞争力。'
	},
	{
		bg: bg4,
		title: '创新驱动 持续发展',
		subtitle: '推进管理模式升级',
		description: '以创新为动力，优化管理流程，提升企业核心竞争力。'
	}
];

onMounted(async () => {
	// 初始化滚动动画
	initScrollAnimations();

	// await initTreeData();
	// const xm_list = await service.dict.info.list({ typeId: 21 });
	// console.log(alldepList);
	const allList = ref<(number | undefined)[]>([]);
	// const alldepList = ref([]);
	const dictList = await service.dict.type.list();
	const xmb = dictList.filter(item => item.name === '项目表');
	const xmb_id: number = xmb[0]?.id as number;
	allList.value.push(xmb_id);

	const hwb = dictList.filter(item => item.name === '货物表');
	const hwb_id = hwb[0].id;
	allList.value.push(hwb_id);
	// 调用getAllChildren接口
	const alldepList = await service.dict.info.getAllChildren({ typeId: xmb_id, status: 1 });
	// 模糊匹配带有部门的id
	// const depList = dictList.filter(item => item.name.includes("部门"));
	// depList.forEach(item => {
	// 	allList.value.push(item.id);
	// });
	const jflbb = dictList.filter(item => item.name === '经费类别表');
	const jflbb_id = jflbb[0].id;
	allList.value.push(jflbb_id);
	const yskzdl = dictList.filter(item => item.name === '预算控制大类');
	const yskzdl_id = yskzdl[0].id;
	allList.value.push(yskzdl_id);
	// 获取项目表、货物表、部门相关的数据  经费类别表、预算控制大类
	const xmb_hwb_dep = await service.dict.info.list({ typeId: allList.value });
	// 完全匹配带typeId与项目id相同的数据
	const xm_list = xmb_hwb_dep.filter(item => item.typeId === xmb_id);

	// depList.forEach(dep_xx => {
	// 	const temp_list = xmb_hwb_dep.filter(item => item.typeId === dep_xx.id);
	// 	temp_list.forEach(item => {
	// 		alldepList.value.push(item);
	// 	});
	// });

	const parentId_list = xmb_hwb_dep.filter(item => item.name === org.name);

	// 构建部门树形查询
	await initTreeData(alldepList[xmb_id], parentId_list);

	if (username == 'admin') {
		org.setListorg(
			xm_list.map(k => {
				return { name: k.name, code: k.value };
			})
		);
		if (!org.listOrg.some(item => item.name === org.name)) {
			org.setOrg(xm_list[0].name);
			org.setCode(xm_list[0].value);
		}
	} else {
		let flag_xm = 1;
		const result_xm: any[] = [];
		const xm_luru_list = await service.luru.luruPermission.list();
		xm_luru_list.forEach((item: any) => {
			if (item.权限所属人 == username && item.projectCode == 'ALL') {
				flag_xm = 0;
			}
		});
		if (flag_xm == 0) {
			org.setListorg(
				xm_list.map(k => {
					return { name: k.name, code: k.value };
				})
			);
			if (!org.listOrg.some(item => item.name === org.name)) {
				org.setOrg(xm_list[0].name);
				org.setCode(xm_list[0].value);
			}
		} else {
			xm_luru_list.forEach((item: any) => {
				if (item.权限所属人 == username) {
					// 判断是否重复插入
					const isExist = result_xm.some(
						entry => entry.name === item.项目名称 && entry.code === item.projectCode
					);
					if (isExist) {
						console.log('对象已存在于数组中。');
					} else {
						result_xm.push({ name: item.项目名称, code: item.projectCode });
					}
				}
			});
			org.setListorg(result_xm);
			if (!org.listOrg.some(item => item.name === org.name)) {
				org.setOrg(result_xm[0].name);
				org.setCode(result_xm[0].code);
			}
		}
	}

	// const hw_list = await service.dict.info.list({ typeId: 22 });
	// 完全匹配带typeId与货物id相同的数据
	const hw_list = xmb_hwb_dep.filter(item => item.typeId === hwb_id);
	org.setListhw(
		hw_list.map(m => {
			return { value: m.name };
		})
	);

	org.listpart = [];
	org.setListpart(
		treeOptions.value.map((a: any) => {
			return { id: a.id, name: a.name, value: a.value, children: a.children };
		})
	);

	const list_yusuandalei = xmb_hwb_dep.filter(item => item.typeId === yskzdl_id);
	org.setListyskzdl(
		list_yusuandalei.map(m => {
			return { value: m.name };
		})
	);

	const list_jingfeileibie = xmb_hwb_dep.filter(item => item.typeId === jflbb_id);
	org.setListjflb(
		list_jingfeileibie.map(m => {
			return { value: m.name };
		})
	);
});

// 清理函数
onUnmounted(() => {
	if (observer) {
		observer.disconnect();
		observer = null;
	}
});
</script>

<style scoped lang="scss">
.home-page {
	width: 100%;
	background-color: #f5f7fa;
	display: block;
	overflow-x: hidden;
}

.main-nav {
	background: #fff;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	width: 100%;
	z-index: 100;

	.nav-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 20px;
		height: 70px;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.logo {
			font-size: 24px;
			font-weight: bold;
			color: #2c7a7b;
		}

		.nav-items {
			display: flex;
			gap: 30px;
			list-style: none;
			margin: 0;
			padding: 0;

			li {
				cursor: pointer;
				padding: 8px 16px;
				border-radius: 4px;
				transition: all 0.3s ease;
				color: #333;

				&:hover,
				&.active {
					background-color: #38a169;
					color: white;
				}
			}
		}
	}
}

.hero-section {
	.el-carousel__item {
		.carousel-content {
			height: 100%;
			position: relative;

			.carousel-bg {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: rgba(0, 0, 0, 0.4);
				z-index: 1;
			}

			.hero-content {
				position: relative;
				z-index: 2;
			}
		}
	}

	:deep(.el-carousel__arrow) {
		background-color: rgba(170, 207, 207, 0.7);

		&:hover {
			background-color: rgba(115, 171, 141, 0.9);
		}
	}

	:deep(.el-carousel__indicators) {
		.el-carousel__button {
			background-color: rgba(255, 255, 255, 0.7);
			width: 30px;
			border-radius: 3px;
		}
	}
}

.hero-content {
	position: relative;
	max-width: 1200px;
	margin: 0 auto;
	padding: 200px 20px;
	color: white;
	text-align: center;
	z-index: 1;

	h1 {
		font-size: 48px;
		margin-bottom: 20px;
		font-weight: bold;
		text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
	}

	h2 {
		font-size: 24px;
		font-weight: normal;
		opacity: 0.9;
	}

	.chinese-text {
		font-size: 1.1rem;
		margin-bottom: 15px;
		margin-top: 20px;
		display: inline-block;
		transform: skewX(-15deg);
		transform-origin: bottom left;
		padding-right: 2px;
		color: #9ae6b4;
		text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
	}
}

.content-section {
	max-width: 1200px;
	margin: -100px auto 50px;
	padding: 0 20px;
	position: relative;
	z-index: 2;

	.features-grid {
		display: grid;
		grid-template-columns: repeat(5, 1fr);
		gap: 30px;
	}

	.feature-card {
		background: white;
		padding: 30px;
		border-radius: 10px;
		text-align: center;
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
		transition: transform 0.3s ease;

		&:hover {
			transform: translateY(-10px);
		}

		.feature-icon {
			color: #38a169;
			margin-bottom: 20px;
		}

		h3 {
			font-size: 20px;
			color: #2c7a7b;
			margin-bottom: 15px;
		}

		.chinese-desc {
			color: #333;
			margin-bottom: 8px;
		}

		.english-desc {
			color: #666;
			font-size: 14px;
			font-style: italic;
		}
	}
}

@media (max-width: 1024px) {
	.content-section .features-grid {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (max-width: 768px) {
	.main-nav .nav-container {
		flex-direction: column;
		height: auto;
		padding: 15px;

		.nav-items {
			flex-wrap: wrap;
			justify-content: center;
			gap: 10px;
		}
	}

	.hero-section {
		height: 400px;

		.hero-content {
			padding: 100px 20px;

			h1 {
				font-size: 32px;
			}

			h2 {
				font-size: 18px;
			}
		}
	}

	.content-section .features-grid {
		grid-template-columns: 1fr;
	}
}

.notice-section {
	max-width: 1200px;
	margin: 50px auto;
	padding: 0 20px;
	.section-title {
		text-align: center;
		font-size: 32px;
		color: #2c7a7b;
		margin-bottom: 40px;
		font-weight: bold;
		position: relative;

		&::after {
			content: '';
			position: absolute;
			bottom: -10px;
			left: 50%;
			transform: translateX(-50%);
			width: 60px;
			height: 3px;
			background: #38a169;
			border-radius: 3px;
		}
	}

	.notice-container {
		display: flex;
		gap: 30px;
		background: linear-gradient(to right, #ffffff 0%, #f8f9fa 100%);
		border-radius: 20px;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
		overflow: hidden;
		border: 1px solid rgba(26, 82, 118, 0.1);
	}

	.notice-left {
		width: 200px;
		background: linear-gradient(135deg, #2c7a7b 0%, #38a169 100%);
		padding: 40px 20px;
		color: white;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;

		&::after {
			content: '';
			position: absolute;
			right: -20px;
			top: 50%;
			transform: translateY(-50%);
			width: 40px;
			height: 40px;
			background: #2c7a7b;
			transform: rotate(45deg);
		}

		.notice-icon-bg {
			width: 120px;
			height: 120px;
			background: rgba(255, 255, 255, 0.1);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 40px;

			.big-icon {
				font-size: 60px;
				color: white;
			}
		}

		.notice-stats {
			width: 100%;
			display: flex;
			flex-direction: column;
			gap: 20px;

			.stat-item {
				text-align: center;
				padding: 15px;
				background: rgba(255, 255, 255, 0.1);
				border-radius: 10px;

				.stat-num {
					display: block;
					font-size: 24px;
					font-weight: bold;
					margin-bottom: 5px;
				}

				.stat-label {
					font-size: 14px;
					opacity: 0.8;
				}
			}
		}
	}

	.notice-right {
		flex: 1;
		padding: 40px;

		.notice-header {
			margin-bottom: 30px;

			.notice-title {
				display: flex;
				align-items: center;
				gap: 10px;
				font-size: 24px;
				color: #2c7a7b;
				margin-bottom: 15px;

				.el-icon {
					font-size: 28px;
				}
			}

			.notice-meta {
				display: flex;
				gap: 20px;
				color: #666;
				font-size: 14px;
			}
		}

		.notice-body {
			.notice-intro {
				position: relative;
				background: rgba(26, 82, 118, 0.05);
				padding: 20px;
				border-radius: 10px;
				margin-bottom: 30px;

				.quote-icon {
					position: absolute;
					top: -10px;
					left: -10px;
					font-size: 24px;
					color: #2c7a7b;
					background: white;
					border-radius: 50%;
				}

				p {
					color: #333;
					line-height: 1.8;
					margin: 0;
				}
			}

			.notice-content {
				.content-item {
					margin-bottom: 25px;

					h3 {
						display: flex;
						align-items: center;
						gap: 10px;
						color: #2c7a7b;
						font-size: 18px;
						margin-bottom: 15px;

						.el-icon {
							color: #2ecc71;
						}
					}

					.time-info {
						display: flex;
						align-items: center;
						gap: 8px;
						color: #2c7a7b;
						margin-bottom: 10px;
					}

					p {
						color: #333;
						line-height: 1.8;
						margin: 0;
						padding-left: 28px;
					}
				}
			}
		}
	}
}

@media (max-width: 768px) {
	.notice-section {
		.notice-container {
			flex-direction: column;
		}

		.notice-left {
			width: 100%;
			padding: 30px 20px;

			&::after {
				display: none;
			}

			.notice-stats {
				flex-direction: row;
				justify-content: center;
				gap: 15px;

				.stat-item {
					flex: 1;
				}
			}
		}

		.notice-right {
			padding: 20px;
		}
	}
}

.footer-section {
	background: #2c7a7b;
	padding: 60px 0 20px;
	color: white;

	.footer-content {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 20px;
	}

	.footer-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 40px;
		margin-bottom: 50px;
	}

	.footer-column {
		h3 {
			font-size: 18px;
			margin-bottom: 20px;
			color: #9ae6b4;
			position: relative;
			padding-bottom: 10px;

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 0;
				width: 30px;
				height: 2px;
				background: #38a169;
			}
		}

		ul {
			list-style: none;
			padding: 0;
			margin: 0;

			li {
				margin-bottom: 12px;
				cursor: pointer;
				transition: color 0.3s;
				opacity: 0.8;

				&:hover {
					color: #9ae6b4;
					opacity: 1;
				}
			}
		}

		p {
			margin-bottom: 12px;
			display: flex;
			align-items: center;
			gap: 8px;
			opacity: 0.8;

			.el-icon {
				color: #9ae6b4;
			}
		}

		.qrcode-placeholder {
			width: 140px;
			height: 140px;
			background: rgba(255, 255, 255, 0.1);
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 8px;
			margin: 0 auto;

			.qrcode-text {
				color: #9ae6b4;
				font-size: 14px;
				text-align: center;
			}
		}
	}

	.footer-bottom {
		padding-top: 20px;
		border-top: 1px solid rgba(255, 255, 255, 0.1);
		text-align: center;

		.slogan {
			font-size: 24px;
			color: #9ae6b4;
			margin-bottom: 15px;
			font-weight: bold;
		}

		.copyright {
			font-size: 14px;
			opacity: 0.6;
		}
	}
}

@media (max-width: 1024px) {
	.footer-section .footer-grid {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (max-width: 768px) {
	.footer-section {
		padding: 40px 0 20px;

		.footer-grid {
			grid-template-columns: 1fr;
			gap: 30px;
		}

		.footer-column {
			text-align: center;

			h3::after {
				left: 50%;
				transform: translateX(-50%);
			}

			p {
				justify-content: center;
			}
		}
	}
}

.showcase-section {
	max-width: 1200px;
	margin: 50px auto;
	padding: 0 20px;

	.section-title {
		text-align: center;
		font-size: 32px;
		color: #2c7a7b;
		margin-bottom: 40px;
		font-weight: bold;
		position: relative;

		&::after {
			content: '';
			position: absolute;
			bottom: -10px;
			left: 50%;
			transform: translateX(-50%);
			width: 60px;
			height: 3px;
			background: #38a169;
			border-radius: 3px;
		}
	}

	.showcase-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 30px;
	}

	.showcase-item {
		background: white;
		border-radius: 10px;
		overflow: hidden;
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
		transition: transform 0.3s ease;

		&:hover {
			transform: translateY(-5px);
		}

		.image-placeholder {
			width: 100%;
			height: 200px;
			background: #f5f7fa;
			display: flex;
			align-items: center;
			justify-content: center;
			border-bottom: 1px solid #eee;

			.placeholder-text {
				color: #999;
				font-size: 14px;
			}
		}

		.item-title {
			font-size: 18px;
			color: #2c7a7b;
			margin: 15px;
			text-align: center;
		}

		.item-desc {
			font-size: 14px;
			color: #666;
			margin: 0 15px 15px;
			text-align: center;
			line-height: 1.5;
		}
	}
}

@media (max-width: 1024px) {
	.showcase-section {
		.showcase-grid {
			grid-template-columns: repeat(2, 1fr);
		}
	}
}

@media (max-width: 768px) {
	.showcase-section {
		.showcase-grid {
			grid-template-columns: 1fr;
		}

		.section-title {
			font-size: 28px;
		}
	}
}

:global(body) {
	margin: 0;
	padding: 0;
	overflow-y: auto !important;
	overflow-x: hidden;
	height: auto;
	min-height: 100%;
}

:global(html) {
	height: 100%;
}

/* 动画样式 */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes fadeInLeft {
	from {
		opacity: 0;
		transform: translateX(-30px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes fadeInRight {
	from {
		opacity: 0;
		transform: translateX(30px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes scaleIn {
	from {
		opacity: 0;
		transform: scale(0.9);
	}
	to {
		opacity: 1;
		transform: scale(1);
	}
}

@keyframes pulse {
	0%,
	100% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.05);
	}
}

@keyframes glow {
	0%,
	100% {
		box-shadow: 0 0 5px rgba(56, 161, 105, 0.3);
	}
	50% {
		box-shadow:
			0 0 20px rgba(56, 161, 105, 0.6),
			0 0 30px rgba(56, 161, 105, 0.4);
	}
}

/* 滚动动画 */
[data-animate] {
	opacity: 0;
	transform: translateY(50px);
	transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

[data-animate].animate-in {
	opacity: 1;
	transform: translateY(0);
}

/* 扩散动画关键帧 */
@keyframes rippleSpread {
	0% {
		transform: scale(0);
		opacity: 0;
	}
	10% {
		opacity: 0.3;
	}
	100% {
		transform: scale(4);
		opacity: 0;
	}
}

@keyframes cardRipple {
	0% {
		background-size: 0% 0%;
		opacity: 0;
	}
	50% {
		opacity: 0.8;
	}
	100% {
		background-size: 400% 400%;
		opacity: 0.1;
	}
}

/* 卡片动画 */
.animate-card {
	transform: translateY(20px);
	opacity: 0;
	animation: fadeInUp 0.6s ease-out forwards;
}

.feature-card {
	position: relative;
	overflow: hidden;
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(
			135deg,
			rgba(56, 161, 105, 0.85) 0%,
			rgba(44, 122, 123, 0.85) 50%,
			rgba(154, 230, 180, 0.85) 100%
		);
		transform: translateY(100%);
		transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		z-index: 1;
	}

	&::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: radial-gradient(
			circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
			rgba(255, 255, 255, 0.3) 0%,
			rgba(255, 255, 255, 0.2) 30%,
			rgba(255, 255, 255, 0.1) 60%,
			transparent 100%
		);
		background-size: 200% 200%;
		opacity: 0;
		transform: scale(0);
		transition: all 0.4s ease;
		z-index: 2;
		pointer-events: none;
	}

	&:hover {
		transform: translateY(-15px) scale(1.02);
		box-shadow: 0 20px 40px rgba(56, 161, 105, 0.25);
		color: white;

		&::before {
			transform: translateY(0%);
		}

		&::after {
			opacity: 1;
			transform: scale(1);
		}

		.card-glow {
			opacity: 1;
			animation: glow 1.5s infinite;
		}

		.card-hover-effect {
			opacity: 1;
			transform: translateX(0);
		}

		.feature-icon {
			transform: scale(1.1);
			animation: pulse 1s infinite;
			color: white;
		}

		h3 {
			color: white;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
		}
	}

	.card-ripple-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: radial-gradient(
			circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
			rgba(255, 255, 255, 0.4) 0%,
			rgba(255, 255, 255, 0.3) 25%,
			rgba(255, 255, 255, 0.2) 50%,
			rgba(255, 255, 255, 0.1) 75%,
			transparent 100%
		);
		background-size: 0% 0%;
		background-repeat: no-repeat;
		opacity: 0;
		pointer-events: none;
		z-index: 3;
		transition: opacity 0.3s ease;

		&.ripple-active {
			animation: cardRipple 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
		}
	}

	.card-glow {
		position: absolute;
		top: -2px;
		left: -2px;
		right: -2px;
		bottom: -2px;
		background: linear-gradient(45deg, #38a169, #68d391, #38a169);
		border-radius: 12px;
		opacity: 0;
		z-index: -1;
		transition: opacity 0.3s ease;
	}

	.card-hover-effect {
		position: absolute;
		top: 15px;
		right: 15px;
		opacity: 0;
		transform: translateX(10px);
		transition: all 0.3s ease;
		z-index: 2;

		.card-arrow {
			display: inline-flex;
			align-items: center;
			justify-content: center;
			width: 30px;
			height: 30px;
			background: #38a169;
			color: white;
			border-radius: 50%;
			font-size: 14px;
			font-weight: bold;
		}
	}

	.feature-icon {
		transition: all 0.3s ease;
		position: relative;
		z-index: 4;
	}

	h3 {
		position: relative;
		z-index: 4;
		transition: all 0.3s ease;
	}

	.card-hover-effect {
		z-index: 5;
	}
}

/* 展示卡片动画 */
.animate-showcase {
	transform: translateY(30px);
	opacity: 0;
	animation: fadeInUp 0.8s ease-out forwards;
}

.showcase-item {
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

	&:hover {
		transform: translateY(-8px);
		box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);

		.image-placeholder img {
			transform: scale(1.1);
		}

		.image-overlay {
			opacity: 1;
			transform: scale(1);
		}

		.item-title {
			color: #38a169;
		}
	}

	.image-placeholder {
		position: relative;
		overflow: hidden;

		img {
			transition: transform 0.4s ease;
		}

		.image-overlay {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: radial-gradient(
				circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
				rgba(56, 161, 105, 0.9) 0%,
				rgba(56, 161, 105, 0.7) 30%,
				rgba(56, 161, 105, 0.5) 60%,
				transparent 100%
			);
			display: flex;
			align-items: center;
			justify-content: center;
			opacity: 0;
			transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
			transform: scale(0.3);
			transform-origin: var(--mouse-x, 50%) var(--mouse-y, 50%);

			.view-more {
				color: white;
				font-size: 16px;
				font-weight: 600;
				padding: 8px 16px;
				border: 2px solid white;
				border-radius: 25px;
				background: transparent;
				transition: all 0.3s ease;

				&:hover {
					background: white;
					color: #38a169;
				}
			}
		}
	}

	.item-title {
		transition: color 0.3s ease;
	}
}

/* 导航动画 */
.main-nav {
	transition: all 0.3s ease;
	backdrop-filter: blur(10px);

	.nav-items li {
		position: relative;
		overflow: hidden;

		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: -100%;
			right: 0;
			bottom: 0;
			background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
			transition: left 0.5s ease;
		}

		&:hover::before {
			left: 100%;
		}
	}
}

/* 轮播动画增强 */
.hero-section {
	.hero-content {
		h1,
		h2,
		.chinese-text {
			opacity: 0;
			animation: fadeInUp 0.8s ease-out forwards;
		}

		h1 {
			animation-delay: 0.2s;
		}

		h2 {
			animation-delay: 0.4s;
		}

		.chinese-text {
			animation-delay: 0.6s;
		}
	}
}

/* 视差滚动效果 */
.carousel-bg {
	transition: transform 0.3s ease;
}

.hero-section:hover .carousel-bg {
	transform: scale(1.05);
}

/* 响应式动画 */
@media (max-width: 768px) {
	.feature-card:hover {
		transform: translateY(-8px) scale(1.01);
	}

	.feature-card .card-ripple-overlay.ripple-active {
		animation-duration: 0.6s;
	}

	.showcase-item:hover {
		transform: translateY(-4px);
	}
}

/* 加载动画 */
@keyframes shimmer {
	0% {
		background-position: -200px 0;
	}
	100% {
		background-position: calc(200px + 100%) 0;
	}
}

.loading-shimmer {
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200px 100%;
	animation: shimmer 1.5s infinite;
}
</style>
