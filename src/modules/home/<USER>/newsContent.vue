<!-- 新闻动态组件 -->
<template>
	<div class="news-container">
		<!-- 左侧新闻列表 -->
		<div class="news-list">
			<div
				v-for="(item, index) in newsList"
				:key="index"
				class="news-item"
				@click="viewDetail(item)"
			>
				<div class="news-image">
					<el-image :src="item.image" fit="cover" />
				</div>
				<div class="news-content">
					<h3 class="news-title">{{ item.title }}</h3>
					<div class="news-desc" v-html="item.description"></div>
					<div class="news-meta">
						<span class="date">
							<el-icon><calendar /></el-icon>
							{{ item.date }}
						</span>
						<span class="views">
							<el-icon><view /></el-icon>
							{{ item.views }} 阅读
						</span>
					</div>
				</div>
			</div>

			<!-- 分页 -->
			<div class="pagination">
				<el-pagination
					v-model:current-page="currentPage"
					v-model:page-size="pageSize"
					:total="total"
					background
					layout="prev, pager, next"
					@current-change="handlePageChange"
				/>
			</div>
		</div>

		<!-- 右侧热门新闻 -->
		<div class="hot-news">
			<h2>热门新闻</h2>
			<ul>
				<li v-for="(item, index) in hotNews" :key="index" @click="viewDetail(item)">
					<span class="hot-rank" :class="{ 'top-3': index < 3 }">{{ index + 1 }}</span>
					<span class="hot-title">{{ item.title }}</span>
				</li>
			</ul>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Calendar, View } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { newsList } from '../data/newsList';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(8);
const hotNews: any = ref([]);
// 热门新闻数据
onMounted(() => {
	hotNews.value = sortByViewsAndFormat(newsList);
});
function sortByViewsAndFormat(array) {
	// 1. 按 views 降序排序（从大到小）
	const sortedArray = [...array].sort((a, b) => b.views - a.views);

	// 2. 提取 id 和 title 组成新数组
	const result = sortedArray.map(item => ({
		id: item.id,
		title: item.title
	}));

	return result;
}

// 查看新闻详情
const emit = defineEmits(['view-detail']);

function viewDetail(item: any) {
	emit('view-detail', item.id);
}

// 处理分页变化
function handlePageChange(page: number) {
	currentPage.value = page;
	// TODO: 加载对应页码的数据
}
</script>

<style lang="scss" scoped>
.news-container {
	display: grid;
	grid-template-columns: 1fr 300px;
	gap: 30px;
	padding: 20px 0;
	max-width: 1200px;
	margin: 0 auto;

	.news-list {
		.news-item {
			display: flex;
			gap: 20px;
			padding: 20px;
			border-radius: 8px;
			background: #fff;
			box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
			margin-bottom: 20px;
			cursor: pointer;
			transition: all 0.3s ease;

			&:hover {
				transform: translateY(-2px);
				box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
			}

			.news-image {
				width: 200px;
				height: 140px;
				border-radius: 4px;
				overflow: hidden;

				.el-image {
					width: 100%;
					height: 100%;
				}
			}

			.news-content {
				flex: 1;

				.news-title {
					font-size: 18px;
					color: #333;
					margin-bottom: 10px;
					line-height: 1.4;
				}

				.news-desc {
					font-size: 14px;
					color: #666;
					line-height: 1.6;
					margin-bottom: 15px;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					overflow: hidden;
				}

				.news-meta {
					display: flex;
					gap: 20px;
					color: #999;
					font-size: 13px;

					span {
						display: flex;
						align-items: center;
						gap: 4px;

						.el-icon {
							font-size: 14px;
						}
					}
				}
			}
		}
	}

	.hot-news {
		background: #fff;
		padding: 20px;
		border-radius: 8px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

		h2 {
			font-size: 18px;
			color: #333;
			margin-bottom: 20px;
			padding-bottom: 10px;
			border-bottom: 2px solid #409eff;
		}

		ul {
			li {
				display: flex;
				align-items: center;
				gap: 10px;
				padding: 12px 0;
				cursor: pointer;
				border-bottom: 1px solid #eee;

				&:last-child {
					border-bottom: none;
				}

				&:hover .hot-title {
					color: #409eff;
				}

				.hot-rank {
					width: 24px;
					height: 24px;
					display: flex;
					align-items: center;
					justify-content: center;
					background: #eee;
					color: #666;
					border-radius: 4px;
					font-size: 13px;

					&.top-3 {
						background: #409eff;
						color: #fff;
					}
				}

				.hot-title {
					flex: 1;
					font-size: 14px;
					color: #333;
					transition: color 0.3s ease;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}
		}
	}

	.pagination {
		margin-top: 30px;
		display: flex;
		justify-content: center;
	}
}
</style>
