<!-- 新闻详情组件 -->
<template>
	<div class="news-detail">
		<div class="detail-container">
			<div class="back-button" @click="handleBack">
				<el-icon><back /></el-icon>
				返回
			</div>
			<h1 class="news-title">{{ currentNews?.title }}</h1>
			<div class="news-meta">
				<span class="date">
					<el-icon><calendar /></el-icon>
					{{ currentNews?.date }}
				</span>
				<!-- <span class="views">
					<el-icon><view /></el-icon>
					{{ currentNews?.views }} 阅读
				</span> -->
			</div>
			<div class="news-content" v-html="currentNews?.description"></div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Calendar, View, Back } from '@element-plus/icons-vue';
import { newsList } from '../data/newsList';

const props = defineProps<{
	id: number;
}>();

const emit = defineEmits(['back']);

const currentNews = ref<any>(null);

onMounted(() => {
	// 根据ID查找对应的新闻
	currentNews.value = newsList.find(news => news.id === props.id);
});

function handleBack() {
	emit('back');
}
</script>

<style lang="scss" scoped>
.news-detail {
	max-width: 1200px;
	margin: 0 auto;
	padding: 30px 20px;

	.detail-container {
		position: relative;
		background: #fff;
		padding: 40px;
		border-radius: 8px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

		.back-button {
			display: flex;
			align-items: center;
			gap: 5px;
			color: #666;
			cursor: pointer;
			padding: 8px 16px;
			border-radius: 4px;
			transition: all 0.3s ease;

			&:hover {
				color: #409eff;
				background: #f5f7fa;
			}
		}

		.news-title {
			font-size: 28px;
			color: #333;
			margin-bottom: 20px;
			text-align: center;
		}

		.news-meta {
			display: flex;
			justify-content: center;
			gap: 20px;
			color: #999;
			font-size: 14px;
			margin-bottom: 30px;
			padding-bottom: 20px;
			border-bottom: 1px solid #eee;

			span {
				display: flex;
				align-items: center;
				gap: 4px;

				.el-icon {
					font-size: 16px;
				}
			}
		}

		.news-content {
			color: #333;
			line-height: 1.8;
			font-size: 16px;

			:deep(p) {
				margin-bottom: 1em;
			}

			:deep(img) {
				max-width: 100%;
				height: auto;
				margin: 20px 0;
				border-radius: 4px;
			}

			:deep(h2) {
				font-size: 24px;
				color: #2c7a7b;
				margin: 30px 0 20px;
			}

			:deep(h3) {
				font-size: 20px;
				color: #2c7a7b;
				margin: 25px 0 15px;
			}

			:deep(ul),
			:deep(ol) {
				padding-left: 20px;
				margin: 15px 0;
			}

			:deep(li) {
				margin-bottom: 10px;
			}

			:deep(blockquote) {
				border-left: 4px solid #38a169;
				padding-left: 20px;
				margin: 20px 0;
				color: #666;
				font-style: italic;
			}
		}
	}
}

@media (max-width: 768px) {
	.news-detail {
		padding: 20px;

		.detail-container {
			padding: 20px;

			.news-title {
				font-size: 24px;
			}
		}
	}
}
</style>
