<template>
	<cl-crud ref="Crud">
		<cl-row>
			<cl-refresh-btn />
			<cl-add-btn />
			<cl-multi-delete-btn />
			<input
				type="file"
				ref="upload"
				@change="handleFileChange"
				accept=".xlsx,.xls"
				v-show="false"
				id="filepart"
			/>
			<el-button type="primary" @click="handleFileChange" style="margin-left: 10px"
				>上传文件</el-button
			>
			<el-button type="primary" @click="Download">下载模板</el-button>
			<!-- <el-button type="primary" @click="jQuery">jQuery值获取方法</el-button> -->
			<cl-flex1 />
			<cl-search-key />
		</cl-row>

		<el-row>
			<cl-table
				ref="Table"
				:default-sort="{
					prop: 'createTime',
					order: 'descending'
				}"
			/>
		</el-row>

		<el-row>
			<cl-flex1 />
			<cl-pagination />
		</el-row>

		<cl-upsert ref="Upsert"></cl-upsert>
	</cl-crud>
</template>

<script lang="ts" setup>
import { useTable, useUpsert, useCrud } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import * as XLSX from 'xlsx';

const { service } = useCool();

let excelData = ref<any[]>([]);
let temporarylist: any = [];
const url = `${import.meta.env.VITE_TEMPLATE_URL}/对标单位模板.xlsx`;
// let url_jQuery = 'http://***************:9001/public/uploads/获取页面jQuery值.docx'

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.zhongtie.duibiaodanwei
	},
	app => {
		app.refresh();
	}
);

// cl-upsert 配置
const Upsert = useUpsert({
	dialog: {
		width: '500px'
	},

	items: [
		{
			prop: 'name',
			label: '单位名称',
			// span: 10,
			required: true,
			component: {
				name: 'el-input'
			}
		},
		{
			prop: 'only_code',
			label: '统一代码',
			// span: 24,
			required: true,
			component: {
				name: 'el-input'
			}
		},
		{
			prop: 'as_name',
			label: '名称(简称)',
			// span: 10,
			required: true,
			component: {
				name: 'el-input'
			}
		},
		{
			prop: 'stock_code',
			label: '股票代码',
			// span: 24,
			required: true,
			component: {
				name: 'el-input'
			}
		}
	]
});

// cl-table 配置
const Table = useTable({
	columns: [
		{
			type: 'selection',
			width: 60
		},
		{
			prop: 'name',
			label: '单位名称',
			minWidth: 220
		},
		{
			prop: 'only_code',
			label: '社会统一代码',
			minWidth: 160
		},
		{
			prop: 'as_name',
			label: '名称（简称）',
			minWidth: 120
		},
		{
			prop: 'stock_code',
			label: '股票代码',
			minWidth: 100
		},
		{
			label: '操作',
			type: 'op',
			buttons: ['edit', 'delete']
		}
	]
});

const handleFileChange = (event: Event) => {
	// 定义按钮
	const input = document.querySelector('#filepart') as any;
	// 点击
	input.click();
	console.log(input.value);
	const target = event.target as HTMLInputElement;
	const file = target.files?.[0];
	if (file) {
		excelData = ref([]);
		temporarylist = [];
		const reader = new FileReader();
		reader.onload = e => {
			const data = new Uint8Array(e.target?.result as ArrayBuffer);
			const workbook = XLSX.read(data, { type: 'array' });
			const worksheet = workbook.Sheets[workbook.SheetNames[0]];
			const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

			excelData.value = jsonData.map((row: any, index: number) => ({
				index: index + 1,
				data: row
			}));
		};
		reader.readAsArrayBuffer(file);
		ElMessage({
			type: 'success',
			message: '上传文件成功!'
		});
		setTimeout(() => {
			readExcelData();
			// 重置文件输入的元素
			input.value = '';
		}, 500);
	}
};

const readExcelData = async () => {
	if (excelData.value.length > 1) {
		const list_part = await service.zhongtie.duibiaodanwei.list();
		let name = -1;
		let only_code = -1;
		let as_name = -1;
		let stock_code = -1;

		const Result = excelData.value[0].data;
		for (let i = 0; i < Result.length; i++) {
			if ('单位名称' == Result[i]) {
				name = i;
			}
			if ('社会统一代码' == Result[i]) {
				only_code = i;
			}
			if ('名称（简称）' == Result[i]) {
				as_name = i;
			}
			if ('股票代码' == Result[i]) {
				stock_code = i;
			}
		}
		excelData.value.shift();
		excelData.value.forEach((item: any) => {
			const result = item.data;
			// 标记 判断数据库有无相同代码
			let flag_code = 0;
			for (let i = 0; i < list_part.length; i++) {
				if (result[stock_code] == list_part[i].stock_code) {
					flag_code = 1;
					break;
				}
			}
			if (flag_code == 0) {
				temporarylist.push({
					name: result[name],
					only_code: result[only_code],
					as_name: result[as_name],
					stock_code: result[stock_code]
				});
			}
		});
	}
	// 读取 Excel 数据的逻辑
	const result_list: any[] = [];
	temporarylist.forEach((result: any) => {
		result_list.push({
			name: result.name,
			only_code: result.only_code,
			as_name: result.as_name,
			stock_code: result.stock_code
		});
	});
	if (result_list.length > 0) {
		service.zhongtie.duibiaodanwei.add(result_list);
		ElMessage({
			message: '上传成功，请稍后',
			type: 'success'
		});
	}
	// 将数据设置为空
	excelData = ref([]);
	temporarylist = [];
};

const Download = () => {
	window.location.href = url;
};

// const jQuery = () => {
// 	window.location.href = url_jQuery
// }
</script>
