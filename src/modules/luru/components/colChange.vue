<template>
	<el-dialog
		v-model="changeColVisible"
		title="自定义列(可拖拽排序)"
		width="80%"
		:close-on-click-modal="false"
		@close="closeDialog"
		@open="openDialog"
		>
		<div class="left">
			<draggable
				v-model="internalColumns"
				item-key="name"
				:animation="200"
				ghost-class="ghost"
				class="columns-container"
				@start="onDragStart"
				@end="onDragEnd"
				@change="onDragChange"
			>
				<template #item="{ element }">
					<el-checkbox border v-model="element.selected">{{
						element.showName == "prjectCode" ? "项目代码" : element.showName
					}}</el-checkbox>
				</template>
			</draggable>
		</div>
		<template #footer>
			<el-button @click="closeDialog">取消</el-button>
			<el-button type="primary" @click="submitChanges">确认</el-button>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, toRefs } from "vue";
import Draggable from "vuedraggable/src/vuedraggable";
import { storage } from "/@/cool";

const props = defineProps({
	changeColVisible: Boolean,
	name: String,
	allColumns: {
		type: Array,
		default: () => []
	}
});
const { changeColVisible, name } = toRefs(props);
const emit = defineEmits(["closeDialog", "submitData"]);
const internalColumns = ref();
function openDialog() {
	internalColumns.value = props.allColumns.map((col: any) => ({
		...col,
		selected: col.selected
	}));
}

const closeDialog = () => {
	emit("closeDialog", false);
};

const submitChanges = () => {
	storage.set(name.value, internalColumns.value);
	const updatedColumns = internalColumns.value
		.filter((col) => col.selected)
		.map(({ name }) => ({ name }));
	console.log("updatedColumns", updatedColumns, "internalColumns", internalColumns.value);
	emit("submitData", internalColumns.value);
	closeDialog();
};

const onDragStart = (evt) => {
	console.log("拖拽开始", evt);
};

const onDragEnd = (evt) => {
	console.log("拖拽结束", evt);
};

const onDragChange = (evt) => {
	console.log("拖拽变化", evt);
};
</script>

<style scoped lang="scss">
.left {
	.el-checkbox {
		margin: 5px 10px 5px 0;
	}
}
.columns-container {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
}

.column-item {
	flex: 0 0 calc(25% - 10px); // 每行4列，可以根据需要调整
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 10px;
	background-color: #f5f7fa;
	border-radius: 4px;
	box-sizing: border-box;
}

.handle {
	cursor: move;
	color: #909399;
	padding: 5px;
	background-color: #e0e0e0;
	border-radius: 3px;
}

.ghost {
	opacity: 0.5;
	background: #c8ebfb;
}
</style>
