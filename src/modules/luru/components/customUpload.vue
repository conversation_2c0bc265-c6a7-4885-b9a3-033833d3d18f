<template>
	<div class="custom-upload">
		<el-upload
			ref="uploadRef"
			:action="action"
			:accept="accept"
			:multiple="multiple"
			:limit="limit"
			:auto-upload="true"
			:show-file-list="true"
			:on-exceed="handleExceed"
			:before-upload="beforeUpload"
			:http-request="customUpload"
			:file-list="fileList"
			list-type="picture-card"
			:on-preview="handlePreview"
			:on-remove="handleRemove"
		>
			<el-icon class="avatar-uploader-icon"><plus /></el-icon>
		</el-upload>

		<el-dialog v-model="previewVisible" append-to-body>
			<img w-full :src="previewUrl" alt="Preview Image" />
		</el-dialog>
	</div>
</template>

<script>
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';

export default {
	name: 'custom-upload',
	components: { Plus },
	props: {
		action: { type: String, default: '#' },
		accept: String,
		multiple: Boolean,
		limit: Number,
		uploadHandler: { type: Function, required: true },
		modelValue: { type: [String, Array], default: '' }
	},
	emits: ['update:modelValue'],
	setup(props, { emit }) {
		const fileList = ref([]);
		const previewVisible = ref(false);
		const previewUrl = ref('');

		// 初始化文件列表
		watch(
			() => props.modelValue,
			val => {
				if (val) {
					if (Array.isArray(val)) {
						fileList.value = val.map(url => ({
							url,
							name: url.split('/').pop()
						}));
					} else if (typeof val === 'string') {
						fileList.value = [
							{
								url: val,
								name: val.split('/').pop()
							}
						];
					}
				} else {
					fileList.value = [];
				}
			},
			{ immediate: true }
		);

		const beforeUpload = file => {
			const limitSize = 10;
			if (file.size / 1024 / 1024 >= limitSize) {
				ElMessage.error(`上传文件大小不能超过 ${limitSize}MB!`);
				return false;
			}
			return true;
		};

		const customUpload = async ({ file, onProgress, onSuccess, onError }) => {
			try {
				const res = await props.uploadHandler(file, {
					onProgress: progress => {
						onProgress({ percent: progress });
					}
				});

				// 处理上传成功后的响应
				const url = res.url || res;
				const fileInfo = {
					name: file.name,
					url: url
				};

				// 更新文件列表
				if (props.multiple) {
					const newUrls = [...(fileList.value || []), fileInfo];
					fileList.value = newUrls;
					emit(
						'update:modelValue',
						newUrls.map(f => f.url)
					);
				} else {
					fileList.value = [fileInfo];
					emit('update:modelValue', url);
				}

				onSuccess(res);
			} catch (err) {
				onError(err);
				ElMessage.error(err.message || '上传失败');
			}
		};

		const handlePreview = file => {
			previewUrl.value = file.url;
			previewVisible.value = true;
		};

		const handleRemove = file => {
			const index = fileList.value.findIndex(f => f.url === file.url);
			if (index > -1) {
				const newFileList = [...fileList.value];
				newFileList.splice(index, 1);
				fileList.value = newFileList;

				if (props.multiple) {
					emit(
						'update:modelValue',
						newFileList.map(f => f.url)
					);
				} else {
					emit('update:modelValue', '');
				}
				//customUploasubmit;
			}
		};

		const handleExceed = () => {
			ElMessage.warning(`最多只能上传 ${props.limit} 个文件`);
		};
		return {
			fileList,
			previewVisible,
			previewUrl,
			beforeUpload,
			customUpload,
			handlePreview,
			handleRemove,
			handleExceed
		};
	}
};
</script>
