<template>
	<el-dialog
		v-model="tableVisible"
		:title="steps[stepActive]"
		:show-close="false"
		width="65%"
		draggable
		@close="closeDialog"
		@open="openDialog"
		:fullscreen="true"
		:destroy-on-close="true"
	>
		<template>
			<div class="my-header">
				<el-steps :active="stepActive" finish-status="success" simple class="custom-steps">
					<el-step title="定义列">
						<template #icon>
							<el-icon><list /></el-icon>
						</template>
					</el-step>
					<el-step title="表单流程">
						<template #icon>
							<el-icon><document /></el-icon>
						</template>
					</el-step>
					<el-step title="流程设计">
						<template #icon>
							<el-icon><set-up /></el-icon>
						</template>
					</el-step>
				</el-steps>
			</div>
		</template>
		<div class="design-form" v-loading="loading" element-loading-text="加载中...">
			<div style="width: 100%">
				<fDesigner
					:formRules="formRules"
					:formOptions="formOptions"
					:dictList="dictList"
					:tableType="tableTitle == '编辑' ? rowData.tableType : 'user'"
					:tableTitle="tableTitle"
					v-if="stepActive == 0"
					@nextStep="nextStep"
					@closeDialog="closeDialog"
				/>
				<step2
					:formInfo="formInfo"
					v-if="stepActive == 1"
					@nextStep="nextStep"
					@closeDialog="closeDialog"
				/>
				<step3
					:flowInfo="flowInfo"
					v-if="stepActive == 2"
					@nextStep="nextStep"
					@prevStep="prevStep"
				/>
			</div>
		</div>
	</el-dialog>
</template>
<script setup lang="ts">
import { reactive, toRefs, ref, watch, beforeDestroy } from 'vue';
import { DesignTable, SelectDict, TableList } from '../types';
import { ElMessageBox } from 'element-plus';
import { useCool } from '/@/cool';
import { cloneDeep, flow } from 'lodash-es';
import { PermissionInfo } from '../utils/constants';
import fDesigner from './designForm/fDesigner.vue';
import Step2 from './designForm/step2.vue';
import Step3 from './designForm/step3.vue';
import { ElMessage } from 'element-plus';
import { getColContent } from '../utils/designTable';
import { List, Document, SetUp } from '@element-plus/icons-vue';
import { changeRules } from '/@/modules/luru/utils/luruTable';
const { service, router } = useCool();

const loading = ref(false);
const props = defineProps({
	tableVisible: Boolean,
	tableTitle: {
		type: String,
		default: '' // 默认值为一个空对象
	},
	typeId: {
		type: Number || String,
		default: 0
	},
	rowData: {
		type: Object,
		default: () => ({}) // 默认值为一个空对象
	}
});
const { tableVisible, rowData, tableTitle, typeId } = toRefs(props);
const steps = ['定义列', '表单流程', '流程设计'];

const formData = ref<DesignTable>({
	tableName: '',
	tableInfo: {
		allowEdit: false,
		autoincrement: false
	},
	tableColumn: [
		{
			name: '项目名称',
			showName: '项目名称',
			dictName: '项目表',
			disabled: true,
			dictField: '',
			type: 'dict',
			hasDict: true,
			isProject: true,
			dictId: PermissionInfo.ProjectDictId as number
		},
		{ name: '', showName: '', type: 'string', hasDict: false, isNull: true }
	],
	remark: ''
});
const formInfo = ref({});
const flowInfo = ref({});
const dictList = ref<SelectDict[]>([]);
const tableList = ref<TableList[]>([]);
const stepActive = ref(1);

const formRules = ref();
const formOptions = ref();

const emits = defineEmits(['closeDialog']);

const openDialog = async () => {
	stepActive.value = 0;
	loading.value = true;
	formRules.value = []; // 新增时设置空数组
	formOptions.value = {};
	try {
		if (tableTitle.value == '编辑') {
			formRules.value = cloneDeep(rowData.value.colInfo);
			formOptions.value = cloneDeep(rowData.value.tableInfo);
		}

		// 处理字典数据

		// 设置表单规则和选项
	} catch (error) {
		console.error('数据加载失败:', error);
		ElMessage.error('数据加载失败');
	} finally {
		loading.value = false;
	}
};

const nextStep = (data, rules, type) => {
	//formData.value = data;

	if (stepActive.value == 0) {
		loading.value = true;
		formOptions.value = data;
		formRules.value = rules;
		submitTable(data, rules, type);
	} else if (stepActive.value == 1) {
		if (tableTitle.value == '新增') {
			flowInfo.value = data;
			stepActive.value++;
		} else {
			flowInfo.value = data;
			flowInfo.value.form_id = rowData.value.form_id;
			if (rowData.value.flow_id) flowInfo.value.flow_id = rowData.value.flow_id;
			stepActive.value++;
		}
	} else {
		closeDialog();
	}
	//if (stepActive.value == 2) return;
};

const jumpStep = () => {
	stepActive.value = 2;
};

const prevStep = () => {
	console.log('stepActive上一步', stepActive.value, formData.value);
	if (stepActive.value == 0) return;
	stepActive.value--;
};

const closeDialog = () => {
	emits('closeDialog', false);
	formRules.value = [];
	formOptions.value = [];
	// formData.value = {
	// 	tableName: '',
	// 	tableInfo: {
	// 		allowEdit: false,
	// 		autoincrement: false
	// 	},
	// 	tableColumn: [{ name: '', showName: '', type: 'string', hasDict: false }],
	// 	remark: ''
	// };
};
const getColLength = (colInfo: any) => {
	const regex = /@Column\({[^}]*}\)\s+([^\s:]+):/g;
	const lengthRegex = /length:\s*(\d+)/;

	const fields = {};
	let match;

	while ((match = regex.exec(colInfo)) !== null) {
		const fieldName = match[1].trim();
		const columnOptions = match[0];

		// 检查是否有 length
		const lengthMatch = columnOptions.match(lengthRegex);
		const length = lengthMatch ? parseInt(lengthMatch[1]) : undefined;
		fields[fieldName] = length;
	}

	return fields;
};
const autoincrement = ref(false);
const submitTable = async (data, rules, type) => {
	try {
		//const colInfo = getColInfo(rules);

		if (
			formOptions.value.form.tableInfo &&
			formOptions.value.form.tableInfo.includes('autoincrement')
		) {
			autoincrement.value = true;
		} else {
			autoincrement.value = false;
		}

		if (tableTitle.value == '新增') {
			const content = getColContent(
				formOptions.value.formName,
				formRules.value,
				autoincrement.value
			);
			try {
				const addRes = await service.cloud.db.add({
					name: formOptions.value.formName,
					content: content,
					readme: formOptions.value.form.formCreateMark,
					colInfo: formRules.value,
					tableInfo: formOptions.value,
					status: type == '发布' ? 1 : 2,
					formTypeId: typeId.value,
					operationDesc: null
				});
				console.log('添加表单成功', addRes);
				// const res = await service.cloud.db.list({
				// 	tableName: `func_${formOptions.value.formName}`
				// });
				//formInfo.value.colInfo = changeRules(formRules.value);
				formInfo.value = {
					form_name: formOptions.value.formName,
					form_id: addRes[0],
					colInfo: changeRules(formRules.value)
				};
				loading.value = false;
				if (type == '发布') {
					stepActive.value++;
				} else {
					closeDialog();
				}
				ElMessage.success('新增成功');
			} catch (error: any) {
				loading.value = false;
				console.log('error', error);
				ElMessageBox.confirm(`${error.message}`, '提示', {
					confirmButtonText: '确认',
					type: 'warning'
				});
			}
		} else if (tableTitle.value == '编辑') {
			try {
				console.log('内容', rowData.value.content);
				const colLengthList = getColLength(rowData.value.content);
				console.log('colLengthList', colLengthList);
				const content = getColContent(
					formOptions.value.formName,
					formRules.value,
					autoincrement.value,
					colLengthList
				);
				if (rowData.value?.tableType != 'user') {
					service.cloud.db
						.update({
							id: rowData.value?.form_id,
							name: rowData.value?.form_name,
							status: type == '发布' ? 1 : 2,
							readme: formOptions.value.form.formCreateMark,
							colInfo: formRules.value,
							tableInfo: formOptions.value
							//operationDesc: null
						})
						.then(res => {
							ElMessage.success('编辑成功');
							tableVisible.value = false;
							formInfo.value = cloneDeep(rowData.value);
							formInfo.value.colInfo = changeRules(formRules.value);
							if (type == '发布') {
								stepActive.value++;
							} else {
								closeDialog();
							}
							loading.value = false;
						})
						.catch((error: any) => {
							loading.value = false;
							ElMessageBox.confirm(`${error.message}`, '提示', {
								confirmButtonText: '确认',
								type: 'warning'
							});
						});
				} else {
					service.cloud.db
						.update({
							id: rowData.value?.form_id,
							name: rowData.value?.form_name,
							content:
								rowData.value?.tableType == 'user'
									? content
									: rowData.value?.content,
							status: type === '发布' ? 1 : 2,
							// className: 'pzlbEntity',
							// tableName: 'func_pzlb',
							readme: formOptions.value.form.formCreateMark || '',
							colInfo: formRules.value,
							tableInfo: formOptions.value
							//operationDesc: null
						})
						.then(res => {
							ElMessage.success('编辑成功');
							tableVisible.value = false;
							formInfo.value = cloneDeep(rowData.value);
							formInfo.value.colInfo = changeRules(formRules.value);
							console.log('编辑成功', rowData.value, flowInfo.value);
							if (type == '发布') {
								stepActive.value++;
							} else {
								closeDialog();
							}
							loading.value = false;
						})
						.catch((error: any) => {
							loading.value = false;
							ElMessageBox.confirm(`${error.message}`, '提示', {
								confirmButtonText: '确认',
								type: 'warning'
							});
						});
				}
			} catch (error: any) {
				ElMessageBox.confirm(`${error.message}`, '提示', {
					confirmButtonText: '确认',
					type: 'warning'
				});

				console.log(error);
				return;
			}
		}
	} catch (error) {
		console.error('提交失败:', error);
		loading.value = false;
	} finally {
	}

	//closeDialog();
};
</script>
<style lang="scss">
/* 移除 scoped，让样式全局生效 */
.el-dialog__header {
	padding-bottom: 8px !important;
}

.el-steps--simple {
	padding: 5px 8% !important;
	border-radius: 4px;
	background: none !important;
}
</style>

<style lang="scss" scoped>
/* 其他组件的局部样式保持 scoped */
.design-form {
	position: relative;
}
.right10 {
	margin-right: 10px;
}
.left10 {
	margin-left: 10px;
}
.Xitem {
	margin-left: 20px;

	width: 100%;

	.col {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 10px;
		&_value {
			display: flex;
			width: 90%;
			align-items: center;
			justify-content: space-between;
		}
		&_btn {
			width: 10%;

			.square-button {
				width: 32px; /* 设置按钮宽度 */
				height: 32px; /* 设置按钮高度 */
				font-size: 18px; /* 设置按钮字体大小 */
			}
		}
	}
	//flex: 1;
}
.footer {
	display: flex;
	justify-content: flex-end;
}
.custom-steps {
	:deep(.el-steps--simple) {
		padding: 5px 8%;
		border-radius: 4px;
		background: none;
	}
	:deep(.el-steps) {
		background-color: transparent;
	}

	:deep(.el-step) {
		.el-step__icon {
			background: transparent;

			.el-icon {
				font-size: 18px;
			}
		}

		&.is-active {
			.el-step__icon {
				.el-icon {
					color: var(--el-color-primary);
				}
			}
		}

		&.is-success {
			.el-step__icon {
				.el-icon {
					color: var(--el-color-success);
				}
			}
		}
	}
}
</style>
