<!-- excel计算字段 -->
<template>
	<div>
		<el-dialog
			:model-value="colCalculateVisible"
			:destroy-on-close="true"
			title="计算字段"
			width="80%"
			draggable
			@open="dialogOpen"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:append-to-body="true"
			:modal-append-to-body="false"
			custom-class="calculate-col-dialog"
		>
			<template #header="{ titleId, titleClass }">
				<div class="my-header">
					<h4 :id="titleId" :class="titleClass">计算字段</h4>
				</div>
			</template>
			<div class="dialogBody">
				<el-row :gutter="24" class="flexjustify">
					<el-col :span="12" style="height: 100%">
						<el-row :gutter="14" class="flexjustify">
							<el-select
								v-model="fieldType"
								class="m-2"
								placeholder="请选择字段类型"
								style="width: 92%; margin-bottom: 10px; padding-left: 8px"
								@change="handleSelectClick"
							>
								<el-option
									v-for="item in fieldTypeList"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
							<el-col :span="11" style="padding-right: 0px; padding-left: 8px">
								<div class="container">
									<div class="header1">字段</div>
									<div class="search1">
										<el-input
											v-model="searchText"
											class="w-50 m-2"
											placeholder="搜索字段"
											:prefix-icon="Search"
											@input="handleOperSearch"
										/>
									</div>
									<div class="list">
										<div
											v-for="item in filterfieldList"
											:key="item"
											class="item"
											draggable="true"
											@dragstart="dragStartHandler(item, 'field', $event)"
										>
											<span
												class="dialogfield"
												@click="getField()"
												style="font-weight: bold"
												>{{ item.name }}</span
											>
										</div>
									</div>
								</div>
							</el-col>
							<el-col
								:span="11"
								style="padding-right: 13px !important; padding-left: 0px !important"
							>
								<div class="container">
									<div class="header1">公式</div>
									<div class="search1">
										<el-input
											v-model="searchOperData"
											class="w-50 m-2"
											placeholder="搜索公式"
											:prefix-icon="Search"
											@input="handleOperSearch"
										/>
									</div>
									<div class="list">
										<div
											v-for="item in filteroperData"
											:key="item"
											class="item dialogoper"
											draggable="true"
											@dragstart="dragStartHandler(item, 'oper', $event)"
											@click="getOperData(item)"
											@mouseover="getOperData(item)"
											style="font-weight: bold"
										>
											{{ item.name }}
										</div>
									</div>
								</div>
							</el-col>
						</el-row>
					</el-col>
					<el-col :span="11" style="height: 100%; padding-left: 0px">
						<div
							style="
								height: 60px;
								min-height: 1px;
								display: flex;
								flex-direction: column;
								justify-content: center;
								vertical-align: middle;
								padding-left: 5px;
								background-color: #f0f0f0;
							"
						>
							<div
								style="
									color: black;
									font-weight: bold;
									font-size: 15px;
									margin-bottom: 6px;
								"
							>
								计算列
							</div>
							<span style="color: rgb(165, 165, 165); font-size: 14px"
								>在左侧拖拽字段或公式，且在英文输入法下编辑</span
							>
						</div>
						<div class="right" style="height: 168px">
							<div id="inputContent" class="edit dropzone1">
								<editor
									v-model="valueHtml"
									:defaultConfig="editorConfig"
									mode="simple"
									@drop="dropHandler"
									@onCreated="handleCreated"
									@onDestroyed="handleDestroyed"
									@contextmenu.prevent.stop="handleClick1"
								/>
							</div>
							<div
								style="
									height: 40px;
									margin-top: 10px;
									min-height: 1px;
									font-size: 15px;
									line-height: 40px;
									font-weight: bold;
									text-align: left;
									width: 100%;
									padding-left: 5px;
									background-color: #f0f0f0;
									color: black;
								"
							>
								{{ operation.name }}
							</div>
							<div
								style="
									width: 100%;
									height: 20vh;
									border: 1px solid #f0f0f0;
									padding: 5px 5px;
								"
							>
								<p></p>
								<p style="text-align: left">
									<span style="color: darkgrey">说明：{{ operation.info }}</span>
								</p>
								<p style="text-align: left">
									<span style="color: darkgrey">示例：{{ operation.eg }} </span>
								</p>
							</div>
						</div>
					</el-col>
				</el-row>
			</div>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="dialogClose">取消</el-button>
					<el-button type="primary" @click="submitData"> 应用 </el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, toRefs, reactive, onBeforeUnmount, shallowRef, watch } from 'vue';
import '@wangeditor/editor/dist/css/style.css'; // 引入 css
import { Editor as editor } from '@wangeditor/editor-for-vue';
import { Search } from '@element-plus/icons-vue';
import { replaceChineseSymbols } from '../../utils/symbolChange';
import { IDomEditor, Boot, SlateTransforms, DomEditor } from '@wangeditor/editor';
import { operData1 } from '../../utils/operatorFormColCalculate';
import { validateSqlStatement } from '../../utils/validations';
import { ElMessage } from 'element-plus';
Boot.registerPlugin(withBreakAndDelete);

const props = defineProps({
	colCalculateVisible: {
		type: Boolean,
		default: false
	},
	colList: {
		type: Array as any
	},
	strText: {
		type: String,
		default: ''
	}
});
const { colList, strText } = toRefs(props);

//字段
//const fieldData = colList.value;
//操作符
const operData = ref(operData1);

const createColDialog = ref(false);

const emit = defineEmits(['DialogShow', 'submitData']);

function dialogOpen() {
	valueHtml.value = '';
	console.log('获取到的col', colList.value);
}

Boot.registerPlugin(withBreakAndDelete);

function getStartLocation(editor) {
	return editor.start(editor, []);
}

//改写富文本API
function withBreakAndDelete<T extends IDomEditor>(editor: T): T {
	// TS 语法
	// function withBreakAndDelete(editor) {                            // JS 语法
	const { insertData, insertText, isInline, deleteBackward, deleteForward, deleteFragment } =
		editor; // 获取当前 editor API
	const newEditor = editor;
	newEditor.insertText = e => {
		if (DomEditor.getSelectedNodeByType(newEditor, 'em')) {
			console.log('eme', e);
			return;
		}
		insertText(e);
	};
	newEditor.insertData = e => {
		if (DomEditor.getSelectedNodeByType(newEditor, 'em')) {
			editorRef.value.move(
				DomEditor.getSelectedNodeByType(newEditor, 'em').children[0].text.length + 1
			);
			return;
		} else {
			editor.select(getStartLocation(editor));
			SlateTransforms.move(editor, { distance: 2, unit: 'character' });
		}

		return;
	};

	newEditor.isInline = elem => {
		//console.log(elem, "elem");
		const { type } = elem;
		if (type === 'em' || type === 'span') {
			return true;
		}
		return isInline(elem);
	};
	// 重写 deleteBackward 向后删除
	newEditor.deleteForward = unit => {
		// 执行默认的删除
		console.log('删除deleteForward', unit);
	};

	newEditor.deleteBackward = unit => {
		// 执行默认的删除				//const [node] = SlateEditor.node(newEditor, selection);
		const selectedTodo = DomEditor.getSelectedNodeByType(editor, 'em');
		const node = DomEditor.getSelectedTextNode(newEditor);
		if (node && node.bold) {
			SlateTransforms.removeNodes(newEditor, {
				match: n => DomEditor.checkNodeType(n, 'em')
			});
			return;
		} else if (node && (node.text == ' ' || node.text == '')) {
			editor.moveReverse(1);
		} else {
			deleteBackward('character');
		}
	};

	return newEditor;
}

const fieldType = ref('string');
const fieldTypeList = reactive([
	{
		value: 'number',
		label: '数字字段'
	},
	{
		value: 'date',
		label: '日期字段'
	},
	{
		value: 'string',
		label: '文本字段'
	}
]);

const searchText = ref('');
const searchOperData = ref('');
const editorRef = shallowRef();
const valueHtml = ref('');
const editorConfig = {
	MENU_CONF: {}
};
let fieldData: any = reactive([]);
const filterfieldList: any = ref([]);
const filteroperData: any = ref([]);

watch(
	colList,
	newcol => {
		if (newcol) {
			fieldData = colList.value;
			filterfieldList.value = fieldData.filter((item: any) => item.type == fieldType.value);
		}
	},
	{ deep: true, immediate: true }
);
watch(
	operData,
	newcol => {
		if (newcol) {
			filteroperData.value = operData.value.filter(
				(item: any) => item.type == fieldType.value
			);
		}
	},
	{ deep: true, immediate: true }
);

watch(
	searchText,
	newcol => {
		if (newcol) {
			console.log('新值');
			filterfieldList.value = fieldData.filter((item: any) =>
				item.name.includes(searchText.value)
			);
		} else if ((newcol = '')) {
			filterfieldList.value = fieldData.filter((item: any) => item.type == fieldType.value);
		}
	},
	{ deep: true, immediate: true }
);
watch(
	searchOperData,
	newcol => {
		if (newcol) {
			console.log('新值');
			filteroperData.value = operData.value.filter((item: any) =>
				item.name.toLowerCase().includes(searchOperData.value.toLowerCase())
			);
		} else if ((newcol = '')) {
			filteroperData.value = operData.value.filter(
				(item: any) => item.type == fieldType.value
			);
		}
	},
	{ deep: true, immediate: true }
);
watch(
	strText,
	newVal => {
		if (strText.value) {
			valueHtml.value = strText.value;
		}

		console.log('strText', newVal);
	},
	{ deep: true, immediate: true }
);
function handleOperSearch() {
	if (searchOperData.value == '') {
		filteroperData.value = operData.value.filter((item: any) => item.type == fieldType.value);
	}
	if (searchText.value == '') {
		filterfieldList.value = fieldData.filter((item: any) => item.type == fieldType.value);
	}
}

const operation = reactive({
	name: '',
	info: '',
	eg: ''
});

function getOperData(item: any) {
	operation.name = item.name;
	operation.info = item.msg.info;
	operation.eg = item.msg.eg;
}

function getField() {
	operation.name = '';
	operation.info = '';
	operation.eg = '';
}

function handleSelectClick() {
	searchText.value = '';
	filterfieldList.value = fieldData.filter((item: any) => item.type == fieldType.value);
	filteroperData.value = operData.value.filter((item: any) => item.type == fieldType.value);
}

const handleCreated = (editor: any) => {
	editorRef.value = editor; // 记录 editor 实例，重要！
};

const handleClick1 = () => {
	console.log('点击事件');
	SlateTransforms.removeNodes(editorRef.value, {
		match: n => DomEditor.checkNodeType(n, 'span')
	});
};

function dropHandler(event: any) {
	// event.stopPropagation();
	event.preventDefault();
	event.dataTransfer.dropEffect = 'none';
	event.dataTransfer.effectAllowed = 'none';
	const data: any = JSON.parse(event.dataTransfer.getData('text/plain'));
	let node = {};
	let node1 = {};
	if (data.type == 'field') {
		node = {
			type: 'em',
			children: [
				{
					text: '\`' + `${data.name}` + '\`',
					bgColor: '#88C9BB',
					color: 'white',
					fontSize: '15px',
					bold: true
				}
			]
		};
		node1 = {
			type: 'span',
			children: [{ text: ` ` }]
		};
	} else {
		node = {
			type: 'em',
			children: [
				{
					text: ` ${data.name}`,
					color: '#AE2CC1',
					fontSize: '18px',
					bold: true
				}
			]
		};
		if (data.name.includes('like')) {
			node1 = {
				type: 'span',
				children: [{ text: `  '%%' `, fontSize: '16px' }]
			};
		} else if (data.name.includes('now')) {
			node1 = {
				type: 'span',
				children: [{ text: ` ` }]
			};
		} else if (data.name.includes('date_add')) {
			node1 = {
				type: 'span',
				children: [{ text: `(  ,INTERVAL   DAY) `, fontSize: '15px' }]
			};
		} else if (data.name.includes('datediff')) {
			node1 = {
				type: 'span',
				children: [{ text: `(day,  ) `, fontSize: '16px' }]
			};
		} else if (data.name.includes('convert')) {
			node1 = {
				type: 'span',
				children: [{ text: `(  , DATETIME ) `, fontSize: '16px' }]
			};
		} else {
			node1 = {
				type: 'span',
				children: [{ text: `( ) `, fontSize: '16px' }]
			};
		}
	}
	//editorRef.value.insertNode(node);
	//插入空字符串
	//editorRef.value.insertNode(node1);
	const nodeList = [node, node1];
	//editor.insertText('xxx')
	SlateTransforms.insertNodes(editorRef.value, nodeList);
	//editorRef.value.insertNode(node1);
	event.dataTransfer.setData('text/plain', '');
}

function dragStartHandler(item: any, activeName: any, event: any) {
	if (activeName == 'oper') {
		event.dataTransfer.setData(
			'text/plain',
			JSON.stringify({ name: item.props, type: activeName })
		);
		return;
	} else {
		event.dataTransfer.setData(
			'text/plain',
			JSON.stringify({ name: item.name, type: activeName })
		);
	}
}

onBeforeUnmount(() => {
	console.log('注销');
	const editor = editorRef.value;
	if (editor == null) return;
	editor.destroy();
});
function handleDestroyed() {
	const editor = editorRef.value;
	if (editor == null) return;
	editor.destroy();
	editorRef.value = null;
}

function dialogClose() {
	valueHtml.value = '';
	handleDestroyed();
	emit('DialogShow', false);
}

function openCreateColDialog() {
	createColDialog.value = true;
}

function submitData() {
	const formulaText = replaceChineseSymbols(editorRef.value.getEditableContainer().textContent);

	// 添加验证逻辑
	// try {
	// 	if (validateSqlStatement(formulaText)) {
	// 		createColDialog.value = false;
	//dialogClose();
	console.log('列计算', formulaText);
	emit('submitData', formulaText);
	// 	} else {
	// 		// 如果验证失败，显示错误消息
	// 		ElMessage.error("SQL 语句语法不正确，请检查并修正。");
	// 	}
	// } catch (err) {
	// 	ElMessage.error("SQL 语句语法不正确，请检查并修正。");
	// }
}
</script>
<style scoped>
.footerBtn {
	width: 150px;
	margin-top: 20px;
	margin-right: 20px;
}
.bold {
	padding: 4px 4px;
}
.right-title {
	text-align: left;
	width: 100%;
	display: block;
	/* margin-bottom: 20px */
}

.demo-tabs .el-tabs__content {
	padding: 5px;
	color: #6b778c;
	font-size: 13px;
	font-weight: 600;
}

.dialogfield {
	margin-bottom: 15px;
	cursor: pointer;
	background-color: rgb(136, 201, 187);
	border-radius: 3px;
	padding: 5px;
	font-size: 14px;
	line-height: 14px;
	height: 22px;
	color: white;
	display: inline-block;
}
.dialogoper {
	margin-bottom: 15px;
	cursor: pointer;
	border-radius: 3px;
	padding: 5px;
	font-size: 14px;
	line-height: 14px;
	height: 16px;
	color: rgb(174, 44, 193);
	display: inline-block;
}
.dropzone1 {
	padding: 10px;
	border: 1px solid #f0f0f0;
	width: 100%;
	height: 100%;
	/* // margin-top: 5px; */
}

.right {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: flex-start;
	flex-wrap: wrap;
	width: 100%;
	height: 100%;
}

.dropped-items {
	display: flex;
	flex-direction: row;
	height: 100%;
	overflow-x: auto;
	flex-wrap: wrap;
	align-items: flex-start;
	align-content: flex-start;
}

.dropped-item {
	padding: 2px;
	margin-right: 10px;
	margin-top: 5px;
	position: relative;
	cursor: pointer;
}

.with-border {
	border: 2px solid #9b4dca;
}

.dropzone {
	padding: 10px;
	border: 1px dashed #ccc;
	width: 100%;
	height: 100%;
	margin-top: 5px;
}

.close-button {
	position: absolute;
	width: 15px;
	height: 15px;
	top: -10px;
	right: -10px;
	cursor: pointer;
	color: rgb(138, 138, 138) !important;
	background-color: rgb(255, 255, 255);
	/* border:1px solid black; */
	/* border-radius: 5px; */
}
.container {
	display: flex;
	flex-direction: column;
	width: 100%;
	padding: 5px 5px;
	border: 1px solid #f0f0f0;
	border-radius: 3px;
	height: 56vh;
}
.header1 {
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	font-size: 15px;
	font-weight: bold;
	color: black;
}

.search1 {
	display: flex;
	align-items: center;
	justify-content: center;
	padding-bottom: 15px;
}
.list {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow-y: auto;
	justify-content: flex-start;
	width: 100%;
}
</style>
<style scoped>
.bold {
	padding: 4px 4px;
}
.right-title {
	text-align: left;
	width: 100%;
	display: block;
	/* margin-bottom: 20px */
}

.demo-tabs .el-tabs__content {
	padding: 32px;
	color: #6b778c;
	font-size: 13px;
	font-weight: 600;
}

.scrollbar-demo-item {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 50px;
	margin: 10px;
	text-align: center;
	border-radius: 4px;
	background: var(--el-color-primary-light-9);
	color: var(--el-color-primary);
}

.right {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: flex-start;
	flex-wrap: wrap;
	width: 100%;
	height: 100%;
}

.dropped-items {
	display: flex;
	flex-direction: row;
	height: 100%;
	overflow-x: auto;
	flex-wrap: wrap;
	align-items: flex-start;
	align-content: flex-start;
}

.dropped-item {
	padding: 2px;
	margin-right: 10px;
	margin-top: 5px;
	position: relative;
	cursor: pointer;
}

.with-border {
	border: 2px solid #9b4dca;
}

.dropzone1 {
	padding: 10px;
	border: 1px solid #f0f0f0;
	width: 100%;
	height: 100%;
	/* // margin-top: 5px; */
}

.close-button {
	position: absolute;
	width: 15px;
	height: 15px;
	top: -10px;
	right: -10px;
	cursor: pointer;
	color: rgb(138, 138, 138) !important;
	background-color: rgb(255, 255, 255);
}
.demo-tabs .el-tabs__content {
	padding: 0px;
}
.container {
	display: flex;
	flex-direction: column;
	width: 90%;
	padding: 5px 5px;
	border: 1px solid #f0f0f0;
	border-radius: 3px;
	height: 56vh;
}

.header1 {
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	font-size: 15px;
	font-weight: bold;
	color: black;
}

.search1 {
	display: flex;
	align-items: center;
	justify-content: center;
	padding-bottom: 15px;
}

.list {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow-y: auto;
	justify-content: flex-start;
	width: 100%;
}

.item {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	font-size: 14px;
	/* // padding: 5px 0;
	//  color: black; */
}

.dialogfield {
	margin-bottom: 8px;
	cursor: pointer;
	background-color: rgb(136, 201, 187);
	border-radius: 3px;
	padding: 5px;
	font-size: 14px;
	line-height: 14px;
	height: 22px;
	color: white;
	/* // display: inline-block; */
}
.dialogoper {
	margin-bottom: 15px;
	cursor: pointer;
	border-radius: 3px;
	/* // padding: 5px; */
	font-size: 14px;
	line-height: 14px;
	height: 16px;
	color: rgb(174, 44, 193);
	/* //display: inline-block; */
}

.flexjustify {
	/* justify-content: space-between !important; */
	height: 100%;
	overflow: auto;
	padding: 0 0 !important;
}
.padding0 {
	padding: 0 0 !important;
}
.dialogBody {
	height: 65vh;
	overflow: hidden;
	font-family: BlinkMacSystemFont;
}
.my-header {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
}
</style>
<style>
.calculate-col-dialog {
	z-index: 3000 !important; /* 确保这个值大于全屏对话框的 z-index */
}
</style>
