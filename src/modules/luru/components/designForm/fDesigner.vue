<template>
	<div class="table-config-container">
		<fc-designer
			ref="designer"
			:config="config"
			@delete="handleDelete"
			@active="handleActive"
		/>

		<div class="footer">
			<el-button @click="closeDialog" class="btn-cancel">取消</el-button>
			<el-button class="btn-cancel" @click="nextStep('暂存')">暂存</el-button>
			<el-button type="success" @click="nextStep('发布')">发布</el-button>
		</div>

		<calculateCol
			v-model:colCalculateVisible="colCalculateVisible"
			:colList="toSqlCols"
			:strText="strText"
			@DialogShow="closeCalculateCol"
			@submitData="submitCalculateCol"
		/>

		<relatedTableDialog
			:relatedTableVisible="relatedTableVisible"
			:relatedList="relatedList"
			:title="tableTitle"
			@submit="submitRelatedTable"
			@closeDialog="closeRelatedTable"
		></relatedTableDialog>
	</div>
</template>

<script setup lang="ts">
import { PropType, toRefs, ref, watch, computed, onMounted, nextTick } from 'vue';
import { ElMessageBox, ElMessage, ElTreeSelect } from 'element-plus';
import { DesignTable, SelectDict, TableList } from '../../types';
import { useCool } from '/@/cool';
import {
	getColType,
	flattenFormDataWithLevel,
	traverseNode,
	checkForDuplicateColumns
} from '/@/modules/luru/utils/designTable';
import FcDesigner from '@form-create/designer';
import type { Config } from '@form-create/designer';
import calculateCol from './calculateCol.vue';
import relatedTableDialog from './relatedTable.vue';
import { cloneDeep } from 'lodash-es';

// 定义单个列项的类型
interface RelatedColItem {
	label: string;
	value: string;
}

// 定义 relatedList 的类型
interface RelatedList {
	primaryCol: RelatedColItem[];
	relatedCol: RelatedColItem[];
}

const { service, router } = useCool();
const props = defineProps({
	formRules: {
		type: Object as PropType<DesignTable>,
		required: true
	},
	formOptions: {
		type: Object as PropType<DesignTable>,
		required: true
	},

	tableTitle: {
		type: String as PropType<string>,
		required: true
	},
	tableType: {
		type: String as PropType<string>,
		required: true
	}
});
const { formRules, formOptions, tableTitle, tableType } = toRefs(props);

const toSqlCols: any = ref([]);
const strText = ref();
const dictList = ref([]);

const styleConfig = {
	formWidth: '800px',
	inputWidth: '200px'
};
const relatedTableRef = ref();
// 为 relatedList 添加类型注解
const relatedList = ref<RelatedList>({
	primaryCol: [],
	relatedCol: []
});
const relatedTable = ref();
const regularStr = ref('');
const regularStrList = ref([]);
const relatedTableVisible = ref(false);

const changeProjectPrimaryKey = ref(0);
const colCalculateVisible = ref(false);
const designer = ref<any>({});

let isMounted = false;
let hasMarkedComponents = false; // 添加标记状态

function relatedTableEmpty() {
	try {
		const currentOptions = designer.value.getOptions();
		if (currentOptions) {
			if ('form' in currentOptions && 'relatedTable' in currentOptions.form) {
				currentOptions.form.relatedTable = [];
			} else if ('relatedTable' in currentOptions) {
				currentOptions.relatedTable = [];
			}
			designer.value.setOption(currentOptions);
			console.log('已尝试使用 getOptions/setOption 将 relatedTable 设置为空数组');
		} else {
			console.error('关联表选择框置空失败');
		}
	} catch (error) {
		console.error('失败:', error);
	}
}

function closeRelatedTable() {
	relatedTableVisible.value = false;
	// 使用 getOptions 和 setOption 更新全局表单配置中的 relatedTable 字段
	if (designer.value) {
		relatedTableEmpty();
	}
}

onMounted(async () => {
	isMounted = true;
	const dictData = await service.dict.type.list();
	dictList.value = arrayToTree(
		dictData.map(item => ({
			...item,
			label: item.name,
			value: item.name
		}))
	);

	if (tableTitle.value == '新增') {
		console.log('非编辑');
		const rule = [
			{
				type: 'select',
				field: 'Fwh5m9cfo3m5adc',
				title: '项目名称',
				info: '',
				$required: false,
				props: {
					hasDict: ['hasDict'],
					dictName: '项目表',
					dictField: '198/204/21',
					dictId: 21,
					isProject: true
				},
				native: {
					canDelete: false // 设置为 false 表示不能删除
				},
				options: [],
				_fc_id: 'id_F4qkm9cfo3m5aec',
				name: 'ref_F3pkm9cfo3m5afc',
				display: true,
				hidden: false,
				_fc_drag_tag: 'select'
			}
		];
		await designer.value.setRule(rule);
	} else {
		await designer.value.setRule(formRules.value);
	}
	// const designer1 = designer.value?.designer;
	// console.log('designer1', designer1);
	// if (!designer1) return;

	//const designer1 = designer.value.getDesigner();
	//designer.value.setHandler('delete', handleBeforeDelete);
	removeFcDesignerIcon();
});

const config: Config = {
	appendConfigData: ['formCreateMark'],
	//showDevice: false,
	//showPreviewBtn: false,
	//showInputData: false,
	showDevice: true,
	showEventForm: true,
	showPageManage: true,
	showJsonPreview: true,
	showTemplate: true,
	showLanguage: true,
	showMenuBar: true,
	showComponentName: false,
	showCustomProps: false,

	showConfig: {
		select: {
			options: false,
			effect: false
		},
		elTreeSelect: {
			options: false,
			effect: false,
			data: false,
			props: {
				data: false
			}
		},
		radio: {
			options: false,
			effect: false,
			fetch: false,
			remoteMethod: false,
			remote: false,
			optionsType: false,
			optionsValue: false,
			optionsLabel: false
		},
		checkbox: {
			options: false,
			effect: false
		},
		switch: {
			options: false,
			effect: false
		}
	},

	//showStyleForm: false,
	formOptions: {
		submitBtn: {
			show: false
		}
	},
	beforeAdd: rule => {
		// 对于select和radio类型，确保options为空
		if (rule.type === 'select' || rule.type === 'elTreeSelect') {
			rule.options = [];
		}
		return true;
	},
	formRule: {
		prepend: true,
		rule() {
			return [
				{
					type: 'checkbox',
					title: '表配置',
					field: 'tableInfo',
					value: [],
					options: [
						{ value: 'allowEdit', label: '允许审核归档后再驳回', disabled: false },
						{ value: 'autoincrement', label: '审核后可编辑', disabled: false }
					]
				},
				{
					type: 'input',
					field: 'formCreateMark',
					title: '表单备注',
					prop: {
						type: 'textarea',
						size: 'medium'
					}
				},
				{
					type: 'checkbox',
					field: 'relatedTable',
					title: '配置关联表',
					value: [],
					options: [{ value: 'isRelatedTable', label: '关联表', disabled: false }],
					on: {
						// 使用 on 而不是 events
						change: e => {
							if (e.length > 0) {
								const res = getRelatedList();
								if (res == 'success') {
									if (
										relatedList.value.primaryCol.length > 0 &&
										relatedList.value.relatedCol.length > 0
									) {
										relatedTableVisible.value = true;
									} else {
										relatedTableEmpty();
										ElMessageBox.confirm(
											"请先检查'条件列'和'自动带出列'是否配置齐全!"
										);
									}
								}
							} else {
								const rules = designer.value.getRule();
								const newRules = cloneDeep(removeProcessFormFields(rules));

								designer.value.setRule(newRules);
								relatedTableEmpty();
							}
						}
					},
					control: [
						{
							handle(val) {
								return val == 'isRelatedTable';
							},
							method: 'hidden',
							rule: ['relatedinfo', 'relatedContent']
						}
					]
				},
				{
					type: 'input',
					title: '关联详情',
					field: 'relatedinfo',
					value: '',
					col: {
						span: 225
					},
					props: {
						type: 'text',
						readonly: true
					}
					//validate: [{ required: true, message: '请进行关联配置', trigger: 'blur' }]
				},
				{
					type: 'input',
					title: '关联内容',
					field: 'relatedContent',
					value: '',
					col: {
						span: 225
					},
					props: {
						type: 'text',
						readonly: true
					}
					//validate: [{ required: true, message: '请进行关联配置', trigger: 'blur' }]
				}
			];
		}
	},
	componentRule: {
		default: {
			prepend: true,
			// append: true, // 添加到底部
			rule() {
				return [
					{
						type: 'checkbox',
						title: '',
						field: 'isPrimaryKey',
						value: [],
						options: [
							{
								value: 'isPrimaryKey',
								label: '是主键',
								disabled:
									tableTitle.value === '编辑' &&
									designer.value?.activeRule?.props?.isExisting
							}
						]
					},
					{
						type: 'checkbox',
						title: '',
						field: 'isPrimaryCol',
						value: [],
						options: [{ value: 'isPrimaryCol', label: '是条件列', disabled: false }],
						control: [
							{
								handle(val) {
									return val == '';
								},
								method: 'disabled',
								rule: ['relatedCol', 'calculatedCol']
							}
						]
					},
					{
						type: 'checkbox',
						title: '',
						field: 'relatedCol',
						value: [],
						options: [{ value: 'relatedCol', label: '是自动带出列', disabled: false }],
						control: [
							{
								handle(val) {
									return val == '';
								},
								method: 'disabled',
								rule: ['isPrimaryCol', 'calculatedCol', 'hasDict']
							}
						],
						on: {
							change: e => {
								if (e.length > 0) {
									designer.value.activeRule.props.disabled = true;
									designer.value.activeRule.info = '自动带出列无需填写';
								} else {
									designer.value.activeRule.props.disabled = false;
									designer.value.activeRule.info = '';
								}
							}
						}
					},
					{
						type: 'input',
						title: `字段名`,
						field: 'fieldName',
						value: '',
						col: {
							span: 225
						},
						props: {
							readonly: true,
							disabled: true
						}
						// on: {
						// 	// 使用 on 而不是 events
						// 	change: e => {
						// 		console.log('改变', e);
						// 		if (tableTitle.value == '新增') {
						// 			const rule = designer.value.activeRule();
						// 			console.log('rule', rule);
						// 			rule.fieldName = rule.title;
						// 			designer.value.updateRuleFormData(rule);
						// 		}
						// 	}
						// }
					}
				];
			}
		},
		elTreeSelect: {
			rule() {
				return [
					{
						type: 'checkbox',
						title: '',
						field: 'hasDict',
						value: [],
						options: [
							{
								value: 'hasDict',
								label: '是字典类型',
								disabled:
									tableTitle.value === '编辑' &&
									designer.value?.activeRule?.props?.isExisting
							}
						],
						control: [
							{
								handle(val) {
									return val == 'hasDict';
								},
								method: 'hidden',
								rule: ['dictName', 'dictParent']
							}
						]
					},
					{
						type: 'elTreeSelect',
						title: '数据选择',
						field: 'dictName',
						props: {
							data: dictList.value,
							checkStrictly: true,
							filterable: true,
							disabled:
								tableTitle.value === '编辑' &&
								designer.value?.activeRule?.props?.isExisting,
							props: {
								label: 'label'
							},
							onChange: (item, index) => {
								changeDictName(item, index);
							},
							onNodeClick: e => {
								getNode(e);
							}
						}
					},
					{
						type: 'elTreeSelect',
						title: '父节点选择',
						field: 'dictParent',
						props: {
							data: dictList.value,
							checkStrictly: true,
							disabled:
								tableTitle.value === '编辑' &&
								designer.value?.activeRule?.props?.isExisting,
							filterable: true,
							props: {
								label: 'label'
							},
							onChange: (item, index) => {
								getParent(item, index);
							},
							onNodeClick: e => {
								getNode(e);
							}
						}
					},
					{
						type: 'switch',
						title: '是否禁用',
						field: 'disabled',
						value: [],
						options: [{}]
					},
					{
						type: 'switch',
						title: '是否可以清空选项',
						field: 'clearable',
						value: [],
						options: [{}]
					},
					{
						type: 'input',
						title: '占位符',
						field: 'placeholder',
						value: '',
						col: {
							span: 225
						}
					}
				];
			}
		},
		select: {
			//prepend: true,
			rule(ee) {
				return [
					{
						type: 'checkbox',
						title: '',
						field: 'hasDict',
						value: [],
						options: [
							{
								value: 'hasDict',
								label: '是字典类型',
								disabled:
									tableTitle.value === '编辑' &&
									designer.value?.activeRule?.props?.isExisting
							}
						],
						control: [
							{
								handle(val) {
									return val == 'hasDict';
								},
								method: 'hidden',
								rule: ['dictName']
							}
						]
					},
					{
						type: 'elTreeSelect',
						title: '数据选择',
						field: 'dictName',
						props: {
							data: dictList.value,
							checkStrictly: true,
							filterable: true,
							disabled:
								tableTitle.value === '编辑' &&
								designer.value?.activeRule?.props?.isExisting,
							props: {
								label: 'label'
							},
							onChange: (item, index) => {
								changeDictName(item, index);
							},
							onNodeClick: e => {
								getNode(e);
							}
						}
					},
					{
						type: 'switch',
						title: '是否禁用',
						field: 'disabled',
						value: [],
						options: [{}]
					},
					{
						type: 'switch',
						title: '是否可以清空选项',
						field: 'clearable',
						value: [],
						options: [{}]
					},
					{
						type: 'input',
						title: '占位符',
						field: 'placeholder',
						value: '',
						col: {
							span: 225
						}
					},
					{
						type: 'switch',
						title: '是否可搜索',
						field: 'filterable',
						value: [],
						options: [{}]
					},
					{
						type: 'input',
						title: '搜索条件无匹配时显示的文字',
						field: 'noMatchText',
						value: '',
						col: {
							span: 225
						}
					},
					{
						type: 'input',
						title: '选项为空时显示的文字',
						field: 'noDataText',
						value: '',
						col: {
							span: 225
						}
					}
				];
			}
		},
		input: {
			prepend: true,
			rule() {
				return [
					{
						type: 'checkbox',
						title: '',
						field: 'calculatedCol',
						value: [],
						options: [
							{
								value: 'calculatedCol',
								label: '是计算列',
								disabled:
									tableTitle.value === '编辑' &&
									designer.value?.activeRule?.props?.isExisting
							}
						],
						on: {
							change: e => {
								if (e.length > 0) {
									// 勾选计算列时
									colCalculateVisible.value = true;
									// 设置disabled和info
									designer.value.activeRule.props.disabled = true;
									designer.value.activeRule.info = '计算列无需填写';
									const cols = designer.value.getRule();
									const newCols = flattenFormDataWithLevel(cols);
									const res = checkForDuplicateColumns(newCols, tableTitle.value);
									if (res.hasDuplicates) {
										ElMessageBox.confirm(
											`主表列名不能重复,请先去除重复列名再配置计算列:${res.duplicates.join(',')}`,
											'提示',
											{
												confirmButtonText: '确认',
												type: 'warning'
											}
										);
										calculatedColEmpty();
										return;
									} else {
										toSqlCols.value = newCols
											.filter(item => item.type != 'tableForm')
											.map((item: any) => {
												return {
													name: item.title,
													type: getColType(item.type)
												};
											});
									}
								} else {
									// 取消勾选时
									calculatedColEmpty();
									// 取消disabled和清空info
									designer.value.activeRule.props.disabled = false;
									designer.value.activeRule.info = '';
								}
							}
						},
						control: [
							{
								handle(val) {
									return val == '';
								},
								method: 'disabled',
								rule: ['isPrimaryCol', 'relatedCol', 'hasDict']
							},
							{
								handle(val) {
									return val == 'calculatedCol';
								},
								method: 'hidden',
								rule: ['sqlStr']
							}
						]
					},
					{
						type: 'input',
						title: '计算列sql',
						field: 'sqlStr',
						value: '',
						col: {
							span: 225
						},
						props: {
							type: 'text',
							readonly: true
						},
						validate: [{ required: true, message: '请进行计算列配置', trigger: 'blur' }]
					}
				];
			}
		},
		inputNumber: {
			prepend: true,
			rule() {
				return [
					{
						type: 'checkbox',
						title: '',
						field: 'calculatedCol',
						value: [],
						options: [
							{
								value: 'calculatedCol',
								label: '是计算列',
								disabled:
									tableTitle.value === '编辑' &&
									designer.value?.activeRule?.props?.isExisting
							}
						],
						on: {
							// 使用 on 而不是 events
							change: e => {
								if (e.length > 0) {
									colCalculateVisible.value = true;
									designer.value.activeRule.props.disabled = true;
									designer.value.activeRule.info = '计算列无需填写';
									const cols = designer.value.getRule();
									const newCols = flattenFormDataWithLevel(cols);
									const res = checkForDuplicateColumns(newCols, tableTitle.value);

									if (res.hasDuplicates) {
										ElMessageBox.confirm(
											`主表列名不能重复,请先去除重复列名再配置计算列:${res.duplicates.join(',')}`,
											'提示',
											{
												confirmButtonText: '确认',
												type: 'warning'
											}
										);
										calculatedColEmpty();
										return;
									} else {
										toSqlCols.value = newCols
											.filter(item => item.type != 'tableForm')
											.map((item: any) => {
												return {
													name: item.title,
													type: getColType(item.type)
												};
											});
									}
								} else {
									calculatedColEmpty();
									designer.value.activeRule.props.disabled = false;
									designer.value.activeRule.info = '';
								}
							}
						},
						control: [
							{
								handle(val) {
									return val == '';
								},
								method: 'disabled',
								rule: ['isPrimaryCol', 'relatedCol', 'hasDict']
							},
							{
								handle(val) {
									return val == 'calculatedCol';
								},
								method: 'hidden',
								rule: ['sqlStr']
							}
						]
					},
					{
						type: 'input',
						title: '计算列sql',
						field: 'sqlStr',
						value: '',
						col: {
							span: 225
						},
						props: {
							type: 'text',
							readonly: true
						},
						validate: [{ required: true, message: '请进行计算列配置', trigger: 'blur' }]
					}
				];
			}
		},
		//
		// 	rule() {
		// 		return [
		// 			{
		// 				type: 'checkbox',
		// 				title: '',
		// 				field: 'hasDict',
		// 				value: [],
		// 				options: [{ value: 'hasDict', label: '是字典类型', disabled: false }],
		// 				control: [
		// 					{
		// 						handle(val) {
		// 							return val == 'hasDict';
		// 						},
		// 						method: 'hidden',
		// 						rule: ['dictName']
		// 					}
		// 				]
		// 			},
		// 			{
		// 				type: 'elTreeSelect',
		// 				title: '数据选择',
		// 				field: 'dictName',
		// 				props: {
		// 					data: dictList,
		// 					checkStrictly: true,
		// 					filterable: true,
		// 					props: {
		// 						label: 'label'
		// 					},
		// 					onChange: (item, index) => {
		// 						changeDictName(item, index);
		// 					},
		// 					onNodeClick: e => {
		// 						getNode(e);
		// 					}
		// 				}
		// 			}
		// 		];
		// 	}
		// },
		datePicker: {
			prepend: true,
			rule() {
				return [
					{
						type: 'checkbox',
						title: '',
						field: 'calculatedCol',
						value: [],
						options: [
							{
								value: 'calculatedCol',
								label: '是计算列',
								disabled:
									tableTitle.value === '编辑' &&
									designer.value?.activeRule?.props?.isExisting
							}
						],
						on: {
							// 使用 on 而不是 events
							change: e => {
								if (e.length > 0) {
									colCalculateVisible.value = true;
									designer.value.activeRule.props.disabled = true;
									designer.value.activeRule.info = '计算列无需填写';
									const cols = designer.value.getRule();
									const newCols = flattenFormDataWithLevel(cols);
									const res = checkForDuplicateColumns(newCols, tableTitle.value);

									if (res.hasDuplicates) {
										ElMessageBox.confirm(
											`主表列名不能重复,请先去除重复列名再配置计算列:${res.duplicates.join(',')}`,
											'提示',
											{
												confirmButtonText: '确认',
												type: 'warning'
											}
										);
										calculatedColEmpty();
										return;
									} else {
										toSqlCols.value = newCols
											.filter(item => item.type != 'tableForm')
											.map((item: any) => {
												return {
													name: item.title,
													type: getColType(item.type)
												};
											});
									}
								} else {
									calculatedColEmpty();
									designer.value.activeRule.props.disabled = false;
									designer.value.activeRule.info = '';
								}
							}
						},
						control: [
							{
								handle(val) {
									return val == '';
								},
								method: 'disabled',
								rule: ['isPrimaryCol', 'relatedCol', 'hasDict']
							},
							{
								handle(val) {
									return val == 'calculatedCol';
								},
								method: 'hidden',
								rule: ['sqlStr']
							}
						]
					},
					{
						type: 'input',
						title: '计算列sql',
						field: 'sqlStr',
						value: '',
						col: {
							span: 225
						},
						props: {
							type: 'text',
							readonly: true
						},
						validate: [{ required: true, message: '请进行计算列配置', trigger: 'blur' }]
					},
					{
						type: 'select',
						title: '日期类型',
						field: 'dateType',
						value: [],
						options: [
							{
								label: '年-月-日',
								value: 'date'
							},
							{
								label: '年-月',
								value: 'month'
							},
							{
								label: '年份',
								value: 'year'
							},
							{
								label: '年-月-日 时:分:秒',
								value: 'datetime'
							}
						]
					}
				];
			}
		}
	},
	updateDefaultRule: {
		input: {
			style: {
				minWidth: '120px'
			}
		},
		select: {
			style: {
				minWidth: '120px'
			}
		},
		elTreeSelect: {
			style: {
				minWidth: '120px'
			}
		},
		inputNumber: {
			style: {
				minWidth: '120px'
			}
		},
		datePicker: {
			style: {
				minWidth: '120px'
			}
		}
	},
	hiddenItem: [
		'group',
		'event',
		'elTransfer',
		'rate',
		'timeRange',
		'dateRange',
		'colorPicker',
		'fcEditor',
		'elButton',
		'slider',
		'timePicker',
		'subForm',
		'tree',
		'cascader'
	],
	hiddenItemConfig: {
		default: ['fetchData', 'effect'],
		input: ['type'],
		event: ['event', 'fetch'],
		datePicker: [
			'type',
			'pickerOptions',
			'startPlaceholder',
			'endPlaceholder',
			'unlinkPanels',
			'rangeSeparator'
		],
		upload: [
			'action',
			'onSuccess',
			'beforeUpload',
			'beforeRemove',
			'headers',
			'withCredentials',
			'data',
			'name'
		],
		select: [
			'fetch',
			'remoteMethod',
			'remote',
			'options',
			'data',
			'dataType',
			'func',
			'effect',
			'multiple',
			'multipleLimit',
			'optionsType',
			'optionsValue',
			'optionsLabel',
			'optionsChildren',
			'optionsProps'
		],
		elTreeSelect: [
			'fetch',
			'remoteMethod',
			'remote',
			'effect',
			'options',
			'props',
			'nodeKey',
			'data',
			'dataType',
			'func',
			'effect',
			'multiple',
			'multipleLimit',
			'optionsType',
			'optionsValue',
			'optionsLabel',
			'optionsChildren',
			'optionsProps',
			'treeData',
			'treeProps',
			'data'
		],
		radio: [
			'options',
			'data',
			'dataType',
			'func',
			'effect',
			'fetch',
			'remoteMethod',
			'remote',
			'optionsType',
			'optionsValue',
			'optionsLabel',
			'optionsChildren',
			'optionsProps'
		]
	},
	beforeDelete: rule => {
		// 检查是否是项目列
		if (rule.props?.isProject) {
			ElMessage.warning('重要列不能删除');
			return false;
		}

		// 编辑状态下的删除确认
		if (tableTitle.value === '编辑' && rule.props?.isExisting) {
			return new Promise(resolve => {
				ElMessageBox.confirm('删除后该字段的数据将被清空，是否继续删除？', '警告', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				})
					.then(() => {
						resolve(true);
					})
					.catch(() => {
						resolve(false);
					});
			});
		}

		return true;
	}
};

//计算列选择框置空，计算列结果置空
function calculatedColEmpty() {
	const activeData = designer.value.activeRule;
	// ElMessageBox.confirm('确认取消该组件的计算列？', '提示')
	// 	.then(item => {
	if (activeData.props && activeData.props.calculatedCol) {
		activeData.props.calculatedCol = [];
		//delete activeData.props.calculatedCol;
		if (activeData.props.sqlStr) {
			activeData.props.sqlStr = '';

			//	delete activeData.props.sqlStr;
		}

		designer.value.updateRuleFormData(activeData);
	}

	// })
	// .catch(item => {
	// 	if (activeData.props.calculatedCol) {
	// 		activeData.props.calculatedCol = ['calculatedCol'];
	// 	} else {
	// 		activeData.props = {
	// 			calculatedCol: ['calculatedCol']
	// 		};
	// 	}
	// });
}

//配置关联表
function getRelatedList() {
	const data = designer.value.getRule();
	relatedList.value.primaryCol = [];
	relatedList.value.relatedCol = [];
	const newData = flattenFormDataWithLevel(data);
	const res = checkForDuplicateColumns(newData, tableTitle.value);
	if (res.hasDuplicates) {
		ElMessageBox.confirm(
			`主表列名重复，请先去除重复列名再配置关联表：${res.duplicates.join(',')}`
		).then(item => {
			relatedTableEmpty();
		});
		return 'error';
	}

	newData.map(item => {
		if (item.props?.isPrimaryCol && item.props?.isPrimaryCol[0]) {
			relatedList.value.primaryCol.push({
				label: item.title,
				value: item.props?.fieldName || item.title
			});
		} else if (item.props?.relatedCol && item.props.relatedCol[0]) {
			relatedList.value.relatedCol.push({
				label: item.title,
				value: item.props?.fieldName || item.title
			});
		}
	});

	return 'success';
}

watch(
	() => props.formRules,
	newVal => {
		removeFcDesignerIcon();
	},
	{ immediate: true }
);

function removeFcDesignerIcon() {
	nextTick(() => {
		const link = document.querySelector('._fc-m-con a');
		if (link) {
			link.style.setProperty('background-image', 'none', 'important'); // 强制覆盖
		}
	});
}

function handleActive(rule) {
	if (rule.type === 'select' || rule.type === 'elTreeSelect') {
		// 清空options
		rule.options = [];
		if (rule.type === 'elTreeSelect') {
			rule.props.data = [];
		}
		// 确保更新到设计器中
		//	designer.value.updateRuleFormData(rule);
	}
	removeFcDesignerIcon();
}

// interface Designer {
// 	rule?: Array<{
// 		type: string;
// 		field: string;
// 		props: any;
// 		[key: string]: any;
// 	}>;
// }
function arrayToTree(items) {
	const rootItems = items.filter(item => item.parentId === -1);
	const idToNode = new Map(items.map(item => [item.id, { ...item, children: [] }]));

	function buildPath(node, parentPath = '', parentIdPath = '') {
		const currentPath = parentPath ? `${parentPath}/${node.label}` : node.label;
		const currentIdPath = parentIdPath ? `${parentIdPath}/${node.id}` : node.id;
		node.path = currentPath;
		node.idPath = currentIdPath;

		for (const child of node.children) {
			buildPath(child, currentPath, currentIdPath);
		}
	}

	for (const item of items) {
		if (item.parentId !== -1) {
			const parentNode = idToNode.get(item.parentId);
			if (parentNode) {
				parentNode.children.push(idToNode.get(item.id));
			}
		}
	}

	const result = rootItems.map(item => idToNode.get(item.id));
	result.forEach(node => buildPath(node));

	return result;
}

function removeFormData(data) {
	if (data.props.isProject) {
		nextTick(async () => {
			const currentRules = designer.value.getRule();
			currentRules.unshift(data);
			await designer.value.setRule(currentRules);
			ElMessageBox.confirm('重要列不能删除', '提示', {
				confirmButtonText: '确认',
				type: 'warning'
			});
		});
	}
	return false;
}

const handleDelete = async rule => {
	// 保存当前所有规则
	const currentRules = cloneDeep(designer.value.getRule());

	// 如果是项目列，直接阻止删除
	if (rule.props?.isProject) {
		ElMessage.warning('重要列不能删除');
		// 恢复组件
		//nextTick(async () => {
		await designer.value.setRule(currentRules);
		//});
		return;
	}
	// 如果是编辑状态下的已存在字段
	else if (tableTitle.value === '编辑' && rule.props?.isExisting) {
		try {
			await ElMessageBox.confirm('删除后该字段的数据将被清空，是否继续删除？', '警告', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(item => {})
				.catch(async err => {
					await designer.value.setRule(currentRules);
				});
			// 用户确认删除，不需要做什么，组件会自动删除
		} catch (err) {
			//
			console.log('取消删除', err);
			nextTick(async () => {
				await designer.value.setRule(currentRules);
			});
		}
	} else if (tableTitle.value === '编辑') {
		await ElMessageBox.confirm(
			'请确认该布局内无字段（如含有字段，其数据将被清空），是否继续删除？',
			'警告',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}
		)
			.then(item => {
				console.log('用户确认删除', item);
			})
			.catch(async err => {
				console.log('用户取消删除', err);
				await designer.value.setRule(currentRules);
			});
	}
};

// 递归遍历并标记已存在的组件
function markExistingComponents(rules) {
	return rules.map(rule => {
		// 创建新的规则对象
		const newRule = { ...rule };

		// 如果是实际的表单组件(不是布局组件)才添加 isExisting 标识
		if (
			rule.type &&
			![
				'fcRow',
				'div',
				'elAlert',
				'elTabs',
				'elDivider',
				'elImage',
				'elTag',
				'html',
				'elDivider'
			].includes(rule.type)
		) {
			newRule.props = {
				...rule.props,
				isExisting: true
			};
		}

		// 递归处理子组件
		if (rule.children && !['html', 'elDivider', 'div', 'elTag'].includes(rule.type)) {
			newRule.children = markExistingComponents(rule.children);
		}

		return newRule;
	});
}

// 修改 watch 部分的代码
watch(
	[() => formRules.value, () => designer.value],
	([rules, designerInstance]) => {
		if (rules && designerInstance) {
			nextTick(async () => {
				if (rules.length > 0 && tableTitle.value == '编辑' && !hasMarkedComponents) {
					// 只在未标记时执行
					// 递归标记所有已存在的实际组件
					const markedRules = markExistingComponents(rules);
					// console.log('formRules and designer ready, setting rules:', markedRules);

					await designerInstance.setRule(markedRules);
					await designerInstance.setOption(formOptions.value);
					hasMarkedComponents = true; // 设置标记状态为已完成
				}
			});
		}
		removeFcDesignerIcon();
	},
	{ immediate: true }
);

const closeCalculateCol = () => {
	console.log('关闭计算列配置');
	colCalculateVisible.value = false;
	calculatedColEmpty();
};

//计算列配置完成，修改对应组件信息
const submitCalculateCol = data => {
	const selected = designer.value.activeRule;
	colCalculateVisible.value = false;
	if (selected) {
		// 修改组件属性
		selected.props.sqlStr = data;
		// 更新组件
		designer.value.updateRuleFormData(selected);
	}
	// formData.value.tableColumn = formData.value.tableColumn.map(item => {
	// 	if (item.type == 'calculatedCol' && item.name == sqlCol.value) {
	// 		return { ...item, sqlStr: data };
	// 	}
	// 	return item;
	// });
	// calculateColData.value = formData.value.tableColumn.filter(
	// 	item => item.type == 'calculatedCol'
	// );
	// console.log('submitCalculateCol', formData.value);
};

//关联表配置完成，修改对应组件信息
async function submitRelatedTable(data, names) {
	const rule = designer.value.getRule();

	// 使用示例
	const result = cloneDeep(processFormFields(rule, data));
	// rule.map(item => {
	// 	//console.log('item', item);
	// });

	designer.value.setRule(result);

	const currentOptions = designer.value.getOptions();

	if (currentOptions) {
		if ('form' in currentOptions && 'relatedTable' in currentOptions.form) {
			currentOptions.form.relatedinfo = `已关联:${names.join(',')}`;
		}

		designer.value.setOption(currentOptions);
	} else {
		console.error('关联表relatedinfo置空失败');
	}

	relatedTableVisible.value = false;
}

//遍历节点，去除关联表信息
function removeProcessFormFields(formData) {
	// 深拷贝原始数据，避免直接修改
	const processedData = JSON.parse(JSON.stringify(formData));

	/**
	 * 递归遍历并处理字段
	 * @param {Array} items 当前层级的节点数组
	 */
	function traverse(items) {
		if (!Array.isArray(items)) return;

		items.forEach(item => {
			// 检查是否是条件列（isPrimaryCol）
			if (item.props?.isPrimaryCol?.length > 0) {
				if (item.props.relatedTables) {
					item.props.relatedTables = [];
				}
			}
			// 检查是否是关联列（relatedCol）
			else if (item.props?.relatedCol?.length > 0 || item.props?.relatedCol) {
				item.props.relatedField = '';
				item.props.relatedCol = ['relatedCol'];
				item.props.relatedTableId = '';
				item.props.relatedTableName = '';
			}

			// 递归处理子节点
			if (item.children && item.type != 'tableForm') {
				traverse(item.children);
			}
		});
	}

	traverse(processedData);
	return processedData;
}

//遍历节点，找出将关联表信息添加到对应字段上
function processFormFields(formData, data) {
	// 深拷贝原始数据，避免直接修改
	const processedData = JSON.parse(JSON.stringify(formData));

	/**
	 * 递归遍历并处理字段
	 * @param {Array} items 当前层级的节点数组
	 */
	function traverse(items) {
		if (!Array.isArray(items)) return;

		items.forEach(item => {
			// 检查是否是主键列（isPrimaryCol）
			if (item.props?.isPrimaryCol?.length > 0) {
				data.forEach(d => {
					const matchedColumnsIndex = d.matchColumns?.findIndex(col => {
						return col.sourceColumn === (item.props?.fieldName || item.title);
					});

					if (matchedColumnsIndex != undefined) {
						const sourceColInfo = d.primaryTableCols[matchedColumnsIndex];
						if (!item.props.relatedTables) {
							item.props.relatedTables = [];
						}
						if (sourceColInfo.props.isProject) {
							item.props.relatedTables.push({
								id: d.relatedTable,
								col: d.matchColumns[matchedColumnsIndex].targetColumn,
								colIsProject: true
							});
						} else if (
							sourceColInfo.props.hasDict &&
							sourceColInfo.props.hasDict.length > 0
						) {
							item.props.relatedTables.push({
								id: d.relatedTable,
								col: d.matchColumns[matchedColumnsIndex].targetColumn,
								sourceColHasDict: true
							});
						} else {
							item.props.relatedTables.push({
								id: d.relatedTable,
								col: d.matchColumns[matchedColumnsIndex].targetColumn
							});
						}
					}
				});
			}
			// 检查是否是关联列（relatedCol）
			else if (item.props?.relatedCol?.length > 0) {
				data.forEach(d => {
					const matchedResultColumns = d.resultColumns?.filter(col => {
						return col.sourceColumn === (item.props?.fieldName || item.title);
					});

					if (matchedResultColumns?.length > 0) {
						const originalProps = JSON.parse(matchedResultColumns[0].targetColumn); // 保留原始属性
						item.props = {
							...originalProps.props,
							fieldName: item.props?.fieldName,
							relatedField: originalProps.props.fieldName || item.title,
							relatedTableId: d.relatedTable,
							relatedCol: ['relatedCol']
							//relatedTableName: matchedResultColumns[0].targetColumnName
						};
						// 如果存在 options，则清空
						if (item.options) {
							item.options = [];
						}
					}
				});
			}

			// 递归处理子节点
			if (item.children && item.type != 'tableForm') {
				traverse(item.children);
			}
		});
	}

	traverse(processedData);
	return processedData;
}

const clickNode = ref();
function getNode(e) {
	clickNode.value = e;
}

//选取字典表后，获取所有选择关联字典的字段
async function changeDictName(dict, index) {
	const selected = designer.value.activeRule;
	if (selected) {
		// 修改组件属性
		selected.props.dictField = clickNode.value.idPath;
		selected.props.dictId = clickNode.value.id;
		// 更新组件
		designer.value.updateRuleFormData(selected);

		//	safeFormData.value.tableColumn[index].dictField = clickNode.value.idPath;
		//safeFormData.value.tableColumn[index].dictId = clickNode.value.id;
		const res = await service.dict.info.list({ typeId: clickNode.value.id });
		if (res.length == 0) {
			await ElMessageBox.confirm('该字典没有子项,无法关联,请重新选择', '提示', {
				confirmButtonText: '确认',
				type: 'warning'
			});

			selected.props.dictField = '';
			selected.props.dictId = '';
			selected.props.dictName = '';
			designer.value.updateRuleFormData(selected);
		}
	}
}

async function getParent(dict, index) {
	//safeFormData.value.tableColumn[index].parentId = clickNode.value.id;
	const selected = designer.value.activeRule;

	if (selected) {
		selected.props.parentId = clickNode.value.id;
		const res = await service.dict.info.list({ typeId: clickNode.value.id });
		if (res.length == 0) {
			await ElMessageBox.confirm('该字典没有子项,无法关联', '提示', {
				confirmButtonText: '确认',
				type: 'warning'
			});
			selected.props.parentId = '';
			//selected.props.dictlabel = '';
			selected.props.dictParent = '';
		}
	}
}

const emit = defineEmits(['closeDialog', 'nextStep']);

const closeDialog = () => {
	emit('closeDialog');
};

const nextStep = async type => {
	if (type === '暂存') {
		try {
			await ElMessageBox.confirm('暂存表后，其流程也会禁用，确定暂存？', '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning'
			});
			// 用户点击确认后继续执行后续代码
		} catch (err) {
			// 用户点击取消，直接返回
			return;
		}
	}

	if (!designer.value.getOptions().formName) {
		ElMessageBox.confirm('表名不能为空', '提示', {
			confirmButtonText: '确认',
			type: 'warning'
		});
		return;
	}
	const rules = designer.value.getRule();
	const newRules = traverseNode(rules, tableTitle.value);
	const newData = flattenFormDataWithLevel(rules);
	const res = checkForDuplicateColumns(newData, tableTitle.value);
	console.log('判断列名重复后', res);
	if (res.hasDuplicates) {
		ElMessageBox.confirm(`主表列名不能重复:${res.duplicates.join(',')}`, '提示', {
			confirmButtonText: '确认',
			type: 'warning'
		});
		return;
	}
	//if (tableTitle.value == '新增') {
	// rules.map(item => {
	// 	console.log('item新增', item);
	// 	if (item.props) {
	// 		item.props.fieldName = item.title;
	// 	} else {
	// 		item.props = {
	// 			fieldName: item.title
	// 		};
	// 	}
	// });
	designer.value.setRule(newRules);
	//}

	hasMarkedComponents = false;
	emit('nextStep', designer.value.getOptions(), newRules, type);
};

// watch(
// 	() => designer.value,
// 	newDesigner => {
// 		if (newDesigner && formRules.value && tableTitle.value == '编辑') {
// 			nextTick(async () => {
// 				await newDesigner.setRule(formRules.value);
// 				await newDesigner.setRule(formOptions.value);
// 			});
// 		}
// 	},
// 	{ immediate: true }
// );
</script>

<style>
/* ._fd-preview-tabs .el-tabs__item:nth-child(4),
._fd-preview-tabs .el-tabs__item:nth-child(3) {
	display: none !important;
} */
._fd-preview-tabs #tab-component,
._fd-preview-tabs #tab-html {
	display: none !important;
}
</style>

<style scoped lang="scss">
.table-config-container {
	margin: 0 auto;

	height: 82vh;
	border-radius: 8px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.input-width {
	width: 200px;
}
.input-width-long {
	width: 150px;
}
.input-width-short {
	width: 120px;
}

.column-item {
	display: flex;
	align-items: center;
	gap: 10px;
	padding: 10px;
	background-color: white;
	border-radius: 8px;
	margin-bottom: 10px;
	transition: all 0.3s ease;

	&:hover {
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}
}

.col-value {
	flex: 1;
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
}

.col-actions {
	display: flex;
	gap: 10px;
}

.footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
	margin-top: 20px;
}
// :deep(.el-main  ._fc-m-con a[href="https://form-create.com/"])
// {
// 	background-image: none !important;
// }

.btn-cancel {
	background-color: #f56c6c;
	color: white;
}

.right10 {
	margin-right: 10px;
}

.left10 {
	margin-left: 10px;
}
.left5 {
	margin-left: 5px;
}
.square-button {
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
