<template>
	<el-dialog
		:modelValue="regularStrTestVisible"
		@update:modelValue="updateVisibility"
		title="正则表达式测试"
		width="600px"
		@close="closeDialog"
	>
		<el-form label-position="top">
			<el-form-item label="正则表达式">
				<el-input
					:modelValue="regularStr"
					@update:modelValue="updateRegularStr"
					placeholder="请输入正则表达式"
				/>
			</el-form-item>
			<el-form-item label="测试文本">
				<el-input v-model="testString" placeholder="请输入要测试的文本" />
			</el-form-item>
		</el-form>
		<el-button type="primary" @click="testRegex"> 测试 </el-button>
		<div class="result-container" v-if="testResult !== null">
			<el-alert
				:title="testResult ? '匹配成功' : '匹配失败'"
				:type="testResult ? 'success' : 'error'"
				:description="
					testResult ? '输入的文本符合正则表达式' : '输入的文本不符合正则表达式'
				"
				show-icon
			/>
		</div>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

const props = defineProps<{
	regularStr: string;
	regularStrTestVisible: boolean;
}>();

const emit = defineEmits(["update:regularStr", "update:regularStrTestVisible"]);

const testString = ref("");
const testResult = ref<boolean | null>(null);

const updateRegularStr = (value: string) => {
	emit("update:regularStr", value);
};

const updateVisibility = (value: boolean) => {
	emit("update:regularStrTestVisible", value);
};

const closeDialog = () => {
	emit("update:regularStrTestVisible", false);
};

const testRegex = () => {
	try {
		const regex = new RegExp(props.regularStr);
		console.log("正则表达式:", regex, "测试文本:", testString.value);
		testResult.value = regex.test(testString.value);
	} catch (error) {
		console.error("无效的正则表达式:", error);
		testResult.value = false;
	}
};
</script>

<style scoped>
.result-container {
	margin-top: 20px;
}
</style>
