<template>
	<el-dialog
		v-model="relatedTableVisible"
		title="关联表详情"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		@open="openDialog"
	>
		<div class="design-form">
			<el-form label-position="top">
				<!-- 关联表配置头部精简 -->
				<div class="relation-header">
					<div class="title-wrapper">
						<h3>关联表配置</h3>
						<el-tag type="info" effect="plain" size="small">可选</el-tag>
					</div>
					<div class="header-buttons">
						<el-button-group class="relation-btn-group">
							<el-button
								type="danger"
								plain
								@click="removeAllRelations"
								:disabled="relations.length == 0"
								size="small"
								class="relation-btn"
							>
								<el-icon class="btn-icon"><delete /></el-icon>
								<span class="btn-text">移除所有关联表</span>
							</el-button>
							<el-button
								type="primary"
								plain
								@click="addRelation"
								:disabled="relations.length >= 10"
								size="small"
								class="relation-btn"
							>
								<el-icon class="btn-icon"><plus /></el-icon>
								<span class="btn-text">添加关联表</span>
							</el-button>
						</el-button-group>
					</div>
				</div>

				<!-- 关联表配置网格容器 -->
				<div class="relations-grid">
					<div v-for="(relation, index) in relations" :key="index" class="relation-item">
						<div class="relation-item-header">
							<div class="header-left">
								<el-tag type="primary" size="small" class="relation-index">{{
									index + 1
								}}</el-tag>
								<span class="relation-title">关联表配置</span>
								<el-button
									type="danger"
									@click="removeRelation(index)"
									size="small"
									link
								>
									<el-icon><delete /></el-icon>
								</el-button>
							</div>
						</div>

						<div class="relation-content">
							<!-- 选择关联表 -->
							<el-form-item label="选择关联表" class="compact-form-item">
								<el-select
									v-model="relation.relatedTable"
									filterable
									style="width: 100%"
									placeholder="请选择要关联的数据表"
									@change="val => handleRelatedTableChange(val, index)"
									size="default"
								>
									<el-option
										v-for="item in tableList"
										:key="item.tableName"
										:label="item.name"
										:value="item.id"
									>
										<span class="table-option">
											<span>{{ item.name }}</span>
											<el-tag size="small" type="info">{{ item.id }}</el-tag>
										</span>
									</el-option>
								</el-select>
							</el-form-item>

							<!-- 匹配列配置 -->
							<el-form-item
								label="匹配列(条件列)配置"
								class="compact-form-item"
								v-if="relation.relatedTable"
							>
								<div class="columns-container">
									<div class="columns-wrapper">
										<div
											v-for="(item, colIndex) in relation.matchColumns"
											:key="colIndex"
											class="column-mapping"
										>
											<div class="mapping-row">
												<el-select
													v-model="item.sourceColumn"
													filterable
													placeholder="选择当前表字段"
													class="mapping-select"
													size="default"
												>
													<el-option
														v-for="col in relatedList.primaryCol"
														:key="col"
														:label="col.label"
														:value="col.value"
													/>
												</el-select>

												<el-icon class="mapping-arrow"><right /></el-icon>

												<el-select
													v-model="item.targetColumn"
													filterable
													placeholder="选择关联表字段"
													class="mapping-select"
													size="default"
												>
													<el-option
														v-for="col in relation.primaryTableCols"
														:key="col.props.fieldName"
														:label="col.title"
														:value="col.props.fieldName"
													/>
												</el-select>

												<el-button
													type="danger"
													@click="removeMatchColumn(index, colIndex)"
													circle
													plain
													size="small"
												>
													<el-icon><close /></el-icon>
												</el-button>
											</div>
										</div>
									</div>
								</div>
							</el-form-item>

							<!-- 结果列配置 -->
							<el-form-item
								label="自动带出列(结果列)配置"
								class="compact-form-item"
								v-if="relation.relatedTable"
							>
								<div class="columns-container">
									<div class="columns-wrapper">
										<div
											v-for="(item, colIndex) in relation.resultColumns"
											:key="colIndex"
											class="column-mapping"
										>
											<div class="mapping-row">
												<el-select
													v-model="item.sourceColumn"
													filterable
													placeholder="选择当前表字段"
													class="mapping-select"
													size="default"
												>
													<el-option
														v-for="col in getAvailableSourceColumns(
															index,
															colIndex
														)"
														:key="col.label"
														:label="col.label"
														:value="col.value"
													/>
												</el-select>

												<el-icon class="mapping-arrow"><right /></el-icon>

												<el-select
													v-model="item.targetColumn"
													filterable
													placeholder="选择关联表字段"
													class="mapping-select"
													size="default"
												>
													<el-option
														v-for="col in relation.relatedTableCols"
														:key="col.label"
														:label="col.label"
														:value="col.value"
													/>
												</el-select>

												<el-button
													type="danger"
													@click="removeResultColumn(index, colIndex)"
													circle
													plain
													size="small"
												>
													<el-icon><close /></el-icon>
												</el-button>
											</div>
										</div>
									</div>

									<el-button
										type="primary"
										@click="addResultColumn(index)"
										size="small"
										plain
										class="add-column-btn"
									>
										<el-icon><plus /></el-icon>添加结果列
									</el-button>
								</div>
							</el-form-item>
						</div>
					</div>
				</div>
			</el-form>

			<!-- 底部按钮 -->
			<div class="footer">
				<el-button @click="hanldeClose">取消</el-button>

				<el-button type="success" @click="nextStep">确认</el-button>
			</div>
		</div>
	</el-dialog>
</template>
<script setup lang="ts">
import { toRefs, computed, PropType, ref, watch } from 'vue';
import { DesignTable, CustomizeColumn, SelectDict, TableList } from '../../types';
import { useCool } from '/@/cool';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Delete, Plus, Right, Close, Warning } from '@element-plus/icons-vue';
import { flattenFormDataWithLevel } from '../../utils/designTable';

const { service, router } = useCool();
const dialogVisible = ref(false);

// 改用数组存储关联表信息
const relations = ref<RelationItem[]>([]);
const props = defineProps({
	relatedList: {
		type: Object as PropType<{}>,
		required: true
	},
	relatedTableVisible: {
		type: Boolean
	},
	title: {
		type: String as PropType<string>,
		required: true
	}
});
const { relatedList, relatedTableVisible, title } = toRefs(props);
// const safeFormData = computed(
// 	() => formData.value || { name: "", tableColumn: [{ name: "", showName: "" }], remark: "" }
// );
const formData = ref<DesignTable>();
const relatedTable = ref('');
const isPrimaryKey = ref(false);
const primaryTableCols = ref<CustomizeColumn[]>([]);
const primaryKeyCols = ref<CustomizeColumn[]>([]);
const relatedCols = ref<CustomizeColumn[]>([]);
const relatedTableCols = ref<{ label: string; value: any }[]>([]);

// 定义关联表数据结构
interface RelationItem {
	relatedTable: string;
	primaryTableCols: CustomizeColumn[];
	relatedTableCols: { label: string; value: any }[];
	matchColumns: MatchColumn[]; // 添加这行
	resultColumns: ResultColumn[]; // 添加这行
}
const tableList = ref<any[]>([]);
async function openDialog() {
	tableList.value = await service.cloud.db.list();
	console.log('tableList', tableList.value);
}

// 添加新的接口定义
interface MatchColumn {
	sourceColumn: string;
	targetColumn: string;
	sourceColIsProject?: boolean;
}

interface ResultColumn {
	sourceColumn: string;
	targetColumn: string;
}

// 初始化一个空的关联表配置
const initRelations = () => {
	relations.value = [
		{
			relatedTable: '',
			primaryTableCols: [],
			relatedTableCols: [],
			matchColumns: [],
			resultColumns: []
		}
	];
};

// 从 formData 重构 relations 数据的函数
const reconstructRelations = (formData: DesignTable) => {
	const relatedTablesMap = new Map();

	// 先从主键列收集关联表信息
	formData.tableColumn.forEach(column => {
		if (column.isPrimaryKey && column.relatedTables) {
			column.relatedTables.forEach(rel => {
				if (!relatedTablesMap.has(rel.relatedTable)) {
					// 获取关联表的完整信息
					const tableInfo = tableList.value.find(item => item.id === rel.relatedTable);

					const relationData = {
						relatedTable: rel.relatedTable,
						primaryTableCols: [],
						relatedTableCols: [],
						matchColumns: [],
						resultColumns: []
					};
					console.log('表信息', relationData, tableInfo, tableList.value);
					// 如果找到表信息，添加列信息
					if (tableInfo) {
						console.log('添加列信息', tableInfo);
						tableInfo.colInfo.forEach((col: any) => {
							if (col.isPrimaryKey) {
								relationData.primaryTableCols.push(col);
							} else {
								relationData.relatedTableCols.push({
									label: col.name,
									value: JSON.stringify(col)
								});
							}
						});
					}

					relatedTablesMap.set(rel.relatedTable, relationData);
				}

				const relationData = relatedTablesMap.get(rel.relatedTable);
				// const { relatedTableCol, relatedColType, relatedShowName } = column;
				// const targetColumnData = {};
				// Object.assign(targetColumnData, column, {
				// 	name: relatedTableCol,
				// 	type: relatedColType,
				// 	showName: relatedShowName
				// });
				relationData.matchColumns.push({
					sourceColumn: column.name,
					targetColumn: rel.relatedTableCol
					//	sourceColIsProject: column?.isProject || false
				});
			});
		}
	});
	console.log('关系表!', relatedTablesMap);
	// 处理关联列
	formData.tableColumn.forEach(column => {
		if (column.type === 'relatedCol' && column.relatedTable) {
			if (!relatedTablesMap.has(column.relatedTable)) {
				return;
			}

			const relationData = relatedTablesMap.get(column.relatedTable);
			relationData.resultColumns.push({
				sourceColumn: column.name,
				targetColumn: column.relatedTableCol
				// targetColumn: JSON.stringify({
				// 	name: column.relatedTableCol,
				// 	type: column.relatedColType,
				// 	showName: column.relatedShowName,
				// 	stringType: column.stringType,
				// 	dateType: column.dateType,
				// 	decimals: column.decimals,
				// 	regularStr: column.regularStr
				// })
			});
		}
	});

	return Array.from(relatedTablesMap.values());
};

// // 监听 formData 变化
// watch(
// 	formData,
// 	newVal => {
// 		if (newVal) {
// 			primaryKeyCols.value = [];
// 			relatedCols.value = [];
// 			// 初始化主键列和关联列
// 			formData.value.tableColumn.map(item => {
// 				if (item.isPrimaryKey) {
// 					isPrimaryKey.value = true;
// 					primaryKeyCols.value.push(item);
// 				}
// 				if (item.type == 'relatedCol') {
// 					relatedCols.value.push(item);
// 				}
// 			});
// 			console.log('title', title.value);
// 			// 如果是新建，则初始化一个空的关联表
// 			if (title.value == '新增') {
// 				console.log('新建');
// 				//initRelations();
// 				const reconstructedRelations = reconstructRelations(formData.value);
// 				console.log('reconstructedRelations', reconstructedRelations);
// 				if (reconstructedRelations.length > 0) {
// 					relations.value = reconstructedRelations;
// 				} else {
// 					initRelations();
// 				}
// 			} else {
// 				console.log('编辑');
// 				// 编辑模式：从 formData 重构 relations
// 				const reconstructedRelations = reconstructRelations(formData.value);
// 				console.log('reconstructedRelations', reconstructedRelations);
// 				if (reconstructedRelations.length > 0) {
// 					relations.value = reconstructedRelations;
// 				} else {
// 					initRelations();
// 				}
// 				console.log('结果', relations.value);
// 			}
// 		}
// 	},
// 	{ immediate: true, deep: true }
// );

// 添加关联表按钮的处理函数
const addRelation = () => {
	console.log('关联表!!', relations.value);
	// 限制最多添加5个关联表
	if (relations.value.length >= 10) {
		ElMessage.warning('最多只能添加10个关联表');
		return;
	}

	relations.value.push({
		relatedTable: '',
		primaryTableCols: [],
		relatedTableCols: [],
		matchColumns: [],
		resultColumns: []
	});
};

// 删除关联表时的处理函数
const removeRelation = (index: number) => {
	// 保持至少有两个关联表
	// if (relations.value.length <= 2) {
	// 	ElMessage.warning("至少需要保留两个关联表");
	// 	return;
	// }
	relations.value.splice(index, 1);
};

// 修改关联表选择处理函数
const handleRelatedTableChange = async (val: string, index: number) => {
	const tableInfo = tableList.value.find(item => item.id == val);
	const currentRelation = relations.value[index];
	console.log('currentRelation', currentRelation, 'tableInfo', tableInfo);
	const rules = tableInfo.colInfo;
	const newRules = flattenFormDataWithLevel(rules);
	currentRelation.relatedTableCols = [];
	currentRelation.primaryTableCols = [];
	newRules.map(item => {
		if (item.props && item.props.isPrimaryKey && item.props.isPrimaryKey[0] == 'isPrimaryKey') {
			currentRelation.primaryTableCols.push(item);
		} else if (item.type != 'tableForm') {
			currentRelation.relatedTableCols.push({
				label: item.title,
				value: JSON.stringify(item)
			});
		}
	});
	console.log(
		'查询主键列',
		newRules,
		currentRelation.primaryTableCols,
		currentRelation.relatedTableCols
	);
	if (tableInfo) {
		// tableInfo.colInfo.map(item => {
		// 	if (item.isPrimaryCol) {
		// 		currentRelation.primaryTableCols.push(item);
		// 	} else {
		// 		currentRelation.relatedTableCols.push({
		// 			label: item.showName,
		// 			value: JSON.stringify(item)
		// 		});
		// 	}
		// });

		if (currentRelation.primaryTableCols.length == 0) {
			ElMessage.warning('关联表无主键列');
			currentRelation.relatedTable = '';
			currentRelation.relatedTableCols = [];
		} else {
			// 根据主键数量初始化匹配列
			currentRelation.matchColumns = currentRelation.primaryTableCols.map(() => ({
				sourceColumn: '',
				targetColumn: ''
			}));
		}
	}
};

const emit = defineEmits(['closeDialog', 'submit']);

const nextStep = () => {
	console.log('关联表!!', relations.value);
	if (relations.value) {
		if (relations.value.length === 0) {
			ElMessage.warning('请至少添加一个关联表');
			return;
		}

		// 收集所有已配置的主键列和自动带出列
		const configuredPrimaryKeys = new Set();
		const configuredRelatedCols = new Set();

		relations.value.forEach(relation => {
			relation.matchColumns.forEach(match => {
				configuredPrimaryKeys.add(match.sourceColumn);
			});
			relation.resultColumns.forEach(result => {
				configuredRelatedCols.add(result.sourceColumn);
			});
		});

		// 检查所有主键列是否都已配置
		const unconfiguredPrimaryKeys = relatedList.value.primaryCol
			.filter(col => !configuredPrimaryKeys.has(col.value))
			.map(col => col.label);

		if (unconfiguredPrimaryKeys.length > 0) {
			ElMessageBox.alert(
				`以下匹配列(条件列)未在任何关联表中配置：${unconfiguredPrimaryKeys.join('、')}`,
				'提示'
			);

			return;
		}

		// 检查所有自动带出列是否都已配置
		const unconfiguredRelatedCols = relatedList.value.relatedCol
			.filter(col => col.type === 'relatedCol' && !configuredRelatedCols.has(col.name))
			.map(col => col.showName);

		if (unconfiguredRelatedCols.length > 0) {
			ElMessageBox.alert(
				`以下自动带出列(结果列)未在任何关联表中配置：${unconfiguredRelatedCols.join('、')}`,
				'提示'
			);
			return;
		}

		// 验证每个关联表的配置
		for (let i = 0; i < relations.value.length; i++) {
			const relation = relations.value[i];
			if (!relation.relatedTable) {
				ElMessage.warning(`第${i + 1}个关联表未选择,如果无需关联,请点击跳过`);
				return;
			}

			// 验证匹配列是否完整
			if (!relation.matchColumns.every(col => col.sourceColumn && col.targetColumn)) {
				ElMessage.warning(`第${i + 1}个关联表的匹配列配置不完整`);
				return;
			}

			// 验证结果列是否完整
			if (!relation.resultColumns.every(col => col.sourceColumn && col.targetColumn)) {
				ElMessage.warning(`第${i + 1}个关联表的结果列配置不完整`);
				return;
			}
		}
		console.log('即将更新formData');
		const relationTableName: string[] = [];
		relations.value.map(item => {
			tableList.value.map(col => {
				if (col.id == item.relatedTable) {
					relationTableName.push(col.name);
				}
			});
		});
		console.log('relationTableName', relationTableName);
		emit('submit', relations.value, relationTableName);
		// 更新 formData 中的数据
		// formData.value.tableColumn.forEach((column, index) => {
		// 	// 处理主键列的关联关系
		// 	if (column.isPrimaryKey) {
		// 		// 只保留有效的关联配置
		// 		column.relatedTables = relations.value
		// 			.map(relation => {
		// 				const matchColumn = relation.matchColumns.find(
		// 					match => match.sourceColumn === column.name
		// 				);
		// 				const relatedTableColInfo = relation.primaryTableCols.find(
		// 					col => col.name == matchColumn?.targetColumn
		// 				);

		// 				return matchColumn?.targetColumn
		// 					? {
		// 							relatedTable: relation.relatedTable,
		// 							relatedTableCol: matchColumn.targetColumn,
		// 							relatedColisProject: relatedTableColInfo?.isProject || false
		// 						}
		// 					: null;
		// 			})
		// 			.filter(Boolean); // 过滤掉 null 值
		// 	}
		// 	const relatedInfo = relations.value
		// 		.map(relation => {
		// 			console.log('relation', relation);
		// 			const resultColumn = relation.resultColumns.find(
		// 				result => result.sourceColumn === column.name
		// 			);
		// 			if (resultColumn) {
		// 				console.log('resultColumn', resultColumn);
		// 				try {
		// 					const targetColumnData = JSON.parse(resultColumn.targetColumn);
		// 					// 将关联表列的信息合并到 column，保留原有的关键属性
		// 					const {
		// 						name,
		// 						showName,
		// 						type,
		// 						isNotShow,
		// 						isNotShowCode = false
		// 					} = column;

		// 					if (targetColumnData.type == 'relatedCol') {
		// 						targetColumnData.relatedColType = targetColumnData.relatedColType;
		// 					} else {
		// 						targetColumnData.relatedColType = targetColumnData.type;
		// 					}
		// 					if (
		// 						targetColumnData.isProject ||
		// 						targetColumnData.isPrimaryKey ||
		// 						targetColumnData.isPrimaryCol
		// 					) {
		// 						delete targetColumnData.isProject;
		// 						delete targetColumnData.isPrimaryKey;
		// 						delete targetColumnData.isPrimaryCol;
		// 					}
		// 					targetColumnData.relatedShowName = targetColumnData.showName;
		// 					Object.assign(column, targetColumnData, {
		// 						name,
		// 						showName,
		// 						type,
		// 						isNotShow
		// 					});
		// 					// 仅保留关联表的基本引用信息
		// 					column.relatedTableCol = targetColumnData.name;
		// 				} catch (error) {
		// 					console.error('解析关联表列失败', error);
		// 				}
		// 				return relation.relatedTable;
		// 			}
		// 			return null;
		// 		})
		// 		.filter(Boolean);
		// 	console.log('relatedInfo', relatedInfo);
		// 	if (relatedInfo.length > 0) {
		// 		column.relatedTable = relatedInfo[0];
		// 	}

		// 	console.log('处理中', index, formData.value.tableColumn.length);
		// });
		//console.log('提交数据', formData.value);
	}
	//	emit('nextStep', formData.value);
	//emit('update:formData', formData.value);
};

// 移除所有关联表
const removeAllRelations = () => {
	relations.value = [];
	// 清除相关的关联列配置
	formData.value.tableColumn.forEach(item => {
		if (item.isPrimaryKey || item.type === 'relatedCol') {
			item.relatedTables = [];
		}
	});
};

// 添加匹配列
const addMatchColumn = (relationIndex: number) => {
	// 可以添加提示
	ElMessage.warning('匹配列数量需要与主键数量一致，无法添加');
};

// 删除匹配列
const removeMatchColumn = (relationIndex: number, columnIndex: number) => {
	// 可以添加提示
	ElMessage.warning('匹配列数量需要与主键数量一致，无法删除');
};

// 添加结果列
const addResultColumn = (relationIndex: number) => {
	const relation = relations.value[relationIndex];
	relation.resultColumns.push({
		sourceColumn: '',
		targetColumn: ''
	});
};

// 删除结果列
const removeResultColumn = (relationIndex: number, columnIndex: number) => {
	const relation = relations.value[relationIndex];
	relation.resultColumns.splice(columnIndex, 1);
};

const getAvailableSourceColumns = (relationIndex: number, currentColIndex: number) => {
	const relation = relations.value[relationIndex];

	// 获取当前关联配置中，*其他*结果列已经选择的源列 (排除当前正在编辑的列)
	// 添加 .filter(value => value) 以处理可能存在的空值或未选中的情况
	const selectedResultColumnsByOthers = relation.resultColumns
		.filter((_, index) => index !== currentColIndex) // 排除自己
		.map(col => col.sourceColumn) // 获取源列的值
		.filter(value => value); // 过滤掉可能存在的空值或未选值

	// 获取当前关联配置中，所有匹配列已经选择的源列
	// 添加 .filter(value => value) 以处理可能存在的空值或未选中的情况
	const selectedMatchColumns = relation.matchColumns
		.map(col => col.sourceColumn) // 获取源列的值
		.filter(value => value); // 过滤掉可能存在的空值或未选值
	console.log(
		'relation',
		relation,
		'selectedMatchColumns',
		selectedMatchColumns,
		'selectedResultColumnsByOthers',
		selectedResultColumnsByOthers,
		'relatedList.value.relatedCol',
		relatedList.value.relatedCol
	);
	// 从所有可能的源列 (relatedList.value.relatedCol，即父组件传入的"自动带出列") 中筛选
	const res = relatedList.value.relatedCol.filter(
		col =>
			// 条件1: 该源列没有被用作匹配列
			!selectedMatchColumns.includes(col.value) &&
			// 条件2: 该源列没有被*其他*结果列使用
			!selectedResultColumnsByOthers.includes(col.value)
	);
	console.log('结果', res);
	return res;
};
const hanldeClose = () => {
	emit('closeDialog');
};
</script>
<style scoped lang="scss">
.design-form {
	padding: 12px;
	background: #fff;
	border-radius: 8px;

	.relations-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
		gap: 12px;
		margin-bottom: 16px;
	}

	.relation-item {
		background: #f8f9fa;
		border-radius: 6px;
		padding: 8px;
		margin-bottom: 8px;
		border: 1px solid #e4e7ed;
		height: 98%;

		.relation-content {
			height: 100%;
			.columns-container {
				padding: 8px;

				.mapping-row {
					display: flex;
					align-items: center;
					gap: 4px;

					.mapping-select {
						flex: 1;
						min-width: 150px; // 防止溢出
					}

					.mapping-arrow {
						flex: none;
						padding: 0 4px;
					}
				}
			}
		}

		.relation-item-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 8px;

			.header-left {
				display: flex;
				align-items: center;
				gap: 8px;
			}

			.relation-index {
				font-size: 12px;
			}

			.relation-title {
				font-size: 14px;
				color: #606266;
				font-weight: 500;
			}
		}
	}

	// 响应式调整
	@media screen and (max-width: 1400px) {
		.relations-grid {
			grid-template-columns: repeat(2, 1fr);
		}
	}

	@media screen and (max-width: 900px) {
		.relations-grid {
			grid-template-columns: 1fr;
		}
	}

	// 优化内部表单布局
	.compact-form-item {
		margin-bottom: 8px;

		:deep(.el-form-item__label) {
			padding-bottom: 2px;
			line-height: 1.2;
		}
	}

	// 优化选择器组合布局
	.column-mapping {
		margin-bottom: 8px;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.relation-header {
		margin-bottom: 16px;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.title-wrapper {
			display: flex;
			align-items: center;
			gap: 8px;
			h3 {
				margin: 0;
				font-size: 15px;
			}
		}

		.header-buttons {
			display: flex;
			gap: 8px;
		}
	}

	.no-primary-notice {
		display: flex;
		align-items: center;
		gap: 8px;
		padding: 16px;
		background: #fff7ed;
		border-radius: 4px;
		color: #666;
		margin-bottom: 20px;

		.el-icon {
			color: #e6a23c;
			font-size: 18px;
		}
	}

	.footer {
		display: flex;
		justify-content: flex-end;
		gap: 8px;
		margin-top: 16px;
		padding-top: 16px;
		border-top: 1px solid #e4e7ed;
	}
}

.table-option {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;

	.el-tag {
		margin-left: 8px;
	}
}

.relation-btn-group {
	.relation-btn {
		padding: 6px 12px;
		display: inline-flex;
		align-items: center;
		gap: 4px;

		.btn-icon {
			font-size: 14px;
		}

		.btn-text {
			margin-left: 2px;
		}

		&:hover {
			opacity: 0.9;
		}

		&:disabled {
			opacity: 0.6;
		}
	}
}
.primary-key-notice {
	display: flex;
	align-items: center;
	padding: 15px;
	background-color: #f4f4f5;
	border-radius: 4px;
	color: #909399;
	font-size: 14px;
	margin-bottom: 20px;
	.primary-key-notice-text {
		color: #666;
		font-weight: bold;
	}
	.el-icon-info {
		margin-right: 10px;
		font-size: 18px;
		color: #909399;
	}

	.el-button--text {
		padding: 0 5px;
		font-weight: bold;
	}
}

.mapping-row {
	display: flex;
	align-items: center;
	gap: 8px;
	margin-bottom: 8px;

	&:last-child {
		margin-bottom: 0;
	}
}

.add-column-btn {
	width: 100%;
	margin-top: 8px;
}

.columns-wrapper {
	display: flex;
	flex-direction: column;
	gap: 8px;
}
</style>
