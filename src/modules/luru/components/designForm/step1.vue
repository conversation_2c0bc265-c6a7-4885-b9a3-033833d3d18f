<template>
	<div class="table-config-container">
		<el-form :model="safeFormData" label-width="120px">
			<el-form-item label="表名">
				<el-input
					v-model="safeFormData.tableName"
					:disabled="tableTitle === '编辑'"
					class="input-width"
				/>
			</el-form-item>

			<el-form-item label="表配置">
				<el-checkbox
					v-model="safeFormData.tableInfo.allowEdit"
					:disabled="tableType == TABLE_TYPE.ONLY_VIEW"
				>
					允许审核归档后再驳回
				</el-checkbox>
				<el-checkbox v-model="safeFormData.tableInfo.autoincrement"> 自动增量 </el-checkbox>
			</el-form-item>

			<el-form-item label="列">
				<draggable
					v-model="safeFormData.tableColumn"
					item-key="index"
					handle=".drag-handle"
					:animation="150"
				>
					<template #item="{ element: item, index }">
						<div class="column-item">
							<el-button class="drag-handle" size="small">
								<el-icon><more /></el-icon>
							</el-button>

							<div class="col-value">
								<el-input
									v-model="item.showName"
									class="input-width right10"
									placeholder="请输入列名"
									@change="changeColName(item.showName, index)"
								/>

								<el-select
									v-model="item.type"
									class="input-width-short"
									:clearable="true"
									filterable
									:disabled="item.disabled || item.typeDisabled"
									placeholder="请选择列类型"
									@change="changeType(item, index)"
								>
									<el-option
										v-for="option in options"
										:key="option.value"
										:label="option.label"
										:value="option.value"
									/>
								</el-select>

								<!-- 动态字段 -->
								<template v-if="item.type === 'dict'">
									<el-tree-select
										v-model="item.dictName"
										:data="dictList"
										:disabled="item.disabled"
										check-strictly
										filterable
										class="input-width-long right10"
										placeholder="请选择字典表"
										@change="changeDictName(item, index)"
										@node-click="getNode"
									/>

									<el-tree-select
										v-show="item.isLeaf"
										:disabled="item.disabled ? true : false"
										v-model="item.parentName"
										:data="dictList"
										class="input-width-long right10"
										check-strictly
										tyle="width: 49%"
										filterable
										placeholder="请选择父级字典表"
										@change="getParent(item, index)"
										@node-click="getNode"
									/>
									<el-checkbox
										v-model="item.isLeaf"
										label="是末级"
										:disabled="item.disabled || item.typeDisabled"
									/>
									<el-checkbox
										v-model="item.isPrimaryKey"
										:disabled="tableType == TABLE_TYPE.ONLY_VIEW"
										label="是条件列"
										@change="changePrimaryKey(item, index)"
									/>
									<el-checkbox
										v-model="item.isPrimaryCol"
										label="是主键列"
										:disabled="item.disabled ? true : false"
										@change="changePrimaryKey(item, index)"
									/>

									<el-checkbox
										v-show="!item.isProject"
										v-model="item.isNull"
										:disabled="tableType != 'user'"
										label="可为空"
										@change="changeIsNull(item, index)"
									/>
									<el-checkbox v-model="item.isNotShow" label="不显示" />
									<el-checkbox v-model="item.isNotShowCode" label="不显示代码" />
								</template>

								<template v-if="item.type === 'number'">
									<el-input
										type="number"
										v-model="item.decimals"
										class="input-width-long right10"
										placeholder="小数位数"
										:disabled="item.disabled || tableType != 'user'"
										:min="0"
									/>
									<el-checkbox
										v-model="item.isPercent"
										:disabled="tableType == TABLE_TYPE.ONLY_VIEW"
										label="百分比"
										@change="changePercent(item, index)"
									/>
									<el-checkbox
										v-model="item.isPrimaryKey"
										:disabled="tableType == TABLE_TYPE.ONLY_VIEW"
										label="是条件列"
										@change="changePrimaryKey(item, index)"
									/>
									<el-checkbox
										v-model="item.isPrimaryCol"
										label="是主键列"
										:disabled="item.disabled ? true : false"
										@change="changePrimaryKey(item, index)"
									/>
									<el-checkbox
										v-show="!item.isProject"
										v-model="item.isNull"
										:disabled="tableType != 'user'"
										label="可为空"
										@change="changeIsNull(item, index)"
									/>
									<el-checkbox v-model="item.isNotShow" label="不显示" />
								</template>

								<template v-if="item.type === 'picture'">
									<el-input
										type="number"
										v-model="item.pictureSize"
										class="input-width-long right10"
										placeholder="请输入图片大小"
										:min="1"
									>
										<template #append>MB</template>
									</el-input>
									<el-checkbox
										v-model="item.isPrimaryKey"
										:disabled="tableType == TABLE_TYPE.ONLY_VIEW"
										label="是条件列"
										@change="changePrimaryKey(item, index)"
									/>
									<el-checkbox
										v-model="item.isPrimaryCol"
										label="是主键列"
										:disabled="item.disabled ? true : false"
										@change="changePrimaryKey(item, index)"
									/>
									<el-checkbox
										v-show="!item.isProject"
										v-model="item.isNull"
										:disabled="tableType != 'user'"
										label="可为空"
										@change="changeIsNull(item, index)"
									/>
									<el-checkbox v-model="item.isNotShow" label="不显示" />
								</template>

								<template v-if="item.type === 'date'">
									<el-select
										v-model="item.dateType"
										class="input-width-long right10"
										filterable
										:disabled="item.disabled || item.typeDisabled"
										placeholder="请选择日期类型"
									>
										<el-option
											v-for="dateOption in dateList"
											:key="dateOption.label"
											:label="dateOption.label"
											:value="dateOption.value"
										/>
									</el-select>
									<el-checkbox
										v-model="item.isPrimaryKey"
										label="是条件列"
										:disabled="tableType == TABLE_TYPE.ONLY_VIEW"
										@change="changePrimaryKey(item, index)"
									/>
									<el-checkbox
										v-model="item.isPrimaryCol"
										label="是主键列"
										:disabled="item.disabled ? true : false"
										@change="changePrimaryKey(item, index)"
									/>
									<el-checkbox
										v-show="!item.isProject"
										v-model="item.isNull"
										:disabled="tableType != 'user'"
										label="可为空"
										@change="changeIsNull(item, index)"
									/>
									<el-checkbox v-model="item.isNotShow" label="不显示" />
								</template>

								<template v-if="item.type === 'string'">
									<el-select
										v-model="item.stringType"
										class="input-width-long right10"
										filterable
										placeholder="请选择文本长度"
										:disabled="item.disabled || item.typeDisabled"
										@change="changeStringType(item, index)"
									>
										<el-option
											v-for="stringOption in stringList"
											:key="stringOption.label"
											:label="stringOption.label"
											:value="stringOption.value"
										/>
									</el-select>

									<template v-if="item.stringType === 'regularStr'">
										<el-input
											v-model="item.regularStr"
											class="input-width right10"
											placeholder="输入正则表达式"
										/>
										<el-button
											class="right10"
											@click="testRegularStr(item, index)"
										>
											测试
										</el-button>
									</template>

									<template v-if="item.stringType === 'valueLength'">
										<el-input-number
											v-model="item.stringLength"
											class="input-width-long right10"
											placeholder="请输入文本长度"
											:disabled="item.disabled || item.typeDisabled"
										/>
									</template>

									<template v-if="item.stringType === 'rangeLength'">
										<input-number-range
											v-model="item.stringLength"
											class="input-width-long right10"
											:precision="1"
											:disabled="item.disabled || item.typeDisabled"
										/>
									</template>
									<el-checkbox
										v-model="item.isPrimaryKey"
										:disabled="tableType == TABLE_TYPE.ONLY_VIEW"
										label="是条件列"
										@change="changePrimaryKey(item, index)"
									/>
									<el-checkbox
										v-model="item.isPrimaryCol"
										label="是主键列"
										:disabled="item.disabled ? true : false"
										@change="changePrimaryKey(item, index)"
									/>
									<el-checkbox
										v-show="!item.isProject"
										v-model="item.isNull"
										:disabled="tableType != 'user'"
										label="可为空"
										@change="changeIsNull(item, index)"
									/>
									<el-checkbox v-model="item.isNotShow" label="不显示" />
								</template>
								<template v-if="item.type === 'relatedCol'">
									<el-checkbox v-model="item.isNotShow" label="不显示" />
								</template>
								<template v-if="item.type === 'calculatedCol'">
									<el-checkbox
										v-show="!item.isProject"
										v-model="item.isNull"
										label="可为空"
										:disabled="tableType != 'user'"
										@change="changeIsNull(item, index)"
									/>
									<el-checkbox v-model="item.isNotShow" label="不显示" />
								</template>
							</div>

							<div class="col-actions">
								<el-button
									v-if="index === 0"
									class="square-button"
									@click="increment"
									:disabled="tableType != 'user'"
								>
									+
								</el-button>
								<el-button
									v-if="index !== 0"
									class="square-button"
									@click="decrement(item, index)"
									:disabled="tableType != 'user'"
								>
									-
								</el-button>
							</div>
						</div>
					</template>
				</draggable>
			</el-form-item>

			<el-form-item label="备注">
				<el-input
					v-model="safeFormData.remark"
					class="input-width"
					autosize
					type="textarea"
				/>
			</el-form-item>
		</el-form>
		<regularStrTest
			v-model:regularStrTestVisible="regularStrTestVisible"
			v-model:regularStr="regularStr"
		/>
		<div class="footer">
			<el-button @click="closeDialog" class="btn-cancel">取消</el-button>
			<el-button type="success" @click="nextStep">下一步</el-button>
		</div>
	</div>
</template>

<script setup lang="ts">
import type { FormInstance } from 'element-plus';
import { PropType, toRefs, ref, watch, computed } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { DesignTable, SelectDict, TableList } from '../../types';
import { useCool } from '/@/cool';
import regularStrTest from './regularStrTest.vue';
import draggable from 'vuedraggable';
import { More, Plus, Delete } from '@element-plus/icons-vue';
import { TABLE_TYPE } from '../../utils/constants';

const { service, router } = useCool();
const props = defineProps({
	formData: {
		type: Object as PropType<DesignTable>,
		required: true
	},
	dictList: {
		type: Array as PropType<SelectDict[]>
	},
	tableTitle: {
		type: String as PropType<string>,
		required: true
	},
	tableType: {
		type: String as PropType<string>,
		required: true
	}
});

const { formData, dictList, tableTitle, tableType } = toRefs(props);
const safeFormData = computed(
	() =>
		formData.value || {
			name: '',
			tableInfo: { allowEdit: false, autoincrement: false },
			tableColumn: [{ name: '', showName: '' }],
			remark: ''
		}
);
const regularStrTestVisible = ref<boolean>(false);
const styleConfig = {
	formWidth: '800px',
	inputWidth: '200px'
};
const relatedTable = ref();
const regularStr = ref('');
const regularStrList = ref([]);

const dateList = ref([
	{ label: '年-月-日', value: 'YYYY-MM-DD' },
	{ label: '年-月', value: 'YYYY-MM' },
	{ label: '年', value: 'YYYY' },
	{ label: '年-月-日 时:分:秒', value: 'YYYY-MM-DD HH:mm:ss' }
]);

const stringList = ref([
	{ label: '不做限制', value: 'anyLength' },
	{ label: '范围限制', value: 'rangeLength' },
	{ label: '精度限制', value: 'valueLength' },
	{ label: '正则匹配', value: 'regularStr' }
]);

const options = ref([
	{ label: '关联字典（枚举型）', value: 'dict' },
	{ label: '文本型（主要用于表单中的备注说明类字段）', value: 'string' },
	{ label: '数值型', value: 'number' },
	{ label: '日期型', value: 'date' },
	{ label: '图片型', value: 'picture' },
	{ label: '自动带出列(结果列)', value: 'relatedCol' },
	{ label: '计算列', value: 'calculatedCol' }
]);

const decrement = (row, index) => {
	console.log('---', index);
	if (row.dictName == 'project') {
		ElMessageBox.confirm('项目字段为必选项，无法删除', '提示', {
			confirmButtonText: '确认',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			return;
		});
	} else {
		if (tableTitle.value == '编辑') {
			ElMessageBox.confirm('删除字段名后数据将被清空，确认删除吗', '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(() => {
					safeFormData.value.tableColumn.splice(index, 1);
					return;
				})
				.catch(() => {});
		} else {
			safeFormData.value.tableColumn.splice(index, 1);
		}
	}
};

function changeType(item, index) {
	console.log('changeType', item, index);
	if (item.type == 'string') {
		safeFormData.value.tableColumn[index].stringType = 'rangeLength';
		safeFormData.value.tableColumn[index].stringLength = [0, 150];
		if (item.pictureSize) {
			delete safeFormData.value.tableColumn[index].pictureSize;
		}
	} else if (item.type == 'picture') {
		safeFormData.value.tableColumn[index].pictureSize = 1;
		if (item.stringType) {
			delete safeFormData.value.tableColumn[index].stringType;
		}
		if (item.stringLength) {
			delete safeFormData.value.tableColumn[index].stringLength;
		}
		if (item.relatedTableCol) {
			delete safeFormData.value.tableColumn[index].relatedTableCol;
			delete safeFormData.value.tableColumn[index].relatedTable;
			//	delete safeFormData.value.tableColumn[index].isRelatedCol;
		}
	} else {
		if (item.pictureSize) {
			delete safeFormData.value.tableColumn[index].pictureSize;
		}

		if (item.stringType) {
			delete safeFormData.value.tableColumn[index].stringType;
		}
		if (item.stringLength) {
			delete safeFormData.value.tableColumn[index].stringLength;
		}
		if (item.relatedTableCol) {
			delete safeFormData.value.tableColumn[index].relatedTableCol;
			delete safeFormData.value.tableColumn[index].relatedTable;
			//	delete safeFormData.value.tableColumn[index].isRelatedCol;
		}
		// if (item.isRelatedCol) {
		// 	delete safeFormData.value.tableColumn[index].isRelatedCol;
		// }
	}
}
function testRegularStr(item, index) {
	console.log('testRegularStr', item, index);
	regularStr.value = item.regularStr;
	regularStrTestVisible.value = true;
	console.log('regularStrTestVisible', regularStrTestVisible.value);
}
function changeStringType(item, index) {
	console.log('changeStringType', item, index);
	if (item.stringType == 'rangeLength') {
		safeFormData.value.tableColumn[index].stringLength = [0, 150];
	} else if (item.stringType == 'regularStr') {
		safeFormData.value.tableColumn[index].stringLength = 200;
	} else if (item.stringType == 'valueLength') {
		safeFormData.value.tableColumn[index].stringLength = 200;
	} else {
		if (safeFormData.value.tableColumn[index].stringLength)
			delete safeFormData.value.tableColumn[index].stringLength;
	}
}
const changeProjectPrimaryKey = ref(0);
function changePrimaryKey(item, index) {
	if (changeProjectPrimaryKey.value == 0) {
		ElMessage({
			message:
				'注意:匹配列(条件列)列/主键列长度只能为150个字符，中文占3个字符，英文数字为一个字符',
			type: 'warning',
			plain: true
		});
	}
	safeFormData.value.tableColumn[index].isNull = false;
	changeProjectPrimaryKey.value++;
	console.log('changePrimaryKey', item, index);
}
function changeIsNull(item, index) {
	if (item.isPrimaryKey && item.isNull) {
		ElMessageBox.confirm('条件列不能为空', '提示', {
			confirmButtonText: '确认',
			type: 'warning'
		});
		safeFormData.value.tableColumn[index].isNull = false;
		return;
	}
	console.log('changeIsNull', item, index);
}

const increment = () => {
	console.log('新增tableVisible', safeFormData.value.tableColumn);
	safeFormData.value.tableColumn.push({
		name: '',
		showName: '',
		type: 'string',
		stringType: 'rangeLength',
		stringLength: [0, 150],
		hasDict: false,
		isNull: true
	});
};

const changePercent = (item, index) => {
	if (item.isPercent) {
		if (!('decimals' in item)) safeFormData.value.tableColumn[index].decimals = 2;
	}
};

const changeColName = (data, i) => {
	const trimmedData = data.trim();
	safeFormData.value.tableColumn[i].showName = trimmedData;
	const result = containsSpecialCharacters(trimmedData);
	console.log('修改列名', trimmedData);
	if (result) {
		ElMessageBox.confirm('列名不能包含特殊字符,只能为中文,字母,数字,下划线_', '提示', {
			confirmButtonText: '确认',
			type: 'warning'
		})
			.then(() => {
				safeFormData.value.tableColumn[i].showName = '';
			})
			.catch(() => {
				safeFormData.value.tableColumn[i].showName = '';
			});
	}
	// console.log("修改表名",safeFormData.value.tableName,rowData.value)
	// if(tableTitle.value=="编辑"){
	//     ElMessageBox.confirm('修改列名后数据将被清空，确认修改吗', '提示', {
	//         confirmButtonText: '确认',cancelButtonText: '取消',type: 'warning'
	//      }).then(() => {
	//      }).catch(()=>{
	//         safeFormData.value.tableColumn[i].name=rowData.value.tableColumn[i].name
	//      })
	// }
};
function containsSpecialCharacters(str) {
	// 正则表达式匹配非文字、非数字、非字母、非括号的字符
	const regex = /[^\u4e00-\u9fa5a-zA-Z0-9_\s()]/;
	return regex.test(str);
}
const clickNode: any = ref();
function getNode(e) {
	clickNode.value = e;
	console.log('clickNode', e);
}
//选取字典表后，获取所有选择关联字典的字段
async function changeDictName(dict, index) {
	safeFormData.value.tableColumn[index].dictField = clickNode.value.idPath;
	safeFormData.value.tableColumn[index].dictId = clickNode.value.id;
	const res = await service.dict.info.list({ typeId: clickNode.value.id });
	if (res.length == 0) {
		ElMessageBox.confirm('该字典没有子项,无法关联', '提示', {
			confirmButtonText: '确认',
			type: 'warning'
		})
			.then(() => {
				safeFormData.value.tableColumn[index] = {
					name: safeFormData.value.tableColumn[index].name,
					showName: safeFormData.value.tableColumn[index].showName,
					type: 'dict',
					hasDict: true,
					isLeaf: false,
					isNull: true
				};
			})
			.catch(() => {
				safeFormData.value.tableColumn[index] = {
					name: safeFormData.value.tableColumn[index].name,
					showName: safeFormData.value.tableColumn[index].showName,
					type: 'dict',
					hasDict: true,
					isLeaf: false,
					isNull: true
				};
			});
	}
	console.log('changeDictName', clickNode, res, dict, index, safeFormData.value);
}

//叶子节点
async function getParent(dict, index) {
	safeFormData.value.tableColumn[index].parentId = clickNode.value.id;
	const res = await service.dict.info.list({ typeId: clickNode.value.id });
	if (res.length == 0) {
		ElMessageBox.confirm('该字典没有子项,无法关联', '提示', {
			confirmButtonText: '确认',
			type: 'warning'
		})
			.then(() => {
				safeFormData.value.tableColumn[index] = {
					name: safeFormData.value.tableColumn[index].name,
					showName: safeFormData.value.tableColumn[index].showName,
					type: 'dict',
					hasDict: true
				};
			})
			.catch(() => {
				safeFormData.value.tableColumn[index] = {
					name: safeFormData.value.tableColumn[index].name,
					showName: safeFormData.value.tableColumn[index].showName,
					type: 'dict',
					hasDict: true
				};
			});
	}
}

const emit = defineEmits(['closeDialog', 'nextStep']);

const closeDialog = () => {
	emit('closeDialog');
	console.log('closeDialog');
};
const nextStep = () => {
	if (
		formData.value.tableName == '' ||
		formData.value.tableColumn[0].showName == '' ||
		formData.value.tableColumn[0].type == ''
	) {
		ElMessageBox.confirm('表名、列名、列类型不能为空', '提示', {
			confirmButtonText: '确认',
			type: 'warning'
		});
		return;
	}
	for (let i = 0; i < formData.value.tableColumn.length; i++) {
		if (
			formData.value.tableColumn[i].showName == '' ||
			formData.value.tableColumn[i].type == ''
		) {
			ElMessageBox.confirm('列名、列类型不能为空', '提示', {
				confirmButtonText: '确认',
				type: 'warning'
			});
			return;
		} else {
			if (
				formData.value.tableColumn[i].hasDict &&
				formData.value.tableColumn[i].dictName == ''
			) {
				ElMessageBox.confirm('选择关联字典后，字典名不能为空', '提示', {
					confirmButtonText: '确认',
					type: 'warning'
				});
				return;
			}
		}
		if (formData.value.tableColumn[i].type == 'dict') {
			formData.value.tableColumn[i].hasDict = true;
			formData.value.tableColumn[i].disabled = true;
		}
		if (formData.value.tableColumn[i].type == 'date') {
			if (!('dateType' in formData.value.tableColumn[i]))
				formData.value.tableColumn[i].dateType = 'YYYY-MM-DD';
		}
		if (tableTitle.value == '新增') {
			formData.value.tableColumn[i].name = formData.value.tableColumn[i].showName;
		} else {
			if (!formData.value.tableColumn[i].name) {
				formData.value.tableColumn[i].name = formData.value.tableColumn[i].showName;
			}
		}
	}
	emit('nextStep', safeFormData.value);
	console.log('nextStep');
};
</script>
<style scoped lang="scss">
.table-config-container {
	max-width: 100%;
	margin: 0 auto;
	padding: 20px;
	background-color: #f9fafb;
	border-radius: 8px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.input-width {
	width: 200px;
}
.input-width-long {
	width: 150px;
}
.input-width-short {
	width: 120px;
}

.column-item {
	display: flex;
	align-items: center;
	gap: 10px;
	padding: 10px;
	background-color: white;
	border-radius: 8px;
	margin-bottom: 10px;
	transition: all 0.3s ease;

	&:hover {
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}
}

.col-value {
	flex: 1;
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
}

.col-actions {
	display: flex;
	gap: 10px;
}

.footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
	margin-top: 20px;
}

.btn-cancel {
	background-color: #f56c6c;
	color: white;
}

.right10 {
	margin-right: 10px;
}

.left10 {
	margin-left: 10px;
}
.left5 {
	margin-left: 5px;
}
.square-button {
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
