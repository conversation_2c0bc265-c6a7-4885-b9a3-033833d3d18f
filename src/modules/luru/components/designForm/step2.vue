<template>
	<el-form
		ref="ruleFormRef"
		:model="formData"
		:rules="rules"
		label-width="120px"
		class="flow-design-form"
	>
		<!-- 基本信息部分 -->
		<div class="section-card">
			<div class="section-header">
				<el-icon><document /></el-icon>
				<span class="section-title">基本信息</span>
			</div>
			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="流程名称" prop="flow_name">
						<el-input v-model="formData.flow_name" placeholder="请输入流程名称">
							<template #prefix>
								<el-icon><edit /></el-icon>
							</template>
						</el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="流程版本" prop="flow_version">
						<el-input
							v-model="formData.flow_version"
							disabled
							placeholder="请输入流程版本"
						>
							<template #prefix>
								<el-icon><info-filled /></el-icon>
							</template>
						</el-input>
					</el-form-item>
				</el-col>
			</el-row>
		</div>

		<!-- 负责人配置部分 -->
		<div class="section-card">
			<div class="section-header">
				<el-icon><user /></el-icon>
				<span class="section-title">负责人配置</span>
			</div>
			<el-form-item label="流程负责人" prop="flow_managers">
				<div class="manager-container">
					<el-button type="primary" @click="addManager" size="small" class="add-button">
						<el-icon><plus /></el-icon>添加负责人
					</el-button>
					<div class="tags-wrapper">
						<el-tag
							v-for="item in formData.flow_managers"
							:key="item.id"
							closable
							:effect="'light'"
							class="manager-tag"
							@close="selectClose(item.id)"
						>
							<el-icon><user /></el-icon>
							{{ item.name }}
						</el-tag>
					</div>
				</div>
			</el-form-item>
		</div>

		<div class="section-card">
			<div class="section-header">
				<el-icon><bell /></el-icon>
				<span class="section-title">流程久悬通知</span>
				<el-tooltip content="流程卡住时，多久通知流程发起人" placement="top">
					<el-icon><info-filled /></el-icon>
				</el-tooltip>
			</div>
			<el-form-item label="通知时间配置" prop="flowUnDoneNoticeDay">
				<el-input-number
					v-model="formData.flowUnDoneNoticeDay"
					:min="1"
					:max="999"
					style="width: 200px; margin-bottom: 10px"
				/>
				<span class="unit-text" style="margin-bottom: 4px">天</span>
			</el-form-item>
		</div>

		<!-- 流程描述部分 -->
		<div class="section-card">
			<div class="section-header">
				<el-icon><document /></el-icon>
				<span class="section-title">流程描述</span>
			</div>
			<el-form-item label="流程描述" prop="flow_description">
				<el-input
					v-model="formData.flow_description"
					type="textarea"
					:rows="3"
					placeholder="请输入流程描述"
					show-word-limit
					maxlength="200"
				/>
			</el-form-item>
		</div>
	</el-form>

	<div class="dialog-footer">
		<el-button @click="closeDialog">取消</el-button>
		<el-tooltip content="不进行流程设计" placement="top">
			<el-button
				type="primary"
				v-if="formData.flow_id"
				@click="submitForm(ruleFormRef, '保存')"
			>
				仅保存
			</el-button>
		</el-tooltip>
		<el-tooltip content="保存并设计流程" placement="top">
			<el-button type="primary" @click="submitForm(ruleFormRef, '保存并设计流程')">
				保存并设计流程
			</el-button>
		</el-tooltip>
	</div>
	<el-dialog v-model="sqlViewVisible" title="sql预览" width="600px">
		<div class="preview-header">
			<div class="preview-title">
				<el-icon><data-analysis /></el-icon>
				<span>预览结果</span>
			</div>
			<span class="preview-count">共 {{ previewData.length }} 条数据</span>
		</div>
		<div class="preview-container">
			<el-table :data="previewData" stripe border class="custom-table">
				<el-table-column
					v-for="(col, index) in Object.keys(previewData[0])"
					:key="index"
					:prop="col"
					:label="col"
					:min-width="100"
				/>
			</el-table>
		</div>
	</el-dialog>
	<!-- 添加SQL帮助对话框 -->
	<el-dialog v-model="sqlHelpVisible" title="SQL帮助" width="600px">
		<div class="sql-help-content">
			<h4>常用SQL示例：</h4>
			<div class="help-item">
				<div
					style="font-size: 14px; color: #ce4300; font-style: italic; margin-bottom: 8px"
				>
					注意：只能使用 SELECT id FROM '表名',不能获取除 id 以外字段
				</div>
				<pre class="help-code"><span>
SELECT id FROM func_{{DBtableName}}
WHERE projectCode='T0001'
AND 日期 LIKE '2025-06%'
AND 印章代码 IN ('001','002','003')
					</span>
				</pre>
			</div>
			<!-- 可以添加更多帮助项 -->
		</div>
	</el-dialog>
	<user-select ref="userSelectRef" @userSelectData="userSelectData"></user-select>
	<role-select ref="roleSelectRef" @roleSelectData="roleSelectData"></role-select>
	<form-select ref="formSelectRef" @formSelectData="formSelectData"></form-select>
</template>

<script setup lang="ts">
import { ref, toRefs, reactive, defineEmits, toRaw, onMounted, onBeforeUnmount, watch } from 'vue';
import iconSelect from '../../components/icon/index.vue';
import UserSelect from '/@/modules/flow/components/design/config/userConfig/userSelect.vue';
import RoleSelect from '/@/modules/flow/components/design/config/userConfig/roleSelect.vue';
import FormSelect from '/@/modules/flow/components/design/config/formConfig/formSelect.vue';
import { useCool } from '/@/cool';
import { ElAnchor, ElMessage, ElMessageBox } from 'element-plus';
import { useStore as useFlowStore } from '/@/modules/flow/store';
import { useStore as useLocalStore } from '../../store';
import { changeRules } from '/@/modules/luru/utils/luruTable';
import { MagicStick, VideoPlay, QuestionFilled } from '@element-plus/icons-vue';
import {
	Document,
	Edit,
	InfoFilled,
	User,
	Setting,
	Plus,
	UserFilled,
	Avatar,
	List,
	Bell,
	Timer,
	Calendar,
	AlarmClock
} from '@element-plus/icons-vue';
import { cloneDeep } from 'lodash-es';
import * as monaco from 'monaco-editor';
import 'monaco-editor/min/vs/editor/editor.main.css';

const editorContainer = ref<HTMLElement | null>(null);

const loading = ref(false);
const { service, router } = useCool();
const { formFlowStore } = useFlowStore();
const { luruTableStore } = useLocalStore();
const iconSelectRef = ref();
const userSelectRef = ref();
const roleSelectRef = ref();
const formSelectRef = ref();
const sqlViewVisible = ref(false);
const sqlHelpVisible = ref(false);

const dialogVisible = ref(false);

const previewData = ref([]);

const title = ref('新增');
const rowData = ref({});
const fieldList = ref([]);
const userType = ref('流程管理员');
const editorView = ref();
const DBtableName = ref();
const props = defineProps({
	formInfo: {
		type: Object,
		required: true
	},

	tableTitle: {
		type: String
	}
});
const { formInfo, tableTitle } = toRefs(props);
const formData = ref({
	form_id: '',
	flow_managers: [],
	form_name: '',
	flow_id: '',
	flow_name: '',
	flow_version: 1,
	flow_description: '',
	flow_active: true,
	flowJson: null,
	flowUnDoneNoticeDay: 1
});

// 使用watch监听formInfo的变化
watch(
	() => formInfo,
	async newVal => {
		if (newVal.value.form_id) {
			DBtableName.value = '';
			console.log('newVal', newVal.value);
			if (newVal.value.flow_id) {
				formData.value = {
					form_id: newVal.value.form_id,
					flow_managers: newVal.value.flow_managers,
					form_name: newVal.value.form_name,
					flow_id: newVal.value.flow_id,
					flow_name: newVal.value.flow_name,
					flow_version: newVal.value.flow_version,
					flow_description: newVal.value.flow_description,
					flow_active: newVal.value.flow_active,
					flowJson: newVal.value.flowJson,
					flowUnDoneNoticeDay: newVal.value.flowUnDoneNoticeDay
				};
				let colInfo = [];
				if (formInfo.value.colInfo && formInfo.value.colInfo.length > 0) {
					colInfo = formInfo.value.colInfo;
				} else {
					const res = await service.cloud.db.page({
						page: 1,
						id: formData.value.form_id
					});
					colInfo = res.list[0].colInfo;
				}
				//console.log('colInfo-watch', colInfo);
				if (!colInfo[0].showName) {
					const colList = changeRules(colInfo);
					console.log('colList-watch', colList);
					fieldList.value = colList.map(item => {
						return {
							value: item.name,
							label: item.showName
						};
					});
				} else {
					console.log('xx');
					fieldList.value = colInfo.map(item => {
						return {
							value: item.name,
							label: item.showName
						};
					});
				}
			} else {
				console.log('没有flowId');
				const res = await service.flow.process.unbindFlow({ formId: newVal.value.form_id });
				resetFormData();
				toRaw(editorView.value).setValue('');
				if (res.length > 0) {
					formData.value.flow_version = res[0].version + 1;
				}
				formData.value.form_id = newVal.value.form_id;
				formData.value.form_name = newVal.value.form_name;
				//formData.value.flow_version = newVal.value.flow_version;
				let colInfo = [];
				if (formInfo.value.colInfo && formInfo.value.colInfo.length > 0) {
					colInfo = formInfo.value.colInfo;
				} else {
					const res = await service.cloud.db.page({
						page: 1,
						id: formData.value.form_id
					});
					colInfo = res.list[0].colInfo;
				}
				//console.log('colInfo-watch', colInfo);
				if (!colInfo[0].showName) {
					const colList = changeRules(colInfo);
					console.log('colList-watch', colList);
					fieldList.value = colList.map(item => {
						return {
							value: item.name,
							label: item.showName
						};
					});
				} else {
					console.log('xx');
					fieldList.value = colInfo.map(item => {
						return {
							value: item.name,
							label: item.showName
						};
					});
				}
			}
			DBtableName.value = await luruTableStore.getDBtableName(newVal.value.form_id);
			console.log('!!!fieldList-watch', fieldList.value.values);
			console.log('formData.value-watch', formData.value);
		}
	},
	{ immediate: true, deep: true } // 立即执行一次
);

// 可以保留原来的onMounted，以防万一
onMounted(async () => {
	console.log('注册编辑器');

	initEditor();
});

const ruleFormRef = ref();

// 处理 自定义表单的校验 blur 无效问题
const tips = ref('');

const rules = reactive({
	flow_name: [{ required: true, message: '必填', trigger: 'blur' }],
	flow_managers: [{ required: true, message: '必填', trigger: 'blur' }],
	flowUnDoneNoticeDay: [{ required: true, message: '必填', trigger: 'blur' }]
});

const previewSql = async () => {
	console.log('预览sql');
	if (!toRaw(editorView.value).getValue()) {
		ElMessage.warning('请输入校验SQL');
		return;
	}
	try {
		const res = await service.quality.check.doQuerySql({
			sql: toRaw(editorView.value).getValue()
		});
		console.log('res', res);
		if (res.length > 0) {
			sqlViewVisible.value = true;
			previewData.value = res;
		} else {
			ElMessageBox.alert('执行sql未获取数据', '提示');
		}
	} catch (error) {
		ElMessageBox.alert(`执行sql错误，${error.message}`);
	}
	// try {
	// 	const rule = {
	// 		sql: toRaw(editorView.value).getValue()
	// 		//formId: form.value.formId
	// 	};
	// 	const res = await service.quality.check.doQuerySql(rule);
	// 	console.log('预览', res);
	// 	if (res.length > 0) {
	// 		previewColumns.value = Object.keys(res[0]);

	// 		console.log('res[0]', res[0], 'values', Object.values(res[0]));
	// 		if (res.every(obj => Object.values(obj).every(val => val === null))) {
	// 			ElMessage.warning('sql验证成功，数据结果为空');
	// 			//previewData.value = [];
	// 			previewData.value = res;
	// 		} else {
	// 			ElMessage.success('sql验证成功，已显示结果(' + res.length + '条)');
	// 			previewData.value = res;
	// 		}
	// 	} else {
	// 		ElMessage.warning('sql验证成功，数据结果为空');
	// 		previewData.value = [];
	// 		//	previewColumns.value = [];
	// 	}
	// } catch (err) {
	// 	ElMessage.error('预览失败：' + err.message);
	// 	previewData.value = [];
	// 	previewColumns.value = [];
	// }
};

function resetFormData() {
	formData.value = {
		form_id: '',
		flow_managers: [],
		form_name: '',
		flow_id: '',
		flow_name: '',
		flow_version: 1,
		flow_description: '',
		flow_active: true,
		flowJson: null,
		flowUnDoneNoticeDay: 1
	};
}

// 选择角色
const selectRole = () => {
	roleSelectRef.value.open();
};

// 选择用户
const selectUser = () => {
	userType.value = '处理人';
	userSelectRef.value.open();
};

// 选择表单
const selectForm = () => {
	formSelectRef.value.open({ formId: formData.value.form_id });
};

onBeforeUnmount(() => {
	console.log('卸载组件');
	const editor = toRaw(editorView.value);
	if (editor && typeof editor.dispose === 'function') {
		editor.dispose();
		console.log('编辑器销毁完成');
	}
});

// 格式化SQL
const formatSql = () => {
	if (!editorView.value) return;

	const editor = toRaw(editorView.value);
	// 获取当前编辑器中的所有内容范围
	const model = editor.getModel();
	if (!model) return;

	const range = model.getFullModelRange();

	// 触发格式化
	editor.trigger('formatter', 'editor.action.formatDocument', {});
};

const selectClose = id => {
	formData.value.flow_managers = formData.value.flow_managers.filter(item => item.id !== id);
};

const addManager = () => {
	userType.value = '流程管理员';
	userSelectRef.value.open();
};

const emits = defineEmits(['tableReload', 'nextStep', 'closeDialog']);
const closeDialog = () => {
	emits('closeDialog');
};

const submitForm = async (formEl, type) => {
	console.log('submitForm-type', formData.value);
	if (!formEl) return;

	await formEl.validate((valid, fields) => {
		if (valid) {
			if (!formData.value.flow_version) {
				formData.value.flow_version = 1;
			}
			formFlowStore.addFormFlowData(formData.value);
			dialogVisible.value = false;
			console.log('submit!', formData.value);
			if (type === '保存') {
				console.log('submit!编辑', formData.value);
				service.flow.flow
					.update({
						id: Number(formData.value.flow_id),
						name: formData.value.flow_name,
						managers: formData.value.flow_managers,
						formId: formData.value.form_id,
						version: formData.value.flow_version,
						description: formData.value.flow_description,
						active: true,
						flowUnDoneNoticeDay: formData.value.flowUnDoneNoticeDay
					})
					.then(res => {
						ElMessage.success('保存成功', res);
						emits('closeDialog');
						console.log(res);
					})
					.catch(e => {
						console.log(e);

						ElMessage.error(e.message);
					});
			} else {
				console.log('保存设计formData.value', formData.value);
				if (formData.value.flow_id) {
					// ElMessageBox.confirm(
					// 	'重新设计流程，会导致当前正在审批的流程终止，确定要重新设计流程吗？',
					// 	'提示',
					// 	{
					// 		confirmButtonText: '确定',
					// 		cancelButtonText: '取消',
					// 		type: 'warning'
					// 	}
					// ).then(() => {
					//console.lo;
					dialogVisible.value = false;
					setTimeout(() => {
						emits('nextStep', {
							flow_id: formData.value.flow_id,
							flow_managers: JSON.stringify(formData.value.flow_managers),
							flow_name: formData.value.flow_name,
							flow_version: formData.value.flow_version,
							flow_description: formData.value.flow_description,
							flow_active: true,
							form_id: formData.value.form_id,
							flowJson: formData.value.flowJson,
							flowUnDoneNoticeDay: formData.value.flowUnDoneNoticeDay
						});
					}, 200);
					//});
				} else {
					dialogVisible.value = false;
					console.log('保存设计并关闭', dialogVisible.value, formData.value);

					setTimeout(() => {
						emits('nextStep', {
							flow_id: formData.value.flow_id,
							flow_managers: JSON.stringify(formData.value.flow_managers),
							flow_name: formData.value.flow_name,
							flow_version: formData.value.flow_version,
							flow_description: formData.value.flow_description,
							flow_active: true,
							form_id: formData.value.form_id,
							flowUnDoneNoticeDay: formData.value.flowUnDoneNoticeDay
							//	flowJson: formData.value.flowJson
						});
					}, 200);
				}
			}
		} else {
			console.log('error submit!', fields);
		}
	});
};
</script>

<style scoped lang="scss">
.flow-design-form {
	padding: 4px;
}

.section-card {
	background-color: var(--el-bg-color);
	border-radius: 8px;
	padding: 4px;
	margin-bottom: 8px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.section-header {
	display: flex;
	align-items: center;
	margin-bottom: 16px;
	padding-bottom: 8px;
	border-bottom: 1px solid var(--el-border-color-lighter);
}

.section-header .el-icon {
	margin-right: 6px;
	font-size: 16px;
	color: var(--el-color-primary);
}

.section-title {
	font-size: 15px;
	font-weight: 600;
	color: var(--el-text-color-primary);
}

.manager-container {
	background-color: var(--el-fill-color-lighter);
	border-radius: 6px;
	padding: 12px;
	display: flex;
	align-items: baseline;
}

.field-config-section {
	background-color: var(--el-fill-color-lighter);
	border-radius: 6px;
	padding: 6px;
	min-height: 180px;
	flex: 1; /* 平均分配空间 */
	min-width: 600px; /* 最小宽度，触发换行 */
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
	.editor {
		height: 100%;
		width: 100%;
		border: 1px solid #e4e7ed;
		border-radius: 8px;
		overflow: hidden;

		:deep(.monaco-editor) {
			.margin {
				background: #f8fafc !important;
			}

			.monaco-scrollable-element {
				border-radius: 0 0 8px 8px;
			}
		}
	}
}

.urge-config-section {
	background-color: var(--el-fill-color-lighter);
	border-radius: 6px;
	padding: 6px;
}

.add-button {
	margin-bottom: 12px;
}

.field-config-card {
	background-color: white;
	border-radius: 6px;
	padding: 12px;
	height: 100%;
}
.editor-actions {
	display: flex;
	flex-direction: column;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
}

.field-select {
	width: 100%;
}

.source-options {
	display: flex;
	gap: 12px;
	flex-wrap: wrap;
}

.source-item {
	flex: 1;
	min-width: 200px;
	border: 1px solid var(--el-border-color-lighter);
	border-radius: 4px;
	padding: 8px;
	background-color: var(--el-fill-color-lighter);
}

.source-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 6px;
}

.source-header .el-checkbox {
	display: flex;
	align-items: center;
}

.source-header .el-checkbox .el-icon {
	margin-right: 4px;
}

.tags-wrapper {
	display: flex;
	flex-wrap: wrap;
	gap: 4px;
	margin-top: 6px;
}

.manager-tag {
	display: flex;
	align-items: center;
	gap: 4px;
	margin: 2px;
}
.preview-header {
	display: flex;
	height: 40px;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;

	.preview-title {
		display: flex;
		align-items: center;
		gap: 8px;
		font-size: 16px;
		font-weight: 500;
		color: #1f2937;

		.el-icon {
			font-size: 18px;
			color: var(--el-color-primary);
		}
	}

	.preview-count {
		font-size: 14px;
		color: #606266;
	}
}
// .sql-help-content {
// 	padding: 8px;

// 	h4 {
// 		color: #1f2937;
// 		font-size: 16px;
// 		margin-bottom: 20px;
// 		padding-bottom: 12px;
// 		border-bottom: 1px solid #e5e7eb;
// 	}

// 	.help-item {
// 		margin-bottom: 24px;

// 		.help-title {
// 			font-size: 14px;
// 			font-weight: 500;
// 			color: #374151;
// 			margin-bottom: 8px;
// 		}

// 		.help-code {
// 			background: #f8fafc;
// 			padding: 16px;
// 			border-radius: 6px;
// 			font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
// 			font-size: 13px;
// 			line-height: 1.6;
// 			color: #374151;
// 			margin-bottom: 10px;
// 		}
// 	}
// }
.preview-container {
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
	padding: 16px;

	.custom-table {
		height: 400px;
		overflow-y: auto;
		:deep(.el-table__header) {
			th {
				background-color: #f8fafc;
				color: #1f2937;
				font-weight: 500;
				padding: 12px 8px;
			}
		}

		:deep(.el-table__body) {
			td {
				padding: 12px 8px;
			}
		}
	}
}
.urge-config-section {
	display: flex;
	align-items: center;
	gap: 12px;
	flex-wrap: wrap;
}

.urge-select {
	width: 200px;
}

.urge-details {
	display: flex;
	align-items: center;
	gap: 8px;
	flex-wrap: wrap;
}

.period-select,
.day-select {
	width: 100px;
}

.unit-text {
	margin-left: 4px;
	color: var(--el-text-color-regular);
}

.dialog-footer {
	padding-top: 16px;
	text-align: right;
	border-top: 1px solid var(--el-border-color-lighter);
}

:deep(.el-dialog__body) {
	padding: 0;
}

:deep(.el-form-item__label) {
	font-weight: 500;
}

:deep(.el-form-item) {
	margin-bottom: 12px;
}

:deep(.el-select-dropdown__item) {
	display: flex;
	align-items: center;
}

:deep(.el-checkbox__label) {
	display: flex;
	align-items: center;
}

:deep(.el-tag) {
	display: flex;
	align-items: center;
	gap: 4px;
	margin: 2px;
}

:deep(.el-input__prefix) {
	display: flex;
	align-items: center;
}

:deep(.el-form-item:last-child) {
	margin-bottom: 0;
}

:deep(.el-dialog__header) {
	padding: 16px;
	margin-right: 0;
}

:deep(.el-dialog__footer) {
	padding: 12px 16px;
}

:deep(.monaco-editor) {
	.current-line {
		border: none !important;
		background-color: #f8f9fa !important;
	}

	.line-numbers {
		color: #999 !important;
	}

	.suggest-widget {
		border-radius: 6px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
	}
}
</style>
