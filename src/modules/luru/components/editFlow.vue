<template>
	<div>
		<el-dialog
			v-model="dialogVisible"
			:title="steps[stepActive]"
			width="65%"
			draggable
			:show-close="false"
			@close="closeDialog"
			@open="openDialog"
			:fullscreen="true"
			:destroy-on-close="true"
			v-loading="loading"
		>
			<template #header>
				<div class="my-header">
					<el-steps :active="stepActive" finish-status="success" simple>
						<el-step title="表单流程"> </el-step>
						<el-step title="流程设计"> </el-step>
					</el-steps>
				</div>
			</template>
			<div>
				<step2
					:formInfo="formInfo"
					v-if="stepActive == 1"
					@nextStep="nextStep"
					@closeDialog="closeDialog"
				/>
				<step3
					:flowInfo="flowInfo"
					v-if="stepActive == 2"
					@nextStep="nextStep"
					@prevStep="prevStep"
					@closeDialog="closeDialog"
				/>
			</div>
		</el-dialog>
	</div>
</template>
<script setup lang="ts">
import { ref, toRefs } from 'vue';
import step2 from './designForm/step2.vue';
import step3 from './designForm/step3.vue';
const stepActive = ref(1);
const steps = ['定义列', '表单流程', '流程设计'];
const formInfo = ref({});
const flowInfo = ref({});
const loading = ref(false);

const props = defineProps({
	dialogVisible: {
		type: Boolean,
		default: false
	},
	rowData: {
		type: Object,
		default: () => ({})
	}
});
const { dialogVisible, rowData } = toRefs(props);
const openDialog = () => {
	formInfo.value = rowData.value;
	stepActive.value = 1;
	//.value = true;
};
const nextStep = data => {
	stepActive.value++;
	if (stepActive.value == 2) {
		flowInfo.value = data;
	}
};
const prevStep = () => {
	stepActive.value--;
};

const emits = defineEmits(['closeDialog', 'nextStep', 'prevStep']);
const closeDialog = () => {
	emits('closeDialog', false);
};
</script>
