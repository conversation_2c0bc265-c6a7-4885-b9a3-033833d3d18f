<template>
	<div>
		<el-select
			v-model="searchData.selectValue"
			placeholder="选择搜索字段"
			filterable
			size="default"
			style="margin-right: 10px; width: 140px"
			@change="changeData"
		>
			<el-option
				v-for="item in columnList"
				:key="item.name"
				:label="item.showName == 'projectCode' ? '项目代码' : item.showName"
				:value="getColValue(item)"
				style="margin-right: 10px"
			/>
		</el-select>
		<el-select
			v-if="
				searchData.selectValue.split('_')[1] == 'number' ||
				searchData.selectValue.split('_')[1] == 'money' ||
				searchData.selectValue.split('_')[1] == 'date'
			"
			v-model="searchData.operation"
			filterable
			placeholder="选择运算"
			style="margin-right: 10px; width: 120px"
		>
			<el-option
				v-for="item in options"
				:key="item.label"
				:label="item.label"
				:value="item.value"
				style="margin-right: 10px"
			/>
		</el-select>

		<el-input
			v-if="
				searchData.selectValue
					? searchData.selectValue.split('_')[1] == 'string' ||
						searchData.selectValue.split('_')[1] == 'checkUser' ||
						searchData.selectValue.split('_')[1] == 'dict'
					: true
			"
			v-model="searchData.inputValue"
			style="width: 150px"
			clearable
		></el-input>
		<el-input-number
			v-if="
				searchData.selectValue.split('_')[1] == 'money' ||
				searchData.selectValue.split('_')[1] == 'number'
			"
			v-model="searchData.inputValue"
			:precision="
				searchData.selectValue.split('_')[1] == 'money'
					? 2
					: searchData.selectValue.split('_')[2] &&
						  Number(searchData.selectValue.split('_')[2]) > 0
						? searchData.selectValue.split('_')[2]
						: 0
			"
			:step="
				searchData.selectValue.split('_')[1] == 'money'
					? 0.01
					: searchData.selectValue.split('_')[2] &&
						  Number(searchData.selectValue.split('_')[2]) > 0
						? 1 / Math.pow(10, Number(searchData.selectValue.split('_')[2]))
						: 1
			"
			style="width: 120px"
		/>
		<el-input-number
			v-if="
				(searchData.selectValue.split('_')[1] == 'money' ||
					searchData.selectValue.split('_')[1] == 'number') &&
				searchData.operation == 'between'
			"
			v-model="betweenData"
			:precision="
				searchData.selectValue.split('_')[1] == 'money'
					? 2
					: searchData.selectValue.split('_')[2] &&
						  Number(searchData.selectValue.split('_')[2]) > 0
						? searchData.selectValue.split('_')[2]
						: 0
			"
			:step="
				searchData.selectValue.split('_')[1] == 'money'
					? 0.01
					: searchData.selectValue.split('_')[2] &&
						  Number(searchData.selectValue.split('_')[2]) > 0
						? 1 / Math.pow(10, Number(searchData.selectValue.split('_')[2]))
						: 1
			"
			style="width: 120px"
		/>
		<!--  <el-input-number v-if="searchData.selectValue.split('_')[1]=='number'" v-model="searchData.inputValue" />
    <el-input-number v-if="searchData.selectValue.split('_')[1]=='money'" v-model="searchData.inputValue" :precision="2" :step="0.1"/>
   -->
		<el-date-picker
			v-if="searchData.selectValue.split('_')[1] == 'date'"
			v-model="searchData.inputValue"
			type="date"
			value-format="YYYY-MM-DD"
			format="YYYY-MM-DD"
			placeholder="日期选择"
			style="width: 130px"
		/>
		<el-date-picker
			v-if="searchData.selectValue.split('_')[1] == 'checkTime'"
			v-model="searchData.inputValue"
			type="date"
			unlink-panels
			value-format="YYYY-MM-DD"
			format="YYYY-MM-DD"
			placeholder="日期选择"
			style="width: 130px"
		/>
		<el-select
			v-if="searchData.selectValue.split('_')[1] == 'luruStatus'"
			v-model="searchData.inputValue"
			filterable
			placeholder="选择状态"
			style="margin-right: 10px; width: 140px"
		>
			<el-option
				v-for="item in luruStatusList"
				:key="item.label"
				:label="item.label"
				:value="item.value"
				style="margin-right: 10px"
			/>
		</el-select>
		<el-select
			v-if="searchData.selectValue.split('_')[1] == 'checkStatus'"
			filterable
			placeholder="选择审核状态"
			style="margin-right: 10px; width: 140px"
		>
			<el-option
				v-for="item in statusList"
				:key="item.label"
				:label="item.label"
				:value="item.value"
				style="margin-right: 10px"
			/>
		</el-select>
		<el-button type="primary" @click="toSearch" style="margin-left: 5px">搜索</el-button>
		<el-tag
			v-for="tag in tags"
			:key="tag"
			closable
			@close="handleClose(tag)"
			style="margin-left: 5px"
		>
			{{ tag.showSelectValue + ' ' + tag.operation + ' ' + tag.inputValue }}
		</el-tag>
	</div>
</template>

<script setup lang="ts">
import { reactive, toRefs, onMounted, ref, watch } from 'vue';
import { SearchData } from '../types';
import { cloneDeep } from 'lodash-es';
import { ElMessageBox } from 'element-plus';
const props = defineProps({
	columnList: Object,
	tableType: String,
	defaultField: String,
	checkData: Object
});
const { columnList, tableType, defaultField, checkData } = toRefs(props);
const options = ref<object>([]);
const tags: any = ref([]);
const searchData = ref<SearchData>({
	selectValue: defaultField?.value || '',
	operation: '',
	inputValue: ''
});
const betweenData = ref('');

const dateOperation = [
	{ label: '等于', value: '=' },
	{ label: '早于', value: '<' },
	{ label: '晚于', value: '>' },
	{ label: '早于或等于', value: '<=' },
	{ label: '晚于或等于', value: '>=' },
	{ label: '不等于', value: '!=' }
	// {label:"位于二者之间",value:"between"}
];
const numberOperation = [
	{ label: '等于', value: '=' },
	{ label: '小于', value: '<' },
	{ label: '大于', value: '>' },
	{ label: '小于等于', value: '<=' },
	{ label: '大于等于', value: '>=' },
	{ label: '不等于', value: '!=' }
	// {label:"位于二者之间",value:"between"}
];
const dayNumberOper = [
	{ label: '等于', value: '=' },
	{ label: '小于(早于)', value: '<' },
	{ label: '大于(晚于)', value: '>' },
	{ label: '小于等于(早于或等于)', value: '<=' },
	{ label: '大于或等于(晚于或等于)', value: '>=' },
	{ label: '不等于', value: '!=' }
	//{label:"位于二者之间",value:"between"}
];
const luruStatusList = [
	{ label: '待审核', value: 0 },
	{ label: '审核通过', value: 1 },
	{ label: '已驳回', value: 2 },
	{ label: '暂存', value: 3 }
];
const statusList = [
	{ label: '未审核', value: 0 },
	{ label: '审核通过', value: 1 },
	{ label: '审核驳回', value: 2 }
];
onMounted(() => {
	console.log('搜索组件columnList', columnList?.value);
	if (checkData?.value && checkData.value.length > 0) {
		tags.value = checkData.value;
	}
	//colList.value = columnList?.value.filter(item=>item.type!="picture")
	// colList.value = columnList?.value.map((item) => ({ label: item.name, type: item.type }));
});
watch(
	checkData,
	newVal => {
		if (newVal && newVal.length > 0) {
			tags.value = newVal;
		}
	},
	{ deep: true }
); // 如果checkData是深层对象，需要deep监听

const changeData = () => {
	console.log('searchData.selectValue', searchData.value.selectValue);
	searchData.value.inputValue = '';
	searchData.value.operation = '';
	console.log(
		'searchData.value.selectValu',
		searchData.value.selectValue,
		searchData.value.selectValue.split('_')[1]
	);
	const type = searchData.value.selectValue.split('_')[1];
	console.log('type', type);
	if (type == 'number' || type == 'money') {
		options.value = numberOperation;
	} else if (type == 'date') {
		options.value = dateOperation;
	}
	console.log('options', options.value);
};
function getColValue(value) {
	if (value.type == 'number') {
		return value.name + '_' + value.type + '_' + (value.decimals || 0);
	} else if (value.type == 'date') {
		return value.name + '_' + value.type + '_' + value.dateType;
	} else if (
		value.subType &&
		(value.subType == 'tableForm' || value.subType == 'checkbox' || value.subType == 'switch')
	) {
		return value.name + '_' + value.type + '_' + value.subType;
	} else {
		if (value.name === '状态') {
			return '审核状态_' + value.type;
		}
		return value.name + '_' + value.type;
	}
}

const handleClose = (tag: string) => {
	tags.value.splice(tags.value.indexOf(tag), 1);
	emits('onSearch', tags.value);
};
const emits = defineEmits(['onSearch']);
const toSearch = () => {
	const data: any = cloneDeep(searchData.value);
	if (data.operation == 'between') {
		data.inputValue = data.inputValue + ',' + betweenData.value;
	}

	const { selectValue, operation, inputValue } = data;

	const hasSameTag = tags.value.some(
		item =>
			item.selectValue === selectValue &&
			item.operation === operation &&
			item.inputValue === inputValue
	);
	console.log('data', data);
	if (hasSameTag) {
		emits('onSearch', tags.value);
	} else {
		// const data = cloneDeep(data);
		const column = columnList?.value.filter(col => col.name == selectValue.split('_')[0]);
		data.showSelectValue = column[0].showName;
		tags.value.push(data);
		emits('onSearch', tags.value);
	}
};
</script>
<style scoped lang="scss"></style>
