<template>
	<el-dialog title="转移" v-model="dialogVisible" width="60%" @close="handleClose">
		<div style="text-align: center; width: 100%" class="edit_dev">
			<el-transfer
				v-model="value"
				filterable
				filter-placeholder="搜索表单"
				:data="formList"
				:titles="['未选择', typeName + '表单']"
				class="responsive-transfer"
			>
				<template #default="{ option }">
					<el-tooltip :content="option.label" placement="top" :show-after="200">
						<span>{{ option.label }}</span>
					</el-tooltip>
				</template>
			</el-transfer>
		</div>
		<div class="footer">
			<el-button type="primary" @click="handleConfirm">确定</el-button>
		</div>
	</el-dialog>
</template>
<script setup lang="ts">
import { ref, toRefs } from 'vue';
const props = defineProps<{
	dialogVisible: boolean;
	typeName: string;
	formList: any[];
}>();
const { dialogVisible, typeName, formList } = toRefs(props);

const value = ref([]);

// const filterMethod = (query, item) => {
// 	return item.initial.toLowerCase().includes(query.toLowerCase());
// };
const emit = defineEmits(['submit', 'dialogClose']);
const handleConfirm = () => {
	console.log('handleConfirm', value.value);
	emit('submit', value.value);
	//dialogVisible.value = false;
};
const handleClose = () => {
	emit('dialogClose', false);
};
</script>
<style scoped lang="scss">
.el-dialog__body {
	padding: 0;
}
.footer {
	display: flex;
	justify-content: flex-end;
	margin-top: 20px;
}
.responsive-transfer {
	width: 100%;
	display: flex;
	justify-content: center;
	gap: 20px;

	:deep(.el-transfer__buttons) {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: flex-end;
		align-self: center;
		padding: 0 10px;
		.el-button + .el-button {
			// 选择第二个按钮
			margin-top: 10px;
			margin-left: 0;
		}
	}

	:deep(.el-transfer-panel) {
		width: 45%;
		min-width: 200px;
		height: 400px;

		.el-transfer-panel__body {
			height: calc(100% - 40px);
		}
	}
}
</style>
