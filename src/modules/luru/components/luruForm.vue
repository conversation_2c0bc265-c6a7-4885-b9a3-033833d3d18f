<!-- eslint-disable vue/require-prop-types -->
<template>
	<div>
		<el-dialog
			v-model="luruVisible"
			title="录入"
			width="65%"
			draggable
			:destroy-on-close="true"
			:fullscreen="true"
			@open="openDialog"
			@close="closeDialog"
		>
			<div>
				<formCreate
					:rule="rules"
					ref="testaa"
					:option="options"
					v-model="formData"
					v-model:api="fApi"
					@change="onChange"
					@mounted="onCreated"
				></formCreate>
			</div>
			<div class="footer">
				<el-button key="back" @click="closeDialog" class="btn">取消</el-button>
				<!-- <el-button key="submit" type="primary" @click="submitTable('提交审核')"
						>提交审核</el-button
				> -->
				<el-button key="submit" type="primary" @click="submitTable('暂存')">暂存</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script lang="ts" setup>
import { reactive, toRefs, ref, computed, watch, onUnmounted, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox, ElIcon, ElUpload, ElDialog } from 'element-plus';
import { useCool } from '/@/cool';
import formCreate from '@form-create/element-ui';
import FcDesigner from '@form-create/designer';
import { cloneDeep, isEqualWith } from 'lodash-es';
import { useStore } from '../store';
import { deepTree, mergeArrays } from '/@/cool/utils';
import { useBase } from '/$/base';
import { getDictFromIndexDB, saveDictToIndexDB } from '../utils/indexDB';
import { useUpload } from '/@/plugins/upload/hooks';
import { Plus } from '@element-plus/icons-vue';
import { AUDIT_STATUS } from '../utils/constants';
import CustomUpload from './customUpload.vue';

const { service } = useCool();
const { dictCols, luruTableStore } = useStore();
const { user } = useBase();
const testaa = ref('');

const props = defineProps({
	luruVisible: Boolean,
	title: String,
	//	ruleColumnList: Object,
	options: Object,
	tableName: String,
	rowData: Object,
	tableId: String,
	autoincrement: Boolean
});

const { luruVisible, title, rowData, options, tableId, tableName, autoincrement } = toRefs(props);

const fApi = ref();
const formData = ref({});
const rules = ref([]);

const columnList = ref([]);
const dictColsLength = ref(0);

const relatedTableData = ref({});

// 添加新的响应式变量
const relatedTablesInfo = ref(new Map()); // 存储关联表信息
const previousConditions = ref(new Map()); // 存储上一次的条件值，用于比较变化

const { options: uploadOptions, toUpload } = useUpload();

formCreate.component('table-form', FcDesigner.tableForm);
// const CustomUpload = {
// 	name: 'custom-upload',
// 	// template: `
// 	// 	<div class="custom-upload">
// 	// 		<el-upload
// 	// 			ref="uploadRef"
// 	// 			:action="action"
// 	// 			:accept="accept"
// 	// 			:multiple="multiple"
// 	// 			:limit="limit"
// 	// 			:auto-upload="true"
// 	// 			:show-file-list="true"
// 	// 			:on-exceed="handleExceed"
// 	// 			:before-upload="beforeUpload"
// 	// 			:http-request="customUpload"
// 	// 			:file-list="fileList"
// 	// 			list-type="picture-card"
// 	// 			:on-preview="handlePreview"
// 	// 			:on-remove="handleRemove"
// 	// 		>
// 	// 			<el-icon class="avatar-uploader-icon"><Plus /></el-icon>
// 	// 		</el-upload>

// 	// 		<!-- 图片预览对话框 -->
// 	// 		<el-dialog v-model="previewVisible" append-to-body>
// 	// 			<img w-full :src="previewUrl" alt="Preview Image" />
// 	// 		</el-dialog>
// 	// 	</div>
// 	// `,
// 	props: {
// 		action: {
// 			type: String,
// 			default: '#'
// 		},
// 		accept: String,
// 		multiple: Boolean,
// 		limit: Number,
// 		uploadHandler: {
// 			type: Function,
// 			required: true
// 		},
// 		modelValue: {
// 			type: [String, Array],
// 			default: ''
// 		}
// 	},
// 	emits: ['update:modelValue'],
// 	setup(props, { emit }) {
// 		const fileList = ref([]);
// 		const previewVisible = ref(false);
// 		const previewUrl = ref('');

// 		// 初始化文件列表
// 		watch(
// 			() => props.modelValue,
// 			val => {
// 				if (val) {
// 					if (Array.isArray(val)) {
// 						fileList.value = val.map(url => ({
// 							url,
// 							name: url.split('/').pop()
// 						}));
// 					} else if (typeof val === 'string') {
// 						fileList.value = [
// 							{
// 								url: val,
// 								name: val.split('/').pop()
// 							}
// 						];
// 					}
// 				} else {
// 					fileList.value = [];
// 				}
// 			},
// 			{ immediate: true }
// 		);

// 		const beforeUpload = file => {
// 			const limitSize = 10;
// 			if (file.size / 1024 / 1024 >= limitSize) {
// 				ElMessage.error(`上传文件大小不能超过 ${limitSize}MB!`);
// 				return false;
// 			}
// 			return true;
// 		};

// 		const customUpload = async ({ file, onProgress, onSuccess, onError }) => {
// 			try {
// 				const res = await props.uploadHandler(file, {
// 					onProgress: progress => {
// 						onProgress({ percent: progress });
// 					}
// 				});

// 				// 处理上传成功后的响应
// 				const url = res.url || res;
// 				const fileInfo = {
// 					name: file.name,
// 					url: url
// 				};

// 				// 更新文件列表
// 				if (props.multiple) {
// 					const newUrls = [...(fileList.value || []), fileInfo];
// 					fileList.value = newUrls;
// 					emit(
// 						'update:modelValue',
// 						newUrls.map(f => f.url)
// 					);
// 				} else {
// 					fileList.value = [fileInfo];
// 					emit('update:modelValue', url);
// 				}

// 				onSuccess(res);
// 			} catch (err) {
// 				onError(err);
// 				ElMessage.error(err.message || '上传失败');
// 			}
// 		};

// 		const handlePreview = file => {
// 			previewUrl.value = file.url;
// 			previewVisible.value = true;
// 		};

// 		const handleRemove = file => {
// 			const index = fileList.value.findIndex(f => f.url === file.url);
// 			if (index > -1) {
// 				const newFileList = [...fileList.value];
// 				newFileList.splice(index, 1);
// 				fileList.value = newFileList;

// 				if (props.multiple) {
// 					emit(
// 						'update:modelValue',
// 						newFileList.map(f => f.url)
// 					);
// 				} else {
// 					emit('update:modelValue', '');
// 				}
// 				//customUploasubmit;
// 			}
// 		};

// 		const handleExceed = () => {
// 			ElMessage.warning(`最多只能上传 ${props.limit} 个文件`);
// 		};

// 		return {
// 			fileList,
// 			previewVisible,
// 			previewUrl,
// 			beforeUpload,
// 			customUpload,
// 			handlePreview,
// 			handleRemove,
// 			handleExceed
// 	}
// }

// 注册自定义上传组件
formCreate.component('custom-upload', CustomUpload);

const ruleColumnList = ref();
const emits = defineEmits(['closeDialog', 'submitData']);
async function openDialog() {
	console.log('已打开表单');
	columnList.value = dictCols.getDictCols();
	ruleColumnList.value = luruTableStore.getluruTableCols();
	// 等待所有规则处理完成
	console.log('获取存储内容');
	rules.value = await flattenFormDataWithLevel(ruleColumnList.value, columnList.value);
	//	rules.value = ruleColumnList.value;
	console.log('得到规则', rules.value);
	// 使用nextTick确保DOM更新
	//await nextTick();

	formCreate.parseJson(JSON.stringify(rules.value));
	formCreate.parseJson(JSON.stringify(options.value));
	console.log('渲染');
	if (title.value === '新增') {
		if (fApi.value) {
			fApi.value.resetFields();
		}
	} else if (title.value === '编辑' && rowData.value) {
		// 处理编辑数据
		const mappedData = {};
		// 遍历规则找到所有需要映射的字段
		function traverseRules(items) {
			items.forEach(item => {
				// 处理普通字段
				if (item.field && item.type !== 'tableForm') {
					const fieldName = item.props?.fieldName || item.title;
					if (fieldName && rowData.value[fieldName] !== undefined) {
						// 处理带有字典的select类型
						if (
							(item.type === 'select' || item.type == 'elTreeSelect') &&
							item.props?.hasDict?.includes('hasDict')
						) {
							if (item.props.isProject) {
								if (rowData.value[fieldName] && rowData.value['projectCode']) {
									mappedData[item.field] =
										`${rowData.value[fieldName]}&${rowData.value['projectCode']}`;
								}
							} else {
								const codeField = `${fieldName}代码`;
								if (rowData.value[fieldName] && rowData.value[codeField]) {
									mappedData[item.field] =
										`${rowData.value[fieldName]}&${rowData.value[codeField]}`;
								}
							}
						} else {
							// 处理其他类型字段
							mappedData[item.field] = rowData.value[fieldName];
						}
					}
				}

				// 处理表格表单类型
				if (item.type === 'tableForm') {
					const fieldName = item.props?.fieldName;
					if (fieldName && Array.isArray(rowData.value[fieldName])) {
						const tableData = rowData.value[fieldName].map(row => {
							const mappedRow = {};
							// 遍历表格的列定义
							item.props.columns.forEach(col => {
								const colField = col.rule[0].field;
								const colLabel = col.label;
								// 使用列的label作为key来获取数据
								if (row[colLabel] !== undefined) {
									mappedRow[colField] = row[colLabel];
								}
							});
							return mappedRow;
						});
						mappedData[item.field] = tableData;
					}
				}

				// 递归处理子项
				if (item.children) {
					traverseRules(item.children);
				}
			});
		}

		traverseRules(ruleColumnList.value);

		// 更新表单数据
		formData.value = mappedData;
		console.log('更新表单数据');
		// 确保表单更新
		nextTick(async () => {
			if (fApi.value) {
				// 1. 批量更新值
				const batchSize = 50; // 每批处理50个字段
				const entries = Object.entries(mappedData);

				for (let i = 0; i < entries.length; i += batchSize) {
					const batch = entries.slice(i, i + batchSize);
					batch.forEach(([field, value]) => {
						fApi.value.setValue(field, value, {
							silent: true
						});
					});
					await new Promise(resolve => setTimeout(resolve, 0));
				}
			}
		});
		emits('getLoading', false);
	}
	console.log('结束');

	dictColsLength.value = dictCols.getDictColsLength();
}
// 修改自定义上传组件

// 添加以下辅助函数
function findAllRelatedFields(rules, tableId) {
	const result = {
		conditions: [], // 存储条件列
		selects: [] // 存储自动带出列
	};

	function traverse(items) {
		items.forEach(item => {
			// 检查是否是条件列
			if (
				item.props?.isPrimaryCol?.includes('isPrimaryCol') &&
				item.props?.relatedTables?.some(t => t.id === tableId)
			) {
				//console.log('关联条件列', item);
				result.conditions.push({
					field: item.field,
					fieldName: item.props.fieldName,
					props: item.props,
					col: item.props.relatedTables.find(t => t.id === tableId).col
				});

				if (item.props.relatedTables.find(t => t.id === tableId).sourceColHasDict) {
					result.conditions[result.conditions.length - 1].hasDict = true;
				}
				if (item.props.relatedTables.find(t => t.id === tableId).colIsProject) {
					result.conditions[result.conditions.length - 1].isProject = true;
				}
			}
			// 检查是否是自动带出列
			if (item.props?.relatedTableId === tableId) {
				result.selects.push({
					field: item.field, // 表单字段名
					relatedCol: item.props.relatedField, // 关联表字段名
					props: item.props // 保存完整的属性
				});
			}

			if (item.children && item.type != 'tableForm') {
				traverse(item.children);
			}
		});
	}
	//if (item.type != 'tableForm') {
	traverse(rules);
	//}
	return result;
}

function generateRelatedTableData(tableId, conditions, selects, formData) {
	const conditionValues = conditions.map(condition => {
		const value = formData[condition.field];
		console.log('关联表value', condition, value);
		if (condition?.isProject) {
			return { projectCode: value.split('&')[1] };
		} else if (condition?.props?.hasDict?.includes('hasDict')) {
			console.log('关联表hasDict', value, value.split('&'), value.split('&')[1]);
			if (condition.hasDict) {
				return { [`${condition.col}代码`]: value.split('&')[1] };
			} else {
				return { [`${condition.col}`]: value.split('&')[1] };
			}
		} else {
			return {
				[condition.col]: value
			};
		}
	});

	// 整理自动带出列的信息
	const autoFillFields = selects.map(select => ({
		responseField: select.relatedCol, // 响应数据中的字段名
		formField: select.field, // 表单中的字段名
		props: select.props // 保存其他可能需要的属性
	}));

	return {
		id: tableId,
		conditions: conditionValues,
		selects: selects.map(s => s.relatedCol), // API 需要的字段列表
		autoFillFields // 用于更新表单的字段映射
	};
}

function areConditionsChanged(oldConditions, newConditions) {
	if (!oldConditions || oldConditions.length !== newConditions.length) {
		return true;
	}

	return !isEqualWith(oldConditions, newConditions, (a, b) => {
		return JSON.stringify(a) === JSON.stringify(b);
	});
}

// 修改 onChange 函数
const onChange = (field, value, rule) => {
	if (rule.props?.isPrimaryCol?.includes('isPrimaryCol')) {
		const relatedTables = rule.props.relatedTables || [];
		relatedTables.forEach(async ({ id: tableId }) => {
			const relatedFields = findAllRelatedFields(ruleColumnList.value, tableId);

			const allConditionsHaveValue = relatedFields.conditions.every(condition => {
				const conditionValue = formData.value[condition.field];
				return conditionValue !== undefined && conditionValue !== '';
			});
			if (allConditionsHaveValue) {
				const newData = generateRelatedTableData(
					tableId,
					relatedFields.conditions,
					relatedFields.selects,
					formData.value
				);
				const oldConditions = previousConditions.value.get(tableId);
				if (areConditionsChanged(oldConditions, newData.conditions)) {
					previousConditions.value.set(tableId, newData.conditions);
					relatedTablesInfo.value.set(tableId, newData);

					try {
						const params = {
							id: newData.id,
							conditions: newData.conditions,
							selects: newData.selects
						};
						const response = await service.cloud.db.join(params);

						if (response && response.length > 0) {
							ElMessage.success(`${tableId}表数据关联成功`);

							// 直接使用预先准备好的字段映射更新表单
							newData.autoFillFields.forEach(field => {
								const value = response[0][field.responseField];
								if (value !== undefined) {
									// 更新 formData
									formData.value[field.formField] = value;
									// 更新表单显示并设置为只读
									if (fApi.value) {
										fApi.value.setValue(field.formField, value);
										// 设置字段为只读
										fApi.value.disabled(field.formField, true);
										// 更新对应规则的 props
										const rule = fApi.value.getRule(field.formField);
										if (rule) {
											rule.props = {
												...rule.props,
												disabled: true,
												readonly: true
											};
										}
									}
								}
							});
						} else {
							ElMessage.warning('未找到匹配的关联数据');
							// 清空相关字段并恢复可编辑状态
							newData.autoFillFields.forEach(field => {
								formData.value[field.formField] = undefined;
								if (fApi.value) {
									fApi.value.setValue(field.formField, undefined);
									// 恢复字段可编辑状态
									fApi.value.disabled(field.formField, false);
									// 更新对应规则的 props
									const rule = fApi.value.getRule(field.formField);
									if (rule) {
										rule.props = {
											...rule.props,
											disabled: false,
											readonly: false
										};
									}
								}
							});
						}
					} catch (error) {
						console.error('获取关联数据失败:', error);
						ElMessage.error('获取关联数据失败');
					}
				}
			}
		});
	}
};

const onCreated = () => {
	const vm = fApi.value.el('Fcevmaeuff8saec');

	nextTick(() => {
		// 获取组件的 el-upload 实例
		const uploadComponent = fApi.value.el('Fcevmaeuff8saec');
		console.log('获取upload', formCreate.getApi('upload'));
		console.log('获取upload-uploadComponent', fApi.value.el('Fcevmaeuff8saec'));
		if (uploadComponent) {
			// 获取 el-upload 组件实例
			const elUpload = uploadComponent.querySelector('.el-upload');
			if (elUpload && elUpload.__vueParentComponent) {
				const uploadInstance = elUpload.__vueParentComponent.ctx;
				// 替换上传方法
				if (uploadInstance) {
					uploadInstance.upload = async rawFile => {
						try {
							const res = await toUpload(rawFile, {
								onProgress(progress) {
									uploadInstance.handleProgress(
										{
											percent: progress
										},
										rawFile
									);
								}
							});
							uploadInstance.handleSuccess(res, rawFile);
							return res;
						} catch (err) {
							uploadInstance.handleError(err, rawFile);
							ElMessage.error(err.message || '上传失败');
							return false;
						}
					};
				}
			}
		}
	});
};

const closeDialog = () => {
	// formData.value = {};
	// filterColumnList.value = [];
	relatedTablesInfo.value = new Map(); // 存储关联表信息
	previousConditions.value = new Map(); // 存储上一次的条件值，用于比较变化

	emits('closeDialog', false);
};
// 处理规则
const processRules = rules => {
	return rules.map(rule => {
		// 移除一些设计器特有的属性
		const { _fc_drag_tag, display, _fc_id, ...rest } = rule;
		return {
			...rest,
			// 确保props存在
			props: rule.props || {}
		};
	});
};

// 在 flattenFormDataWithLevel 中使用自定义组件并传入 toUpload
async function flattenFormDataWithLevel(formData, dictcols) {
	async function traverse(items, level = 0, parent = null) {
		if (!Array.isArray(items)) return;

		// 使用Promise.all处理所有异步操作
		await Promise.all(
			items.map(async item => {
				//console.log('item', item);
				if (item.type === 'select') {
					// select字段的处理保持不变
					item.props = {
						...item.props,
						virtualScroll: true,
						height: 250,
						filterable: true,
						remote:
							dictcols.find(col => col.name == item.props.fieldName)?.values?.length >
							100,
						remoteMethod: query => {}
					};
					dictcols.map(col => {
						if (col.name == item.props.fieldName && col.hasDict) {
							if (col.values.length > 100) {
								item.options = [];
							} else {
								item.options = col.values.map((k, i) => ({
									label: k + '&' + col.codes[i],
									value: k + '&' + col.codes[i]
								}));
							}
							// 添加 remote-method 实现真正的搜索过滤
							item.props.remoteMethod = query => {
								if (query) {
									const filteredOptions = col.values
										.map((k, i) => ({
											label: k,
											value: k + '&' + col.codes[i]
										}))
										.filter(item => item.label.includes(query))
										.slice(0, 100); // 限制搜索结果数量

									// 更新选项
									const rule = fApi.value.getRule(item.field);
									if (rule) {
										rule.options = filteredOptions;
									}
								}
							};
						}
					});
				} else if (item.type === 'elTreeSelect') {
					const treeData = await changeDictName(item.props);
					item.props.data = treeData;
					item.props.lazy = true;
				} else if (item.type === 'datePicker') {
					item.props.type = item.props.dateType;
				} else if (item.type === 'upload') {
					// 使用自定义上传组件
					item.type = 'custom-upload';
					item.uploadHandler = toUpload;
					const data = cloneDeep(item.props);
					console.log('data', data);
					item.props = {
						multiple: data.multiple,
						limit: data.limit,
						uploadHandler: toUpload,
						fieldName: data?.fieldName || item.title // 保存字段名
					};
					if (data.accept) {
						item.props.accept = data.accept;
					}

					// 设置双向绑定
					item.emit = ['update:modelValue'];
					item.emitPrefix = 'on';

					// 如果是编辑模式，设置初始值
					if (title.value === '编辑' && rowData.value) {
						const fieldName = item.props.fieldName;
						if (rowData.value[fieldName]) {
							item.value = rowData.value[fieldName];
						}
					}
				} else if (item.type === 'tableForm') {
					// 使用Promise.all处理表单中的所有列
					await Promise.all(
						item.props.columns.map(async column => {
							const rule = column.rule[0];
							if (
								rule.type === 'select' &&
								rule.props?.hasDict?.includes('hasDict')
							) {
								const matchingDict = dictcols.find(
									col =>
										col.name === column.label &&
										col.isTableForm &&
										col.tableFormName === item.props.fieldName
								);

								if (matchingDict && matchingDict.hasDict) {
									rule.props = {
										...rule.props,
										virtualScroll: true,
										height: 250,
										filterable: true,
										remote: matchingDict.values.length > 100
									};

									rule.options = matchingDict.values.map((k, i) => ({
										label: k + '&' + matchingDict.codes[i],
										value: k + '&' + matchingDict.codes[i]
									}));
								}
							} else if (rule.type === 'elTreeSelect') {
								const treeData1 = await changeDictName(rule.props);
								rule.props.data = treeData1;
							}
						})
					);
				}

				// 处理关联表数据
				if (item.props?.relatedTables?.length > 0) {
					item.props.relatedTables.forEach(k => {
						if (relatedTableData[k.id]) {
							if (relatedTableData[k.id].primaryCol) {
								relatedTableData[k.id].primaryCol++;
								relatedTableData[k.id].primaryColName.push({
									currentName: k.fieldName,
									torgetName: k.col
								});
							} else {
								relatedTableData[k.id].primaryCol = 1;
								relatedTableData[k.id].primaryColName = [
									{
										currentName: k.fieldName,
										torgetName: k.col
									}
								];
							}
						} else {
							relatedTableData[k.id] = {
								primaryCol: 1,
								primaryColName: [
									{
										currentName: k.fieldName,
										torgetName: k.col
									}
								]
							};
						}
					});
				}

				// 递归处理子节点
				if (item.type !== 'tableForm' && item.children) {
					await traverse(item.children, level + 1, item);
				}
			})
		);
	}

	await traverse(formData, dictcols);
	return formData;
}

async function changeDictName(data) {
	let dictAllData = await getDictFromIndexDB([data.parentId]);
	//console.log('indexDB来的数据all', dictAllData);
	if (!dictAllData) {
		const res = await service.dict.info.getAllChildren({
			typeId: [data.parentId],
			status: 1
			//cols: ['id', 'value', 'name', 'parentId', 'typeId']
		});
		//console.log('allDictData', res);
		await saveDictToIndexDB([data.parentId], res);
		dictAllData = res;
	}

	//console.log('末级的父级', data, data.dictId, dictAllData, dictAllData[data.parentId]);
	const dictField = data.dictField;
	const dictFieldArr = dictField.split('/');
	const parentIndex = dictFieldArr.indexOf(data.parentId.toString());
	const leafIndex = dictFieldArr.indexOf(data.dictId.toString());
	const resultArray = dictFieldArr.slice(parentIndex, leafIndex + 1);
	//console.log('resultArray', resultArray);
	dictAllData[data.parentId] = dictAllData[data.parentId].filter(item =>
		resultArray.includes(item.typeId.toString())
	);
	//console.log('dictAllData', dictAllData);
	const res2 = cloneDeep(mergeArrays(dictAllData));
	const treeRes = deepTree(res2);
	//console.log('treeRes', data.dictId, treeRes);
	const treeData = findAndProcessNode(treeRes, data.dictId);
	//console.log('树形结构数据结果', treeData);
	return treeData;
	//leafList.value = treeRes;
	//console.log('leafList.value', leafList.value);
	//loading.value = false;
}

function findAndProcessNode(nodes: any[], dictId: number) {
	if (!nodes) return [];

	return nodes
		.map(node => {
			// 处理当前节点
			const processedNode = {
				label: node.name + '&' + node.value,
				value: node.name + '&' + node.value,
				disabled: false,
				typeId: node.typeId, // 保留typeId用于后续判断
				children:
					node.children && node.children.length > 0
						? findAndProcessNode(node.children, dictId)
						: []
			};

			return processTreeNode(processedNode, dictId);
		})
		.filter(node => node !== null);
}

function processTreeNode(node: any, dictId: number) {
	if (!node) return null;

	// 设置禁用状态：只有叶子节点可选
	node.disabled = node.children && node.children.length > 0;

	// 如果有子节点，递归处理
	if (node.children && node.children.length > 0) {
		node.children = node.children
			.map(child => processTreeNode(child, dictId))
			.filter(child => child !== null);
	}

	return node;
}

interface FlattenedData {
	field: string;
	type: string;
	title: string;
	value: any;
	fieldName?: string;
	[key: string]: any;
}

function flattenFormData(rules: any[], formData: any): FlattenedData[] {
	const result: FlattenedData[] = [];

	// 创建字段映射表
	const fieldMapping: Record<string, string> = {};

	function createFieldMapping(items: any[]) {
		items.forEach(item => {
			if (item.type === 'tableForm') {
				item.props.columns.forEach((col: any) => {
					const field = col.rule[0].field;
					fieldMapping[field] = col.label;
				});
			}

			if (Array.isArray(item.children)) {
				createFieldMapping(item.children);
			}
		});
	}

	function traverse(items: any[]) {
		items.forEach(item => {
			// 处理普通字段
			if (item.field && item.type !== 'tableForm') {
				const flatItem: FlattenedData = {
					field: item.field,
					type: item.type,
					title: item.title,
					value: formData[item.field],
					...item.props
				};
				result.push(flatItem);
			}

			// 处理表格表单
			if (item.type === 'tableForm') {
				const tableData = formData[item.field] || [];
				const columns = item.props.columns;

				// 转换表格数据，将field替换为label
				const convertedTableData = tableData.map((row: any) => {
					const newRow: Record<string, any> = {};
					Object.entries(row).forEach(([key, value]) => {
						newRow[fieldMapping[key] || key] = value;
					});
					return newRow;
				});

				result.push({
					field: item.field,
					type: item.type,
					title: item.title,
					value: convertedTableData,
					rawValue: tableData, // 保留原始数据
					columns: columns.map((col: any) => ({
						label: col.label,
						field: col.rule[0].field,
						type: col.rule[0].type,
						title: col.rule[0].title
					})),
					props: item.props
				});
			}

			// 递归处理子项
			if (Array.isArray(item.children)) {
				traverse(item.children);
			}
		});
	}

	// 先创建字段映射
	createFieldMapping(rules);
	// 然后处理数据
	traverse(rules);
	return result;
}

function formatDate(dateType: string, value: any) {
	if (dateType == 'month') {
		return value + '-01';
	} else if (dateType == 'year') {
		return value + '-01-01';
	} else {
		return value;
	}
}

const submitTable = async (submitType: string) => {
	//console.log('submitTable ', formData.value);
	try {
		const res = flattenFormData(ruleColumnList.value, formData.value);
		console.log('提交_添加值', res);
		const submitData = {};
		res.map(item => {
			if (item.type == 'tableForm') {
				submitData[item.fieldName || item.title] = item.value;
			} else if (item.type == 'input') {
				submitData[item.fieldName] = item.value;
			} else if (item.type == 'select' || item.type == 'elTreeSelect') {
				if (item.hasDict && item.hasDict[0] == 'hasDict') {
					if (item.isProject && item.value) {
						console.log('project', item.value);
						submitData[item.fieldName] = item.value.split('&')[0];
						submitData['projectCode'] = item.value.split('&')[1];
					} else {
						if (item.value) {
							console.log('dict', item);
							submitData[item.fieldName] = item.value.split('&')[0];
							submitData[`${item.fieldName}代码`] = item.value.split('&')[1];
						}
					}
				} else {
					submitData[item.fieldName] = item.value;
				}
			} else if (item.type == 'inputNumber') {
				submitData[item.fieldName] = item.value;
			} else if (item.dateType) {
				submitData[item.fieldName] = formatDate(item.dateType, item.value);
			} else if (item.type == 'custom-upload') {
				console.log('上传', item, item.value, Array.isArray(item.value));
				if (Array.isArray(item.value)) {
					submitData[item.fieldName] = item.value;
				} else {
					submitData[item.fieldName] = [item.value];
				}
			} else {
				submitData[item.fieldName] = item.value;
			}
		});
		console.log('提交_最终数据', title.value, submitData);

		if (title.value === '新增') {
			console.log('新增', submitData);
			submitData['审核状态'] = AUDIT_STATUS.DRAFT;
			submitData['审核说明'] = ' ';
			submitData['录入人'] = user.info?.username;
			if (autoincrement.value) {
				console.log('是最新编辑人');
				submitData.最新编辑人 = user.info?.username;
			}

			try {
				await service.cloud.db
					.data({ id: tableId.value, method: 'add', params: [submitData] })
					.then(res => {})
					.catch(err => {
						ElMessage.error(err.message);
					});
			} catch (err) {
				ElMessage.error(err.message);
			}
		} else {
			submitData['录入人'] = user.info?.username;
			submitData['id'] = rowData?.value.id;
			submitData['审核状态'] = AUDIT_STATUS.DRAFT;
			if (autoincrement.value) {
				console.log('是最新编辑人');
				submitData.最新编辑人 = user.info?.username;
			}
			try {
				await service.cloud.db
					.data({ id: tableId?.value, method: 'update', params: submitData })
					.then(res => {})
					.catch(err => {
						ElMessage.error(err.message);
					});
			} catch (err) {
				ElMessage.error('提交失败:' + err);
			}
		}
	} catch (err) {
		ElMessage.error('提交失败:' + err);
	}
	closeDialog();
	// console.log('urls', urls.value);
	// console.log('formData-yyy', formData.value);
	// console.log('columnList', filterColumnList?.value);
	// // 表单验证
	// try {
	// 	await formRef.value.validate();
	// } catch (error) {
	// 	ElMessage.error('请检查表单填写是否完整正确');
	// 	return;
	// }
	// const submitData = cloneDeep(formData.value);
	// // const missingFields = filterColumnList.value
	// // 	.filter(col => !col.isNull && !submitData[col.name])
	// // 	.map(col => col.showName);
	// // if (missingFields.length > 0) {
	// // 	ElMessage.error(`以下字段为必填: ${missingFields.join(", ")}`);
	// // 	return;
	// // }
	// const filteredSubmitData = Object.fromEntries(
	// 	Object.entries(submitData).filter(([key]) => {
	// 		const column = filterColumnList.value.find(col => col.name === key);
	// 		return column && column.type !== 'calculatedCol';
	// 	})
	// );
	// if (title.value == '审核编辑' && submitType == '提交审核') {
	// 	ElMessageBox.confirm('是否确认再次提交审核？', '提示', {
	// 		confirmButtonText: '确认',
	// 		cancelButtonText: '取消',
	// 		type: 'warning'
	// 	})
	// 		.then(() => {
	// 			filteredSubmitData['审核状态'] = 0;
	// 			filteredSubmitData['审核说明'] = ' ';
	// 			filteredSubmitData['id'] = rowData?.value.id;
	// 			console.log('formData-ypy', filteredSubmitData);
	// 			emits('submitData', filteredSubmitData);
	// 			closeDialog();
	// 		})
	// 		.catch(err => {
	// 			console.log(err);
	// 		});
	// } else if (title.value == '审核编辑' && submitType == '暂存') {
	// 	ElMessageBox.confirm('是否确认暂存？', '提示', {
	// 		confirmButtonText: '确认',
	// 		cancelButtonText: '取消',
	// 		type: 'warning'
	// 	})
	// 		.then(() => {
	// 			filteredSubmitData['审核状态'] = 3;
	// 			filteredSubmitData['审核说明'] = ' ';
	// 			filteredSubmitData['id'] = rowData?.value.id;
	// 			console.log('formData-ypy', filteredSubmitData);
	// 			emits('submitData', filteredSubmitData);
	// 			closeDialog();
	// 		})
	// 		.catch(err => {
	// 			console.log(err);
	// 		});
	// } else {
	// 	filteredSubmitData['审核状态'] = submitType == '提交审核' ? 0 : 3;
	// 	emits('submitData', filteredSubmitData);
	// 	closeDialog();
	// }
};

// 修改自定义上传方法
// const customUpload = async options => {
// 	const { file } = options;

// 	// 文件大小检查
// 	const limitSize = 10;
// 	if (file.size / 1024 / 1024 >= limitSize) {
// 		ElMessage.error(`上传文件大小不能超过 ${limitSize}MB!`);
// 		return false;
// 	}

// 	try {
// 		// 使用本地 upload.vue 的上传方法
// 		const res = await toUpload(file, {
// 			onProgress(progress) {
// 				if (options.onProgress) {
// 					options.onProgress({ percent: progress });
// 				}
// 			}
// 		});

// 		// 调用成功回调
// 		if (options.onSuccess) {
// 			options.onSuccess(res);
// 		}

// 		return res;
// 	} catch (err) {
// 		// 调用错误回调
// 		if (options.onError) {
// 			options.onError(err);
// 		}
// 		ElMessage.error(err.message || '上传失败');
// 		return false;
// 	}
// };

// 修改 changeUpload 函数
// function changeUpload(field) {
// 	// 获取上传组件实例
// 	const vm = fApi.value.el(field);
// 	if (!vm) return;

// 	// 获取组件的 $el (实际的 DOM 元素)
// 	const uploadEl = vm.$el || vm;
// 	if (!uploadEl) return;

// 	// 查找 el-upload 元素
// 	const elUpload = uploadEl.querySelector('.el-upload');
// 	if (!elUpload || !elUpload.__vueParentComponent) return;
// 	//console.log('elUpload', elUpload);
// 	// 获取上传组件实例
// 	const uploadInstance = elUpload.__vueParentComponent.ctx;
// 	if (!uploadInstance) return;

// 	// 替换上传方法
// 	uploadInstance.upload = async rawFile => {
// 		try {
// 			//console.log('rawFile', rawFile);
// 			const res = await toUpload(rawFile, {
// 				onProgress(progress) {
// 					uploadInstance.handleProgress(
// 						{
// 							percent: progress
// 						},
// 						rawFile
// 					);
// 				}
// 			});

// 			uploadInstance.handleSuccess(res, rawFile);
// 			return res;
// 		} catch (err) {
// 			uploadInstance.handleError(err, rawFile);
// 			ElMessage.error(err.message || '上传失败');
// 			return false;
// 		}
// 	};
// }
</script>
