import { defineStore } from "pinia";
import { ref } from "vue";
import { storage } from "/@/cool/utils";

//const data = storage.info();

export const useDictCols = defineStore("dictCols", function () {
	const dictCols = ref([]);
	const dictColsLength = ref({});

	const setDictCols = (data: any) => {
		dictCols.value = data;
		dictCols.value.map((item: any) => {
			if (item.type == "dict" && item.values) {
				dictColsLength.value[item.name] = item.values.length;
			}
		});
		console.log("dictCols存入pinia", data);
	};
	const getDictColsLength = () => {
		return dictColsLength.value;
	};
	const getDictCols = () => {
		return dictCols.value;
	};
	return {
		//dictCols,
		setDictCols,
		getDictCols,
		getDictColsLength
	};
});
