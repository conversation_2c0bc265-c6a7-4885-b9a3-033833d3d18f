import { defineStore } from 'pinia';
import { service } from '/@/cool';
import { ElMessage } from 'element-plus';

// 定义状态接口
interface LuruTableState {
	luruTableInfo: Record<
		string,
		{
			data: any;
			timestamp: number;
		}
	>;
	luruTableCols: any[];
	loading: boolean;
	DBtableName: object;
}

// 缓存过期时间 (30分钟)
const CACHE_EXPIRATION = 30 * 60 * 1000;

// 定义store
export const useLuruTableStore = defineStore('luruTable', {
	// 状态
	state: (): LuruTableState => ({
		luruTableInfo: {},
		luruTableCols: [],
		loading: false,
		DBtableName: {} //数据库表格名 func_表格
	}),

	// actions
	actions: {
		// 检查缓存是否过期
		isCacheExpired(id: string): boolean {
			const cache = this.luruTableInfo[id];
			if (!cache) return true;

			const now = Date.now();
			return now - cache.timestamp > CACHE_EXPIRATION;
		},

		// 获取表格数据
		async getluruTableInfo(id: string) {
			// 如果缓存中存在数据且未过期，直接返回
			if (this.luruTableInfo[id] && !this.isCacheExpired(id)) {
				return this.luruTableInfo[id].data;
			}

			// 设置加载状态
			this.loading = true;

			try {
				// 调用API获取数据
				const res = await service.cloud.db.list({
					id
				});
				console.log('得到的数据', res, id, this.luruTableInfo);

				// 缓存数据，添加时间戳
				this.luruTableInfo[id] = {
					data: res[0],
					timestamp: Date.now()
				};

				return this.luruTableInfo[id].data;
			} catch (err) {
				console.error('获取表格数据失败:', err);
				throw err;
			} finally {
				this.loading = false;
			}
		},

		getluruTableCols() {
			return this.luruTableCols;
		},

		setluruTableCols(cols: any[]) {
			this.luruTableCols = cols;
		},

		async getDBtableName(id) {
			if (this.DBtableName[id]) {
				return this.DBtableName[id];
			} else {
				try {
					const res = await service.cloud.db.page({ id: id });
					this.DBtableName[id] = res.list[0].tableName;
					return this.DBtableName[id];
				} catch (err) {
					ElMessage.error('获取数据库表名失败');
				}
			}
		},

		// 清除缓存
		clearCache(id?: string) {
			console.log('清除缓存');
			if (id) {
				// 清除指定id的缓存
				delete this.luruTableInfo[id];
			} else {
				// 清除所有缓存
				this.luruTableInfo = {};
			}
		},

		// 清除过期缓存
		clearExpiredCache() {
			const now = Date.now();
			Object.keys(this.luruTableInfo).forEach(id => {
				if (this.isCacheExpired(id)) {
					delete this.luruTableInfo[id];
				}
			});
		}
	}
});
