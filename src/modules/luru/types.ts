export interface DesignTable {
	tableName: string;
	type?: string;
	tableColumn: CustomizeColumn[];
	tableInfo: TableInfo;
	remark: string;
}
export interface TableInfo {
	allowEdit: boolean;
	autoincrement: boolean;
}
export interface CustomizeColumn {
	name: string;
	showName: string;
	type: string;
	hasDict: boolean;
	dictId?: number;
	isLeaf?: boolean;
	parentId?: number;
	parentName?: string;
	dictName?: string;
	dictField?: string;
	dictFieldName?: string;
	dictCode?: string;
	disabled?: boolean;
	typeDisabled?: boolean;
	isCode?: string;
	isProject?: boolean;
	decimals?: number;
	dateFormat?: string;
	dateType?: string;
	isPercent?: boolean;
	isNull?: boolean;
	stringType?: string;
	stringLength?: number | number[];
	isPrimaryKey?: boolean; //主键列
	isPrimaryCol?: boolean; //条件列
	relatedTable?: string;
	relatedTableCol?: string;
	relatedColType?: string;
	regularStr?: string;
	sqlStr?: string;
	values?: string[];
	codes?: string[];
	ids?: string[];
	calculateType?: string;
	relatedShowName?: string;
	relatedTables?: any[];
	pictureSize?: number;
	isNotShow?: boolean;
	isNotShowCode?: boolean;
}

export interface DesignRow {
	id: number;
	createTime?: string;
	updateTime?: string;
	name: string;
	tableName?: string;
	colInfo?: object;
	tableInfo?: TableInfo;
	readme: string;
	tableType?: string;
	content?: string;
}

export interface SelectDict {
	label: string;
	key: string;
}
export interface TableList {
	id: number;
	label: string;
	colInfo: CustomizeColumn[];
}

export interface SearchData {
	selectValue: string;
	operation: string;
	inputValue: string | number;
}
