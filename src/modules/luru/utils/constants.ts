export enum SystemDict {
	Project = 'project_system',
	Form = 'form_system',
	User = 'jquser_system',
	SendStrategy = 'send_strategy_system',
	SendStrategyPeriod = 'period_system',
	Customer = 'system_customer',
	PermissionType = 'permission_system'
}
export const PerFormPermission = ['录入', '审核', '查看'];
export const PerProjectPermission = [
	'xzh',
	'kucun_admin',
	'zdf_ruku',
	'zdf_chuku',
	'zdf_kcxx',
	'zdf_kcls',
	'zdf_shenhe',
	'yusuan_admin',
	'yusuan_adjust'
];
export enum PermissionInfo {
	ProjectDictId = 21,
	CustomerDictId = 170,
	SendStrategyDictId = 171,
	TableName = 'func_luru_permission',
	LURU = '录入',
	SHENHE = '审核',
	SHEJI = '设计',
	DICT = '字典维护',
	CHAKAN = '查看',
	FormPermission = '表单权限'
}
export enum TABLE_TYPE {
	SYSTEM = 'system',
	ONLY_VIEW = 'only_view',
	ONLY_ADD = 'only_add',
	ONLY_AUDIT = 'only_audit',
	USER = 'user'
}
export const AUDIT_STATUS = {
	UNAUDITED: 0, // 未审核
	APPROVED: 1, // 审核通过
	REJECTED: 2, // 审核驳回
	DRAFT: 3, // 暂存
	INVALID: 4, // 作废
	ARCHIVED: 5 // 归档
};
