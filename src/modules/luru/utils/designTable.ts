//生成表结构
export function getColContent(name, column, autoincrement, colLengthList?) {
	const newData = flattenFormDataWithLevel(column);
	const res = getColInfo(newData, colLengthList);
	console.log('判断结果', newData, res);
	let content = `import { BaseEntity } from '@cool-midway/core';
import { Column, Entity, Index } from 'typeorm';
/**
 * 描述
 */
@Entity('${name}')
`;
	const primaryKey = res.isPrimaryKey;
	//console.log('primaryCol', primaryKey);

	if (primaryKey.length > 0) {
		content = content + `@Index(${JSON.stringify(primaryKey)}, { unique: true })\n`;
	}
	content = content + `export class ${name}Entity extends BaseEntity {\n`;
	let colText;
	if (autoincrement) {
		colText = `@Column({ comment: '录入人',nullable: true})
    录入人: string;\n
	@Column({ comment: '最新编辑人',nullable: true})
    最新编辑人: string;\n`;
	} else {
		colText = `@Column({ comment: '录入人',nullable: true})
    录入人: string;\n`;
	}
	//console.log('column', column);
	newData.forEach((item: any) => {
		//	console.log('colText', colText);
		console.log('item', item);
		if (item.props.hasDict && item.props.hasDict[0] == 'hasDict') {
			if (item.props.isProject) {
				let col = `@Column({ comment: '${item.props.fieldName}', length: ${item.props.strLength},nullable: ${!item['$required']} })
						${item.props.fieldName}: string;\n`;
				colText = colText + col;
				if (colLengthList && colLengthList.projectCode) {
					col = ` @Column({ comment: 'projectCode', length: ${colLengthList.projectCode}})
						projectCode: string;\n`;
				} else {
					col = ` @Column({ comment: 'projectCode', length: 150})
						projectCode: string;\n`;
				}
				colText = colText + col;

				//console.log('colText-isProject', colText);
			} else {
				let col = `@Column({ comment: '${item.props.fieldName}', length: ${item.props.strLength},nullable: ${!item['$required']} })
						${item.props.fieldName}: string;\n`;

				colText = colText + col;
				col = `@Column({ comment: '${item.props.fieldName + '代码'}', length: ${item.props.strLength},nullable: ${!item['$required']} })
				${item.props.fieldName + '代码'}: string;\n`;
				colText = colText + col;
				//console.log('colText-dict', colText);
			}
		} else if (item.props.calculatedCol && item.props.calculatedCol[0] == 'calculatedCol') {
			colText = colText + getType(item, 'calculatedCol');
			console.log('计算列', getType(item, 'calculatedCol'));
		}
		// else if (item.type == 'relatedCol') {
		// 	colText = colText + getType(item.name, item.relatedColType, item);
		// }
		else {
			colText = colText + getType(item, item.type);
		}
		//colText = colText + getType(getColText.name, getColText.text);
	});
	const checkContent = `@Column({ comment: '审核状态', default: 0})
    审核状态: number;\n @Column({ comment: '审核时间', nullable: true })
    审核时间: Date;\n @Column({ comment: '审核人', nullable: true })
    审核人: string;\n @Column({ comment: '审核说明', nullable: true })
    审核说明: string;\n`;
	return content + colText + checkContent + '}';
}

export function sanitizeSqlExpression(sqlStr: string): string {
	return sqlStr
		.replace(/[\u0000-\u001F\u007F-\u009F\uFEFF]/g, '') // 移除不可见字符，包括零宽度空格
		.replace(/`/g, '') // 移除反引号
		.replace(/"/g, "'") // 将双引号替换为单引号
		.replace(/\s+/g, ' ') // 将多个空格替换为单个空格
		.trim(); // 移除
}

//根据type生成对应数据结构
function getType(data, type) {
	const name = data.props?.fieldName;
	console.log('分析每一列', data, name);
	if (type === 'inputNumber') {
		if (data.props.precision) {
			return ` @Column({ comment:  '${name}',nullable: ${!data['$required']},type:'decimal', precision: 12, scale:${data.props.precision}})
               ${name}: number;\n`;
		} else {
			return ` @Column({ comment: ' ${name}',nullable: ${!data['$required']}})
               ${name}: number;\n`;
		}
	} else if (type === 'input' || type == 'select' || type == 'elTreeSelect' || type == 'radio') {
		if (type == 'radio') {
			console.log('radio单选', data);
		}
		return ` @Column({ comment: '${name}', length: ${data.props.strLength},nullable:${!data['$required']} })
            ${name}: string;\n`;
	} else if (type === 'timePicker' || type === 'datePicker') {
		if (type === 'timePicker') {
			return ` @Column({ comment:  '${name}', type: 'time',nullable: ${!data['$required']}  })
                ${name}: Date;\n`;
		} else {
			if (data.props.datetime) {
				return ` @Column({ comment:  '${name}', type: 'datetime', nullable: ${!data['$required']} })
                ${name}: Date;\n`;
			} else {
				return ` @Column({ comment:  '${name}', type: 'date', nullable: ${!data['$required']} })
                ${name}: Date;\n`;
			}
		}
	} else if (type === 'calculatedCol') {
		const colType = getColType(data.type);
		console.log('colType', colType);
		const sanitizedExpression = sanitizeSqlExpression(data.props.sqlStr);
		if (colType === 'string') {
			return ` @Column({
			type: "varchar",
			generatedType: "VIRTUAL",
			asExpression: \`${sanitizedExpression}\`,
			})
			${name}: string;\n`;
		} else if (colType === 'number') {
			if (data.props.decimals || data.props.precision) {
				return ` @Column({
				type: "decimal",
				generatedType: "VIRTUAL",
				asExpression: \`${sanitizedExpression}\`,
				precision: 12,
				scale: ${data.props.precision}
				})
				${name}: number;\n`;
			} else {
				return ` @Column({
					type: "int",
					generatedType: "VIRTUAL",
					asExpression: \`${sanitizedExpression}\`,
					precision: 12,
					scale: ${data.props.precision}
					})
					${name}: number;\n`;
			}
		} else if (colType === 'date') {
			if (data.props.dateType == 'datetime') {
				return ` @Column({
					type: "datetime",
					generatedType: "VIRTUAL",
					asExpression: \`${sanitizedExpression}\`,
					})
					${name}: Date;\n`;
			} else {
				return ` @Column({
					type: "date",
					generatedType: "VIRTUAL",
					asExpression: \`${sanitizedExpression}\`,
					})
					${name}: Date;\n`;
			}
		}
	} else if (type == 'tableForm') {
		return ` @Column({ comment: '${name}',type: 'json',nullable:${!data['$required']} })
            ${name}: string;\n`;
	} else if (type == 'switch') {
		return ` @Column({ comment: '${name}',type: 'boolean',nullable:${!data['$required']} })
            ${name}: boolean;\n`;
	} else if (type == 'checkbox') {
		return ` @Column({ comment: '${name}',type: 'json',nullable:${!data['$required']} })
            ${name}: string;\n`;
	} else if (type == 'upload') {
		return ` @Column({ comment: '${name}',type: 'json',nullable:${!data['$required']} })
            ${name}: string;\n`;
	}
}

export function getCrudContent(name) {
	return `import { CloudCrud } from '@cool-midway/cloud';\n\n/**\n * 描述\n */\nexport class ${name} extends CloudCrud {\n  async main() {\n    this.setCurdOption({\n      entity: ${name},\n      api: ['add', 'delete', 'update', 'info', 'list', 'page']\n    });\n  }\n}\n`;
}

//获取主键
export function getColInfo(rules, colLengthList?) {
	const isPrimaryKey: string[] = [];
	console.log('处理rules', typeof rules, rules);
	rules.map(item => {
		if (item.props && item.props.hasDict && item.props.hasDict[0] == 'hasDict') {
			if (!item.props.strLength) item.props.strLength = 200;
			if (colLengthList && colLengthList[item.props.fieldName]) {
				item.props.strLength = colLengthList[item.props.fieldName];
			}
			if (item.props.isPrimaryKey && item.props.isPrimaryKey[0] == 'isPrimaryKey') {
				if (item.props.isProject) {
					isPrimaryKey.push('projectCode');
				} else {
					isPrimaryKey.push(`${item.props?.fieldName}代码`);
				}
			}
		} else if (
			item.type == 'input' ||
			item.type == 'select' ||
			item.type == 'elTreeSelect' ||
			item.type == 'upload' ||
			item.type == 'radio'
		) {
			if (
				item.props &&
				item.props.isPrimaryKey &&
				item.props.isPrimaryKey[0] == 'isPrimaryKey'
			) {
				isPrimaryKey.push(item.props?.fieldName);
			}
			if (item.props && item.props.maxlength) {
				item.props.strLength = item.props.maxlength;
				if (colLengthList && colLengthList[item.props.fieldName]) {
					item.props.strLength = colLengthList[item.props.fieldName];
				}
			} else {
				console.log('区分列类型', item.title, item);
				if (item.props) {
					item.props.strLength = 100;
				} else {
					item.props = {
						strLength: 100
					};
				}
			}
		} else {
			if (
				item.props &&
				item.props?.isPrimaryKey &&
				item.props?.isPrimaryKey[0] == 'isPrimaryKey'
			) {
				isPrimaryKey.push(item.props?.fieldName);
			}
		}
	});
	return { rules, isPrimaryKey };
}

//判断字段类型
export function getColType(type) {
	switch (type) {
		case 'input':
		case 'select':
		case 'elTreeSelect':
		case 'tree':
		case 'radio':
		case 'checkbox':
		case 'switch':
		case 'cascader':
		case 'upload':
			return 'string';
		case 'inputNumber':
		case 'slider':
			return 'number';
		case 'datePicker':
		case 'timePicker':
			return 'date';
		case 'radio':
		case 'checkbox':
		case 'switch':
			return 'string';
		default:

		// 当 expression 不等于任何 case 时执行的代码
	}
}

export function flattenFormDataWithLevel(formData, mode?) {
	const result = [];

	function traverse(items, level = 0, parent = null) {
		if (!Array.isArray(items)) return;

		items.forEach(item => {
			const flattenedItem = {
				...item,
				level,
				parentType: parent?.type || null
			};

			// 条件优先级：tableForm 或普通字段
			if (item.type === 'tableForm' || item.field) {
				result.push(flattenedItem);
			}

			// 如果不是 tableForm，递归处理子节点
			if (item.type !== 'tableForm' && item.children) {
				traverse(item.children, level + 1, item);
			}
		});
	}

	traverse(formData);
	return result;
}

//检查列名重复
export function checkForDuplicateColumns(flattenedData, mode) {
	const valueCounts = {};
	const duplicates = [];

	flattenedData.forEach(item => {
		// 新增模式：检查 title 是否重复
		if (mode === '新增') {
			const title = item.title;
			if (!title) return; // 如果没有 title，跳过检查
			valueCounts[title] = (valueCounts[title] || 0) + 1;
			if (valueCounts[title] === 2) {
				duplicates.push(title);
			}
		}
		// 编辑模式：检查 field 是否重复
		else if (mode === '编辑') {
			//		console.log('编辑模式检查field', item, item.field);
			const fieldName = item.props?.fieldName;
			if (!fieldName) return; // 如果没有 field，跳过检查
			valueCounts[fieldName] = (valueCounts[fieldName] || 0) + 1;
			if (valueCounts[fieldName] === 2) {
				duplicates.push(fieldName);
			}
		}
	});
	//	console.log('检查列名重复结果', valueCounts, duplicates);
	return {
		hasDuplicates: duplicates.length > 0,
		duplicates
	};
}

export function traverseNode(formData, mode?) {
	console.log('新增遍历节点', formData, mode);
	// 深拷贝原始数据，避免直接修改
	const processedData = JSON.parse(JSON.stringify(formData));

	/**
	 * 递归遍历并处理字段
	 * @param {Array} items 当前层级的节点数组
	 */
	function traverse(items) {
		if (!Array.isArray(items)) return;

		items.forEach(item => {
			if (item.type == 'input') {
				if (item.props) {
					item.props.type = 'text';
				} else {
					item.props = {
						type: 'text'
					};
				}
			}
			if (mode == '新增' && item.title) {
				console.log('flattenedItem新增遍历', item.title, item);
				if (item.props) {
					item.props.fieldName = item.title;
				} else {
					item.props = {
						fieldName: item.title
					};
				}
				console.log('flattenedItem新增遍历后', item);
			} else {
				if (item.title) {
					item.props = {
						...item.props,
						fieldName: item.props?.fieldName || item.title
					};
				}
			}
			//console.log('新增遍历子节点', item);
			// 递归处理子节点
			if (item.children && item.type && item.type != 'tableForm') {
				traverse(item.children);
			}
		});
	}

	traverse(processedData);
	return processedData;
}
