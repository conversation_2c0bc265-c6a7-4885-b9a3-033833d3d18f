import fs from "file-saver";
import * as XLSX from "xlsx/dist/xlsx.full.min.js";

export const ExportExcel = (json: any, fields: any, filename: any) => {
	console.log("json00", json);

	const sheetName = filename; //excel的文件名称
	const wb = XLSX.utils.book_new(); //工作簿对象包含一SheetNames数组，以及一个表对象映射表名称到表对象。XLSX.utils.book_new实用函数创建一个新的工作簿对象。
	const ws = XLSX.utils.json_to_sheet(json, { header: fields }); //将JS对象数组转换为工作表。
	console.log("json", json);

	ws["!cols"] = [
		{ wch: 20 },
		{ wch: 20 },
		{ wch: 20 },
		{ wch: 20 },
		{ wch: 20 },
		{ wch: 20 },
		{ wch: 20 },
		{ wch: 20 },
		{ wch: 20 },
		{ wch: 20 },
		{ wch: 20 },
		{ wch: 20 },
		{ wch: 20 }
	];

	wb.SheetNames.push(sheetName);
	wb.Sheets[sheetName] = ws;
	const dataInfo: any = wb.Sheets[wb.SheetNames[0]];
	for (const i in dataInfo) {
		if (i == "!ref" || i == "!merges" || i == "!cols" || i == "!rows") {
		} else {
			dataInfo[i + ""].s = {
				alignment: {
					horizontal: "center",
					vertical: "center"
				},
				font: {
					name: "宋体",
					sz: 12
				}
			};
		}
	}
	console.log("dataInfo", dataInfo);

	const defaultCellStyle = {
		font: { name: "宋体", sz: 12 },
		//fill: { fgColor: { rgb: "FFFFAA00" } },
		alignment: { horizontal: "center", vertical: "center", wrap_text: true }
	}; //设置表格的样式

	const wopts = {
		bookType: "xlsx",
		bookSST: false,
		type: "binary",
		cellStyles: true,
		defaultCellStyle: defaultCellStyle,
		showGridLines: false
	}; //写入的样式
	const wbout = XLSX.write(wb, wopts);
	const blob = new Blob([s2ab(wbout)], { type: "application/octet-stream" });
	fs.saveAs(blob, filename + ".xlsx");
};
const s2ab = s => {
	if (typeof ArrayBuffer !== "undefined") {
		const buf = new ArrayBuffer(s.length);
		const view = new Uint8Array(buf);
		for (let i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
		return buf;
	} else {
		const buf = new Array(s.length);
		for (let i = 0; i != s.length; ++i) buf[i] = s.charCodeAt(i) & 0xff;
		return buf;
	}
};
