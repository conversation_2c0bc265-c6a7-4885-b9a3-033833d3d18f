interface TreeNode {
	id: number;
	name: string;
	value: string;
	typeId: number;
	children?: TreeNode[];
}

export function filterAndBuildTree(data: any[], projectCode: string): TreeNode | null {
	// 创建一个映射来加速查找
	const dataMap = new Map(data.map(item => [item.id.toString(), item]));

	// 找到匹配的根节点
	const root = data.find(item => item.value === projectCode);
	if (!root) return null;

	// 构建树节点
	const buildNode = (node: any): TreeNode => {
		const treeNode: TreeNode = {
			id: node.id,
			name: node.name,
			value: node.value
		};

		// 查找子节点
		const children = data.filter(item => item.parentId === node.id.toString());
		if (children.length > 0) {
			treeNode.children = children.map(buildNode);
		}

		return treeNode;
	};

	return buildNode(root);
}

export function buildTree(data: any[]): TreeNode[] {
	const nodeMap = new Map<string, TreeNode>();
	const rootNodes: TreeNode[] = [];

	// 首先创建所有节点，并确保每个节点都有一个 children 属性
	data.forEach(item => {
		const node: TreeNode = {
			id: item.id.toString(),
			name: item.name,
			typeId: item.typeId,
			value: item.value,
			children: []
		};
		nodeMap.set(node.id, node);
	});

	// 然后构建树结构
	data.forEach(item => {
		const node = nodeMap.get(item.id.toString())!;
		if (item.parentId === null || item.parentId === undefined) {
			// 如果 parentId 为 null 或 undefined，视为根节点
			rootNodes.push(node);
		} else {
			const parentNode = nodeMap.get(item.parentId.toString());
			if (parentNode) {
				parentNode.children!.push(node);
			} else {
				// 如果 parentId 不存在于 nodeMap 中，视为根节点
				rootNodes.push(node);
			}
		}
	});

	return rootNodes;
}
