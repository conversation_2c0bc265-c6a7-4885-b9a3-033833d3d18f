import { openDB } from "idb";
import { useCool } from "/@/cool";
import { useBase } from "/$/base";
import CryptoJS from "crypto-js";

// 添加加密解密工具函数
function encrypt(data: any, username: string) {
	const jsonStr = JSON.stringify(data);
	return CryptoJS.AES.encrypt(jsonStr, username).toString();
}

function decrypt(encryptedData: string, username: string) {
	//	console.log("解密", encryptedData, username);
	const bytes = CryptoJS.AES.decrypt(encryptedData, username);
	return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
}

// 初始化数据库
export async function initDB() {
	return await openDB("dictDB", 1, {
		upgrade(db) {
			if (!db.objectStoreNames.contains("dict")) {
				db.createObjectStore("dict");
			}
		}
	});
}

// 从 IndexDB 获取字典数据
export async function getDictFromIndexDB(dictId: string | number | (string | number)[]) {
	try {
		const { service } = useCool();
		const { user } = useBase();
		const username = user?.info?.username;
		const db = await initDB();

		// 处理数组类型的 dictId
		if (Array.isArray(dictId)) {
			console.log("多个id", dictId);
			const result = {};
			const arrIds: any[] = [];
			const dicts: any[] = [];
			// 一次性获取所有数据并解密
			for (const id of dictId) {
				const encryptedData = await db.get("dict", id.toString());

				if (encryptedData) {
					if (
						Date.now() - encryptedData.timestamp <
						import.meta.env.VITE_CACHE_EXPIRATION_TIME
					) {
						// 只解密一次
						result[id] = await decrypt(encryptedData.value, username || "");
						//result[id] = res;
						//const ll = JSON.stringify(res);
						console.log("ll", dicts);
					} else {
						arrIds.push(id);
					}
				}
				console.log("dicts", dicts, result[id]);
				//result[id] = [];
			}

			// 处理过期数据
			if (arrIds.length > 0) {
				const versions = await service.dict.type.getVersions({ typeIds: arrIds });
				for (const id of arrIds) {
					const data = await db.get("dict", id.toString());
					if (data && versions[id] && data.timestamp > new Date(versions[id]).getTime()) {
						// 只解密一次

						result[id] = await decrypt(data.value, username || "");
						await db.put(
							"dict",
							{
								...data,
								timestamp: Date.now()
							},
							id.toString()
						);
					}
				}
			}

			return dictId.every(id => result[id]) ? result : null;
		}
		// 处理单个 dictId
	} catch (error) {
		console.error("从IndexDB获取数据失败:", error);
		return null;
	}
}

// 保存字典数据到 IndexDB
export async function saveDictToIndexDB(dictId: string | number | (string | number)[], data: any) {
	try {
		const db = await initDB();
		const { user } = useBase();
		const username = user?.info?.username;
		// 处理数组类型的 dictId
		if (Array.isArray(dictId)) {
			await Promise.all(
				dictId.map(async id => {
					const encryptedValue = encrypt(data[id], username || "");
					await db.put(
						"dict",
						{
							value: encryptedValue,
							timestamp: Date.now()
						},
						id.toString()
					);
				})
			);
		}
		// 处理单个 dictId
		else {
			const encryptedValue = encrypt(data[dictId], username || "");
			await db.put(
				"dict",
				{
					value: encryptedValue,
					timestamp: Date.now()
				},
				dictId.toString()
			);
		}
	} catch (error) {
		console.error("保存数据到IndexDB失败:", error);
	}
}

// 清空字典数据库
export async function clearDictDB() {
	try {
		const db = await initDB();
		await db.clear("dict");
		console.log("数据库清空成功");
	} catch (error) {
		console.error("清空数据库失败:", error);
	}
}
