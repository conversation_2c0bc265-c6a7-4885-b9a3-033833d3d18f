import { flattenFormDataWithLevel } from './designTable';
export function changeRules(rules) {
	// console.log("开始",rules[0])
	const newRules = flattenFormDataWithLevel(rules);
	//console.log('扁平化后', newRules);
	//console.log(newRules)
	const col: any[] = [];
	newRules.map(item => {
		// 处理普通字段
		if (item.type !== 'tableForm') {
			col.push({
				name: item.props?.fieldName || item.title,
				showName: item.title,
				selected: true,
				...getType(item)
			});
		}
		// 处理表单类型的字段
		else {
			// 遍历子表单中的columns
			col.push({
				name: item.props?.fieldName || item.title,
				showName: item.title,
				selected: true,
				...getType(item)
			});

			item.props.columns.forEach(column => {
				// 处理每个column中的rule
				column.rule.forEach(ruleItem => {
					col.push({
						name: column.label,
						showName: column.label,
						selected: false,
						isTableForm: true,
						tableFormName: item.props?.fieldName || item.title,
						...getType(ruleItem)
					});
				});
			});
		}
	});
	//console.log('完成列', col);
	return col;
}
function getType(item) {
	const obj: any = {};
	if (item.type === 'select' || item.type === 'elTreeSelect') {
		if (item.props.hasDict?.[0] == 'hasDict') {
			obj.type = 'dict';
			obj.dictId = item.props.dictId;
			obj.hasDict = true;
			obj.dictName = item.props.dictName;
			obj.disabled = true;
			obj.dictField = item.props.dictField;
			if (item.props.isProject) {
				obj.isProject = true;
				obj.isPrimaryCol = true;
			} else if (item.props.isPrimaryKey) {
				obj.isPrimaryCol = item.props.isPrimaryKey;
			}
			if (item.type === 'elTreeSelect') {
				obj.isLeaf = true;
				obj.parentId = item.props.parentId;
			}
		} else {
			obj.type = 'string';
			obj.isNull = !item['$required'];
			obj.hasDict = false;
			if (item.props.isPrimaryKey && item.props.isPrimaryKey[0] == 'isPrimaryKey') {
				obj.isPrimaryCol = true;
			}
		}
	} else if (item.type === 'input') {
		obj.type = 'string';
		obj.isNull = !item['$required'];
		obj.hasDict = false;
		//	console.log('item', item);
		if (item.props && item.props.isPrimaryKey && item.props.isPrimaryKey[0] == 'isPrimaryKey') {
			obj.isPrimaryCol = true;
		}
	} else if (item.type === 'inputNumber') {
		obj.isNull = !item['$required'];
		obj.type = 'number';
		obj.hasDict = false;
		if (item.props && item.props.precision) {
			obj.decimals = item.props.precision;
		}
		if (item.props && item.props.isPrimaryKey && item.props.isPrimaryKey[0] == 'isPrimaryKey') {
			obj.isPrimaryCol = true;
		}
	} else if (item.type === 'datePicker') {
		obj.isNull = !item['$required'];
		obj.type = 'date';
		obj.hasDict = false;
		obj.format = item.props.format;
		if (item.props && item.props.isPrimaryKey && item.props.isPrimaryKey[0] == 'isPrimaryKey') {
			obj.isPrimaryCol = true;
		}
		if (item.props && item.props.dateType) {
			switch (item.props.dateType) {
				case 'date':
					obj.dateType = 'YYYY-MM-DD';
					break;
				case 'month':
					obj.dateType = 'YYYY-MM';
					break;
				case 'year':
					obj.dateType = 'YYYY';
					break;
				case 'datetime':
					obj.dateType = 'YYYY-MM-DD HH:mm:ss';
					break;
			}
		}
	} else if (item.type === 'upload') {
		obj.type = 'upload';
		obj.hasDict = false;
		obj.listType = item.props.listType;
		obj.isNull = !item['$required'];
	} else if (item.type === 'tableForm') {
		obj.type = 'string';
		obj.subType = item.type;
		obj.hasDict = false;
		obj.tableForm = true;
		obj.isNull = !item['$required'];
	} else if (item.type === 'radio' || item.type === 'checkbox' || item.type == 'switch') {
		obj.type = 'string';
		obj.subType = item.type;
		obj.isNull = !item['$required'];
	}
	return obj;
}
