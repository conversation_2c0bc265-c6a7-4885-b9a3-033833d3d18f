//sql函数操作
export const operData1 = [
	{
		name: "截取(left)",
		props: "left",
		msg: {
			eg: "left(a,length):length为截取位数，如left(34212,2),得到34",
			info: "返回参与字段从左至右截取位数后的值,,length可以为0"
		},
		type: "number"
	},
	{
		name: "取模(mod)",
		props: "mod",
		msg: { eg: "mod(a,b)", info: "返回参与的两个字段的取余值，只能用于数字字段" },
		type: "number"
	},
	{
		name: "四舍五入(round)",
		props: "round",
		msg: {
			eg: "round(a)：对非整数进行四舍五入，如round(2.6543),得到3；round(a,n):n为保留小数的位数，如round(2.6542,2),得到2.65",
			info: "返回参与字段四舍五入后的值,只能用于数字字段"
		},
		type: "number"
	},

	{
		name: "截取小数(truncate)",
		props: "truncate",
		msg: {
			eg: "truncate(a,n):n为保留小数的位数，如truncate(2.6542,1),得到2.6，不进行四舍五入",
			info: "返回参与字段截取小数后的值,只能用于数字字段,n可以为0"
		},
		type: "number"
	},
	{
		name: "次方(power)",
		props: "power",
		msg: { eg: "power(a,n)", info: "返回参与字段的n次方,只能用于数字字段" },
		type: "number"
	},
	{
		name: "截取位数(substr)",
		props: "substr",
		msg: {
			eg: "substr(a,n1,n2),如substr('编码表',1,2)，得到'编码'",
			info: "返回参与的字段截取开头位置n1到末尾位置n2"
		},
		type: "string"
	},
	{
		name: "截取(left)",
		props: "left",
		msg: {
			eg: "left(a,length):length为截取位数，如left('文字测试',2),得到'文字'",
			info: "返回参与字段从左至右截取位数后的值,,length可以为0"
		},
		type: "string"
	},
	{
		name: "截取(right)",
		props: "right",
		msg: {
			eg: "right(a,length):length为截取位数，如right('文字测试',2),得到'测试'",
			info: "返回参与字段从右至左截取位数后的值,,length可以为0"
		},
		type: "string"
	},
	{
		name: "获取长度(length)",
		props: "length",
		msg: {
			eg: "length(a):如length('abcd'),得到'4'",
			info: "返回参与字段的字符长度"
		},
		type: "string"
	},
	{
		name: "字符串拼接(concat)",
		props: "concat",
		msg: {
			eg: "concat(a,b),concat('编码','字段'),得到'编码字段'",
			info: "返回参与的字符串a,b拼接后的值"
		},
		type: "string"
	},
	{
		name: "字符串替换(replace)",
		props: "replace",
		msg: {
			eg: "replace(a, 要替换内容, 新内容 ),replace('编码','编','字段'),得到:'字段码'",
			info: "返回参与的字符串a中要替换内容替换为新内容后的值"
		},
		type: "string"
	},
	{
		name: "包含(like)",
		props: "like",
		msg: { eg: "a like '%b%'", info: "返回所有参与字符串a中包含'b'的值" },
		type: "string"
	},
	{
		name: "不包含(not like)",
		props: "not like",
		msg: { eg: "a not like '%b%'", info: "返回所有参与字符串a中不包含'b'的值" },
		type: "string"
	},
	{
		name: "文本转日期(convert)",
		props: "convert",
		msg: {
			eg: "convert('2023/05/01',date)",
			info: "将文本转化为日期格式"
		},
		type: "string"
	},

	{
		name: "日期相差(datediff)",
		props: "datediff",
		msg: {
			eg: "datediff(day,date1,date2),datediff(day,'2020-01-01','2020-01-02') 得到:1",
			info: "返回参与日期的差值，单位为天，小日期在前，大日期在后"
		},
		type: "date"
	},
	{
		name: "获取当前日期(CURDATE)",
		props: "CURDATE()",
		msg: { eg: "CURDATE(),得到2022-05-02", info: "返回当前时间" },
		type: "date"
	},
	{
		name: "增加天数(date_add)",
		props: "date_add",
		msg: {
			eg: "date_add(date1,INTERVAL 2 DAY),date_add(2022-02-01,INTERVAL 2 DAY),得到:2022-02-03",
			info: "返回参与日期加上2天后的日期"
		},
		type: "date"
	},
	{
		name: "截取当前日期年份(year)",
		props: "year",
		msg: { eg: "year(date1),year(2022-02-10),得到:2022", info: "返回当前日期的年份" },
		type: "date"
	},
	{
		name: "截取当前日期月份(month)",
		props: "month",
		msg: { eg: "month(date1),month(2022-02-10),得到:2", info: "返回当前日期的月份" },
		type: "date"
	}
];

//sql聚合函数
export const aggregatedOperData = [
	{
		name: "求和(SUM)",
		props: "SUM",
		msg: { eg: "SUM(a)", info: "对字段进行求和" },
		type: "number"
	},
	{
		name: "平均值(AVG)",
		props: "AVG",
		msg: { eg: "AVG(a)", info: "对字段取平均这" },
		type: "number"
	},
	{
		name: "计数(COUNT)",
		props: "COUNT",
		msg: { eg: "COUNT(a)", info: "对字段进行计数" },
		type: "number"
	},
	{
		name: "最大值(MAX)",
		props: "MAX",
		msg: { eg: "MAX(a)", info: "取字段中的最大值" },
		type: "number"
	},
	{
		name: "最小值(MIN)",
		props: "MIN",
		msg: { eg: "MIN(a)", info: "取字段中的最小值" },
		type: "number"
	},
	{
		name: "方差(VARIANCE) ",
		props: "VARIANCE",
		msg: { eg: "VARIANCE(表达式)", info: "求字段的方差" },
		type: "number"
	},
	{
		name: "标准差(STDDEV) ",
		props: "STDDEV",
		msg: { eg: "SUM(a)", info: "求字段的标准差" },
		type: "number"
	},
	{
		name: "截取位数(substr)",
		props: "substr",
		msg: { eg: "substr(a,n1,n2)", info: "返回参与的字段截取开头位置n1到末尾位置n2" },
		type: "string"
	},
	{
		name: "字符串拼接(concat)",
		props: "concat",
		msg: { eg: "concat(a,b)", info: "返回参与的字符串a,b拼接后的值" },
		type: "string"
	},
	{
		name: "字符串替换(replace)",
		props: "replace",
		msg: {
			eg: "replace(a, 要替换内容, 新内容 )",
			info: "返回参与的字符串a中要替换内容替换为新内容后的值"
		},
		type: "string"
	}
];
