export function searchByField(data, queries) {
	console.log('搜索函数', data, queries);

	return data.filter(item => {
		return queries.every(query => {
			const { selectValue, operation, inputValue } = query;
			const type = selectValue.split('_')[1];
			let subType = '';
			if (selectValue.split('_')[2]) {
				subType = selectValue.split('_')[2];
			}
			const label = selectValue.split('_')[0];
			const itemValue = item[label];

			switch (operation) {
				case '':
					// 处理字符串包含和状态等于的情况
					if (type == 'string' || type == 'checkUser' || type == 'dict') {
						if (subType) {
							if (subType == 'tableForm') {
								return itemValue.some(item => {
									const hasMatchingKey = Object.keys(item).some(key =>
										key.includes(inputValue)
									);
									const hasMatchingValue = Object.values(item).some(value =>
										String(value).includes(inputValue)
									);
									return hasMatchingKey || hasMatchingValue;
								});
							} else if (subType == 'checkbox') {
								return itemValue.some(value => String(value).includes(inputValue));
							} else if (subType == 'switch') {
								console.log('switch', inputValue);
								return inputValue == '1' || inputValue == 'true';
							}
						}
						return itemValue ? itemValue.includes(inputValue) : false;
					} else if (type == 'checkStatus' || type == 'luruStatus') {
						console.log(
							'状态',
							itemValue ? itemValue == parseInt(inputValue, 10) : false
						);
						if (itemValue == 0) {
							return itemValue == parseInt(inputValue, 10);
						}
						return itemValue ? itemValue == parseInt(inputValue, 10) : false;
					}
					break;
				case '>':
					if (type == 'number' || type == 'money') {
						return itemValue === 0 || itemValue
							? itemValue > parseFloat(inputValue)
							: false;
					} else if (type == 'date' || type == 'checkTime') {
						return itemValue ? new Date(itemValue) > new Date(inputValue) : false;
					}
					break;
				case '>=':
					if (type == 'number' || type == 'money') {
						return itemValue === 0 || itemValue
							? itemValue >= parseFloat(inputValue)
							: false;
					} else if (type == 'date' || type == 'checkTime') {
						return itemValue ? new Date(itemValue) >= new Date(inputValue) : false;
					}
					break;
				case '<':
					if (type == 'number' || type == 'money') {
						return itemValue === 0 || itemValue
							? itemValue < parseFloat(inputValue)
							: false;
					} else if (type == 'date' || type == 'checkTime') {
						return itemValue ? new Date(itemValue) < new Date(inputValue) : false;
					}
					break;
				case '<=':
					if (type == 'number' || type == 'money') {
						return itemValue === 0 || itemValue
							? itemValue <= parseFloat(inputValue)
							: false;
					} else if (type == 'date' || type == 'checkTime') {
						return itemValue ? new Date(itemValue) <= new Date(inputValue) : false;
					}
					break;
				case '=':
					if (type == 'number' || type == 'money') {
						return itemValue === 0 || itemValue
							? itemValue == parseFloat(inputValue)
							: false;
					} else if (type == 'date' || type == 'checkTime') {
						return itemValue
							? new Date(itemValue).getTime() === new Date(inputValue).getTime()
							: false;
					}
					break;
				case '!=':
					if (type == 'number' || type == 'money') {
						return itemValue === 0 || itemValue
							? itemValue != parseFloat(inputValue)
							: false;
					} else if (type == 'date' || type == 'checkTime') {
						return itemValue
							? new Date(itemValue).getTime() !== new Date(inputValue).getTime()
							: false;
					}
					break;
				case 'between':
					// 处理位于两者之间的情况
					if (type == 'number' || type == 'money') {
						console.log('计算-betwen', inputValue);
						const [min, max] = inputValue.split(',').map(parseFloat);
						console.log('计算-betwen', min, max);
						return itemValue ? itemValue > min && itemValue < max : false;
					} else if (type == 'date' || type == 'checkTime') {
						const [min, max] = inputValue.split(',').map(date => new Date(date));
						return itemValue
							? new Date(itemValue) > min && new Date(itemValue) < max
							: false;
					}
					break;
				// 可以根据需要添加更多的操作符
				default:
					return false;
			}
		});
	});
}

export function searchByIds(data, ids) {
	// 确保 ids 是数组
	if (!Array.isArray(ids)) {
		return data;
	}
	console.log('搜索-ids', data, ids);
	// 使用 filter 方法筛选出 id 在 ids 数组中的数据
	return data.filter(item => {
		return ids.includes(String(item.id));
	});
}
