const chineseSymbols =
	/[\u3000\u3001\u3002\u3008-\u3011\u3014-\u301F\uff01-\uff0f\uff1a-\uff20\uff3b-\uff40\uff5b-\uff65]/g;
const englishSymbols: { [key: string]: string } = {
	"\u3000": " ",
	"\u3001": ",",
	"\u3002": ".",
	"\u3008": "<",
	"\u3009": ">",
	"\u300A": "<<",
	"\u300B": ">>",
	"\u300C": "「",
	"\u300D": "」",
	"\u300E": "『",
	"\u300F": "』",
	"\u3010": "[",
	"\u3011": "]",
	"\u3014": "[",
	"\u3015": "]",
	"\u3016": "<<",
	"\u3017": ">>",
	"\u3018": "(",
	"\u3019": ")",
	"\u301A": "<<",
	"\u301B": ">>",
	"\u301C": "~",
	"\u301D": '"',
	"\u301E": '"',
	"\u301F": "_",
	"\uff01": "!",
	"\uff02": '"',
	"\uff03": "#",
	"\uff04": "$",
	"\uff05": "%",
	"\uff06": "&",
	"\uff07": "'",
	"\uff08": "(",
	"\uff09": ")",
	"\uff0A": "*",
	"\uff0B": "+",
	"\uff0C": ",",
	"\uff0D": "-",
	"\uff0E": ".",
	"\uff0F": "/",
	"\uff1A": ":",
	"\uff1B": ";",
	"\uff1C": "<",
	"\uff1D": "=",
	"\uff1E": ">",
	"\uff1F": "?",
	"\uff20": "@",
	"\uff3B": "[",
	"\uff3C": "\\",
	"\uff3D": "]",
	"\uff3E": "^",
	"\uff3F": "_",
	"\uff40": "`",
	"\uff5B": "{",
	"\uff5C": "|",
	"\uff5D": "}",
	"\uff5E": "~",
	"\uff65": "・"
};
export const replaceChineseSymbols = (str: string) => {
	return str.replace(chineseSymbols, (match: string) => englishSymbols[match]);
};
