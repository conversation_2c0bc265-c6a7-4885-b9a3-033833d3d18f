import alasql from "alasql";
import { sanitizeSqlExpression } from "./designTable";

export function validateSqlStatement(sql: string): boolean {
	try {
		sql = sanitizeSqlExpression(sql);
		console.log("修改后sql", sql);
		// 创建一个临时表来模拟数据
		alasql("CREATE TABLE temp (文本 STRING, 数字 NUMBER, 日期 DATE)");

		// 将 CASE 表达式包装在一个 SELECT 语句中
		const wrappedSql = `SELECT ${sql} AS result FROM temp`;

		// 尝试解析 SQL 语句
		alasql.parse(wrappedSql);

		// 如果没有抛出异常，则认为语法正确
		return true;
	} catch (error) {
		console.error("SQL 语法错误:", error);
		return false;
	} finally {
		// 清理临时表
		alasql("DROP TABLE IF EXISTS temp");
	}
}
