<template>
	<cl-crud class="cl-crud">
		<cl-row>
			<fieldSerch
				:columnList="checkColumnList"
				@onSearch="onSearch"
				defaultField="审核状态_checkStatus"
			></fieldSerch>

			<cl-flex1 />
			<el-button type="success" :icon="Refresh" @click="refresh" />
			<el-dropdown v-if="isMultiBtn">
				<el-button type="primary">
					批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
				</el-button>
				<template #dropdown>
					<el-dropdown-menu>
						<el-dropdown-item @click="selectedPass">全部通过</el-dropdown-item>
						<el-dropdown-item @click="selectedReject">全部驳回</el-dropdown-item>
					</el-dropdown-menu>
				</template>
			</el-dropdown>
		</cl-row>
		<cl-row>
			<el-table
				v-show="tableName"
				:data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
				@selection-change="handleSelectionChange"
			>
				<el-table-column type="selection" width="55" />
				<el-table-column
					v-for="(item, index) in columnList"
					:key="index"
					:label="item.showName === 'projectCode' ? '项目代码' : item.showName"
					:prop="item.name"
					:hidden="item.selected"
				>
					<template #default="scope">
						<span v-if="item.type === 'date'">
							{{ formatDate(item.dateType, scope.row[item.name]) }}
						</span>
						<span v-else-if="item.type === 'number' && item.isPercent">
							{{ (scope.row[item.name] * 100).toFixed(2) }}%
						</span>
						<span v-else-if="item.type === 'picture'">
							<img :src="scope.row[item.name]" min-width="70" height="70" />
						</span>
						<span v-else>
							{{ scope.row[item.name] }}
						</span>
					</template>
				</el-table-column>
				<el-table-column label="审核状态" prop="审核状态">
					<template #default="scope">
						<el-tag
							:type="
								scope.row.审核状态 == AUDIT_STATUS.UNAUDITED
									? 'primary'
									: scope.row.审核状态 == AUDIT_STATUS.APPROVED
										? 'success'
										: scope.row.审核状态 == AUDIT_STATUS.REJECTED
											? 'danger'
											: 'info'
							"
							disable-transitions
						>
							{{
								scope.row.审核状态 == AUDIT_STATUS.UNAUDITED
									? '未审核'
									: scope.row.审核状态 == AUDIT_STATUS.APPROVED
										? '已通过'
										: scope.row.审核状态 == AUDIT_STATUS.REJECTED
											? '已驳回'
											: scope.row.审核状态 == AUDIT_STATUS.DRAFT
												? '已暂存'
												: scope.row.审核状态 == AUDIT_STATUS.INVALID
													? '已作废'
													: '已归档'
							}}
						</el-tag></template
					>
				</el-table-column>
				<el-table-column label="录入人" prop="录入人" />
				<el-table-column label="审核人" prop="审核人" />
				<el-table-column label="审核时间" prop="审核时间" />
				<el-table-column label="审核说明" prop="审核说明" />
				<el-table-column label="操作" fixed="right">
					<template #default="scope">
						<el-button
							v-if="scope.row.审核状态 == AUDIT_STATUS.UNAUDITED"
							size="small"
							type="success"
							@click="handlePass(scope.$index, scope.row)"
							style="margin-left: 10px"
						>
							通过
						</el-button>
						<el-button
							v-if="scope.row.审核状态 == AUDIT_STATUS.APPROVED"
							size="small"
							type="info"
							@click="handleOut(scope.$index, scope.row)"
							style="margin-left: 10px"
						>
							归档
						</el-button>
						<el-button
							v-if="scope.row.审核状态 == AUDIT_STATUS.UNAUDITED || allowEdit"
							size="small"
							type="danger"
							style="margin-left: 10px"
							@click="handleReject(scope.$index, scope.row)"
						>
							驳回
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</cl-row>
		<div>
			<el-pagination
				v-model:current-page="currentPage"
				:page-size="pageSize"
				:disabled="disabled"
				layout="total, prev, pager, next"
				:total="tableData.length"
				@current-change="handleCurrentChange"
				style="margin-top: 15px; margin-right: 10px; float: right"
			/>
		</div>
	</cl-crud>
</template>

<script setup lang="ts" name="checkTable-data">
import { watch, toRefs, onMounted, ref, onActivated } from 'vue';
import { useCool } from '/@/cool';
import { useBase } from '/$/base';
import { ElMessageBox } from 'element-plus';
import { cloneDeep } from 'lodash-es';
import { ElMessage } from 'element-plus';
import fieldSerch from '../components/field-serch.vue';
import { searchByField } from '../utils/searchByField';
import { ArrowDown, Refresh } from '@element-plus/icons-vue';
import { PermissionInfo, AUDIT_STATUS } from '../utils/constants';
import { changeRules } from '../utils/luruTable';

const { service, router } = useCool();
const { user } = useBase();
const tableName = ref<string>('');
const metaData = ref([]);
const tableData = ref([]);
const addtableVisible = ref<boolean>(false);
const importtableVisible = ref<boolean>(false);

const NAME = PermissionInfo.SHENHE;

const columnList: any = ref([]);
const checkColumnList: any = ref([]);
const uncheckedNum = ref<number>(0);
const isMultiBtn = ref<boolean>(false);

const currentPage = ref(1);
const pageSize = ref(10);
const disabled = ref(false);

const filename = ref<string>('');
const multipleSelection = ref();
const tableId = ref();
const allowEdit = ref();

onActivated(async () => {
	console.log('router.options.history.state', router.options.history.state);
	if (router.options.history.state.name) {
		tableName.value = router.options.history.state.name as string;
		tableId.value = router.options.history.state.id;
		allowEdit.value = router.options.history.state.allowEdit;

		//columnList.value = JSON.parse(router.options.history.state?.columnList) || [];
		filename.value = router.options.history.state?.name as string;
		uncheckedNum.value = Number(router.options.history.state.uncheckedNum);
		console.log('router传来的columnList', columnList.value);
	}
	refresh();
});

//表格里勾选的数据行
const handleSelectionChange = (val: any) => {
	console.log('已选择的', val);
	multipleSelection.value = val;
	if (val.length > 0) {
		isMultiBtn.value = true;
	} else {
		isMultiBtn.value = false;
	}
};

//切换表格页数
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
};
const searchData = ref([]);
//分字段搜索
function onSearch(data: any) {
	searchData.value = data;
	console.log('搜索数据', searchData.value, metaData.value);
	tableData.value = searchByField(metaData.value, data);
}

//将勾选的数据审核状态改为通过
const selectedPass = async () => {
	ElMessageBox.confirm('此操作会将选择的数据通过审核，是否继续？', '提示', {
		type: 'warning'
	})
		.then(async () => {
			const multiValue: any = [];
			multipleSelection.value.map((item: any) => {
				multiValue.push(item.id);
			});
			console.log('multiValue', multiValue);
			await service.cloud.db
				.audit({
					tableId: tableId.value,
					recordIds: multiValue,
					status: AUDIT_STATUS.APPROVED,
					auditDesc: ' '
				})
				.then(res => {});
			refresh();
		})
		.catch(() => null);
};
//将勾选的数据审核状态改为驳回
const selectedReject = async () => {
	ElMessageBox.prompt('请输入驳回原因', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消'
	})
		.then(async ({ value }) => {
			console.log('驳回信息', value);
			const recordIds: any = [];
			multipleSelection.value.map((item: any) => {
				recordIds.push(item.id);
			});

			await service.cloud.db
				.audit({
					tableId: tableId.value,
					recordIds: recordIds,
					status: AUDIT_STATUS.REJECTED,
					auditDesc: value
				})
				.then(res => {})
				.catch(err => {
					ElMessage.error(err.message);
				});
			refresh();
		})
		.catch(() => null);
};

//通过
async function handlePass(index, row) {
	ElMessageBox.confirm('此操作会将选择的数据通过审核，是否继续？', '提示', {
		type: 'warning'
	})
		.then(async () => {
			await service.cloud.db
				.audit({
					tableId: tableId.value,
					recordIds: [row.id],
					status: AUDIT_STATUS.APPROVED,
					auditDesc: ''
				})

				.then(res => {});

			refresh();
		})
		.catch(() => null);
}

//归档
async function handleOut(index, row) {
	ElMessageBox.confirm('此操作会将选择的数据归档，是否继续？', '提示', {
		type: 'warning'
	})
		.then(async () => {
			await service.cloud.db
				.audit({
					tableId: tableId.value,
					recordIds: [row.id],
					status: AUDIT_STATUS.ARCHIVED,
					auditDesc: ''
				})

				.then(res => {});

			refresh();
		})
		.catch(() => null);
}

//驳回
async function handleReject(index, row) {
	ElMessageBox.prompt('如果确定驳回该数据，请输入驳回原因', '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消'
	})
		.then(async value => {
			console.log('驳回信息', value);
			await service.cloud.db
				.audit({
					tableId: tableId.value,
					recordIds: [row.id],
					status: AUDIT_STATUS.REJECTED,
					auditDesc: value.value
				})
				.then(res => {});
			refresh();
		})
		.catch(() => null);
}

function formatDate() {
	const date = new Date();
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const hours = String(date.getHours()).padStart(2, '0');
	const minutes = String(date.getMinutes()).padStart(2, '0');
	const seconds = String(date.getSeconds()).padStart(2, '0');
	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

const refresh = () => {
	service.cloud.db
		.data({
			id: tableId.value,
			method: 'list',
			params: { formType: NAME }
		})
		.then(res => {
			console.log('结果data', res);
			//columnList.value = res.colInfo;
			columnList.value = changeRules(res.colInfo).filter(item => item.selected);
			console.log('columnList', columnList.value);
			checkColumnList.value = [
				...columnList.value,
				{ name: '审核状态', showName: '审核状态', type: 'checkStatus', selected: true },
				{ name: '审核人', showName: '审核人', type: 'checkUser', selected: true },
				{ name: '审核时间', showName: '审核时间', type: 'checkTime', selected: true },
				{ name: '审核说明', showName: '审核说明', type: 'string', selected: true }
			];
			console.log('checkColumnList', checkColumnList.value);
			if (res.list) {
				metaData.value = res.list.filter(item => {
					return item['审核状态'] != AUDIT_STATUS.DRAFT;
				});
				tableData.value = cloneDeep(metaData.value);
			} else {
				ElMessage.info('暂无数据');
			}
		});
};

const closeDialog = () => {
	addtableVisible.value = false;
	importtableVisible.value = false;
};
</script>
<style scoped lang="scss"></style>
