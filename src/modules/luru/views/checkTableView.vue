<template>
	<cl-view-group ref="ViewGroup">
		<template #left>
			<!-- 部门列表 -->
			<form-type-list
				@refresh="refresh"
				@table-add="onTableAdd"
				@tree-data="onTreeData"
				:isEdit="false"
			/>
		</template>
		<template #right>
			<cl-crud ref="Crud">
				<cl-row>
					<!-- 刷新按钮 -->
					<cl-refresh-btn />
					<!-- 新增按钮 -->

					<cl-flex1 />
					<!-- 关键字搜索 -->
				</cl-row>

				<cl-row>
					<el-table
						:data="
							tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)
						"
					>
						<el-table-column label="表名" prop="name" />
						<el-table-column label="未审核数量" prop="uncheckedNum" />
						<el-table-column label="操作">
							<template #default="scope">
								<el-button
									size="small"
									type="success"
									@click="openTableData(scope.row)"
									style="margin-left: 10px"
								>
									开始审核
								</el-button>
							</template>
						</el-table-column>
					</el-table>
				</cl-row>
				<div>
					<el-pagination
						v-model:current-page="currentPage"
						:page-size="pageSize"
						:disabled="disabled"
						layout="total, prev, pager, next"
						:total="tableData.length"
						@current-change="handleCurrentChange"
						style="margin-top: 15px; margin-right: 10px; float: right"
					/>
				</div>
				<!-- 新增、编辑 -->
				<cl-upsert ref="Upsert">
					<template #slot-value="{ scope }">
						<div class="form-value">
							<el-input
								v-model="scope.value"
								placeholder="请填写代码"
								clearable
								type="textarea"
								:rows="4"
							/>
							<div class="op"></div>
						</div>
					</template>
				</cl-upsert>
			</cl-crud>
		</template>
	</cl-view-group>
	<el-dialog v-model="tableListVisible">
		<el-form-item label="选择表单">
			<el-select></el-select>
		</el-form-item>
	</el-dialog>
	<form-type-transfer
		:dialog-visible="dialogVisible"
		:typeName="typeName"
		:form-list="formTransferData"
		@dialog-close="dialogClose"
		@submit="dialogSubmit"
	/>
</template>
<script lang="ts" name="checkTableView" setup>
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { useBase } from '/$/base';
import { ref, onMounted } from 'vue';
import { useViewGroup } from '/@/plugins/view';
import { ElMessage } from 'element-plus';
import FormTypeList from '../components/formTypeList.vue';
import FormTypeTransfer from '../components/formTypeTransfer.vue';
import { PermissionInfo } from '../utils/constants';

const { service, router } = useCool();
const dialogVisible = ref(false);
const formList: any = ref([]);
const formTransferData: any = ref([]);
const { user } = useBase();
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const disabled = ref(false);
// 常量定义
const NAME = PermissionInfo.LURU;

let promission;
const formPermissions = user?.userPromission[NAME];
console.log('formPermissions', user?.userPromission, formPermissions);
if (formPermissions['ALL'] && formPermissions['ALL'][0] == 'ALL') {
	promission = ['ALL'];
} else {
	promission = formPermissions[router.options.history.state.id] || '';
}

onMounted(async () => {});

const typeId = ref();
const typeName = ref('');
const tableListVisible = ref<boolean>(false);
const Table = useTable({
	contextMenu: [],
	columns: [
		{ label: '序号', prop: 'id', minWidth: 60 },
		{ label: '名称', prop: 'name', align: 'left', minWidth: 220 },
		{ label: '备注', prop: 'readme', showOverflowTooltip: true, minWidth: 150 },
		// 启用状态显示成el-switch，不可编辑
		{
			label: '启用状态',
			prop: 'status',
			showOverflowTooltip: true,
			minWidth: 60,
			dict: [
				{
					label: '启用',
					value: 1,
					type: 'success'
				},
				{
					label: '禁用',
					value: 0,
					type: 'danger'
				}
			]
		},
		{ label: '创建时间', prop: 'createTime', sortable: 'custom', minWidth: 100 },

		{
			type: 'op',
			minWidth: 120,
			buttons({ scope }) {
				return ['slot-btn'];
			}
		}
	]
});

const onTableAdd = () => {
	tableListVisible.value = true;
};
const onTreeData = list => {
	console.log('list', list);
};
const openTableData = async row => {
	console.log('row跳转', row, row.tableId);
	router.push({
		path: '/checkTable/data',
		state: {
			id: row.tableId,
			name: row.name,
			columnList: [],
			//columnList: JSON.stringify(res[0].colInfo),
			uncheckedNum: row.uncheckedNum,
			allowEdit: row.tableInfo.form?.tableInfo?.includes('allowEdit') || false
		}
	});
};
const dialogSubmit = value => {
	dialogClose();
	console.log('dialogSubmit', value, typeId.value);
	const updateData: any = [];
	value.map(item => {
		updateData.push({
			id: item,
			formTypeId: typeId.value
		});
	});
	service.cloud.db.update(updateData).then(res => {
		console.log('res', res);
	});
};
const dialogClose = () => {
	dialogVisible.value = false;
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
};
const { ViewGroup } = useViewGroup({
	label: '类型',
	title: '表单',
	service: service.cloud.formType,
	onSelect(item) {
		console.log('点击item', item);
		typeId.value = item.id;
		typeName.value = item.name;
		// refresh({
		// 	formType: item.id,
		// 	page: 1
		// });
	},
	onData(list) {
		console.log('list', list);
		return list;
	},
	onEdit(item) {
		console.log(item);
		return {};
	}
});

function onRowClick(row: any, column: any) {
	if (column?.property && row.children) {
		Table.value?.toggleRowExpansion(row);
	}
}

async function refresh(params?: any) {
	const res = await service.cloud.db.uncheckedDetail({ formTypeId: typeId.value });
	const dataMap = {};
	tableData.value = [];
	res.list.forEach(item => {
		if (item.tableName in dataMap) {
			dataMap[item.tableName].uncheckedNum += item.uncheckedNum;
		} else {
			dataMap[item.tableName] = {
				uncheckedNum: item.uncheckedNum,
				tableId: item.tableId,
				tableInfo: item.tableInfo
			};
		}
	});
	console.log('dataMap', dataMap);
	if (Object.keys(dataMap).length > 0) {
		Object.entries(dataMap).forEach(([name, data]) => {
			tableData.value.push({
				name,
				uncheckedNum: data.uncheckedNum,
				tableId: data.tableId,
				tableInfo: data.tableInfo
			});
		});
	}
	console.log('tableData', tableData.value);
}
</script>
