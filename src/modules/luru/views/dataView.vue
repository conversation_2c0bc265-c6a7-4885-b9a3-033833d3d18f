<!-- 跳转查看data库中表单数据 -->
<template>
	<cl-crud class="cl-crud">
		<cl-row style="align-items: center">
			<cl-flex1 />
			<el-button type="primary" @click="operationDescVisible = true" v-if="operationDesc"
				><el-icon
					:size="18"
					class="no-inherit"
					@click="operationDescVisible = true"
					style="margin-right: 5px"
				>
					<document /> </el-icon
				>操作说明</el-button
			>
			<!-- <el-tooltip content="操作说明" placement="top" v-if="operationDesc">
				<el-button
					type="primary"
					style="margin-right: 20px"
					@click="operationDescVisible = true"
				>
					操作说明
				</el-button>
				<el-icon
					color="#4165d7"
					:size="22"
					class="no-inherit"
					@click="operationDescVisible = true"
					style="margin-right: 20px"
				>
					<document />
				</el-icon>
			</el-tooltip> -->
			<el-button :icon="Download" @click="exportExcel" type="primary">导出</el-button>
		</cl-row>

		<el-table
			:data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
			stripe
			border
		>
			<el-table-column
				v-for="(item, index) in columns"
				:key="index"
				:label="item.label"
				:prop="item.prop"
				:min-width="getColumnWidth(item.label)"
			>
				<template #default="scope">
					<el-tooltip
						:content="scope.row[item.prop]"
						popper-style="width:300px;"
						placement="top"
						:show-after="100"
						effect="dark"
						:enterable="true"
						:popper-class="getTooltipClass(scope.row[item.prop])"
						v-if="scope.row[item.prop]?.length > item.label.length"
					>
						<span class="ellipsis-text">{{ scope.row[item.prop] }}</span>
					</el-tooltip>
					<span v-else>{{ scope.row[item.prop] }}</span>
				</template>
			</el-table-column>
		</el-table>

		<el-pagination
			v-model:current-page="currentPage"
			style="margin-top: 15px"
			:page-size="10"
			:total="total"
			background
			layout="prev, pager, next"
			@current-change="handleCurrentChange"
		/>
		<cl-dialog v-model="operationDescVisible" title="操作 说明" width="50%">
			<div class="notice-preview">
				<h3 class="preview-title">
					{{ tableName }}
				</h3>

				<div class="preview-content" v-html="operationDesc"></div>
			</div>
			<template #footer>
				<div style="display: flex; justify-content: flex-end">
					<el-button @click="operationDescVisible = false">关闭</el-button>
					<el-button type="primary" @click="downloadPDF">
						<el-icon style="margin-right: 5px"><download /></el-icon>
						下载PDF
					</el-button>
				</div>
			</template>
		</cl-dialog>
	</cl-crud>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useCool } from '/@/cool';
import { ElMessage } from 'element-plus';
import { Document, Download } from '@element-plus/icons-vue';
import { ExportExcel } from '/@/modules/luru/utils/exportExcel';

const { service, router } = useCool();
const tableData = ref([]);
const columns = ref<any[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableName = ref('');
const operationDesc = ref('');
const operationDescVisible = ref(false);

// 计算列宽度的函数
function getColumnWidth(label: string) {
	const baseWidth = 10;
	const charWidth = /[\u4e00-\u9fa5]/.test(label) ? 18 : 10;
	const width = baseWidth + label.length * charWidth;
	return Math.max(width, 120);
}

function handleCurrentChange(page: number) {
	currentPage.value = page;
}

onMounted(() => {
	tableName.value = router.options.history.state.id as string;
	if (router.options.history.state.tableType == 'system') {
		service.cloud.db.list({ id: router.options.history.state.id }).then((res: any) => {
			operationDesc.value = res[0].operationDesc;
			console.log('operationDesc', operationDesc.value);
		});
	} else {
		service.cloud.dataOperationDesc
			.list({
				tableName: tableName.value
			})
			.then((res: any) => {
				operationDesc.value = res[0].operationDesc;
				console.log('operationDesc', operationDesc.value);
			});
	}

	getTableData();
});

async function getTableData() {
	try {
		const res = await service.quality.check.outterRecords({
			ruleId: Number(router.options.history.state.ruleId),
			rowIds: JSON.parse(router.options.history.state.rowIds) as string[]
		});
		tableData.value = res;
		console.log('tableData', tableData.value);
		if (res.length > 0) {
			total.value = res.length;
			columns.value = Object.keys(res[0])
				.filter(key => key !== 'tenantId') // 先过滤不需要的键
				.map(key => ({ label: key, prop: key }));
		}

		console.log('columns', columns.value);
	} catch (error) {
		console.error('获取数据失败:', error);
		ElMessage.error('获取数据失败' + error.message);
		return;
	}
}

async function exportExcel() {
	const columns1 = columns.value.map(item => item.label);
	ExportExcel(tableData.value, columns1, tableName.value);
}

// 根据内容长度计算tooltip类名
function getTooltipClass(content: string) {
	if (!content) return 'my-tooltip-small';
	const textWidth = content.length * (/[\u4e00-\u9fa5]/.test(content) ? 18 : 10);
	if (textWidth > 300) return 'my-tooltip-large';
	return 'my-tooltip-small';
}

// 下载PDF功能
async function downloadPDF() {
	const content = document.querySelector('.notice-preview');
	if (!content) return;

	// 创建新窗口用于打印
	const printWindow = window.open('', '_blank');
	if (!printWindow) {
		ElMessage.error('请允许打开新窗口');
		return;
	}

	// 获取所有图片并转换为base64
	const images = content.querySelectorAll('img');
	const imagePromises = Array.from(images).map(
		img =>
			new Promise(resolve => {
				const canvas = document.createElement('canvas');
				const ctx = canvas.getContext('2d');
				const newImg = new Image();

				newImg.crossOrigin = 'anonymous'; // 处理跨域图片
				newImg.onload = () => {
					canvas.width = newImg.width;
					canvas.height = newImg.height;
					ctx?.drawImage(newImg, 0, 0);
					try {
						const base64 = canvas.toDataURL('image/png');
						img.src = base64; // 替换原始src为base64
						resolve(true);
					} catch (e) {
						console.error('图片转换失败:', e);
						resolve(false);
					}
				};
				newImg.onerror = () => {
					console.error('图片加载失败:', img.src);
					resolve(false);
				};
				newImg.src = img.src;
			})
	);

	try {
		await Promise.all(imagePromises);
	} catch (e) {
		console.error('图片处理失败:', e);
	}

	// 写入打印内容
	printWindow.document.write(`
    <html>
      <head>
        <title>${tableName.value}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            padding: 20px;
            color: #303133;
          }
          .preview-title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
          }
          .preview-content {
            line-height: 1.6;
            font-size: 14px;
            color: #606266;
          }
          img {
            max-width: 100%;
            height: auto;
            margin: 10px 0;
            page-break-inside: avoid;
          }
          @media print {
            body { -webkit-print-color-adjust: exact; }
            img { max-width: 100% !important; }
          }
        </style>
      </head>
      <body>
        ${content.innerHTML}
      </body>
    </html>
  `);

	printWindow.document.close();

	// 等待内容加载完成后打印
	printWindow.onload = function () {
		setTimeout(() => {
			printWindow.print();
			// 打印完成后关闭窗口
			setTimeout(() => {
				printWindow.close();
			}, 1000);
		}, 500);
	};
}
</script>

<style>
.ellipsis-text {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	display: block;
}

.my-tooltip-large {
	max-width: 300px !important;
}

.my-tooltip-small {
	max-width: fit-content !important;
}

:deep(.my-tooltip-large),
:deep(.my-tooltip-small) {
	white-space: normal !important;
	word-break: break-word !important;
	line-height: 1.5 !important;
}

:deep(.my-tooltip-large .el-popper__content) {
	max-width: 300px !important;
	white-space: normal !important;
	word-break: break-word !important;
}

:deep(.my-tooltip-small .el-popper__content) {
	max-width: fit-content !important;
	white-space: normal !important;
	word-break: break-word !important;
}
</style>
