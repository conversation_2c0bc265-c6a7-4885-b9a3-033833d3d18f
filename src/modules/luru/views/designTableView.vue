<template>
	<cl-view-group ref="ViewGroup">
		<template #left>
			<!-- 部门列表 -->
			<form-type-list
				@refresh="refresh"
				@table-add="onTableAdd"
				@tree-data="onTreeData"
				:isEdit="true"
			/>
		</template>
		<template #right>
			<cl-crud ref="Crud">
				<cl-row>
					<!-- 刷新按钮 -->
					<cl-refresh-btn />
					<!-- 新增按钮 -->
					<el-button type="primary" v-if="typeId != -1" @click="formToFormType"
						>转移</el-button
					>
					<el-button type="primary" v-if="typeId != -1" @click="addTable">新增</el-button>
					<cl-flex1 />
					<!-- 关键字搜索 -->
				</cl-row>

				<cl-row>
					<!-- 数据表格 -->
					<cl-table ref="Table" row-key="id" @row-click="onRowClick">
						<template #slot-btn="{ scope }">
							<el-tooltip content="修改表名" placement="top">
								<el-button
									class="operation-btn"
									type="primary"
									v-if="scope.row.tableType == TABLE_TYPE.USER"
									@click="editTableName(scope.row)"
									:icon="Setting"
									size="small"
									circle
								/>
							</el-tooltip>
							<el-tooltip content="编辑" placement="top">
								<el-button
									class="operation-btn"
									type="primary"
									v-if="scope.row.tableType == TABLE_TYPE.USER"
									@click="editTable(scope.row)"
									:icon="Edit"
									size="small"
									circle
								/>
							</el-tooltip>
							<el-tooltip content="操作说明" placement="top">
								<el-button
									class="operation-btn"
									type="primary"
									@click="openTableOperationDesc(scope.row)"
									:icon="Document"
									size="small"
									circle
								/>
							</el-tooltip>
							<el-tooltip content="流程编辑" placement="top">
								<el-button
									class="operation-btn"
									type="success"
									v-if="
										(scope.row.tableType == TABLE_TYPE.USER ||
											scope.row.tableType == TABLE_TYPE.ONLY_ADD) &&
										scope.row.form_status == 1
									"
									@click="editProcess(scope.row)"
									:icon="Connection"
									size="small"
									circle
								/>
							</el-tooltip>
							<el-tooltip content="解绑流程" placement="top">
								<el-button
									class="operation-btn"
									type="danger"
									:icon="Remove"
									circle
									v-if="
										(scope.row.tableType == TABLE_TYPE.USER ||
											scope.row.tableType == TABLE_TYPE.ONLY_ADD) &&
										scope.row.form_status == 1
									"
									size="small"
									v-show="scope.row.flow_id"
									@click="delFormFlowData(scope.row)"
								/>
							</el-tooltip>
							<el-tooltip content="历史版本" placement="top">
								<el-button
									class="operation-btn"
									type="info"
									:icon="Clock"
									circle
									v-if="
										(scope.row.tableType == TABLE_TYPE.USER ||
											scope.row.tableType == TABLE_TYPE.ONLY_ADD) &&
										scope.row.form_status == 1
									"
									size="small"
									@click="getOldVersionList(scope.row)"
								/>
							</el-tooltip>
						</template>
					</cl-table>
				</cl-row>

				<cl-row>
					<cl-flex1 />
					<cl-pagination />
				</cl-row>
				<!-- 新增、编辑 -->
				<cl-upsert ref="Upsert">
					<template #slot-value="{ scope }">
						<div class="form-value">
							<el-input
								v-model="scope.value"
								placeholder="请填写代码"
								clearable
								type="textarea"
								:rows="4"
							/>
							<div class="op"></div>
						</div>
					</template>
				</cl-upsert>
			</cl-crud>
		</template>
	</cl-view-group>
	<el-dialog v-model="tableOperationDescVisible">
		<cl-editor-wang
			v-model="tableOperationDesc"
			:isAll="false"
			:isSpace="false"
			:filePath="filePath"
		/>
		<template #footer>
			<el-button type="primary" @click="submitTableOperationDesc">确认</el-button>
		</template>
	</el-dialog>
	<el-dialog v-model="tableListVisible">
		<el-form-item label="选择表单">
			<el-select></el-select>
		</el-form-item>
	</el-dialog>
	<form-type-transfer
		:dialog-visible="dialogVisible"
		:typeName="typeName"
		:form-list="formTransferData"
		@dialog-close="dialogClose"
		@submit="dialogSubmit"
	/>
	<designForm
		:tableVisible="tableVisible"
		:tableTitle="tableTitle"
		:rowData="rowData"
		:tableType="tableType"
		:typeId="typeId"
		@closeDialog="closeTable"
		@submitData="getFormData"
	></designForm>
	<editFlow :dialogVisible="flowEditVisible" :rowData="rowData" @closeDialog="closeFlowEdit" />
	<el-drawer v-model="drawerVisible" size="80%" draggable>
		<template #header="{ titleId, titleClass }">
			<h4 :id="titleId" :class="titleClass">历史版本</h4>
		</template>
		<el-tabs v-model="tabData" tab-position="left" style="min-height: 40vh; max-height: 80vh">
			<el-tab-pane
				v-for="item in oldVersionList"
				style="
					display: flex;
					flex: 1 1 0%;
					position: relative;
					overflow: auto;
					min-height: 60vh;
					max-height: 80vh;
					background-color: var(--global-flow-background-color);
				"
				:key="item.id"
				:label="`版本${item.version}`"
				:name="`版本${item.version}`"
			>
				<div style="height: 90px">
					<el-button
						type="primary"
						v-show="isActive"
						plain
						style="margin: 5px"
						@click="chooseVersion(item.version, item.id)"
						>复用此版本</el-button
					>
				</div>
				<flow-index :readonly="true" :flowData="item.flowJson"></flow-index>
			</el-tab-pane>
		</el-tabs>
	</el-drawer>
</template>
<script lang="ts" name="designTableView" setup>
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { useBase } from '/$/base';
import { ref, onMounted } from 'vue';
import { useViewGroup } from '/@/plugins/view';
import FormTypeList from '../components/formTypeList.vue';
import FormTypeTransfer from '../components/formTypeTransfer.vue';
import { PermissionInfo, TABLE_TYPE } from '../utils/constants';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getColContent, getCrudContent } from '../utils/designTable';
import designForm from '../components/designForm.vue';
import { DesignTable, DesignRow } from '../types';
import { useStore } from '../store';
import { Plus, Edit, Remove, Clock, Setting, Connection, Document } from '@element-plus/icons-vue';
import { useStore as useBaseStore } from '/@/modules/flow/store/index';
import editFlow from '../components/editFlow.vue';
import FlowIndex from '/@/modules/flow/components/design/flowIndex.vue';
import { changeRules } from '/@/modules/luru/utils/luruTable';

const { service, router } = useCool();

const formListStore = useBaseStore();
const dialogVisible = ref(false);
const formList: any = ref([]);
const formTransferData: any = ref([]);
const { user } = useBase();

const drawerVisible = ref(false);
const oldVersionList = ref([]);
const tabData = ref('版本1');
const isActive = ref(false);

const tableOperationDescVisible = ref(false);
const filePath = ref('');
const tableOperationDesc = ref('');

// 常量定义
const NAME = PermissionInfo.SHEJI;

const tableVisible = ref(false);
const tableTitle = ref('');
const tableType = ref('');
const rowData = ref();
const { designFormData } = useStore();

let promission;
const formPermissions = user?.userPromission[NAME];
console.log('formPermissions', user?.userPromission, formPermissions);
if (formPermissions['ALL'] && formPermissions['ALL'][0] == 'ALL') {
	promission = ['ALL'];
} else {
	promission = formPermissions[router.options.history.state.id] || '';
}

onMounted(async () => {
	await getFormList();
});

async function getFormList() {
	await service.cloud.db
		.list({
			tableType: [TABLE_TYPE.USER, TABLE_TYPE.ONLY_ADD, TABLE_TYPE.ONLY_VIEW],
			formType: NAME
		})
		.then(res => {
			formList.value = res.map((item: any) => {
				return {
					label: item.name,
					key: item.id,
					formTypeId: item.formTypeId
				};
			});
		});
}

const typeId = ref();
const typeName = ref('');
const tableListVisible = ref<boolean>(false);
const flowEditVisible = ref<boolean>(false);
const editTable = row => {
	tableVisible.value = true;
	tableTitle.value = '编辑';
	rowData.value = row;
	//tableType.value = row.tableType;
	//	console.log('rowData', rowData.value);
};

const editProcess = row => {
	flowEditVisible.value = true;
	// if (row.colInfo) {
	// 	console.log('编辑流程row', row);
	// 	row.colInfo = changeRules(row.colInfo);
	// }
	rowData.value = row;
	//console.log('编辑流程-rowData', rowData.value);
};

const closeFlowEdit = () => {
	flowEditVisible.value = false;
	refresh({});
};

const addTable = () => {
	tableVisible.value = true;
	console.log('tableVisible', tableVisible.value);
	// router.push("/designform")
	tableTitle.value = '新增';
};

const editTableName = row => {
	ElMessageBox.prompt('请输入表名', '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消'
	})
		.then(async ({ value }) => {
			await service.cloud.db.modifyTableName({
				oldTableName: row.form_name,
				newTableName: value
			});
			console.log('更新表名');
			refresh({});
		})
		.catch(() => {
			// ElMessage({
			//   type: 'info',
			//   message: 'Input canceled',
			// })
		});
};

function chooseVersion(version, id) {
	console.log('version', version);
	service.flow.flow
		.update({ id: Number(id), active: true })
		.then(res => {
			console.log('res', res);
			ElMessage.success('启用成功');
			drawerVisible.value = false;
			refresh({});
		})
		.catch(err => {
			console.log('err', err);
			ElMessage.error('启用失败' + err.message);
		});
}

const delFormFlowData = row => {
	ElMessageBox.confirm('正在运行的流程会被强行终止，确定要解绑流程吗？', '提示', {
		type: 'warning'
	}).then(async () => {
		try {
			await service.flow.flow.update({ id: Number(row.flow_id), active: false });
			ElMessage.success('解绑成功');
			formListStore.formListStore.delFlowFormListData(row.id);
			refresh({});
		} catch (err) {
			console.log('err', err);
			ElMessage.error('解绑失败' + err.message);
		}
		console.log('row', row);
	});
};

const getOldVersionList = async row => {
	try {
		console.log('row', row.form_id, row.flow_id);
		const res = await service.flow.process.unbindFlow({ formId: row.form_id });
		console.log('res', res);
		if (res.length > 0) {
			if (row.flow_id) {
				isActive.value = false;
			} else {
				isActive.value = true;
			}
			oldVersionList.value = res;
			console.log('oldVersionList', oldVersionList.value);
			tabData.value = `版本${res[0].version}`;
			drawerVisible.value = true;
		} else {
			ElMessage.warning('该流程没有历史版本');
		}
	} catch (error) {
		console.log('error', error);
		ElMessage.error('获取历史版本失败' + error.message);
	}
};

function openTableOperationDesc(row) {
	console.log('row', row);
	filePath.value = `操作说明/${row.form_id}`;
	console.log('filePath', filePath.value);
	tableOperationDescVisible.value = true;

	tableOperationDesc.value = row.operationDesc;
	rowData.value = row;
}

function submitTableOperationDesc() {
	console.log('上传更新');
	tableOperationDescVisible.value = false;
	//console.log('rowData.value', rowData.value);
	if (typeId.value == -1) {
		if (rowData.value.id) {
			service.cloud.dataOperationDesc
				.update({
					id: rowData.value.id,
					operationDesc: tableOperationDesc.value
				})
				.then(res => {
					ElMessage.success('更新成功');
					refresh({
						formTypeId: typeId.value
					});
				})
				.catch(err => {
					ElMessage.error('更新失败' + err.message);
				});
		} else {
			service.cloud.dataOperationDesc
				.add({
					tableName: rowData.value.form_name,
					operationDesc: tableOperationDesc.value
				})
				.then(res => {
					ElMessage.success('更新成功');
					refresh({
						formTypeId: typeId.value
					});
				})
				.catch(err => {
					ElMessage.error('更新失败' + err.message);
				});
		}
	} else {
		service.cloud.db
			.update({
				id: rowData.value.form_id,
				operationDesc: tableOperationDesc.value
			})
			.then(res => {
				ElMessage.success('更新成功');
				refresh({});
			})
			.catch(err => {
				ElMessage.error('更新失败' + err.message);
			});
	}
}

const Table = useTable({
	contextMenu: [],
	columns: [
		{ label: 'ID', prop: 'form_id', index: 1, minWidth: 80 },
		{ label: '表名', prop: 'form_name', index: 1, minWidth: 250 },
		{ label: '流程名', prop: 'flow_name', minWidth: 220 },
		{
			label: '表单状态',
			prop: 'form_status',
			hidden: false,
			index: 2,
			minWidth: 80,
			dict: [
				{
					label: '发布',
					value: 1,
					type: 'success'
				},
				{
					label: '发布',
					value: 3,
					type: 'success'
				},
				{
					label: '暂存',
					value: 2,
					type: 'danger'
				}
			]
		},
		{
			label: '流程状态',
			prop: 'flow_active',
			minWidth: 120,
			dict: [
				{
					label: '启用',
					value: 1,
					type: 'success'
				},
				{
					label: '禁用',
					value: 0,
					type: 'danger'
				}
			]
		},
		{ label: '表单备注', prop: 'form_readme', minWidth: 200 },
		{ label: '流程版本', prop: 'flow_version', minWidth: 100 },
		{ label: '流程备注', prop: 'flow_description', showOverflowTooltip: true, minWidth: 150 },
		// { label: "更新时间", prop: "updateTime", minWidth: 100 },
		{ label: '流程创建时间', prop: 'flow_createTime', minWidth: 220 },
		{
			type: 'op',
			width: 150,
			buttons({ scope }) {
				return ['slot-btn'];
			}
		}
	]
});

const onTableAdd = () => {
	tableListVisible.value = true;
};
const onTreeData = list => {
	console.log('list', list);
};
const dialogSubmit = value => {
	dialogClose();
	console.log('dialogSubmit', value, typeId.value);
	const updateData: any = [];
	value.map(item => {
		updateData.push({
			id: item,
			formTypeId: typeId.value
		});
	});
	try {
		service.cloud.db.updateFormTypeId(updateData).then(res => {
			console.log('res', res);
		});
	} catch (err: any) {
		ElMessage.error(err);
	}
};
const dialogClose = () => {
	dialogVisible.value = false;
};

const getFormData = async (options: any, rules: any, type: string) => {
	try {
		console.log('options', options, 'rules', rules, type, rowData.value);
		//const colInfo = getColInfo(rules);
		console.log('演示表');
		if (tableTitle.value == '新增') {
			try {
				const content = getColContent(options.formName, rules, type);
				await service.cloud.db.add({
					name: options.formName,
					content: content,
					readme: options.form.remark,
					colInfo: rules,
					tableInfo: options,
					status: type === '发布' ? '1' : '0'
				});

				ElMessage.success('新增成功');
				tableVisible.value = false;
				designFormData.setLoading(false);
				Crud.value?.refresh();
			} catch (error: any) {
				designFormData.setLoading(false);
				console.log('error', error);
				ElMessageBox.confirm(`${error.message}`, '提示', {
					confirmButtonText: '确认',
					type: 'warning'
				});
			}
		} else if (tableTitle.value == '编辑') {
			try {
				const colLengthList = getColLength(rowData.value.content);
				console.log('colLengthList', colLengthList);
				const content = getColContent(options.formName, rules, type, colLengthList);
				if (rowData.value?.tableType != 'user') {
					service.cloud.db
						.update({
							id: rowData.value?.id,
							name: rowData.value?.name,
							status: type === '发布' ? 1 : 0,
							tableName: rowData.value?.tableName,
							readme: options.form.remark,
							colInfo: rules,
							tableInfo: options
						})
						.then(res => {
							Crud.value?.refresh();
							ElMessage.success('编辑成功');
							tableVisible.value = false;

							designFormData.setLoading(false);
						})
						.catch((error: any) => {
							designFormData.setLoading(false);
							ElMessageBox.confirm(`${error.message}`, '提示', {
								confirmButtonText: '确认',
								type: 'warning'
							});
						});
				} else {
					service.cloud.db
						.update({
							id: rowData.value?.id,
							name: options.formName,
							content:
								rowData.value?.tableType == 'user'
									? content
									: rowData.value?.content,
							status:
								rowData.value?.tableType == TABLE_TYPE.ONLY_VIEW
									? 3
									: type === '发布'
										? 1
										: 0,
							tableName: rowData.value?.tableName,
							readme: options.form.remark,
							colInfo: rules,
							tableInfo: options
						})
						.then(res => {
							Crud.value?.refresh();
							ElMessage.success('编辑成功');
							tableVisible.value = false;

							designFormData.setLoading(false);
						})
						.catch((error: any) => {
							designFormData.setLoading(false);
							ElMessageBox.confirm(`${error.message}`, '提示', {
								confirmButtonText: '确认',
								type: 'warning'
							});
						});
				}
			} catch (error: any) {
				designFormData.setLoading(false);
				ElMessageBox.confirm(`${error.message}`, '提示', {
					confirmButtonText: '确认',
					type: 'warning'
				});

				console.log(error);
				return;
			}
		}
	} catch (error) {
		console.error('提交失败:', error);
		designFormData.setLoading(false);
	} finally {
		Crud.value?.refresh();
		setTimeout(() => {
			designFormData.setLoading(false);
		}, 5000);
	}
};

const getColLength = (colInfo: any) => {
	const regex = /@Column\({[^}]*}\)\s+([^\s:]+):/g;
	const lengthRegex = /length:\s*(\d+)/;

	const fields = {};
	let match;

	while ((match = regex.exec(colInfo)) !== null) {
		const fieldName = match[1].trim();
		const columnOptions = match[0];

		// 检查是否有 length
		const lengthMatch = columnOptions.match(lengthRegex);
		const length = lengthMatch ? parseInt(lengthMatch[1]) : undefined;
		fields[fieldName] = length;
	}
	console.log('列和长度', fields);
	return fields;
};

const closeTable = () => {
	console.log('关闭！！');
	tableVisible.value = false;
	refresh({});
};

const formToFormType = () => {
	console.log('formList', formList.value);
	formTransferData.value = formList.value.filter(item => item.formTypeId != typeId.value);
	console.log('formTransferData', formTransferData.value);
	dialogVisible.value = true;
	console.log('formToFormType', typeId.value, Table.value);
	console.log('ViewGroup', ViewGroup.value, Crud.value);
};

const { ViewGroup } = useViewGroup({
	label: '类型',
	title: '表单',
	service: service.cloud.formType,
	onSelect(item) {
		console.log('点击item', item);
		typeId.value = item.id;
		typeName.value = item.name;
		if (item.id != -1) {
			refresh({
				formTypeId: item.id,
				page: 1
			});
		}
	},
	onData(list) {
		console.log('list', list);
		return list;
	},
	onEdit(item) {
		console.log(item);
		return {};
	}
});

const Crud = useCrud({
	service: service.flow.flow

	// async onRefresh(params, { next }) {
	// 	// 默认使用 next(params)，也可以自己对数据进行处理
	// 	// params.tableType = ['user'];
	// 	// params.formType = NAME;
	// 	next({
	// 		...params
	// 	});
	// }
});

function onRowClick(row: any, column: any) {
	if (column?.property && row.children) {
		Table.value?.toggleRowExpansion(row);
	}
}

async function refresh(params?: any) {
	console.log('刷新111', params);
	//formRef.value?.closeForm();
	// if (params?.formTypeId) {
	// 	params.typeId = params.formTypeId;
	// 	delete params.formTypeId;
	// }

	if (params?.formTypeId) {
		params.formTypeId = typeId.value;
	}
	params.tableType = [TABLE_TYPE.USER, TABLE_TYPE.ONLY_ADD, TABLE_TYPE.SYSTEM];
	params.formType = NAME;
	console.log('刷新22', params);
	Crud.value?.refresh(params);
}
</script>
