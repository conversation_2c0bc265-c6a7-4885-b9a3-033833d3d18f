<template>
	<cl-crud class="cl-crud div1">
		<cl-row>
			<fieldSerch :columnList="luruInfoColumnList" @onSearch="onSearch"></fieldSerch>
			<cl-flex1 />
			<el-button type="primary" @click="refresh">刷新</el-button>
		</cl-row>
		<cl-row>
			<el-table
				v-show="tableName"
				:data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
			>
				<el-table-column label="创建时间" prop="createTime"></el-table-column>
				<el-table-column label="更新时间" prop="updateTime"></el-table-column>
				<el-table-column label="录入人" prop="录入人" />
				<el-table-column label="审核状态" prop="审核状态">
					<template #default="scope">
						<el-tag
							:type="
								scope.row.审核状态 == AUDIT_STATUS.UNAUDITED
									? 'primary'
									: scope.row.审核状态 == AUDIT_STATUS.APPROVED
										? 'success'
										: 'danger'
							"
							disable-transitions
						>
							{{
								scope.row.审核状态 == AUDIT_STATUS.UNAUDITED
									? '未审核'
									: scope.row.审核状态 == AUDIT_STATUS.APPROVED
										? '已通过'
										: scope.row.审核状态 == AUDIT_STATUS.REJECTED
											? '已驳回'
											: '已归档'
							}}
						</el-tag></template
					>
				</el-table-column>
				<el-table-column label="审核人" prop="审核人" />
				<el-table-column label="审核时间" prop="审核时间" />
				<el-table-column label="审核说明" prop="审核说明" />

				<el-table-column
					v-for="(item, index) in columnList"
					:key="index"
					:label="item.showName"
					:prop="item.name"
				>
					<template #default="scope">
						<span v-if="item.type === 'date'">
							{{ formatDate(item, scope.row[item.name]) }}
						</span>
						<span v-else-if="item.type === 'number' && item.isPercent">
							{{ (scope.row[item.name] * 100).toFixed(2) }}%
						</span>
						<span v-else-if="item.type === 'picture'">
							<img :src="scope.row[item.name]" min-width="70" height="70" />
						</span>
						<span v-else>
							{{ scope.row[item.name] }}
						</span>
					</template>
				</el-table-column>

				<el-table-column label="操作" fixed="right">
					<template #default="scope">
						<el-button
							type="primary"
							v-show="scope.row.审核状态 == AUDIT_STATUS.REJECTED"
							@click="handleEdit(scope.$index, scope.row)"
							style="margin-left: 10px"
						>
							编辑
						</el-button>
						<!-- <el-button
         					  size="small"
         					  type="danger"
         					  @click="handleDelete(scope.$index, scope.row)"
         				>
         					  删除
         				</el-button> -->
					</template>
				</el-table-column>
			</el-table>
		</cl-row>
		<div>
			<el-pagination
				v-model:current-page="currentPage"
				:page-size="pageSize"
				:disabled="disabled"
				layout="total, prev, pager, next"
				:total="tableData.length"
				@current-change="handleCurrentChange"
				style="margin-top: 15px; margin-right: 10px; float: right"
			/>
		</div>

		<luruForm
			:luruVisible="addtableVisible"
			:columnList="columnList"
			:title="title"
			:rowData="rowData"
			@submitData="getRowData"
			@closeDialog="closeDialog"
		></luruForm>
	</cl-crud>
</template>

<script setup lang="ts" name="luruTable-checkInfo">
import { ref, onMounted, onActivated } from 'vue';
import { useBase } from '/$/base';
import { useCool } from '/@/cool';
import { ElMessage } from 'element-plus';
import luruForm from '../components/luruForm.vue';
import { cloneDeep } from 'lodash-es';
import { searchByField } from '../utils/searchByField';
import fieldSerch from '../components/field-serch.vue';
import { PermissionInfo, AUDIT_STATUS } from '../utils/constants';

// 常量定义
const NAME = PermissionInfo.LURU;
const PAGE_SIZE = 10;

// 组合式API
const { service, router } = useCool();
const { user } = useBase();

// 响应式状态
const tableName = ref<string>('');
const metaData = ref([]);
const tableData = ref([]);
const addtableVisible = ref<boolean>(false);
const columnList = ref([]);
const luruInfoColumnList: any = ref([]);
const title = ref<string>('审核编辑');
const rowData = ref();
const currentPage = ref(1);
const pageSize = ref(PAGE_SIZE);
const disabled = ref(false);
const searchData = ref([]);
const tableId = ref();

onActivated(async () => {
	console.log('进入已审核数据', router.options.history.state);
	if (router.options.history.state.name) {
		initializeComponent();
	}
});

/**
 * 初始化组件数据
 */
function initializeComponent() {
	tableName.value = router.options.history.state.name as string;
	tableId.value = router.options.history.state.id;
	columnList.value = JSON.parse(router.options.history.state.columnList);
	setupLuruInfoColumnList();
	refresh();
}

/**
 * 设置录入信息列表
 */
function setupLuruInfoColumnList() {
	luruInfoColumnList.value = [
		{ name: '录入人', showName: '录入人', type: 'string' },
		{ name: '审核人', showName: '审核人', type: 'string' },
		{ name: '审核状态', showName: '审核状态', type: 'string' },
		{ name: '审核说明', showName: '审核说明', type: 'string' },
		{ name: '审核时间', showName: '审核时间', type: 'string' },
		{ name: 'projectCode', showName: '项目代码', type: 'string' },
		...columnList.value
	];

	addDictCodeColumns();
}

/**
 * 为字典类型添加代码列
 */
function addDictCodeColumns() {
	columnList.value.forEach(item => {
		if (item.type === 'dict' && !item.isProject) {
			luruInfoColumnList.value.push({
				name: `${item.name}代码`,
				showName: `${item.showName}代码`,
				type: 'string'
			});
		}
	});
}

/**
 * 处理编辑操作
 * @param {number} index 行索引
 * @param {Object} row 行数据
 */
function handleEdit(index, row) {
	addtableVisible.value = true;
	title.value = '审核编辑';
	rowData.value = row;
}

/**
 * 处理页码变化
 * @param {number} val 新的页码
 */
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
};

/**
 * 关闭对话框
 */
const closeDialog = () => {
	addtableVisible.value = false;
};

/**
 * 处理搜索操作
 * @param {any} data 搜索条件
 */
function onSearch(data: any) {
	searchData.value = data;
	tableData.value = searchByField(metaData.value, data);
}

/**
 * 格式化日期
 * @param {Object} row 行数据
 * @param {string|number} value 日期值
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(row, value) {
	const date = new Date(value);
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const hours = String(date.getHours()).padStart(2, '0');
	const minutes = String(date.getMinutes()).padStart(2, '0');
	const seconds = String(date.getSeconds()).padStart(2, '0');
	if (!row.dateType) row.dateType = 'YYYY-MM-DD';
	switch (row.dateType) {
		case 'YYYY-MM':
			return `${year}-${month}`;
		case 'YYYY':
			return `${year}`;
		case 'YYYY-MM-DD':
			return `${year}-${month}-${day}`;
		case 'YYYY-MM-DD HH:mm:ss':
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		default:
			throw new Error('错误的日期格式');
	}
}

/**
 * 处理表单提交的数据
 * @param {Object} data 表单数据
 */
async function getRowData(data) {
	processFormData(data);
	await updateDatabaseRecord(data);
	refresh();
}

/**
 * 处理表单数据
 * @param {Object} data 表单数据
 */
function processFormData(data) {
	columnList.value.forEach(item => {
		if (item.type == 'dict' && data[item.name]) {
			const [value, code] = data[item.name].split('&');
			data[item.name] = value;
			if (item.isProject) {
				data.projectCode = code;
			} else {
				data[`${item.name}代码`] = code || ' ';
			}
		}
	});
}

/**
 * 更新数据库记录
 * @param {Object} data 更新的数据
 */
async function updateDatabaseRecord(data) {
	try {
		await service.cloud.db.data({
			id: tableId.value,
			method: 'update',
			params: data
		});
	} catch (err) {
		ElMessage.error(err.message);
	}
}

/**
 * 刷新表格数据
 */
const refresh = () => {
	fetchTableData();
};

/**
 * 获取表格数据
 */
async function fetchTableData() {
	try {
		const res = await service.cloud.db.data({
			id: tableId.value,
			method: 'list',
			params: {
				录入人: user.info.username,
				审核状态: [
					AUDIT_STATUS.UNAUDITED,
					AUDIT_STATUS.APPROVED,
					AUDIT_STATUS.REJECTED,
					AUDIT_STATUS.ARCHIVED
				]
			}
		});

		if (res.list) {
			processTableData(res.list);
		} else {
			ElMessage.info('暂无数据');
		}
	} catch (error) {
		ElMessage.error('获取数据失败');
	}
}

/**
 * 处理表格数据
 * @param {Array} list 原始数据列表
 */
function processTableData(list) {
	metaData.value = filterTableData(list);
	tableData.value = cloneDeep(metaData.value);

	if (searchData.value.length > 0) {
		tableData.value = searchByField(metaData.value, searchData.value);
	}
}

/**
 * 根据用户权限过滤表格数据
 * @param {Array} list 原始数据列表
 * @returns {Array} 过滤后的数据列表
 */
function filterTableData(list) {
	return list.filter(item => {
		if (user.info?.username == 'admin') {
			return (
				item['审核状态'] === AUDIT_STATUS.APPROVED ||
				item['审核状态'] === AUDIT_STATUS.REJECTED
			);
		} else {
			return (
				(item['审核状态'] === AUDIT_STATUS.APPROVED &&
					item['录入人'] == user.info?.username) ||
				(item['审核状态'] == AUDIT_STATUS.REJECTED && item['录入人'] == user.info?.username)
			);
		}
	});
}
</script>

<style scoped lang="scss">
// ... 样式保持不变 ...
</style>
