<template>
	<cl-crud class="cl-crud" v-loading="loading">
		<cl-row style="align-items: center">
			<div v-if="checkDataIds.length == 0">
				<fieldSerch
					:columnList="luruColumnList"
					:checkData="searchData"
					@onSearch="onSearch"
				></fieldSerch>
			</div>
			<el-button type="primary" @click="changeCol">自定义列</el-button>
			<cl-flex1 />
			<el-button type="primary" @click="operationDescVisible = true"
				><el-icon
					:size="18"
					class="no-inherit"
					@click="operationDescVisible = true"
					style="margin-right: 5px"
				>
					<document /> </el-icon
				>操作说明</el-button
			>
			<!-- <el-tooltip content="操作说明" placement="top" v-if="operationDesc">
				<el-icon
					color="#4165d7"
					:size="22"
					class="no-inherit"
					@click="operationDescVisible = true"
					style="margin-right: 20px"
				>
					<document />
				</el-icon>
			</el-tooltip> -->
			<el-button
				type="primary"
				@click="addTable"
				v-show="tableType != 'system'"
				:disabled="!promissionIsOk"
				>新增</el-button
			>

			<el-button type="primary" @click="refresh">刷新</el-button>

			<div>
				<cl-import-btn
					ref="hiddenButton"
					tips="上传excel文件"
					:on-submit="onSubmit"
					:disabled="!promissionIsOk"
				/>
			</div>

			<cl-export-btn
				:columns="exportCol"
				:data="onExportData"
				:primary="'success'"
				:disabled="!promissionIsOk"
				:filename="filename"
			/>
		</cl-row>
		<cl-row>
			<el-table :data="tableData" border>
				<el-table-column
					v-for="(item, index) in visibleColumns"
					:key="index"
					:label="item.showName === 'projectCode' ? '项目代码' : item.showName"
					:prop="item.name"
					:width="item.showName.length * 25 + 20"
					:hidden="item.selected"
				>
					<template #default="scope">
						<span v-if="item.type === 'date'">
							{{ formatDate(item.dateType, scope.row[item.name]) }}
						</span>
						<span v-else-if="item.type === 'number' && item.isPercent">
							{{
								(scope.row[item.name] * 100).toFixed(
									item.decimals - 2 >= 0 ? item.decimals - 2 : 0
								)
							}}%
						</span>
						<span v-else-if="item.type === 'number'">
							{{ formatNumber(scope.row[item.name], item.decimals) }}
						</span>
						<span v-else-if="item.type == 'upload'">
							<img
								:src="scope.row[item.name][0] || scope.row[item.name]"
								min-width="70"
								height="70"
							/>
							<!-- <img :src="scope.row[item.name][0] || ''" min-width="70" height="70" />
							<img :src="scope.row[item.name][1] || ''" min-width="70" height="70" /> -->
						</span>
						<span v-else-if="item.name == '审核状态'">
							<el-tag
								:type="
									scope.row.审核状态 == AUDIT_STATUS.UNAUDITED
										? 'primary'
										: scope.row.审核状态 == AUDIT_STATUS.APPROVED
											? 'success'
											: scope.row.审核状态 == AUDIT_STATUS.REJECTED
												? 'danger'
												: 'info'
								"
								disable-transitions
							>
								{{
									scope.row.审核状态 == AUDIT_STATUS.UNAUDITED
										? '待审核'
										: scope.row.审核状态 == AUDIT_STATUS.APPROVED
											? '审核通过'
											: scope.row.审核状态 == AUDIT_STATUS.REJECTED
												? '已驳回'
												: scope.row.审核状态 == AUDIT_STATUS.DRAFT
													? '暂存'
													: scope.row.审核状态 == AUDIT_STATUS.INVALID
														? '作废'
														: '已归档'
								}}
							</el-tag>
						</span>
						<span v-else>
							{{ scope.row[item.name] }}
						</span>
					</template>
				</el-table-column>
				<el-table-column
					label="最新编辑人"
					prop="最新编辑人"
					v-if="autoincrement"
				></el-table-column>
				<el-table-column label="创建时间" prop="createTime"></el-table-column>
				<el-table-column label="更新时间" prop="updateTime"></el-table-column>
				<el-table-column label="操作" fixed="right">
					<template #default="scope">
						<el-button
							size="small"
							key="submit"
							v-if="scope.row.审核状态 == AUDIT_STATUS.DRAFT"
							@click="updateStatus(scope.row)"
						>
							提交审核
						</el-button>
						<el-button
							size="small"
							v-if="
								scope.row.审核状态 == AUDIT_STATUS.UNAUDITED &&
								user?.info?.username == scope.row.录入人
							"
							@click="toUrge(scope.row)"
							style="margin-left: 10px"
						>
							催办
						</el-button>
						<el-button
							size="small"
							@click="handleEdit(scope.row)"
							:disabled="!promissionIsOk"
							style="margin-left: 10px"
							v-if="
								(scope.row.审核状态 != AUDIT_STATUS.APPROVED ||
									tableType == TABLE_TYPE.ONLY_ADD ||
									autoincrement) &&
								scope.row.审核状态 != AUDIT_STATUS.UNAUDITED
							"
						>
							编辑
						</el-button>
						<el-button
							size="small"
							type="danger"
							@click="handleDelete(scope.$index, scope.row)"
							v-if="scope.row.审核状态 == AUDIT_STATUS.DRAFT"
						>
							作废
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</cl-row>
		<div>
			<el-pagination
				v-model:current-page="currentPage"
				:page-size="pageSize"
				:disabled="disabled"
				layout="total, prev, pager, next"
				:total="total"
				@current-change="handleCurrentChange"
				style="margin-top: 15px; margin-right: 10px; float: right"
			/>
		</div>

		<colChange
			:changeColVisible="changeColVisible"
			:name="colName"
			:allColumns="luruColumnList"
			@closeDialog="closeColChange"
			@submitData="getNewCol"
		></colChange>
		<luruForm
			:luruVisible="addtableVisible"
			:title="title"
			:tableName="tableName"
			:rowData="rowData"
			:tableId="tableId"
			:options="tableInfo"
			@submitData="getRowData"
			@closeDialog="closeLuruDialog"
		></luruForm>
		<cl-dialog v-model="operationDescVisible" title="操作 说明" width="50%">
			<div class="notice-preview">
				<h3 class="preview-title">
					{{ tableName }}
				</h3>

				<div class="preview-content" v-html="operationDesc"></div>
			</div>
			<template #footer>
				<div style="display: flex; justify-content: flex-end">
					<el-button @click="operationDescVisible = false">关闭</el-button>
					<el-button type="primary" @click="downloadPDF">
						<el-icon style="margin-right: 5px"><download /></el-icon>
						下载PDF
					</el-button>
				</div>
			</template>
		</cl-dialog>
	</cl-crud>
</template>

<script setup lang="ts" name="luruTable-data">
import { onMounted, ref, computed, onErrorCaptured, nextTick, onActivated } from 'vue';
import { useBase } from '/$/base';
import { useCool } from '/@/cool';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Document } from '@element-plus/icons-vue';
import luruForm from '../components/luruForm.vue';
import { cloneDeep } from 'lodash-es';
import fieldSerch from '../components/field-serch.vue';
import { buildTree, filterAndBuildTree } from '../utils/findDict';
import { TABLE_TYPE } from '../utils/constants';
import colChange from '../components/colChange.vue';
import { storage } from '/@/cool';
import { PermissionInfo } from '../utils/constants';
import { useStore } from '../store';
import { formatNumber } from '../utils/formatTableData';
import { getDictFromIndexDB, saveDictToIndexDB } from '../utils/indexDB';
import { flattenFormDataWithLevel } from '../utils/designTable';
import { changeRules } from '../utils/luruTable';
import { AUDIT_STATUS } from '../utils/constants';

const { ProjectDictId, TableName, LURU, SHENHE, SHEJI, FormPermission } = PermissionInfo;

const { service, router, route } = useCool();
const { user } = useBase();
const { dictCols, luruTableStore } = useStore();
const tableType: any = ref('');
const loading = ref(false);

const NAME = LURU;
const promissionIsOk = ref(true);
let promission;
const tableId = ref();
const dbTableName: any = ref('');
try {
	const formPermissions = user?.userPromission[NAME];
	console.log('formPermissions', formPermissions);
	if (formPermissions['ALL'] && formPermissions['ALL'][0] == 'ALL') {
		promission = ['ALL'];
	} else {
		console.log(
			'formPermissions',
			formPermissions,
			router.options.history.state.id,
			tableId.value
		);
		promission =
			formPermissions[router.options.history.state.id] ||
			formPermissions[tableId.value] ||
			'';
		if (formPermissions['ALL']) {
			promission = [...promission, ...formPermissions['ALL']];
		}
	}
} catch (err) {
	console.log('获取权限失败', err);
	ElMessageBox.confirm('获取权限失败，请联系管理员', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'error'
	});
	router.push('/');
}
console.log('￥￥￥权限', promission);
const colName = ref<string>('');
const tableName = ref<string>('');
const metaData = ref([]);
const tableData = ref([]);
const addtableVisible = ref(false);

const clickRow = ref();
const changeColVisible = ref(false);

const columnList: any = ref([]);
const luruColumnList: any = ref([]);
const ruleColumnList: any = ref([]);
const tableInfo = ref({});

const title = ref<string>('新增');
const rowData = ref();

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const disabled = ref(false);

const checkDataIds = ref([]);
const ruleId = ref();

const operationDesc = ref();
const operationDescVisible = ref(false);

const hiddenButton = ref(null);
const exportCol: any = ref([]);
const filename = ref<string>('');
const metaColData: any = ref([]);
const autoincrement: any = ref(false);
const isSystemTable: any = ref(false);

onActivated(async () => {
	promissionIsOk.value = true;
	if (router.options.history.state.name) {
		loading.value = true;
		//exportCol.value=[]
		const luruTableInfo: any = await luruTableStore.getluruTableInfo(
			router.options.history.state.id as string
		);
		if (luruTableInfo.tableType) isSystemTable.value = true;
		console.log('luruTableInfo', luruTableInfo);
		operationDesc.value = luruTableInfo.operationDesc;
		console.log('operationDesc', operationDesc.value);
		tableId.value = router.options.history.state.id;
		dbTableName.value = luruTableInfo.tableName;

		tableName.value = luruTableInfo.name as string;
		tableType.value = luruTableInfo.tableType;
		tableInfo.value = luruTableInfo.tableInfo;
		if (tableInfo.value.form.tableInfo) {
			if (tableInfo.value.form.tableInfo.includes('autoincrement')) {
				autoincrement.value = true;
			} else {
				autoincrement.value = false;
			}
		} else {
			autoincrement.value = false;
		}
		console.log('tableId.valu', tableId.value, tableName.value);
		ruleColumnList.value = luruTableInfo.colInfo;
		columnList.value = changeRules(ruleColumnList.value);
		if (router.options.history.state?.checkDataIds) {
			checkDataIds.value = JSON.parse(router.options.history.state?.checkDataIds);
			ruleId.value = router.options.history.state?.ruleId;
		} else {
			checkDataIds.value = [];
			ruleId.value = null;
		}
		//console.log("columnList", columnList.value);
		luruColumnList.value = [
			{ name: 'id', showName: '序号', type: 'number', selected: true },
			{ name: '录入人', showName: '录入人', type: 'string', selected: true },
			{ name: 'projectCode', showName: '项目代码', type: 'string', selected: true },
			{ name: '审核状态', showName: '状态', type: 'luruStatus', selected: true }
		];
		exportCol.value = await getExportCol(columnList.value);
		colName.value = '录入' + '-' + tableName.value;
		columnList.value = cloneDeep(exportCol.value);
		console.log('导出列', exportCol.value);
		dictCols.setDictCols(columnList.value);

		columnList.value.forEach(item => {
			if (item.isTableForm) {
				return;
			}
			if (item.type === 'dict' && !item.isProject) {
				luruColumnList.value.push({
					name: item.name,
					showName: item.showName,
					type: 'string'
					//selected: true
				});
				luruColumnList.value.push({
					name: `${item.name}代码`,
					showName: `${item.showName}代码`,
					type: 'string'
					//selected: true
				});
			} else if (item.type == 'relatedCol' && item.relatedTableCol) {
				luruColumnList.value.push({ ...item });
			} else if (item.type == 'calculatedCol') {
				console.log('计算列', item);
				const calculatedData: any = {};
				calculatedData.name = item.name;
				calculatedData.showName = item.showName;
				calculatedData.type = item.calculateType;
				if (calculatedData.type == 'number') {
					calculatedData.decimals = parseInt(item.decimals) || 0;
				} else if (calculatedData.type == 'date') {
					calculatedData.dateType = item.dateType || 'YYYY-MM-DD';
				}
				console.log('calculatedData', item.name, calculatedData);
				luruColumnList.value.push({ ...calculatedData });
			} else {
				if (item.type == 'number' && item.decimals) {
					item.decimals = parseInt(item.decimals);
				}
				luruColumnList.value.push({ ...item });
				//exportCol.value.push({label:item.name,prop:item.name,type:item.type})
			}
		});
		metaColData.value = cloneDeep(luruColumnList.value);
		if (storage.get(colName.value)) {
			luruColumnList.value = updateAWithB(storage.get(colName.value), luruColumnList.value);
		}
		//  exportCol.value.push
		console.log('传给搜索组件的数据', luruColumnList.value, columnList.value);
		filename.value = router.options.history.state.name;
		console.log('组件间传值', columnList.value);
		//exportCol.value=luruColumnList.value.map((item:any)=>{return {label:item.name,prop:item.name,type:item.type}})
		if (router.options.history.state.type == '新增') {
			luruTableStore.setluruTableCols(ruleColumnList.value);
			setTimeout(() => {
				title.value = '新增';
				addtableVisible.value = true;
				console.log('打开新增表单');
			}, 300);
		} else if (router.options.history.state.type == '导入') {
			if (hiddenButton.value) {
				console.log('hiddenButton.value', hiddenButton.value);
				hiddenButton.value.open();
			}
		}
		console.log('columnList', columnList.value);
		refresh();

		loading.value = false;
	} else {
		//await nextTick();
		// console.log('路由值', router.options.history.state);
		// console.log('值', tableName.value, tableType.value, columnList.value);
	}
});

/*
根据最新列信息修改已保存的自定义列信息
a:已保存的自定义列信息
b:最新列信息
返回根据b修改后的a
 */
function updateAWithB(a, b) {
	// 1. 创建一个 map，方便通过 name 快速查找 b 中的对象
	const bMap = new Map();
	b.forEach(item => {
		bMap.set(item.name, item);
	});

	// 2. 遍历 a，更新或删除
	const aUpdated = a.filter(item => {
		if (bMap.has(item.name)) {
			// 如果 b 中有同名对象，更新 a 中的对象（保留 selected）
			const bItem = bMap.get(item.name);
			Object.assign(item, bItem); // 用 b 的数据覆盖 a
			bMap.delete(item.name); // 标记为已处理
			return true; // 保留在 a 中
		}
		return false; // 如果 b 中没有，则从 a 中移除
	});

	// 3. 遍历剩余的 b 对象（新增的），添加到 a 的末尾
	bMap.forEach(item => {
		aUpdated.push(item);
	});

	return aUpdated;
}

onErrorCaptured((err, instance, info) => {
	console.log('Captured an error:', err, instance, info);
	// 返回 false 以阻止错误继续传播
	return false;
});

async function getExportCol(columns) {
	let exportCols: any = [];
	const dictIdMap = new Map();
	console.log('columns!!!!', columns);
	// 收集所有需要请求的 dictId
	columns.forEach(item => {
		if (item.type === 'dict') {
			if (item.dictId) {
				if (!dictIdMap.has(item.dictId)) {
					dictIdMap.set(item.dictId, []);
				}
				dictIdMap.get(item.dictId).push(item);
				if (item.isLeaf) {
					dictIdMap.set(item.parentId, []);
				}
			}
		}
	});
	let dictAllData = await getDictFromIndexDB(Array.from(dictIdMap.keys()));
	console.log('indexDB来的数据all', dictAllData);
	if (!dictAllData) {
		const allDictData = await service.dict.info.getAllChildren({
			typeId: Array.from(dictIdMap.keys()),
			status: 1
			//cols: ['id', 'value', 'name', 'parentId', 'typeId']
		});
		console.log('allDictData', allDictData);
		await saveDictToIndexDB(Array.from(dictIdMap.keys()), allDictData);
		dictAllData = allDictData;
	}
	// 一次性请求所有需要的字典数据

	// 处理每个列
	for (const item of columns) {
		if (item.type === 'dict') {
			item.values = [];
			item.codes = [];
			item.ids = [];

			if (item.isProject && user?.info?.username !== 'admin') {
				console.log('dictAllData', dictAllData, Number(ProjectDictId));
				const dictParentList = dictAllData[Number(ProjectDictId)] || [];
				//item.values=[]
				console.log('promission', promission);
				if (promission.includes('ALL')) {
					item.values.push('ALL');
					item.codes.push('ALL');
					item.ids.push('ALL');
					let cachedData = await getDictFromIndexDB([item.dictId]);
					console.log('indexDB来的数据', item.dictId, cachedData);
					if (!cachedData) {
						console.log('无缓存xxxxx');
						const res = await service.dict.info.getAllChildren({
							typeId: [item.dictId]
							//cols: ['id', 'value', 'name', 'parentId', 'typeId']
						});
						await saveDictToIndexDB(item.dictId, res);
						cachedData = res;
					}

					const resTree = buildTree(cachedData[item.dictId]);
					console.log('项目not管理员ALL', cachedData, resTree);
					resTree.map(tree => {
						item.codes.push(tree.value);
						item.ids.push(tree.id);
						item.values.push(tree.name);
					});
				} else {
					console.log(
						'项目not管理员notALL',
						typeof promission,
						promission,
						promission[0],
						dictParentList
					);
					try {
						promission.map(codeId => {
							const treeFields = filterAndBuildTree(dictParentList, codeId);
							console.log('treeFields11', item.name, treeFields);
							item.values.push(treeFields.name);
							item.ids.push(treeFields.id);
							item.codes.push(treeFields.value);
						});
					} catch (error) {
						console.log('error', error);
						promissionIsOk.value = false;
						ElMessage({
							showClose: true,
							type: 'error',
							message: `${tableName.value} 录入权限不足，请在 录入管理-权限表 申请 ${tableName.value} 的录入权限或联系管理员`,
							duration: 6000
						});
					}
					console.log('item');
				}
			} else {
				let dictData = dictAllData[item.dictId] || [];
				if (item.isLeaf) {
					dictData = dictAllData[item.parentId] || [];
					const resTree = buildTree(dictData);
					console.log('resTree-叶子', resTree);
					const dictField = item.dictField;
					const dictFieldArr = dictField.split('/');
					const parentIndex = dictFieldArr.indexOf(item.parentId.toString());
					const leafIndex = dictFieldArr.indexOf(item.dictId.toString());
					const childrenNum = leafIndex - parentIndex;
					const collectLeafNodes = (nodes: any[], currentDepth: number) => {
						nodes.forEach(node => {
							if (currentDepth < childrenNum && node.children?.length > 0) {
								// 如果未达到最大深度且有子节点，继续递归
								collectLeafNodes(node.children, currentDepth + 1);
							} else {
								// 如果到达最大深度或没有子节点，收集数据
								item.codes.push(node.value); // 注意：这里改为 value，因为之前的 code 可能是笔误
								item.ids.push(node.id);
								item.values.push(node.name);
							}
						});
					};
					collectLeafNodes(resTree, 0);
				} else {
					const resTree = buildTree(dictData);
					console.log('resTree', resTree);
					if (item.name === FormPermission && dbTableName.value === TableName) {
						item.values.push('ALL');
						item.codes.push('ALL');
						item.ids.push('ALL');
					}

					resTree.forEach(tree => {
						item.codes.push(tree.value);
						item.ids.push(tree.id);
						item.values.push(tree.name);
					});
				}
			}

			exportCols.push(cloneDeep(item));
		} else {
			exportCols.push(item);
		}
	}

	exportCols = sortDataArrayByName(columns, exportCols);
	if (tableName.value == '客商采集表维护(询证)' || tableName.value == '发函策略表维护(询证)') {
		exportCols.map(item => {
			if (item.name == '客商名称') {
				item.values.push('ALL');
				item.codes.push('ALL');
				item.ids.push('ALL');
			}
		});
	}
	console.log('导出', exportCols);
	return exportCols;
}

async function onExportData(params: any) {
	//const res = await service.cloud.db.data({id:router.options.history.state.id,method:"list",params:{projectCode:promission}}).then((res) => res.list);
	// const mapList=await findEnumMap(exportCol.value,columnList.value)
	// enumMap.value=mapList.enumMap
	// codeMap.value=mapList.codeMap
	// console.log("enumMap",enumMap.value,codeMap.value)
	console.log('导出列', exportCol.value);
	const obj = [];
	if (searchData.value.length > 0) {
		console.log('searchData.value111', searchData.value);
		searchData.value.map(item => {
			obj.push({
				colName: item.selectValue.split('_')[0],
				type: item.selectValue.split('_')[1],
				value: item.inputValue,
				operation: item.operation
			});
		});
		// tableData.value = searchByField(metaData.value, searchData.value);
	}
	const res = await service.cloud.db.data({
		id: tableId.value,
		method: 'list',
		params: {
			formType: NAME,
			page: currentPage.value,
			size: pageSize.value,
			searchParams: obj
		}
	});

	const numObj = {};
	const dateObj = {};
	columnList.value.map(item => {
		if (item.type == 'number' && item.isPercent) {
			numObj[item.name] = true;
		} else if (item.type == 'date') {
			dateObj[item.name] = item.dateType;
		}
	});
	const data = res.list.map((item: any) => {
		Object.keys(item).forEach(key => {
			if (numObj[key]) {
				item[key] = item[key] * 100;
			} else if (dateObj[key]) {
				item[key] = formatDate(dateObj[key], item[key]);
			}
		});
		return item;
	});
	console.log('导出', data);
	return data;
}

//排序
function sortDataArrayByName(sortArray, dataArray) {
	// 创建一个映射，将 sortArray 中的 name 映射到它们的顺序
	const nameOrderMap = new Map();
	sortArray.forEach((item, index) => {
		nameOrderMap.set(item.name, index);
	});

	// 使用映射对 dataArray 进行排序
	return dataArray.sort((a, b) => {
		return nameOrderMap.get(a.name) - nameOrderMap.get(b.name);
	});
}

async function toUrge(row) {
	ElMessageBox.prompt('确认催办吗？可填写催办通知', '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		inputValue: '请尽快办理'
	}).then(async ({ value }) => {
		try {
			await service.flow.process.urge({
				formId: tableId.value,
				businessId: row.id,
				message: value
			});
			ElMessage.success('催办消息已送出');
		} catch (err) {
			ElMessage.error('催办失败' + JSON.stringify(err.message));
		}
	});
	console.log('催办', row);
}

const visibleColumns = computed(() => {
	return luruColumnList.value.filter(column => column.selected);
});
//自定义列
function changeCol() {
	changeColVisible.value = true;
}

function closeColChange() {
	changeColVisible.value = false;
}

function getNewCol(data) {
	console.log('返回的自定义列', data);
	luruColumnList.value = [];
	// data.forEach(ele => {
	//   const newCol=metaColData.value.filter((item)=> item.name==ele.name)
	//   if(newCol.length>0){
	//     luruColumnList.value.push({selected:ele.selected,...newCol[0]})
	//   }
	// });
	luruColumnList.value = data;
}

function formatDate(dateType, value) {
	// console.log("日期格式",dateType,value)
	const date = new Date(value);
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const hours = String(date.getHours()).padStart(2, '0');
	const minutes = String(date.getMinutes()).padStart(2, '0');
	const seconds = String(date.getSeconds()).padStart(2, '0');
	if (!dateType) dateType = 'YYYY-MM-DD';
	switch (dateType) {
		case 'YYYY-MM':
			return `${year}-${month}`;
		case 'YYYY':
			return `${year}`;
		case 'YYYY-MM-DD':
			return `${year}-${month}-${day}`;
		case 'YYYY-MM-DD HH:mm:ss':
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		default:
			throw new Error('错误的日期格式');
	}
}

async function onSubmit(data: any, { next, done, setProgress }) {
	console.log('导入数据', data);
	let hasProjectCode = true;
	console.log('上传成功', data);
	const uploadData: any = [];
	const errors: any = [];
	data.list.map((item, index) => {
		item.审核状态 = 3;
		exportCol.value.map(col => {
			if (col.values) {
				if (item[col.name] && !col.values.includes(item[col.name])) {
					errors.push(
						`第${index + 1}行 "${col.name}" 列的值 "${item[col.name]}" 不在允许的选项中`
					);
				}
			} else if (col.type == 'date') {
				item[col.name] = submitFormatDate(item[col.name], col.dateType);
				if (!item[col.name]) {
					if (isNaN(Date.parse(item[col.name]))) {
						errors.push(
							`第${index + 1}行 "${
								col.name
							}" 列的值 "${item[col.name]}" 不是有效的日期时间格式`
						);
					}
				}
			} else if (col.type == 'number') {
				console.log('number', item[col.name]);
				if (col.isPercent) {
					item[col.name] = parseFloat(item[col.name]) / 100;
				}
				if (isNaN(parseFloat(item[col.name]))) {
					errors.push(
						`第${index + 1}行 "${col.name}" 列的值 "${parseFloat}" 不是有效的数字格式`
					);
				}
			}
		});
		item['录入人'] = user.info?.username;
		//const col=columnList.value
		if (!('项目代码' in item)) {
			hasProjectCode = false;
		} else {
			item.projectCode = item.项目代码;
			// delete item['项目代码']
		}
		// for (let key in colSource) {
		//   if(item[key]==null) return
		//   console.log("查找",colSource[key].indexOf(item[key])==-1,item[key])
		//   const isInArrayResult=isInArray(item[key],colSource[key])
		//   if(!isInArrayResult){
		//     console.log("colSource[key]",colSource[key],item[key])
		//     isOk=false
		//     if((key!="项目代码"&&key!="projectCode")){
		//       wrongData.push(item[key])
		//     }
		//   }
		// }
		delete item['_index'];
		// if(isOk){
		//   uploadData.push(item)
		// }
	});
	console.log('处理完成');
	if (!hasProjectCode) {
		ElMessageBox.confirm('导入数据不符合要求，必须含有项目代码', '提示', {
			confirmButtonText: '确认',
			type: 'warning'
		});
		done();
		close();
		return;
	} else if (errors.length > 0) {
		ElMessageBox.confirm(`导入数据不符合要求，请检查${errors.join(',')}`, '提示', {
			confirmButtonText: '确认',
			type: 'warning'
		});
		done();
		close();
		return;
	}
	service.cloud.db
		.data({ id: tableId.value, method: 'add', params: data.list })
		.then(() => {
			ElMessage.success('导入成功');
			done();

			close();
			//closeDialog();
		})
		.catch(err => {
			ElMessage.error(err.message);
			done();
		})
		.finally(() => {
			done();
			refresh();
			close();
		});
}

//转化日期格式
function submitFormatDate(dateStr, type) {
	// 正则表达式匹配 yyyy-mm-dd 或 yyyy-mm-dd hh:mm:ss 格式
	const dateRegex = /^\d{4}-\d{2}-\d{2}( \d{2}:\d{2}:\d{2})?$/;

	// 检查日期字符串是否符合格式
	if (!dateRegex.test(dateStr)) {
		// 如果不符合格式，尝试将其转换为符合的格式
		const date = new Date(dateStr);
		if (isNaN(date.getTime())) {
			// 如果日期无效，返回 null 或其他错误处理
			return null;
		}

		// 获取年、月、日、时、分、秒
		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const day = String(date.getDate()).padStart(2, '0');
		const hours = String(date.getHours()).padStart(2, '0');
		const minutes = String(date.getMinutes()).padStart(2, '0');
		const seconds = String(date.getSeconds()).padStart(2, '0');
		if (type == 'YYYY-MM-DD HH:mm:ss') {
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		} else {
			return `${year}-${month}-${day}`;
		}
		// 返回符合的日期格式
	}

	// 如果已经符合格式，直接返回原字符串
	return dateStr;
}

//切换表格页数
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};
const searchData = ref([]);

//分字段搜索
function onSearch(data: any) {
	searchData.value = data;
	//console.log('开始搜索', metaData.value, 'data', data);
	//tableData.value = cloneDeep(searchByField(metaData.value, data));
	//console.log('搜索结果', tableData.value);
	refresh();
}

const addTable = () => {
	console.log('新增', tableId.value);
	loading.value = true;
	luruTableStore.setluruTableCols(ruleColumnList.value);
	setTimeout(() => {
		title.value = '新增';
		addtableVisible.value = true;
		loading.value = false;
	}, 300);
};

const getRowData = async (data: any) => {
	console.log('修改data', data, 'title', title.value);
	columnList.value.forEach(item => {
		if (item.type == 'dict' && data[item.name]) {
			const project = data[item.name].split('&');
			console.log('project', project);
			// data[item.name]=data[item.name].split("&")[0]
			if (item.isProject) {
				data.projectCode = project[0];
			} else {
				data[`${item.name}代码`] = project[0];
			}
			data[item.name] = project[1];
		}
	});
	console.log('daya', data);
	if (title.value == '新增') {
		data.录入人 = user.info?.username;
		const colField = columnList.value.filter(item => 'dictField' in item);
		console.log('colField', colField, columnList.value);
		if (colField.length > 0) {
			colField.map(item => {
				if (!item.isProject && !item.name) {
					data[item.name] = ' ';
					data[item.name + '代码'] = ' ';
				}
			});
		}
		try {
			await service.cloud.db
				.data({ id: tableId.value, method: 'add', params: data })
				.then(res => {})
				.catch(err => {
					ElMessage.error(err.message);
				});
		} catch (err) {
			ElMessage.error('提交失败:' + err);
		}
		refresh();
	} else if (title.value == '编辑') {
		data.录入人 = user.info?.username;
		data.id = clickRow.value.id;
		data.审核状态 = AUDIT_STATUS.UNAUDITED;
		console.log('data', tableType.value, autoincrement.value);
		if (autoincrement.value) {
			console.log('是最新编辑人');
			data.最新编辑人 = user.info?.username;
		}

		try {
			await service.cloud.db
				.data({ id: tableId.value, method: 'update', params: data })
				.then(res => {})
				.catch(err => {
					ElMessage.error(err.message);
				});
		} catch (err) {
			ElMessage.error('提交失败:' + err);
		}
		refresh();
	}
};

function handleEdit(row) {
	luruTableStore.setluruTableCols(ruleColumnList.value);
	clickRow.value = row;
	rowData.value = row;
	title.value = '编辑';
	addtableVisible.value = true;
	console.log('打开编辑', rowData.value);
}

function handleDelete(index, row) {
	console.log('禁用', row);
	ElMessageBox.confirm('此操作将会禁用选择的数据，是否继续？', '提示', {
		type: 'warning'
	})
		.then(() => {
			service.cloud.db
				.data({
					id: tableId.value,
					method: 'update',
					params: {
						id: row.id,
						审核状态: AUDIT_STATUS.INVALID
					}
				})
				.then(res => {
					refresh();
				})
				.catch(err => {
					ElMessage.error(err.message);
				});
		})
		.catch(err => ElMessage.error(err.message));
}
function changeType(type) {
	switch (type) {
		case 'dict':
		case 'checkUser':
			return 'string';
		case 'money':
		case 'luruStatus':
		case 'checkStatus':
			return 'number';
		case 'checkTime':
			return 'date';
		default:
			return type;
	}
}
const refresh = () => {
	loading.value = true;
	if (checkDataIds.value.length > 0) {
		service.quality.check
			.outterRecords({
				rowIds: checkDataIds.value,
				ruleId: ruleId.value
			})
			.then(res => {
				tableData.value = res;
				// if (searchData.value.length > 0) {
				// 	tableData.value = searchByField(res, searchData.value);
				// }
				console.log('搜索结果', tableData.value);
			});
	} else {
		const obj: any = [];
		if (searchData.value.length > 0) {
			console.log('searchData.value111', searchData.value);
			searchData.value.map(item => {
				obj.push({
					colName: item.selectValue.split('_')[0],
					type: changeType(item.selectValue.split('_')[1]),
					value: item.inputValue,
					operation: item.operation
				});
			});
			// tableData.value = searchByField(metaData.value, searchData.value);
		}
		console.log('obj', obj);
		service.cloud.db
			.data({
				id: tableId.value,
				method: 'page',
				params: {
					formType: NAME,
					page: currentPage.value,
					size: pageSize.value,
					searchParams: obj
				}
			})
			.then(res => {
				console.log('获取全部结果data', res);
				if (res.list) {
					// metaData.value = res.list.sort(
					// 	(a, b) => new Date(b.updateTime) - new Date(a.updateTime)
					// );
					// tableData.value = cloneDeep(metaData.value);
					tableData.value = res.list;
					total.value = res.pagination.total;
					console.log('排序之后', tableData.value, checkDataIds.value);
					console.log('searchData', searchData.value);
				}
				loading.value = false;
			})
			.catch(err => {
				ElMessage.error(err.message);
				loading.value = false;
			})
			.finally(() => {
				loading.value = false;
			});
	}
};

const closeLuruDialog = () => {
	console.log('关闭');
	if (addtableVisible.value) {
		console.log('对话框开');
		addtableVisible.value = false;
	}
	refresh();
	//
	console.log('关闭成功', addtableVisible.value);
};

// 下载PDF功能
async function downloadPDF() {
	const content = document.querySelector('.notice-preview');
	if (!content) return;

	// 创建新窗口用于打印
	const printWindow = window.open('', '_blank');
	if (!printWindow) {
		ElMessage.error('请允许打开新窗口');
		return;
	}

	// 获取所有图片并转换为base64
	const images = content.querySelectorAll('img');
	const imagePromises = Array.from(images).map(
		img =>
			new Promise(resolve => {
				const canvas = document.createElement('canvas');
				const ctx = canvas.getContext('2d');
				const newImg = new Image();

				newImg.crossOrigin = 'anonymous'; // 处理跨域图片
				newImg.onload = () => {
					canvas.width = newImg.width;
					canvas.height = newImg.height;
					ctx?.drawImage(newImg, 0, 0);
					try {
						const base64 = canvas.toDataURL('image/png');
						img.src = base64; // 替换原始src为base64
						resolve(true);
					} catch (e) {
						console.error('图片转换失败:', e);
						resolve(false);
					}
				};
				newImg.onerror = () => {
					console.error('图片加载失败:', img.src);
					resolve(false);
				};
				newImg.src = img.src;
			})
	);

	try {
		await Promise.all(imagePromises);
	} catch (e) {
		console.error('图片处理失败:', e);
	}

	// 写入打印内容
	printWindow.document.write(`
    <html>
      <head>
        <title>${tableName.value}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            padding: 20px;
            color: #303133;
          }
          .preview-title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
          }
          .preview-content {
            line-height: 1.6;
            font-size: 14px;
            color: #606266;
          }
          img {
            max-width: 100%;
            height: auto;
            margin: 10px 0;
            page-break-inside: avoid;
          }
          @media print {
            body { -webkit-print-color-adjust: exact; }
            img { max-width: 100% !important; }
          }
        </style>
      </head>
      <body>
        ${content.innerHTML}
      </body>
    </html>
  `);

	printWindow.document.close();

	// 等待内容加载完成后打印
	printWindow.onload = function () {
		setTimeout(() => {
			printWindow.print();
			// 打印完成后关闭窗口
			setTimeout(() => {
				printWindow.close();
			}, 1000);
		}, 500);
	};
}
// 添加 updateStatus 函数
const updateStatus = async row => {
	rowData.value = row;
	//reminderVisible.value = true;
	try {
		const flowList = await service.flow.flow.list({
			formId: tableId.value,
			active: 1
		});
		console.log('flowList', flowList);
		if (flowList && flowList.length > 0) {
			try {
				const res = await service.flow.process.start({
					flowId: flowList[0].id,
					businessId: rowData.value.id,
					formData: rowData.value
				});
				console.log('提交res', res);
				await service.cloud.db.data({
					id: tableId.value,
					method: 'update',
					params: {
						id: rowData.value.id,
						projectCode: rowData.value.projectCode,
						审核状态: AUDIT_STATUS.UNAUDITED // 将审核状态更新为0
					}
				});
			} catch (error) {
				console.error('审核流程启用失败:', error);
				ElMessage.error(`审核流程启用失败,请重试${JSON.stringify(error)}`);
			}
		} else {
			await service.cloud.db.data({
				id: tableId.value,
				method: 'update',
				params: {
					id: rowData.value.id,
					projectCode: rowData.value.projectCode,
					审核状态: AUDIT_STATUS.UNAUDITED // 将审核状态更新为0
				}
			});
		}
		//reminderVisible.value = false;
		ElMessage.success('提交审核成功');
		loading.value = false;
		refresh(); // 刷新表格数据
	} catch (error) {
		console.error('提交审核失败:', error);
		ElMessage.error(`提交审核失败,请重试${JSON.stringify(error.message)}`);
	}
};
</script>
<style scoped lang="scss">
.notice-preview {
	padding: 8px;

	.preview-title {
		margin: 0 0 12px;
		padding-bottom: 10px;
		font-size: 16px;
		font-weight: bold;
		color: #303133;
		border-bottom: 1px solid #ebeef5;
		position: relative;
	}

	.preview-info {
		display: flex;
		align-items: center;
		gap: 10px;
		margin-bottom: 16px;
		padding: 0 4px;

		.preview-time {
			color: #909399;
			font-size: 13px;
		}
	}

	.preview-content {
		padding: 12px;
		font-size: 14px;
		line-height: 1.6;
		color: #606266;
		max-height: 300px;
		overflow-y: auto;
		background-color: #f8f9fa;
		border-radius: 4px;

		:deep(p) {
			margin: 8px 0;
		}

		:deep(img) {
			max-width: 100%;
			height: auto;
			border-radius: 4px;
			margin: 8px 0;
		}

		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-thumb {
			background-color: #dcdfe6;
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background-color: #f0f2f5;
			border-radius: 3px;
		}
	}
}
</style>
