<template>
	<cl-view-group ref="ViewGroup">
		<template #left>
			<!-- 部门列表 -->
			<form-type-list
				@refresh="refresh"
				@table-add="onTableAdd"
				@tree-data="onTreeData"
				:isEdit="false"
			/>
		</template>
		<template #right>
			<cl-crud ref="Crud">
				<cl-row>
					<!-- 刷新按钮 -->
					<cl-refresh-btn @click="luruTableStore.clearCache()" />
					<!-- 新增按钮 -->

					<cl-flex1 />
					<!-- 关键字搜索 -->
				</cl-row>

				<cl-row>
					<!-- 数据表格 -->
					<cl-table ref="Table" row-key="id" @row-click="onRowClick">
						<template #slot-btn="{ scope }">
							<el-button
								v-if="scope.row.tableType != TABLE_TYPE.SYSTEM"
								type="success"
								@click="openTableData(scope.row, '查看')"
								text
								bg
								style="margin-bottom: 10px"
								>查看
							</el-button>

							<el-button
								v-if="
									scope.row.tableType != TABLE_TYPE.SYSTEM &&
									scope.row.tableType != TABLE_TYPE.ONLY_ADD &&
									!scope.row.tableInfo.autoincrement
								"
								type="primary"
								@click="openTableData(scope.row, '新增')"
								text
								bg
								style="margin-bottom: 10px"
								>新增
							</el-button>
						</template>
					</cl-table>
				</cl-row>

				<cl-row>
					<cl-flex1 />
					<cl-pagination />
				</cl-row>
				<!-- 新增、编辑 -->
				<cl-upsert ref="Upsert">
					<template #slot-value="{ scope }">
						<div class="form-value">
							<el-input
								v-model="scope.value"
								placeholder="请填写代码"
								clearable
								type="textarea"
								:rows="4"
							/>
							<div class="op"></div>
						</div>
					</template>
				</cl-upsert>
			</cl-crud>
		</template>
	</cl-view-group>
	<el-dialog v-model="tableListVisible">
		<el-form-item label="选择表单">
			<el-select></el-select>
		</el-form-item>
	</el-dialog>
</template>
<script lang="ts" name="luruTableView" setup>
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { useBase } from '/$/base';
import { ref, onMounted } from 'vue';
import { useViewGroup } from '/@/plugins/view';
import { ElMessage } from 'element-plus';
import FormTypeList from '../components/formTypeList.vue';
import FormTypeTransfer from '../components/formTypeTransfer.vue';
import { PermissionInfo, TABLE_TYPE } from '../utils/constants';
import { useStore } from '../store';

const { service, router } = useCool();
const dialogVisible = ref(false);
const formList: any = ref([]);
const formTransferData: any = ref([]);
const { user } = useBase();
const { luruTableStore } = useStore();

// 常量定义
const NAME = PermissionInfo.LURU;

let promission;
const formPermissions = user?.userPromission[NAME];
console.log('formPermissions', user?.userPromission, formPermissions);
if (formPermissions['ALL'] && formPermissions['ALL'][0] == 'ALL') {
	promission = ['ALL'];
} else {
	promission = formPermissions[router.options.history.state.id] || '';
}

onMounted(async () => {});

const typeId = ref();
const typeName = ref('');
const tableListVisible = ref<boolean>(false);
const Table = useTable({
	contextMenu: [],
	columns: [
		{ label: '序号', prop: 'id', minWidth: 60 },
		{ label: '名称', prop: 'name', align: 'left', minWidth: 220 },
		{ label: '备注', prop: 'readme', showOverflowTooltip: true, minWidth: 150 },
		// 启用状态显示成el-switch，不可编辑
		{
			label: '启用状态',
			prop: 'status',
			showOverflowTooltip: true,
			minWidth: 60,
			dict: [
				{
					label: '启用',
					value: 1,
					type: 'success'
				},
				{
					label: '禁用',
					value: 0,
					type: 'danger'
				}
			]
		},
		{ label: '创建时间', prop: 'createTime', sortable: 'custom', minWidth: 100 },

		{
			type: 'op',
			minWidth: 120,
			buttons({ scope }) {
				return ['slot-btn'];
			}
		}
	]
});

const onTableAdd = () => {
	tableListVisible.value = true;
};
const onTreeData = list => {
	console.log('list', list);
};
const dialogSubmit = value => {
	dialogClose();
	console.log('dialogSubmit', value, typeId.value);
	const updateData: any = [];
	value.map(item => {
		updateData.push({
			id: item,
			formTypeId: typeId.value
		});
	});
	service.cloud.db.update(updateData).then(res => {
		console.log('res', res);
	});
};
const dialogClose = () => {
	dialogVisible.value = false;
};

const openTableData = (row, type) => {
	console.log('row', row, row.colInfo);

	router.push({
		path: '/luruTable/data',
		state: {
			id: row.id,
			name: row.name,
			type: type,
			checkDataId: null
		}
	});
};

const { ViewGroup } = useViewGroup({
	label: '类型',
	title: '表单',
	service: service.cloud.formType,
	onSelect(item) {
		console.log('点击item', item);
		typeId.value = item.id;
		typeName.value = item.name;
		// refresh({
		// 	formType: item.id,
		// 	page: 1
		// });
	},
	onData(list) {
		console.log('list', list);
		return list;
	},
	onEdit(item) {
		console.log(item);
		return {};
	}
});

const Crud = useCrud({
	service: service.cloud.db,

	async onRefresh(params, { next }) {
		// 默认使用 next(params)，也可以自己对数据进行处理

		params.status = 1;
		params.tableType = [TABLE_TYPE.USER, TABLE_TYPE.ONLY_ADD];
		params.formType = NAME;
		next({
			...params
		});
	}
});

function onRowClick(row: any, column: any) {
	if (column?.property && row.children) {
		Table.value?.toggleRowExpansion(row);
	}
}

async function refresh(params?: any) {
	// console.log("刷新")
	Crud.value?.refresh(params);
}
</script>
