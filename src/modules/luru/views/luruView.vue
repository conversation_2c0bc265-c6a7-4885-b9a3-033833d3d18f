<template>
	<cl-view-group ref="ViewGroup">
		<template #left>
			<!-- 部门列表 -->
			<form-type-list
				@refresh="refresh"
				@table-add="onTableAdd"
				@tree-data="onTreeData"
				:isEdit="false"
			/>
		</template>
		<template #right>
			<cl-crud ref="Crud">
				<cl-row>
					<!-- 刷新按钮 -->
					<cl-refresh-btn />
					<!-- 新增按钮 -->

					<cl-flex1 />
					<!-- 关键字搜索 -->
				</cl-row>

				<cl-row>
					<!-- 数据表格 -->
					<cl-table ref="Table">
						<template #slot-btn="{ scope }">
							<el-button
								type="success"
								@click="openTableData(scope.row, '查看')"
								text
								bg
								style="margin-bottom: 10px; width: 90px"
								>查看
							</el-button>
						</template>
					</cl-table>
				</cl-row>

				<cl-row>
					<cl-flex1 />
					<cl-pagination />
				</cl-row>
				<!-- 新增、编辑 -->
				<cl-upsert ref="Upsert">
					<template #slot-value="{ scope }">
						<div class="form-value">
							<el-input
								v-model="scope.value"
								placeholder="请填写代码"
								clearable
								type="textarea"
								:rows="4"
							/>
							<div class="op"></div>
						</div>
					</template>
				</cl-upsert>
			</cl-crud>
		</template>
	</cl-view-group>
	<el-dialog v-model="tableListVisible">
		<el-form-item label="选择表单">
			<el-select></el-select>
		</el-form-item>
	</el-dialog>
</template>
<script lang="ts" name="luruView" setup>
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { useBase } from '/$/base';
import { ref, onMounted } from 'vue';
import { useViewGroup } from '/@/plugins/view';
import { ElMessage } from 'element-plus';
import FormTypeList from '../components/formTypeList.vue';

import { PermissionInfo, TABLE_TYPE } from '../utils/constants';

const { service, router } = useCool();
const dialogVisible = ref(false);

const { user } = useBase();

// 常量定义
const NAME = PermissionInfo.CHAKAN;

let promission;
const formPermissions = user?.userPromission[NAME];
console.log('formPermissions', user?.userPromission, formPermissions);
if (formPermissions['ALL'] && formPermissions['ALL'][0] == 'ALL') {
	promission = ['ALL'];
} else {
	promission = formPermissions[router.options.history.state.id] || '';
}

onMounted(async () => {});

const typeId = ref();
const typeName = ref('');
const tableListVisible = ref<boolean>(false);
const Table = useTable({
	contextMenu: [],
	columns: [
		{
			label: '序号',
			prop: 'id',
			minWidth: 50
		},
		{
			label: '表名',
			prop: 'name',
			minWidth: 250,
			search: {
				isInput: false, // 默认false，是否输入框模式
				value: '', // 默认值
				refreshOnChange: false, // 默认false，搜索时刷新数据，service 的 page 接口请求参数为 { page: 1, [绑定的prop]: 输入值 }
				// 自定义渲染组件
				component: {
					name: 'el-input',
					props: {
						placeholder: '搜索表名',
						onChange(val) {
							Crud.value?.refresh({
								page: 1,
								//name: val,
								keyWord: val
							});
						}
					}
				}
			}
		},

		{
			label: '备注',
			prop: 'readme',
			minWidth: 200,
			search: {
				isInput: false, // 默认false，是否输入框模式
				value: '', // 默认值
				refreshOnChange: true, // 默认false，搜索时刷新数据，service 的 page 接口请求参数为 { page: 1, [绑定的prop]: 输入值 }
				// 自定义渲染组件
				component: {
					name: 'el-input',
					props: {
						placeholder: '搜索备注'
					}
				}
			}
		},
		{
			label: '创建时间',
			prop: 'createTime',
			minWidth: 150,
			search: {
				component: {
					name: 'cl-date-picker', // cl-date-picker 自带 onChange 刷新
					props: {
						type: 'daterange',
						valueFormat: 'YYYY-MM-DD'
					}
				}
			}
		},
		{
			type: 'op',
			buttons: ['slot-btn']
		}
	]
});

const onTableAdd = () => {
	tableListVisible.value = true;
};
const onTreeData = list => {
	console.log('list', list);
};

const dialogClose = () => {
	dialogVisible.value = false;
};
const openTableData = (row, type) => {
	console.log('row', row, row.colInfo);
	if (row.name == '询证函') {
		router.push({
			path: '/xzhView'
		});
	} else if (row.name == '询证函详情') {
		router.push({
			path: '/xzhDetailView'
		});
	} else {
		router.push({
			path: '/luruViewData',
			state: {
				id: row.id,
				name: row.name,
				type: type
			}
		});
	}
};

const { ViewGroup } = useViewGroup({
	label: '类型',
	title: '表单',
	service: service.cloud.formType,
	onSelect(item) {
		console.log('点击item', item);
		typeId.value = item.id;
		typeName.value = item.name;
		refresh({
			formType: item.id,
			page: 1
		});
	},
	onData(list) {
		console.log('list', list);
		return list;
	},
	onEdit(item) {
		console.log(item);
		return {};
	}
});

const Crud = useCrud({
	service: service.cloud.db,

	async onRefresh(params, { next }) {
		// 默认使用 next(params)，也可以自己对数据进行处理

		params.status = [1, 3];
		params.tableType = [
			TABLE_TYPE.USER,
			TABLE_TYPE.ONLY_ADD,
			TABLE_TYPE.ONLY_VIEW,
			TABLE_TYPE.SYSTEM
		];
		params.formType = NAME;
		next({
			...params
		});
	}
});

async function refresh(params?: any) {
	// console.log("刷新")
	Crud.value?.refresh(params);
}
</script>
