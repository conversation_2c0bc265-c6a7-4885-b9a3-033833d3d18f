<template>
	<cl-crud class="cl-crud div1" v-loading="loading">
		<cl-row>
			<fieldSerch :columnList="luruColumnList" @onSearch="onSearch"></fieldSerch>
			<el-button type="primary" @click="changeCol">自定义列</el-button>
			<cl-flex1 />
			<el-button type="primary" v-if="operationDesc" @click="operationDescVisible = true"
				><el-icon
					:size="18"
					class="no-inherit"
					@click="operationDescVisible = true"
					style="margin-right: 5px"
				>
					<document /> </el-icon
				>操作说明</el-button
			>
			<!-- <el-tooltip content="操作说明" placement="top" v-if="operationDesc">
				<el-icon
					color="#4165d7"
					:size="22"
					class="no-inherit"
					@click="operationDescVisible = true"
					style="margin-right: 20px"
				>
					<document />
				</el-icon>
			</el-tooltip> -->
			<el-button type="primary" @click="refresh">刷新</el-button>

			<el-button :icon="Download" @click="exportExcel">导出</el-button>
		</cl-row>
		<cl-row>
			<el-table :data="tableData" style="width: 100%" :max-height="500">
				<el-table-column
					v-for="(item, index) in visibleColumns"
					:key="index"
					:label="item.showName === 'projectCode' ? '项目代码' : item.showName"
					:prop="item.name"
					:hidden="item.selected"
					:width="
						item.showName.includes('项目') || item.showName.includes('单位')
							? 330
							: item.showName.includes('备注')
								? 520
								: item.showName.length * 20 + 60
					"
					:show-overflow-tooltip="false"
				>
					<template #default="scope">
						<span v-if="item.type === 'date'">
							{{ formatDate(item.dateType, scope.row[item.name]) }}
						</span>
						<span v-else-if="item.type === 'number' && item.isPercent">
							{{
								(scope.row[item.name] * 100).toFixed(
									item.decimals - 2 >= 0 ? item.decimals - 2 : 0
								)
							}}%
						</span>
						<span v-else-if="item.type === 'number'">
							{{ formatNumber(scope.row[item.name], item.decimals) }}
						</span>
						<span v-else-if="item.type === 'picture'">
							<img :src="scope.row[item.name][0] || ''" min-width="70" height="70" />
						</span>
						<span v-else-if="item.name == '审核状态'">
							<el-tag
								:type="
									scope.row.审核状态 == AUDIT_STATUS.UNAUDITED
										? 'primary'
										: scope.row.审核状态 == AUDIT_STATUS.APPROVED
											? 'success'
											: scope.row.审核状态 == AUDIT_STATUS.REJECTED
												? 'danger'
												: 'info'
								"
								disable-transitions
							>
								{{
									scope.row.审核状态 == AUDIT_STATUS.UNAUDITED
										? '待审核'
										: scope.row.审核状态 == AUDIT_STATUS.APPROVED
											? '审核通过'
											: scope.row.审核状态 == AUDIT_STATUS.REJECTED
												? '已驳回'
												: scope.row.审核状态 == AUDIT_STATUS.DRAFT
													? '暂存'
													: scope.row.审核状态 == AUDIT_STATUS.INVALID
														? '作废'
														: '已归档'
								}}
							</el-tag>
						</span>
						<span v-else>
							{{ scope.row[item.name] }}
						</span>
					</template>
				</el-table-column>
				<el-table-column
					label="最新编辑人"
					prop="最新编辑人"
					v-if="tableType == TABLE_TYPE.ONLY_ADD"
					width="120"
				></el-table-column>
				<el-table-column label="创建时间" prop="createTime" width="180"></el-table-column>
				<el-table-column label="更新时间" prop="updateTime" width="180"></el-table-column>
			</el-table>
		</cl-row>
		<div>
			<el-pagination
				v-model:current-page="currentPage"
				:page-size="pageSize"
				:disabled="disabled"
				layout="total, prev, pager, next"
				:total="total"
				@current-change="handleCurrentChange"
				style="margin-top: 15px; margin-right: 10px; float: right"
			/>
		</div>

		<colChange
			:changeColVisible="changeColVisible"
			:name="colName"
			:allColumns="luruColumnList"
			@closeDialog="closeColChange"
			@submitData="getNewCol"
		></colChange>
		<cl-dialog v-model="operationDescVisible" title="操作 说明" width="50%">
			<div class="notice-preview">
				<h3 class="preview-title">
					{{ tableName }}
				</h3>

				<div class="preview-content" v-html="operationDesc"></div>
			</div>
			<template #footer>
				<div style="display: flex; justify-content: flex-end">
					<el-button @click="operationDescVisible = false">关闭</el-button>
					<el-button type="primary" @click="downloadPDF">
						<el-icon style="margin-right: 5px"><download /></el-icon>
						下载PDF
					</el-button>
				</div>
			</template>
		</cl-dialog>
	</cl-crud>
</template>

<script setup lang="ts" name="luruViewData">
import { onMounted, ref, computed, onErrorCaptured, toRefs, nextTick, onActivated } from 'vue';
import { useBase } from '/$/base';
import { useCool } from '/@/cool';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Document } from '@element-plus/icons-vue';
import { cloneDeep } from 'lodash-es';
import fieldSerch from '../components/field-serch.vue';
import { TABLE_TYPE } from '../utils/constants';
import colChange from '../components/colChange.vue';
import { storage } from '/@/cool';
import { PermissionInfo } from '../utils/constants';
import { useStore } from '../store';
import { formatNumber } from '../utils/formatTableData';
import { getDictFromIndexDB, saveDictToIndexDB } from '../utils/indexDB';
import { ExportExcel } from '/@/modules/luru/utils/exportExcel';
import { Download } from '@element-plus/icons-vue';
import { changeRules } from '../utils/luruTable';
import { AUDIT_STATUS } from '../utils/constants';
const { ProjectDictId, TableId, CHAKAN, SHENHE, SHEJI, FormPermission } = PermissionInfo;

const { service, router, route } = useCool();
const { user } = useBase();
const { dictCols, luruTableStore } = useStore();
const tableType: any = ref('');
const loading = ref(false);

const NAME = CHAKAN;

let promission;
const tableId = ref();
const props = defineProps({
	pName: {
		type: String,
		default: ''
	},
	pId: {
		type: Number
	},
	pType: {
		type: String,
		default: ''
	}
});
const { pName, pId, pType } = toRefs(props);
try {
	const formPermissions = user?.userPromission[NAME];
	if (formPermissions['ALL'] && formPermissions['ALL'][0] == 'ALL') {
		promission = ['ALL'];
	} else {
		promission =
			formPermissions[router.options.history.state.id || pId.value] ||
			formPermissions[tableId.value] ||
			'';
	}
} catch (err) {
	console.log('获取权限失败', err);
	ElMessageBox.confirm('获取权限失败，请联系管理员', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'error'
	});
	router.push('/');
}
console.log('￥￥￥权限', promission);
const colName = ref<string>('');
const tableName = ref<string>('');
const metaData = ref([]);
const tableData = ref([]);
const addtableVisible = ref(false);

const operationDescVisible = ref(false);
const operationDesc = ref('');

const clickRow = ref();
const changeColVisible = ref(false);

const columnList: any = ref([]);
const luruColumnList: any = ref([]);

const title = ref<string>('新增');
const rowData = ref();

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const disabled = ref(false);

const hiddenButton = ref(null);
const exportCol: any = ref([]);
const filename = ref<string>('');
const metaColData: any = ref([]);
const ruleColumnList = ref([]);
const checkDataIds = ref([]);
const ruleId = ref();

onActivated(async () => {
	if (router.options.history.state.name || pName.value) {
		loading.value = true;
		//exportCol.value=[]
		tableId.value = router.options.history.state.id || pId.value;
		const luruTableInfo: any = await luruTableStore.getluruTableInfo(tableId.value as string);
		if (router.options.history.state?.checkDataIds) {
			checkDataIds.value = JSON.parse(router.options.history.state?.checkDataIds);
			ruleId.value = router.options.history.state?.ruleId;
		} else {
			checkDataIds.value = [];
		}
		console.log('rluruTableInfo', luruTableInfo);
		tableName.value = (router.options.history.state.name as string) || pName.value;
		tableType.value = luruTableInfo.tableType;
		operationDesc.value = luruTableInfo.operationDesc as string;
		console.log('tableId.valu', tableId.value, tableName.value);
		ruleColumnList.value = luruTableInfo.colInfo;

		columnList.value = changeRules(ruleColumnList.value);
		luruColumnList.value = [
			{ name: 'id', showName: '序号', type: 'number', selected: true },
			{ name: '录入人', showName: '录入人', type: 'string', selected: true },
			{ name: '审核状态', showName: '状态', type: 'luruStatus', selected: true },
			{ name: 'projectCode', showName: '项目代码', type: 'string', selected: true }
		];

		colName.value = '查看' + '-' + tableName.value;

		console.log('导出列', exportCol.value);
		if (storage.get(colName.value)) {
			luruColumnList.value = storage.get(colName.value);
		} else {
			columnList.value.forEach(item => {
				if (item.isTableForm) {
					return;
				}
				if (item.type === 'dict') {
					luruColumnList.value.push({
						name: item.name,
						showName: item.showName,
						type: 'string',
						selected: !item.isNotShow
					});
					if (item.isProject) {
						luruColumnList.value.push({
							name: 'projectCode',
							showName: 'projectCode',
							type: 'string',
							selected: !item.isNotShowCode
						});
					} else {
						luruColumnList.value.push({
							name: `${item.name}代码`,
							showName: `${item.showName}代码`,
							type: 'string',
							selected: !item.isNotShowCode
						});
					}
				} else if (item.type == 'relatedCol' && item.relatedTableCol) {
					luruColumnList.value.push({ selected: !item.isNotShow, ...item });
				} else if (item.type == 'calculatedCol') {
					console.log('计算列', item);
					const calculatedData: any = {};
					calculatedData.name = item.name;
					calculatedData.showName = item.showName;
					calculatedData.type = item.calculateType;
					if (calculatedData.type == 'number') {
						calculatedData.decimals = parseInt(item.decimals) || 0;
					} else if (calculatedData.type == 'date') {
						calculatedData.dateType = item.dateType || 'YYYY-MM-DD';
					}
					console.log('calculatedData', item.name, calculatedData);
					luruColumnList.value.push({ selected: !item.isNotShow, ...calculatedData });
				} else {
					if (item.type == 'number' && item.decimals) {
						item.decimals = parseInt(item.decimals);
					}
					luruColumnList.value.push({ selected: !item.isNotShow, ...item });
					//exportCol.value.push({label:item.name,prop:item.name,type:item.type})
				}
			});
		}
		metaColData.value = cloneDeep(luruColumnList.value);

		//  exportCol.value.push
		console.log('传给搜索组件的数据！！！', luruColumnList.value, columnList.value);
		filename.value = router.options.history.state.name || pName.value;
		console.log('组件间传值', columnList.value);
		//exportCol.value=luruColumnList.value.map((item:any)=>{return {label:item.name,prop:item.name,type:item.type}})

		console.log('columnList', columnList.value);
		refresh();
		loading.value = false;
	} else {
		//await nextTick();
		console.log('路由值', router.options.history.state);
		console.log('值', tableName.value, tableType.value, columnList.value);
	}
});
onErrorCaptured((err, instance, info) => {
	console.log('Captured an error:', err, instance, info);
	// 返回 false 以阻止错误继续传播
	return false;
});

async function exportExcel() {
	loading.value = true;
	const columns = columnList.value.map(item => item.name);
	const obj: any = [];
	if (searchData.value.length > 0) {
		console.log('searchData.value111', searchData.value);
		searchData.value.map(item => {
			obj.push({
				colName: item.selectValue.split('_')[0],
				type: item.selectValue.split('_')[1],
				value: item.inputValue,
				operation: item.operation
			});
		});
		// tableData.value = searchByField(metaData.value, searchData.value);
	}
	const res = await service.cloud.db.data({
		id: tableId.value,
		method: 'list',
		params: {
			formType: NAME,
			page: currentPage.value,
			size: pageSize.value,
			searchParams: obj
		}
	});
	loading.value = false;
	ExportExcel(res.list, columns, tableName.value);
}
//排序
function sortDataArrayByName(sortArray, dataArray) {
	// 创建一个映射，将 sortArray 中的 name 映射到它们的顺序
	const nameOrderMap = new Map();
	sortArray.forEach((item, index) => {
		nameOrderMap.set(item.name, index);
	});

	// 使用映射对 dataArray 进行排序
	return dataArray.sort((a, b) => {
		return nameOrderMap.get(a.name) - nameOrderMap.get(b.name);
	});
}

const visibleColumns = computed(() => {
	return luruColumnList.value.filter(column => column.selected);
});
//自定义列
function changeCol() {
	changeColVisible.value = true;
}

function closeColChange() {
	changeColVisible.value = false;
}

function getNewCol(data) {
	console.log('返回的自定义列', data);
	luruColumnList.value = [];
	// data.forEach(ele => {
	//   const newCol=metaColData.value.filter((item)=> item.name==ele.name)
	//   if(newCol.length>0){
	//     luruColumnList.value.push({selected:ele.selected,...newCol[0]})
	//   }
	// });
	luruColumnList.value = data;
}

function formatDate(dateType, value) {
	// console.log("日期格式",dateType,value)
	const date = new Date(value);
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const hours = String(date.getHours()).padStart(2, '0');
	const minutes = String(date.getMinutes()).padStart(2, '0');
	const seconds = String(date.getSeconds()).padStart(2, '0');
	if (!dateType) dateType = 'YYYY-MM-DD';
	switch (dateType) {
		case 'YYYY-MM':
			return `${year}-${month}`;
		case 'YYYY':
			return `${year}`;
		case 'YYYY-MM-DD':
			return `${year}-${month}-${day}`;
		case 'YYYY-MM-DD HH:mm:ss':
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		default:
			throw new Error('错误的日期格式');
	}
}

//切换表格页数
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};
const searchData = ref([]);

//分字段搜索
function onSearch(data: any) {
	searchData.value = data;
	// console.log("搜索数据",searchData.value,metaData.value,data)
	console.log('开始搜索', metaData.value, 'data', data);
	refresh();
	//tableData.value = cloneDeep(searchByField(metaData.value, data));
}

const refresh = () => {
	if (checkDataIds.value.length > 0) {
		service.quality.check
			.outterRecords({
				rowIds: checkDataIds.value,
				ruleId: ruleId.value
			})
			.then(res => {
				tableData.value = res;
				// if (searchData.value.length > 0) {
				// 	tableData.value = searchByField(res, searchData.value);
				// }
				console.log('搜索结果', tableData.value);
			});
	} else {
		const obj: any = [];
		if (searchData.value.length > 0) {
			console.log('searchData.value111', searchData.value);
			searchData.value.map(item => {
				obj.push({
					colName: item.selectValue.split('_')[0],
					type: item.selectValue.split('_')[1],
					value: item.inputValue,
					operation: item.operation
				});
			});
			// tableData.value = searchByField(metaData.value, searchData.value);
		}
		service.cloud.db
			.data({
				id: tableId.value,
				method: 'page',
				params: {
					formType: NAME,
					page: currentPage.value,
					size: pageSize.value,
					searchParams: obj
				}
			})
			.then(res => {
				console.log('获取全部结果data', res);
				if (res.list) {
					tableData.value = res.list;
					total.value = res.pagination.total;
				}
			})
			.catch(err => {
				ElMessage.error(err.message);
			});
	}
};

// 下载PDF功能
async function downloadPDF() {
	const content = document.querySelector('.notice-preview');
	if (!content) return;

	// 创建新窗口用于打印
	const printWindow = window.open('', '_blank');
	if (!printWindow) {
		ElMessage.error('请允许打开新窗口');
		return;
	}

	// 获取所有图片并转换为base64
	const images = content.querySelectorAll('img');
	const imagePromises = Array.from(images).map(
		img =>
			new Promise(resolve => {
				const canvas = document.createElement('canvas');
				const ctx = canvas.getContext('2d');
				const newImg = new Image();

				newImg.crossOrigin = 'anonymous'; // 处理跨域图片
				newImg.onload = () => {
					canvas.width = newImg.width;
					canvas.height = newImg.height;
					ctx?.drawImage(newImg, 0, 0);
					try {
						const base64 = canvas.toDataURL('image/png');
						img.src = base64; // 替换原始src为base64
						resolve(true);
					} catch (e) {
						console.error('图片转换失败:', e);
						resolve(false);
					}
				};
				newImg.onerror = () => {
					console.error('图片加载失败:', img.src);
					resolve(false);
				};
				newImg.src = img.src;
			})
	);

	try {
		await Promise.all(imagePromises);
	} catch (e) {
		console.error('图片处理失败:', e);
	}

	// 写入打印内容
	printWindow.document.write(`
    <html>
      <head>
        <title>${tableName.value}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            padding: 20px;
            color: #303133;
          }
          .preview-title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
          }
          .preview-content {
            line-height: 1.6;
            font-size: 14px;
            color: #606266;
          }
          img {
            max-width: 100%;
            height: auto;
            margin: 10px 0;
            page-break-inside: avoid;
          }
          @media print {
            body { -webkit-print-color-adjust: exact; }
            img { max-width: 100% !important; }
          }
        </style>
      </head>
      <body>
        ${content.innerHTML}
      </body>
    </html>
  `);

	printWindow.document.close();

	// 等待内容加载完成后打印
	printWindow.onload = function () {
		setTimeout(() => {
			printWindow.print();
			// 打印完成后关闭窗口
			setTimeout(() => {
				printWindow.close();
			}, 1000);
		}, 500);
	};
}
</script>
<style scoped lang="scss">
.notice-preview {
	padding: 8px;

	.preview-title {
		margin: 0 0 12px;
		padding-bottom: 10px;
		font-size: 16px;
		font-weight: bold;
		color: #303133;
		border-bottom: 1px solid #ebeef5;
		position: relative;
	}

	.preview-info {
		display: flex;
		align-items: center;
		gap: 10px;
		margin-bottom: 16px;
		padding: 0 4px;

		.preview-time {
			color: #909399;
			font-size: 13px;
		}
	}

	.preview-content {
		padding: 12px;
		font-size: 14px;
		line-height: 1.6;
		color: #606266;
		max-height: 300px;
		overflow-y: auto;
		background-color: #f8f9fa;
		border-radius: 4px;

		:deep(p) {
			margin: 8px 0;
		}

		:deep(img) {
			max-width: 100%;
			height: auto;
			border-radius: 4px;
			margin: 8px 0;
		}

		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-thumb {
			background-color: #dcdfe6;
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background-color: #f0f2f5;
			border-radius: 3px;
		}
	}
}
</style>
