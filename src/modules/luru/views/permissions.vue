<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />

			<cl-flex1 />
			<!-- 关键字搜索 -->
			<cl-search-key />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table">
				<!-- <template #slot-btn="{ scope }">
					<el-button
						v-if="scope.row.审核状态 == 1"
						type="success"
						@click="changeStatus(scope.row)"
						text
						bg
						style="margin-bottom: 10px; width: 90px"
						>归档
					</el-button>
				</template> -->
			</cl-table>
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { useStore } from '/@/modules/base/store';
import {
	SystemDict,
	PermissionInfo,
	PerFormPermission,
	PerProjectPermission
} from '../utils/constants';
import { ElMessage } from 'element-plus';
import { cloneDeep } from 'lodash-es';
import { AUDIT_STATUS } from '../utils/constants';

const { service } = useCool();
const { user } = useStore();
const projectNameList = ref<any[]>([]);
const formNameList = ref<any[]>([]);
const userNameList = ref<any[]>([]);
const projectDictName = SystemDict.Project;
const formDictName = SystemDict.Form;
const userDictName = SystemDict.User;
const permissionTypeDictName = SystemDict.PermissionType;

const roleList = ref<any[]>([]);
const permissionTypeList = ref<any[]>([]);
const xzhInfo = ref();
const xzhDetailInfo = ref();
// TODO 后续重写成从pinia中获取，同时设置定时器，pinia重新获取所有字典，后端设置缓存
onMounted(async () => {
	const res = await service.dict.info.data({
		types: [projectDictName, permissionTypeDictName, formDictName, userDictName]
	});
	projectNameList.value = res[projectDictName]
		.filter(dict => {
			return dict.parentId === null;
		})
		.map(item => {
			return { label: item.name, value: item.name + '&' + item.value };
		});
	projectNameList.value.unshift({ label: '所有项目', value: 'ALL&ALL' });
	//	const permissionTypeRes = await service.dict.info.data({ types: [permissionTypeDictName] });
	//console.log('permissionTypeRes', permissionTypeRes);
	permissionTypeList.value = res[permissionTypeDictName].map(item => {
		return {
			label:
				item.name +
				(PerFormPermission.includes(item.name) || PerProjectPermission.includes(item.value)
					? '（基于表单和项目）'
					: '（基于功能）'),
			value: item.name + '&' + item.value
		};
	});
	//const formNameRes = await service.dict.info.data({ types: [formDictName] });
	xzhInfo.value = res[formDictName]
		.filter(item => ['询证函'].includes(item.name))
		.map(item => ({
			label: item.name,
			value: item.name + '&' + item.value
		}));
	xzhDetailInfo.value = res[formDictName].filter(item => item.name == '询证函详情')[0].value;
	formNameList.value = res[formDictName]
		.filter(item => item.name !== '询证函' && item.name !== '询证函详情')
		.map(item => ({
			label: item.name,
			value: item.name + '&' + item.value
		}));
	formNameList.value.unshift({ label: '所有表单', value: 'ALL&ALL' }); // 修改这里，保持格式一致

	// const userNameRes = await service.dict.info.data({
	// 	types: [userDictName]
	// });
	userNameList.value = res[userDictName].map(item => {
		return { label: item.name, value: item.value };
	});
	userNameList.value.unshift({ label: 'ALL(所有用户)', value: 'ALL' }); // 添加这一行
	roleList.value = (await service.base.sys.role.list())
		.filter(item => item.type === 'user')
		.map(item => ({
			label: item.name,
			value: `${item.name}& ${item.label} `
		}));
	console.log('roleList', roleList.value);
});

const updateFormList = (permissionType?: string) => {
	// 先移除可能存在的询证函相关选项
	formNameList.value = formNameList.value.filter(
		item => item.label !== '询证函' && item.label !== '询证函详情'
	);

	Upsert.value.form.formName = '';
	// 只在权限类型为"查看"时添加询证函相关选项
	if (permissionType === '查看') {
		formNameList.value = [...xzhInfo.value, ...formNameList.value];
		//formNameList.value.push({ label: '询证函详情', value: '询证函详情&询证函详情' });
	}
};

const loading = ref(false);
const filteredUserList = ref([]);
// async function changeStatus(row: any) {
// 	console.log('row', row);
// 	await service.luru.luruPermission.update({
// 		id: row.id,
// 		params: {
// 			审核状态: 5
// 		}
// 	});
// }
const remoteSearchForms = async (query: string) => {
	if (query !== '') {
		loading.value = true;
		// 模拟API调用，实际使用时替换为真实的API调用
		await new Promise(resolve => setTimeout(resolve, 200));
		filteredUserList.value = userNameList.value
			.filter(item => {
				return item.label.toLowerCase().includes(query.toLowerCase());
			})
			.slice(0, 20); // 只返回前20个匹配结果
		loading.value = false;
	} else {
		filteredUserList.value = [];
	}
};
// cl-table
const Table = useTable({
	contextMenu: [],
	columns: [
		{ label: '创建时间', prop: 'createTime', minWidth: 150 },
		{ label: '更新时间', prop: 'updateTime', minWidth: 150 },
		{ label: '权限所属人', prop: '权限所属人', minWidth: 100 },
		{ label: '权限所属角色', prop: '权限所属角色', minWidth: 100 },
		{ label: '表单权限', prop: '表单权限', minWidth: 120 },
		{ label: '表单权限代码', prop: '表单权限代码', minWidth: 100 },
		{ label: '项目名称', prop: '项目名称', minWidth: 260 },
		{ label: '项目代码', prop: 'projectCode', minWidth: 130 },
		{ label: '功能权限', prop: '功能权限', minWidth: 190 },
		{ label: '审核人', prop: '审核人', minWidth: 120 },
		{
			label: '审核状态',
			prop: '审核状态',
			minWidth: 140,
			dict: [
				{
					label: '未审核',
					value: AUDIT_STATUS.UNAUDITED,
					type: 'warning'
				},
				{
					label: '审核通过',
					value: AUDIT_STATUS.APPROVED,
					type: 'success'
				},
				{
					label: '已驳回',
					value: AUDIT_STATUS.REJECTED,
					type: 'danger'
				},
				{
					label: '已作废',
					value: AUDIT_STATUS.INVALID,
					type: 'danger'
				},
				{
					label: '已暂存',
					value: AUDIT_STATUS.DRAFT,
					type: 'danger'
				},
				{
					label: '已归档',
					value: AUDIT_STATUS.ARCHIVED,
					type: 'info'
				}
			]
		},
		{ label: '审核说明', prop: '审核说明', minWidth: 190 }
		// {
		// 	type: 'op',
		// 	buttons({ scope }) {
		// 		return ['slot-btn'];
		// 	}
		// }
	]
});

const Upsert = useUpsert({
	async onInfo(data, { done, next }) {
		const newData = await next({
			...data,
			status: false
		});
		newData.permissionName = data.permissionName.split(',');
		newData.projectName = [data.projectName + '&' + data.projectCode];

		done(newData);
	},
	items: [
		{
			//【很重要】必须为 tabs
			type: 'tabs',
			props: {
				// 分组样式
				type: 'card',
				// 分组列表，必须是 { label, value } 的数组格式
				labels: [
					{
						label: '基于用户', // 标题
						value: 'user' // 唯一标识
					},
					{
						label: '基于角色',
						value: 'role'
					}
				],
				onChange: name => {
					console.log('切换name', name);
					if (name == 'user') {
						Upsert.value.form.权限所属角色 = '';
					} else {
						Upsert.value.form.权限所属人 = '';
					}
				}
			}
		},
		{
			group: 'user',
			label: '权限所属人',
			prop: '权限所属人',

			component: {
				name: 'el-select',
				props: {
					clearable: true,
					placeholder: '键入用户名搜索',
					filterable: true,
					remote: true,
					remoteMethod: remoteSearchForms,
					loading: loading
				},
				options: filteredUserList
			}
		},
		{
			group: 'role',
			label: '权限所属角色',
			prop: '权限所属角色',

			component: {
				name: 'el-select',
				props: {
					clearable: true,
					filterable: true
				},
				options: roleList
			}
		},
		{
			label: '功能权限',
			prop: 'permissionName',
			component: {
				name: 'el-select',
				props: {
					clearable: true,
					filterable: true,
					onChange: val => {
						const permissionType = val?.split('&')[1];
						updateFormList(permissionType);
					}
				},
				options: permissionTypeList
			}
		},
		{
			hidden({ scope }) {
				return (
					scope?.permissionName &&
					!PerFormPermission.includes(scope.permissionName.split('&')[1]) &&
					!PerProjectPermission.includes(scope.permissionName.split('&')[1])
				);
			},
			label: '项目名',
			prop: 'projectName',
			component: {
				name: 'el-select',
				props: {
					clearable: true,
					multiple: true,
					filterable: true
				},
				options: projectNameList
			}
		},
		{
			hidden({ scope }) {
				return (
					scope?.permissionName &&
					!PerFormPermission.includes(scope.permissionName.split('&')[1])
				);
			},
			label: '表单名',
			prop: 'formName',
			component: {
				name: 'el-select',
				props: {
					clearable: true,
					filterable: true
				},
				options: formNameList
			}
		}
	],

	async onSubmit(data, { done, close, next }) {
		const newData: any = [];
		console.log('data', data, Upsert.value, Upsert.value.Form.value);
		console.log('dd');
		if (!data.权限所属人 && !data.权限所属角色) {
			ElMessage.error('权限所属人或权限所属角色未选择');
			done();
			return;
		}

		//const user
		try {
			if (
				!PerFormPermission.includes(data.permissionName.split('&')[1]) &&
				!PerProjectPermission.includes(data.permissionName.split('&')[1])
			) {
				data.projectName = ['ALL&ALL'];
				data.formName = 'ALL&ALL';
			} else if (PerProjectPermission.includes(data.permissionName.split('&')[1])) {
				data.formName = 'ALL&ALL';
			}
			console.log('projectNameList', projectNameList.value);
			data.projectName.map(item => {
				console.log('item', data, item);
				const project = item.split('&');
				console.log('project', item, project);
				console.log('data.formName', data.formName);
				const formName = data.formName.split('&');
				console.log('formName', formName);
				newData.push({
					录入人: user.info?.username,
					权限所属人: data.权限所属人?.trim() || '',
					权限所属人代码: data.权限所属人?.trim() || '',
					权限所属角色: data.权限所属角色?.split('&')[0] || '',
					权限所属角色代码: data.权限所属角色?.split('&')[1] || '',
					权限类型: data.权限所属人 ? 'user' : 'role',
					projectCode: project[1],
					项目名称: project[0],
					功能权限: data.permissionName.split('&')[0],
					功能权限代码: data.permissionName.split('&')[1],
					表单权限: formName[0],
					表单权限代码: formName[1],
					审核状态: AUDIT_STATUS.UNAUDITED,
					审核说明: ' '
				});
			});
			if (
				data.permissionName.split('&')[1] === '查看' &&
				data.formName.split('&')[0] == '询证函'
			) {
				const viewData = cloneDeep(newData);
				viewData.map(item => {
					item.表单权限 = '询证函详情';
					item.表单权限代码 = xzhDetailInfo.value;
				});
				newData.push(...viewData);
			}
			//console.log('newData', xxx);
			data = newData;
			console.log('dat111a', data);
			next(data);
			close();
		} catch (err) {
			ElMessage.error(`新增权限失败,${err.message}`);
			close();
		}
		// try {
		// 	await service.cloud.db.data({
		// 		id: permissionTableId,
		// 		method: 'add',
		// 		params: data
		// 	});
		// } catch (err) {
		// 	ElMessage.error(err.message);
		// 	close();
		// }
		// close();
	}
});

// cl-crud
const Crud = useCrud(
	{
		service: service.luru.luruPermission,
		//service: service.luru.designTable,
		onRefresh(params, { next }) {
			// 默认使用 next(params)，也可以自己对数据进行处理
			next({
				...params
			});
		}
	},
	app => {
		app.refresh();
	}
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
