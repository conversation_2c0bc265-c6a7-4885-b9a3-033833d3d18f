<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-column-custom :columns="dynamicColumns" name="xzhDetailView" />
			<!-- 删除按钮 -->

			<cl-flex1 />
			<span class="cl-dateText">账期选择：</span>
			<monthMultiSelect
				v-model="selectedMonths"
				format="YYYY-MM"
				@submitBtn="searchMonths"
				@resetBtn="searchMonths"
			></monthMultiSelect>
			<!-- 关键字搜索 -->
			<cl-search-key />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" :columns="dynamicColumns">
				<template #column-合同函证影像="{ scope }">
					<cl-row type="flex" align="middle">
						<el-button type="text" @click="openPDF(scope.row.合同函证影像)"
							>预览</el-button
						>
					</cl-row>
				</template>
			</cl-table>
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
		<viewer :ref="setRefs('viewer')" />
	</cl-crud>
</template>

<script lang="ts" name="xzhDetailView" setup>
import { ref, onMounted, onActivated, watch } from 'vue';
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatNumber } from '/@/modules/luru/utils/formatTableData';
import monthMultiSelect from '/@/modules/yszk/components/monthMultiSelect.vue';
import { yearToMonth } from '/@/modules/yszk/utils/xzh';
import { sortAndHideColumns } from '/@/modules/yszk/utils/xzh';
import viewer from '/@/plugins/upload/components/upload-item/viewer.vue';
import { getType } from '/@/plugins/upload/utils';

const { service, router, setRefs, refs } = useCool();
const isPdfPreviewVisible = ref(false);
const pdfUrl = ref('');
const searchField = ref();
const selectedMonths = ref();
const projectCode = ref();
const ksdm = ref();
const dynamicColumns = ref<any[]>([]);
const columns = ref([
	{ label: '询证函单号', prop: '询证函单号', width: 100 },
	{ label: '所属项目代码', prop: 'projectCode', width: 100 },
	{ label: '所属项目名称', prop: '所属项目名称', width: 200 },
	{ label: '科目', prop: '科目' },
	{ label: '客商名称代码', prop: '客商名称代码', width: 100 },
	{ label: '客商名称', prop: '客商名称', width: 200 },
	{ label: '账期', prop: '账期' },
	{ label: '账龄', prop: '账龄' },
	{ prop: '凭证日期', label: '凭证日期' },
	{ prop: '凭证类别', label: '凭证类别' },
	{ prop: '凭证号', label: '凭证号' },
	{ prop: '确权时间', label: '确权时间' },
	{
		label: '确权状态',
		prop: '确权状态',
		hidden: false,
		index: 2,
		orderNum: 6,
		minWidth: 100,
		dict: [
			{
				label: '已确权',
				value: 1,
				type: 'success'
			},
			{
				label: '未确权',
				value: 0,
				type: 'danger'
			}
		]
	},
	{ label: '合同代码', prop: '合同代码', width: 100 },
	{ label: '合同名称', prop: '合同名称', width: 200 },
	{ label: '责任人', prop: '责任人' },

	{
		label: '待确权金额',
		prop: '待确权金额',
		formatter: (row, column, value) => formatNumber(value, 2)
	},
	{
		label: '已确权金额',
		prop: '已确权金额',
		formatter: (row, column, value) => formatNumber(value, 2)
	},
	{
		prop: '合同函证影像',
		label: '合同函证影像',
		minWidth: 90
	},
	{
		label: '回函确认状态',
		prop: '回函确认状态',
		minWidth: 120,
		dict: [
			{ label: '其它方式回函', value: 1, type: 'primary' },
			{ label: '正式回函', value: 2, type: 'success' },
			{ label: '未回函', value: 0, type: 'danger' }
		]
	},
	{ label: '确权人', prop: '确权人' },
	{ label: '备注', prop: '备注' }
]);

// 监听动态列变化
watch(
	dynamicColumns,
	newColumns => {
		if (Table.value) {
			Table.value.columns = newColumns;
		}
	},
	{ deep: true }
);
onActivated(async () => {
	await initializeColumns();
	if (router.options.history.state.询证函单号) {
		console.log('onActivated', router.options.history.state);

		searchField.value = router.options.history.state.searchFields;
		projectCode.value = router.options.history.state.projectCode;
		ksdm.value = router.options.history.state.客商名称代码;
		console.log('projectCode.value', projectCode.value);
		console.log('ksdm.value', ksdm.value);
		//refresh();
		console.log('row', searchField.value, router.options.history.state);
	}
	//refresh();
});

function isImageFile(url: string) {
	const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
	return imageExtensions.some(ext => url.toLowerCase().includes(ext));
}

function openPDF(url: string) {
	console.log('dakai');
	if (url) {
		//window.open(url, "_blank");
		const type = getType(url);
		console.log('type', type);
		refs.viewer.open({ url, type });
		console.log('打开');
		pdfUrl.value = url;
		//isPdfPreviewVisible.value = true;
	} else {
		ElMessage.error('暂无附件');
	}
}

function searchMonths() {
	console.log('selectedMonths.value', selectedMonths.value);
	const months = yearToMonth(selectedMonths.value);
	console.log('months', months);
	Crud.value?.refresh();
}

async function getColumns() {
	try {
		const res = await service.cloud.db.page({
			tableName: 'func_xzh_detail',
			page: 1
		});

		if (res.list?.[0]?.colInfo) {
			return sortAndHideColumns(columns.value, res.list[0].colInfo);
		}
		return [];
	} catch (error) {
		console.error('获取列配置失败:', error);
		return [];
	}
}

// cl-table
const Table = useTable({
	contextMenu: []
});

// 初始化和更新列
async function initializeColumns() {
	//loading.value = true;
	try {
		const columns = await getColumns();
		dynamicColumns.value = columns;
		// 更新 Table 的列配置
		if (Table.value) {
			Table.value.columns = columns;
		}
		//	loading.value = false;
	} catch (error) {
		console.error('初始化列失败:', error);
	} finally {
		//	loading.value = false;
	}
}
// cl-crud
const Crud = useCrud(
	{
		service: service.yszk.xzhDetail,
		dict: {
			label: {
				op: '操作',
				update: '确权'
			}
		},
		onRefresh(params, { next }) {
			// 默认使用 next(params)，也可以自己对数据进行处理
			params = selectedMonths.value
				? {
						projectCode: projectCode.value,
						客商名称代码: ksdm.value,
						账期: yearToMonth(selectedMonths.value),
						formType: '查看',
						page: params.page,
						size: params.size
					}
				: { formType: '查看', page: params.page, size: params.size };
			next({
				...params
			});
		}
	},

	app => {
		app.refresh(
			selectedMonths.value
				? { projectCode: projectCode.value, 客商名称代码: ksdm.value, formType: '查看' }
				: { formType: '查看' }
		);
	}
);

// 刷新
function refresh() {
	Crud.value?.refresh();
}
</script>
<style>
.cl-dateText {
	font-size: 14px;
	color: #606266;
	display: flex;
	align-items: center;
}
</style>
