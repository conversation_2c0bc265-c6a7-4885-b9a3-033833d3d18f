<template>
	<el-dialog
		title="添加用户组"
		v-model="dialogVisible"
		width="65%"
		@open="getDatas"
		:close-on-click-modal="false"
		:show-close="false"
		:destroy-on-close="true"
	>
		<el-form
			:model="formData"
			:rules="rules"
			ref="formRef"
			label-width="80px"
			class="dialog-form"
		>
			<el-form-item label="名称" prop="name">
				<el-input
					v-model="formData.name"
					placeholder="请输入用户组名称"
					style="width: 350px"
					show-word-limit
				/>
			</el-form-item>
			<el-form-item label="全部用户" prop="isAllUser">
				<el-checkbox
					v-model="formData.isAllUser"
					@change="handleCheckAllChange"
					style="margin-right: 20px"
				/>
			</el-form-item>
			<el-form-item label="用户" prop="userIds" v-if="!formData.isAllUser">
				<div class="user-select-container">
					<el-button @click="showTransferDialog">选择用户</el-button>
					<div class="selected-users" v-if="formData.userIds.length">
						<template v-if="formData.userIds.length <= 3">
							<el-tag
								v-for="userId in formData.userIds"
								:key="userId"
								class="user-tag"
								closable
								@close="removeUser(userId)"
							>
								{{ getUserLabel(userId) }}
							</el-tag>
						</template>
						<template v-else>
							<el-popover placement="bottom" :width="300" trigger="hover">
								<template #reference>
									<div class="selected-count">
										已选择 {{ formData.userIds.length }} 个用户
									</div>
								</template>
								<div class="user-list">
									<div
										v-for="userId in formData.userIds"
										:key="userId"
										class="user-row"
									>
										{{ userId }}
										{{ getUserLabel(userId) }}
										<el-button
											type="danger"
											link
											@click="removeUser(userId)"
											style="margin-left: 10px"
											>删除</el-button
										>
										<!-- <el-icon
											@click="removeUser(userId)"
											style="margin-left: 10px"
											><close-bold color="red"
										/></el-icon> -->
									</div>
								</div>
							</el-popover>
						</template>
					</div>
				</div>
			</el-form-item>

			<el-form-item label="角色" prop="roleIds" v-if="!formData.isAllUser">
				<el-select
					v-model="formData.roleIds"
					multiple
					filterable
					placeholder="请选择角色"
					style="width: 100%"
				>
					<el-option
						v-for="item in roleOptions"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					/>
				</el-select>
			</el-form-item>

			<el-form-item label="备注" prop="description">
				<el-input
					v-model="formData.description"
					type="textarea"
					:rows="3"
					placeholder="请输入备注信息"
					maxlength="200"
					show-word-limit
				/>
			</el-form-item>
		</el-form>

		<!-- 用户选择弹窗 -->

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="handleSubmit">确 定</el-button>
			</span>
		</template>
	</el-dialog>

	<userSelect ref="userSelectRef" @userSelectData="userSelectData" />
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useCool } from '/@/cool';
import userSelect from '/@/modules/flow/components/design/config/userConfig/userSelect.vue';
import { CloseBold } from '@element-plus/icons-vue';
const dialogVisible = ref(false);
const formRef = ref(null);
const { service } = useCool();

// 表单数据
const formData = reactive({
	name: '',
	userIds: [],
	roleIds: [],
	description: '',
	isAllUser: false
});
const userData: any = ref([]);
const roleOptions: any = ref([]);
const userSelectRef = ref(null);
const transferData: any = ref([]);
//const userId = ref([]);
//const selectedUsers = ref([]);
// 表单验证规则
const rules = {
	name: [
		{ required: true, message: '请输入用户组名称', trigger: 'blur' },
		{ min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
	],

	userIds: [{ required: false, message: '请选择用户', trigger: 'change' }],
	roleIds: [{ required: false, message: '请选择角色', trigger: 'change' }]
};

const getDatas = async () => {
	const param: any = {};

	const res = await service.base.sys.user.list();
	userData.value = res;
	transferData.value = userData.value.map((item: any) => ({
		key: item.id,
		label: item.name
	}));
	console.log('userData', userData.value);
	const roleRes = await service.base.sys.role.list();
	roleOptions.value = roleRes.map((item: any) => ({
		value: item.id,
		label: item.name
	}));
	console.log('roleOptions', roleOptions.value);
};
// 全选
const handleCheckAllChange = () => {
	if (formData.isAllUser) {
		formData.userIds = [];
		formData.roleIds = [];
	}
};
// 用户选择table
function userSelectData(data, mutiSelect) {
	console.log('table返回', data, mutiSelect);
	if (data.length > 0) {
		data.forEach(item => {
			if (!formData.userIds.includes(item.id)) {
				formData.userIds.push(item.id);
			}
		});
		formData.isAllUser = false;
	}
}

// 穿梭框过滤方法
const filterMethod = (query, item) => {
	return item.label.indexOf(query) > -1;
};

// 用户选择弹窗控制
const transferDialogVisible = ref(false);
const closeTransferDialog = (value: boolean) => {
	transferDialogVisible.value = value;
};
const getUserData = (data: any) => {
	formData.userIds = data;
};
// 显示用户选择弹窗
const showTransferDialog = () => {
	//transferDialogVisible.value = true;
	userSelectRef.value.open();
};

// 确认用户选择
const confirmUserSelection = () => {
	transferDialogVisible.value = false;
	//formData.users = tableSelect.value.map(item => item.id);
};

// 移除已选用户
const removeUser = (userId: number) => {
	const index = formData.userIds.indexOf(userId);
	if (index > -1) {
		formData.userIds.splice(index, 1);
	}
};

// 获取用户标签文本
const getUserLabel = (userId: number) => {
	const user = userData.value.find(item => item.id === userId);

	console.log('user', user, userId);
	return user ? user.name : '';
};
const emit = defineEmits(['getUserGroupData']);
// 提交表单
const handleSubmit = async () => {
	if (!formRef.value) return;
	await formRef.value.validate(valid => {
		if (valid) {
			console.log('提交的表单数据：', formData);
			emit('getUserGroupData', formData);
			dialogVisible.value = false;
		}
	});
};

// 添加用户组
const open = (row?: any) => {
	dialogVisible.value = true;
	if (row) {
		console.log('编辑', row);
		formData.name = row.name;
		formData.userIds = row.userIds;
		formData.roleIds = row.roleIds;
		formData.description = row.description;
		formData.isAllUser = row.isAllUser ? true : false;
	} else {
		formData.isAllUser = false;
		formData.name = '';
		formData.userIds = [];
		formData.roleIds = [];
		formData.description = '';
	}
};
defineExpose({ open });
</script>

<style scoped>
.dialog-form {
	padding: 20px;
}

.el-transfer {
	margin: 0 auto;
}

:deep(.el-transfer__buttons) {
	padding: 0 20px;
}

.dialog-footer {
	padding-top: 20px;
	text-align: right;
}

.user-select-container {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	align-items: center;
}

.selected-users {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}

.user-tag {
	margin-right: 8px;
}

.selected-count {
	cursor: pointer;
	color: #409eff;
}

.user-list {
	max-height: 200px;
	overflow-y: auto;
}

.user-row {
	&:hover {
		background-color: #f5f7fa;
	}
}

.user-list div {
	padding: 4px 0;
}
</style>
