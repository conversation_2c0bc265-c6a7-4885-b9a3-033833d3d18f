<template>
	<cl-crud ref="Crud">
		<cl-row>
			<cl-refresh-btn />

			<cl-flex1 />
			<cl-search-key field="选择列" placeholder="请输入" :field-list="searchFieldsList" />
		</cl-row>

		<cl-row>
			<cl-table ref="Table">
				<template #slot-btn="{ scope }">
					<el-tooltip content="查看" placement="top">
						<el-button
							type="primary"
							@click="openTableData(scope.row)"
							:icon="View"
							size="small"
							circle
						/>
					</el-tooltip>
					<el-tooltip content="处理" placement="top">
						<el-button
							type="success"
							v-if="scope.row.isProcessed == 0"
							@click="viewNoticeDetail(scope.row)"
							:icon="Edit"
							size="small"
							circle
						/>
					</el-tooltip>
				</template>
			</cl-table>
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<cl-pagination />
		</cl-row>

		<cl-upsert ref="Upsert" />
		<cl-dialog v-model="dialogVisible" title="查看" width="50%">
			<div class="notice-preview">
				<h3 class="preview-title">
					{{ rowView.title }}
				</h3>
				<div class="preview-info">
					<span class="preview-time">{{ rowView.createTime }}</span>
				</div>
				<div class="preview-content" v-html="rowView.content"></div>
			</div>
		</cl-dialog>
	</cl-crud>
</template>

<script lang="ts" name="messageRead" setup>
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Document, View, Edit } from '@element-plus/icons-vue';
import { reactive, ref, onActivated, computed, h } from 'vue';
import { useCool } from '/@/cool';
import { useBase } from '/$/base';
import { noticeTypeList } from '../utils/noticTypeList';

const { service, router, route } = useCool();
const { user } = useBase();
const userGroupList: any = ref([]);
const rowView: any = ref({});
const dialogVisible = ref(false);

onActivated(async () => {
	const res = await service.messages.group.list({
		page: 1,
		limit: 1000
	});

	userGroupList.value = res.map((item: any) => ({
		label: item.name,
		value: item.id
	}));

	Crud.value?.refresh();
});
// 点击公告跳转
const viewNoticeDetail = async (notice: any) => {
	console.log('查看公告详情', notice);

	// 定义要跳转的路由和参数
	let targetPath = '';
	let targetState = {};

	//久悬
	if (notice.noticeType === 'overdue') {
		targetPath = '/myTask';
		targetState = { instanceId: notice.businessData.instanceId };

		// 添加路由判断
		if (route.path === targetPath) {
			router.replace({
				path: targetPath,
				state: targetState,
				force: true
			});
			window.location.reload();
		} else {
			router.push({
				path: targetPath,
				state: { instanceId: targetState.instanceId },
				replace: true
			});
		}
	} else if (notice.noticeType === 'started') {
		//发起流程
		targetPath = '/myTask';
		targetState = { instanceId: notice.businessData.instanceId };

		// 添加路由判断
		if (
			route.path === targetPath
			// &&
			// router.options.history.state.instanceId == notice.businessData.instanceId
		) {
			router.replace({
				path: targetPath,
				state: targetState,
				force: true
			});

			window.location.reload();
			//	console.log('state', router.options.history.state);
		} else {
			router.push({
				path: targetPath,
				state: { instanceId: targetState.instanceId },
				replace: true
			});
		}
	} else if (
		notice.noticeType === 'urge' ||
		notice.noticeType === 'transfer' ||
		notice.noticeType === 'add_sign'
	) {
		//催办、转办、加签

		targetPath = '/todoTask';
		targetState = {
			taskId: notice.taskId,
			instanceId: notice.businessData.instanceId
		};

		// 添加路由判断
		if (route.path === targetPath) {
			router.replace({
				path: targetPath,
				state: targetState,
				force: true
			});
			console.log('当前路由与目标路由完全相同');
			window.location.reload();
		} else {
			router.push({
				path: targetPath,
				state: targetState,
				replace: true
			});
		}
	} else if (notice.noticeType === 'completed') {
		//流程已完成
		targetPath = '/myTask';
		// 添加路由判断
		if (route.path === targetPath) {
			router.replace({
				path: targetPath,
				state: targetState,
				force: true
			});
			window.location.reload();
		} else {
			router.push({
				path: targetPath,
				state: { instanceId: notice.businessData.instanceId },
				replace: true
			});
		}
	} else if (notice.noticeType === 'data_correction') {
		try {
			if (Number(notice.businessData.formId)) {
				const res = await service.cloud.db.list({ id: notice.businessData.formId });
				if (res && res.length > 0) {
					console.log('res查看跳转', res[0]);

					if (res[0].tableType == 'system') {
						targetPath = '/dataView';
						targetState = {
							id: notice.businessData.formId,
							rowIds: JSON.stringify(notice.businessData.rowIds),
							ruleId: notice.businessData.ruleId,
							tableType: res[0].tableType
						};
					} else {
						targetPath = '/luruTable/data';
						targetState = {
							id: res[0].id,
							name: res[0].name,
							type: '查看',
							checkDataIds: JSON.stringify(notice.businessData.rowIds),
							ruleId: notice.businessData.ruleId
						};
					}
					console.log('targetState', router.options.history.state);
					const currentState = router.options.history.state;
					if (route.path === targetPath) {
						router.replace({
							path: targetPath,
							state: targetState,
							force: true
						});
						window.location.reload();
					} else {
						router.push({
							path: targetPath,
							state: targetState,
							replace: true
						});
					}
				} else {
					ElMessage.error('暂无数据');
					return;
				}
			} else {
				targetPath = '/dataView';
				if (route.path === targetPath) {
					//console.log('强制更新state', state);
					router.replace({
						path: '/dataView',
						state: {
							id: notice.businessData.formId,
							rowIds: JSON.stringify(notice.businessData.rowIds),
							ruleId: notice.businessData.ruleId,
							tableType: 'data'
						},
						force: true
					});
					window.location.reload();
				} else {
					router.push({
						path: '/dataView',
						state: {
							id: notice.businessData.formId,
							rowIds: JSON.stringify(notice.businessData.rowIds),
							ruleId: notice.businessData.ruleId,
							tableType: 'data'
						}
					});
				}
				console.log('router.options.history.state', router.options.history.state);
			}
		} catch (error) {
			console.error('获取数据失败:', error);
			ElMessage.error('获取数据失败' + error.message);
			return;
		}
	} else {
		ElMessageBox.confirm('确认设置为已读？', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(async () => {
			await service.messages.notification.read({ ids: [notice.id] });
			Crud.value?.refresh();
		});
	}

	// 判断当前路由是否与目标路由完全相同（包括路径和参数）
	//
};

function openTableData(row: any) {
	rowView.value = row;
	dialogVisible.value = true;
}
// cl-table
const Table = useTable({
	columns: [
		{
			label: '通知名称',
			prop: 'title'
		},
		{
			label: '处理状态',
			prop: 'isProcessed',
			dict: [
				{
					label: '已处理',
					value: 1,
					type: 'success'
				},
				{
					label: '未处理',
					value: 0,
					type: 'danger'
				}
			]
		},
		{
			label: '通知类型',
			prop: 'noticeType',
			formatter: (row: any) => {
				return noticeTypeList.find(item => item.value === row.noticeType)?.label;
			}
		},
		{
			label: '通知内容',
			prop: 'content',
			width: 300,
			formatter: (row: any) => {
				// 创建临时 div 来解析 HTML 内容
				const div = document.createElement('div');
				div.innerHTML = row.content;
				// 获取纯文本内容
				const text = div.textContent || div.innerText || '';
				// 截取前 50 个字符，超出显示省略号
				const displayText = text.length > 50 ? text.slice(0, 50) + '...' : text;

				return h('div', {
					innerHTML: displayText,
					style: {
						overflow: 'hidden',
						textOverflow: 'ellipsis',
						whiteSpace: 'nowrap'
					}
				});
			}
		},
		{
			label: '用户组',
			prop: 'groupId',
			minWidth: 120,
			formatter: (row: any) => {
				let usertag = '';
				if (row.groupId) {
					const obj = userGroupList.value.find((item: any) => item.value == row.groupId);
					usertag += obj.label + ' ';
				}
				return usertag;
			}
		},
		{
			label: '备注',
			prop: 'remark',
			width: 120
		},
		{
			label: '创建时间',
			prop: 'createTime'
		},
		{
			label: '操作',
			type: 'op',
			buttons: ['slot-btn']
		}
	]
});
const searchFieldsList = [
	{
		label: '通知名称',
		value: 'title'
	},
	{
		label: '处理状态',
		value: 'isProcessed',
		component: {
			name: 'el-select',
			props: {
				clearable: true
			},
			options: [
				{
					label: '关闭',
					value: 0
				},
				{
					label: '开启',
					value: 1
				}
			]
		}
	}
];
// cl-upsert
const Upsert = useUpsert({
	dialog: {
		width: '1000px'
	},
	async onOpen() {
		// 添加 onOpen 钩子
		if (userGroupList.value.length === 0) {
			const res = await service.messages.notification.list({
				page: 1,
				limit: 1000
			});
			userGroupList.value = res.map((item: any) => ({
				label: item.name,
				value: item.id
			}));
		}
	},
	items: [
		{
			prop: 'title',
			label: '名称',
			span: 12,
			required: true,
			component: {
				name: 'el-input'
			}
		},
		{
			prop: 'noticeType',
			label: '类型',
			span: 12,
			required: true,
			component: {
				name: 'el-select',
				props: {
					required: true
				},
				options: noticeTypeList
			}
		},
		{
			prop: 'groupId',
			label: '用户组',
			component: {
				name: 'cl-select',
				props: {
					multiple: true,
					options: userGroupList
				}
			}
		},
		{
			prop: 'content',
			label: '内容',
			required: true,
			component: {
				name: 'cl-editor',
				props: {
					name: 'cl-editor-wang',
					rows: 10
				}
			}
		},
		{
			prop: 'remark',
			label: '备注',
			component: {
				name: 'el-input',
				props: {
					placeholder: '请输入备注',
					rows: 3,
					type: 'textarea'
				}
			}
		}
	],

	onSubmit(data, { done, close, next }) {
		console.log('提交的数据', data);
		data.updateUser = user?.info?.username;
		service.messages.notification
			.announcement(data)
			.then(res => {
				done();
				close();
				Crud.value?.refresh();
			})
			.catch(err => {
				console.log('提交失败', err);
				ElMessage.error('提交失败');
				done();
			});
	}
});
// cl-crud
const Crud = useCrud({ service: service.messages.notification }, app => {
	//app.refresh({ type: "announcement" });
	app.refresh();
});
</script>
<style lang="scss" scoped>
.notice-preview {
	padding: 8px;

	.preview-title {
		margin: 0 0 12px;
		padding-bottom: 10px;
		font-size: 16px;
		font-weight: bold;
		color: #303133;
		border-bottom: 1px solid #ebeef5;
		position: relative;
	}

	.preview-info {
		display: flex;
		align-items: center;
		gap: 10px;
		margin-bottom: 16px;
		padding: 0 4px;

		.preview-time {
			color: #909399;
			font-size: 13px;
		}
	}

	.preview-content {
		padding: 12px;
		font-size: 14px;
		line-height: 1.6;
		color: #606266;
		max-height: 300px;
		overflow-y: auto;
		background-color: #f8f9fa;
		border-radius: 4px;

		:deep(p) {
			margin: 8px 0;
		}

		:deep(img) {
			max-width: 100%;
			height: auto;
			border-radius: 4px;
			margin: 8px 0;
		}

		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-thumb {
			background-color: #dcdfe6;
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background-color: #f0f2f5;
			border-radius: 3px;
		}
	}
}
</style>
