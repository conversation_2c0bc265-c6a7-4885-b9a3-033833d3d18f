<template>
	<cl-crud ref="Crud">
		<cl-row>
			<el-button type="primary" @click="getTableData">刷新</el-button>
			<el-button type="primary" @click="openAddDialog">新增</el-button>
			<cl-flex1 />
			<el-input placeholder="搜索名称" v-model="searchKey" style="width: 300px">
				<template #append>
					<el-button type="primary" @click="getTableData">搜索</el-button>
				</template>
			</el-input>
		</cl-row>

		<cl-row>
			<el-table :data="tableData" style="width: 100%" border>
				<el-table-column prop="title" label="通知名称" :width="120"></el-table-column>
				<el-table-column prop="status" label="状态" :width="120">
					<template #default="scope">
						<el-switch
							v-model="scope.row.status"
							:active-value="1"
							:inactive-value="0"
							@change="handleStatusChange(scope.row)"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="effectiveTime" label="公告有效期" :width="120">
				</el-table-column>
				<el-table-column prop="content" label="通知内容" :min-width="300">
					<template #default="scope">
						<div
							:title="scope.row.content"
							style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
						>
							{{ formatContent(scope.row.content) }}
						</div>
					</template>
				</el-table-column>
				<el-table-column prop="noticeType" label="通知类型" :width="120">
					<template #default="scope">
						<span>{{
							noticeTypeList.find(item => item.value === scope.row.noticeType)?.label
						}}</span>
					</template>
				</el-table-column>

				<el-table-column prop="groupId" label="用户组" :min-width="120">
					<template #default="scope">
						<span v-if="scope.row.groupId">
							{{ getUserGroups(scope.row.groupId) }}
						</span>
					</template>
				</el-table-column>
				<el-table-column prop="remark" label="备注" :width="120"></el-table-column>
				<el-table-column prop="createTime" label="创建时间"></el-table-column>
				<el-table-column label="操作" :width="150" fixed="right">
					<template #default="scope">
						<el-button
							type="success"
							size="small"
							@click="openTableData(scope.row)"
							text
							bg
							>查看
						</el-button>
						<el-button type="primary" size="small" @click="openAddDialog(scope.row)"
							>编辑</el-button
						>
						<!-- <el-button type="danger" size="small" @click="deleteItem(scope.row)"
							>删除</el-button
						> -->
					</template>
				</el-table-column>
			</el-table>
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<el-pagination
				v-model:current-page="currentPage"
				v-model:page-size="size"
				:page-sizes="[10, 20, 30, 40, 50]"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			></el-pagination>
		</cl-row>

		<!-- <cl-upsert ref="Upsert" /> -->
		<cl-dialog v-model="dialogVisible" title="查看" width="60%">
			<div class="notice-preview">
				<h3 class="preview-title">
					{{ rowView.title }}
				</h3>
				<div class="preview-info">
					<span class="preview-time">{{ rowView.createTime }}</span>
				</div>
				<div class="preview-content" v-html="rowView.content"></div>
			</div>
		</cl-dialog>
		<cl-dialog v-model="newDialogVisible" title="获取所有通知" width="60%">
			<el-form :model="formData" label-width="100px">
				<el-row>
					<el-col :span="12">
						<el-form-item label="标题">
							<el-input v-model="formData.title" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="类型">
							<el-select v-model="formData.noticeType" style="width: 220px">
								<el-option label="系统通知" value="announcement" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="用户组">
							<el-select v-model="formData.groupId" :disabled="dialogType === 'edit'">
								<el-option
									v-for="item in userGroupList"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="消息有效期">
							<el-date-picker
								v-model="formData.effectiveTime"
								type="datetime"
								value-format="YYYY-MM-DD HH:mm:ss"
								placeholder="选择日期"
							/>
						</el-form-item>
					</el-col>
					<el-form-item label="内容">
						<cl-editor-wang
							:isAll="false"
							:isSpace="false"
							v-model="formData.content"
							:filePath="filePath"
						/>
					</el-form-item>
					<el-col :span="24">
						<el-form-item label="备注">
							<el-input v-model="formData.remark" type="textarea" />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<div style="text-align: right">
				<el-button type="primary" @click="newDialogVisible = false">取消</el-button>
				<el-button type="primary" @click="handleSubmit">提交</el-button>
			</div>
		</cl-dialog>
	</cl-crud>
</template>

<script lang="ts" name="messageView" setup>
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Document, Refresh } from '@element-plus/icons-vue';
import { reactive, ref, onActivated, computed, h } from 'vue';
import { useCool } from '/@/cool';
import { useBase } from '/$/base';
import { noticeTypeList } from '../utils/noticTypeList';

const { service } = useCool();
const { user } = useBase();
const userGroupList: any = ref([]);
const rowView: any = ref({});
const dialogVisible = ref(false);
const newDialogVisible = ref(false);
const searchKey = ref('');
const formData: any = ref({
	title: '',
	content: '',
	noticeType: '系统',
	effectiveTime: '',
	remark: '',
	groupId: '',
	updateUser: ''
});
const tableData: any = ref([]);
const size = ref(10);
const currentPage = ref(1);
const total = ref(0);
const dialogType = ref('');
const filePath = ref('');

onActivated(async () => {
	const res = await service.messages.group.list({
		page: 1,
		limit: 1000
	});
	userGroupList.value = res.map((item: any) => ({
		label: item.name,
		value: item.id
	}));
	getTableData();
});

function handleStatusChange(row: any) {
	console.log('状态改变', row);
	if (row.status == 1) {
		ElMessageBox.confirm('确定要启用该条通知吗？', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		})
			.then(() => {
				service.messages.notification
					.update({
						id: row.id,
						status: row.status
					})
					.then(res => {
						getTableData();
						ElMessage.success('通知启用成功');
					})
					.catch(err => {
						console.log('修改状态失败', err);
						ElMessage.error('通知启用失败');
					});
			})
			.catch(err => {
				row.status = !row.status;
				console.log('修改状态失败', err);
				//ElMessage.error('修改状态失败');
			});
	} else {
		ElMessageBox.confirm('确定要废止该条通知吗？', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		})
			.then(() => {
				service.messages.notification
					.update({
						id: row.id,
						status: row.status
					})
					.then(res => {
						getTableData();
						ElMessage.success('通知废止成功');
					})
					.catch(err => {
						console.log('修改状态失败', err);
						ElMessage.error('通知废止失败');
					});
			})
			.catch(err => {
				getTableData();
				console.log('修改状态失败', err);
				//ElMessage.error('修改状态失败');
			});
	}
}

function openTableData(row: any) {
	console.log('打开表格数据', row);
	rowView.value = row;
	dialogVisible.value = true;
}

function handleSubmit() {
	console.log('提交数据', formData.value);
	formData.value.updateUser = user?.info?.username;
	if (dialogType.value == 'add') {
		service.messages.notification
			.announcement(formData.value)
			.then(res => {
				getTableData();
				newDialogVisible.value = false;
			})
			.catch(err => {
				console.log('提交失败', err);
				ElMessage.error('提交失败');
			});
	} else {
		console.log('更新数据', formData.value);
		service.messages.notification
			.update(formData.value)
			.then(res => {
				getTableData();
				newDialogVisible.value = false;
			})
			.catch(err => {
				console.log('提交失败', err);
				ElMessage.error('提交失败');
			});
	}
}

function handleSizeChange(size) {
	size.value = size;
	getTableData();
}

function handleCurrentChange(page) {
	console.log('当前页', page);
	currentPage.value = page;
	getTableData();
}

function formatContent(content) {
	const div = document.createElement('div');
	div.innerHTML = content;
	const text = div.textContent || div.innerText || '';
	return text.length > 50 ? text.slice(0, 50) + '...' : text;
}

function getUserGroups(groupId) {
	console.log('获取用户组', groupId);
	const obj = userGroupList.value.find(item => item.value == groupId);
	console.log('获取用户组', obj, obj.label);
	// return groupIds
	// 	.map(userId => {
	// 		const obj = userGroupList.value.find(item => item.value == userId);
	// 		return obj ? obj.label : "";
	// 	})
	// 	.join(" ");
	return obj ? obj.label : '';
}

function deleteItem(row) {
	ElMessageBox.confirm('确定删除吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		service.messages.notification
			.delete({ ids: [row.id] })
			.then(res => {
				getTableData();
			})
			.catch(err => {
				console.log('删除失败', err);
				ElMessage.error('删除失败');
			});
	});
}

async function openAddDialog(row?) {
	console.log('打开新增对话框', row);
	filePath.value = `通知/${Math.floor(Date.now() / 1000)}`;
	console.log('filePath', filePath.value);
	formData.value =
		row && row.title
			? JSON.parse(JSON.stringify(row))
			: {
					title: '',
					content: '',
					noticeType: '系统',
					remark: '',
					effectiveTime: '',
					groupId: '',
					updateUser: ''
				};
	dialogType.value = row && row.title ? 'edit' : 'add';

	newDialogVisible.value = true;
	console.log('newDialogVisible设置为:', newDialogVisible.value);
}

async function getTableData() {
	const res = await service.messages.notification.allNotification({
		page: currentPage.value,
		size: size.value,
		searchKey: searchKey.value ? searchKey.value : null
	});
	tableData.value = res.list;
	console.log('表格数据', tableData.value);
	total.value = res.pagination.total;
	console.log('表格数据', tableData.value, total);
}

// cl-upsert

// cl-crud
// const Crud = useCrud({ service: service.messages.notification }, app => {
// 	app.refresh();
// 	//app.refresh({ type: "announcement" });
// });
</script>
<style lang="scss" scoped>
.notice-preview {
	padding: 8px;

	.preview-title {
		margin: 0 0 12px;
		padding-bottom: 10px;
		font-size: 16px;
		font-weight: bold;
		color: #303133;
		border-bottom: 1px solid #ebeef5;
		position: relative;
	}

	.preview-info {
		display: flex;
		align-items: center;
		gap: 10px;
		margin-bottom: 16px;
		padding: 0 4px;

		.preview-time {
			color: #909399;
			font-size: 13px;
		}
	}

	.preview-content {
		padding: 12px;
		font-size: 14px;
		line-height: 1.6;
		color: #606266;
		max-height: 300px;
		overflow-y: auto;
		background-color: #f8f9fa;
		border-radius: 4px;

		:deep(p) {
			margin: 8px 0;
		}

		:deep(img) {
			max-width: 100%;
			height: auto;
			border-radius: 4px;
			margin: 8px 0;
		}

		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-thumb {
			background-color: #dcdfe6;
			border-radius: 3px;
		}

		&::-webkit-scrollbar-track {
			background-color: #f0f2f5;
			border-radius: 3px;
		}
	}
}
</style>
