<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 添加按钮 -->
			<el-button type="primary" @click="addUserGroupFun">新增</el-button>
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />

			<cl-flex1 />
			<!-- 关键字搜索 -->
			<cl-search-key placeholder="搜索名称、用户、角色" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table">
				<template #slot-btn="{ scope }">
					<el-button
						type="success"
						@click="editUserGroup(scope.row)"
						text
						bg
						style="margin-bottom: 10px; width: 90px"
						>编辑
					</el-button>
					<el-button
						type="danger"
						@click="deleteUserGroup(scope.row)"
						text
						bg
						style="margin-bottom: 10px; width: 90px"
						>删除
					</el-button>
				</template>
			</cl-table>
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
		<addUserGroup ref="addUserGroupRef" @getUserGroupData="getUserGroupData" />
	</cl-crud>
</template>

<script lang="ts" name="userGroup" setup>
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { useBase } from '/$/base';
import addUserGroup from '../components/addUserGroup.vue';
import { reactive, ref, onActivated, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
const addUserGroupRef = ref();
const { service } = useCool();
const { user } = useBase();
const opType = ref('add');
const editId = ref('');
const userList = ref([]);
const roleList = ref([]);
onMounted(async () => {
	const res = await service.base.sys.user.list();
	userList.value = res.map((item: any) => ({
		label: item.username,
		value: item.id
	}));
	const roleRes = await service.base.sys.role.list();
	console.log('角色列表', roleList);
	roleList.value = roleRes.map((item: any) => ({
		label: item.name,
		value: item.id
	}));
});

function addUserGroupFun() {
	opType.value = 'add';
	addUserGroupRef.value?.open();
}
const editUserGroup = (row: any) => {
	console.log('编辑', row);
	opType.value = 'edit';
	editId.value = row.id;
	addUserGroupRef.value?.open(row);
};
// cl-table
const Table = useTable({
	columns: [
		{
			label: '名称',
			prop: 'name',
			minWidth: 150
		},
		{
			label: '用户',
			prop: 'userIds',
			minWidth: 100,
			formatter: (row: any) => {
				console.log('row', row);
				if (row.isAllUser) {
					console.log('全部用户', row);
					return '全部用户';
				} else {
					return userList.value
						.filter((item: any) => row.userIds.includes(item.value))
						.map((item: any) => item.label)
						.join(',');
				}
			}
		},
		{
			label: '角色',
			prop: 'roleIds',
			minWidth: 120,
			formatter: (row: any) => {
				return roleList.value
					.filter((item: any) => row.roleIds.includes(item.value))
					.map((item: any) => item.label)
					.join(',');
			}
		},

		{
			label: '描述',
			prop: 'description',
			minWidth: 120
		},
		{
			label: '创建时间',
			prop: 'createTime',
			sortable: 'desc',
			minWidth: 120
		},

		{
			label: '操作',
			type: 'op',
			width: 220,
			buttons: ['slot-btn']
		}
	]
});

const getUserGroupData = (data: any) => {
	console.log('获取到的数据', data);
	try {
		if (opType.value == 'add') {
			service.messages.group
				.add({
					name: data.name,
					description: data.description,
					userIds: data.userIds,
					roleIds: data.roleIds,
					isAllUser: data.isAllUser
				})
				.then(() => {
					ElMessage.success('新增成功');
					Crud.value?.refresh();
				})
				.catch(error => {
					ElMessage.error('新增失败' + error.message);
				});
		} else {
			service.messages.group
				.update({
					id: editId.value,
					description: data.description,
					userIds: data.userIds,
					roleIds: data.roleIds,
					isAllUser: data.isAllUser
				})
				.then(() => {
					ElMessage.success('编辑成功');
					Crud.value?.refresh();
				})
				.catch(error => {
					ElMessage.error('编辑失败' + error.message);
				});
		}
	} catch (error) {
		ElMessage.error('操作失败' + error.message);
		console.log('错误', error);
	}
};

const deleteUserGroup = (row: any) => {
	console.log('删除', row);
	ElMessageBox.confirm('确定删除吗？')
		.then(async () => {
			try {
				await service.messages.group.delete({
					ids: row.id
				});
				Crud.value?.refresh();
			} catch (error) {
				ElMessage.error(error.message);
				console.log('错误', error);
			}
		})
		.catch(() => null);
};

// cl-upsert
const Upsert = useUpsert({
	dialog: {
		width: '600px'
	},
	items: [
		{
			prop: 'name',
			label: '名称',
			component: { name: 'el-input' },
			required: true
		},
		{
			prop: 'userIds',
			label: '用户',
			component: {
				name: 'el-button',
				props: {
					type: 'primary',
					icon: 'el-icon-plus'
				}
			}
		},
		{
			prop: 'remark',
			label: '备注',
			component: { name: 'el-input' },
			required: true
		}
	]
});

// cl-crud
const Crud = useCrud(
	{
		service: service.messages.group
	},
	app => {
		app.refresh();
	}
);
</script>
