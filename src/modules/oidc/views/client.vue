<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: 'oidc-client'
});

import { useCrud, useTable, useUpsert, useSearch } from '@cool-vue/crud';
import { useCool } from '/@/cool';

const { service } = useCool();

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: 'Client ID',
			prop: 'clientId',
			required: true,
			component: { name: 'el-input', props: { placeholder: '唯一标识' } }
		},
		{
			label: 'Client Secret',
			prop: 'clientSecret',
			required: true,
			component: { name: 'el-input', props: { placeholder: '公开客户端可为空' } }
		},
		{ label: '名称', prop: 'name', component: { name: 'el-input' } },
		{
			label: '回调地址',
			prop: 'redirectUris',
			required: true,
			component: {
				name: 'el-input-tag',
				props: { placeholder: '形如 https://app.example.com/callback' }
			}
		},
		{
			label: '授权类型',
			prop: 'grantTypes',
			component: {
				name: 'cl-select',
				props: {
					multiple: true,
					options: [
						{ label: 'authorization_code', value: 'authorization_code' },
						{ label: 'refresh_token', value: 'refresh_token' },
						{ label: 'client_credentials', value: 'client_credentials' }
					]
				}
			}
		},
		{
			label: '响应类型',
			prop: 'responseTypes',
			component: {
				name: 'cl-select',
				props: { multiple: true, options: [{ label: 'code', value: 'code' }] }
			}
		},
		{
			label: '认证方式',
			prop: 'tokenEndpointAuthMethod',
			component: {
				name: 'el-select',
				options: [
					{ label: 'client_secret_basic', value: 'client_secret_basic' },
					{ label: 'client_secret_post', value: 'client_secret_post' },
					{ label: 'none', value: 'none' }
				]
			}
		},
		{
			label: '状态',
			prop: 'status',
			value: 1,
			component: { name: 'el-switch', props: { activeValue: 1, inactiveValue: 0 } }
		}
	]
});

// cl-table
const Table = useTable({
	columns: [
		{ type: 'selection' },
		{ label: 'ID', prop: 'id', width: 80 },
		{ label: 'Client ID', prop: 'clientId', minWidth: 200 },
		{ label: '名称', prop: 'name', minWidth: 200 },
		{
			label: '回调地址',
			prop: 'redirectUris',
			minWidth: 320,
			formatter: (row: any) => (row.redirectUris || []).join('\n')
		},
		{
			label: '授权类型',
			prop: 'grantTypes',
			minWidth: 200,
			formatter: (row: any) => (row.grantTypes || []).join(',')
		},
		{
			label: '响应类型',
			prop: 'responseTypes',
			minWidth: 160,
			formatter: (row: any) => (row.responseTypes || []).join(',')
		},
		{ label: '认证方式', prop: 'tokenEndpointAuthMethod', minWidth: 160 },
		{
			label: '状态',
			prop: 'status',
			width: 90,
			formatter: (row: any) => (row.status === 1 ? '启用' : '禁用')
		},
		{ type: 'op', width: 220, buttons: ['edit', 'delete'] }
	]
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.oidc.client
	},
	app => {
		app.refresh();
	}
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
