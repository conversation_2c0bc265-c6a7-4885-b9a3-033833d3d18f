<template>
	<div class="form-tree">
		<div class="form-tree__header">
			<el-text>{{ t('表单类型') }}</el-text>

			<div class="form-tree__op">
				<div class="item" @click="refresh()">
					<el-tooltip :content="t('刷新')">
						<cl-svg name="refresh" />
					</el-tooltip>
				</div>
			</div>
		</div>

		<div class="form-tree__container">
			<el-scrollbar>
				<el-tree
					v-loading="loading"
					node-key="id"
					:data="list"
					:props="{
						label: 'name'
					}"
					highlight-current
					:expand-on-click-node="false"
					@node-click="rowClick"
				>
					<template #default="{ node, data }">
						<div class="form-tree__node">
							<span
								class="form-tree__node-label"
								:class="{
									'is-active': data.id == ViewGroup?.selected?.id
								}"
								>{{ node.label }}</span
							>
							<span v-if="browser.isMini" class="form-tree__node-icon">
								<el-icon>
									<more-filled />
								</el-icon>
							</span>
						</div>
					</template>
				</el-tree>
			</el-scrollbar>
		</div>

		<cl-form ref="Form" />
	</div>
</template>

<script lang="ts" setup>
defineOptions({
	name: 'form-list'
});

import { nextTick, onMounted, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useCool } from '/@/cool';
import { deepTree, revDeepTree } from '/@/cool/utils';

import { useForm } from '@cool-vue/crud';
import { Refresh as RefreshIcon, Operation, MoreFilled } from '@element-plus/icons-vue';

import { useViewGroup } from '/@/plugins/view';
import { useI18n } from 'vue-i18n';

const props = defineProps({
	drag: {
		type: Boolean,
		default: true
	},
	level: {
		type: Number,
		default: 99
	}
});

const emit = defineEmits(['refresh', 'formType-add']);

const { service, browser } = useCool();
const Form = useForm();
const { ViewGroup } = useViewGroup();
const { t } = useI18n();

// 树形列表
const list = ref<Eps.BaseSysDepartmentEntity[]>([]);

// 加载中
const loading = ref(false);

// 刷新
async function refresh() {
	loading.value = true;

	console.log('列表刷新');
	await service.quality.check.formList().then(res => {
		list.value = deepTree(res);
		if (!ViewGroup.value?.selected) {
			rowClick();
		}
	});

	loading.value = false;
}

// 获取 ids
function rowClick(item?: Eps.BaseSysDepartmentEntity) {
	if (!item) {
		item = list.value[0];
	}
	console.log('rowClick', item);
	if (item) {
		const ids = item.children ? revDeepTree(item.children).map(e => e.id) : [];
		ids.unshift(item.id);

		// 选择
		ViewGroup.value?.select(item);

		nextTick(() => {
			// 刷新列表
			emit('refresh', { page: 1, formId: ids });
		});
	}
}

onMounted(function () {
	refresh();
});
</script>

<style lang="scss" scoped>
.form-tree {
	height: 100%;
	width: 100%;

	:deep(.el-tree-node__label) {
		display: block;
		height: 100%;
		width: 100%;
	}

	&__header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 40px;
		padding: 0 10px;
		border-bottom: 1px solid var(--el-border-color-extra-light);
	}

	&__op {
		display: flex;
		align-items: center;

		.item {
			display: flex;
			justify-content: center;
			align-items: center;
			list-style: none;
			margin-left: 5px;
			cursor: pointer;
			border-radius: 6px;
			font-size: 16px;
			height: 26px;
			width: 26px;
			color: var(--el-text-color-primary);

			.cl-svg {
				outline: none;
			}

			&:hover {
				background-color: var(--el-fill-color-light);
			}
		}

		.btns {
			display: flex;
			align-items: center;
			justify-content: center;

			.item {
				&:hover {
					&:first-child {
						color: var(--el-color-success);
					}

					&:last-child {
						color: var(--el-color-danger);
					}
				}
			}
		}
	}

	&__container {
		height: calc(100% - 40px);
		padding: 10px;

		:deep(.el-tree-node__content) {
			height: 38px;
			border-radius: 4px;

			.el-tree-node__expand-icon {
				margin-left: 5px;
			}
		}
	}

	&__node {
		display: flex;
		align-items: center;
		height: 100%;
		width: 100%;
		box-sizing: border-box;

		&-label {
			display: flex;
			align-items: center;
			flex: 1;
			height: 100%;
			font-size: 14px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;

			&.is-active {
				color: var(--el-color-primary);
			}
		}

		&-icon {
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: #eee;
			height: 26px;
			width: 26px;
			text-align: center;
			margin-right: 5px;
			border-radius: 6px;
		}
	}
}
</style>
