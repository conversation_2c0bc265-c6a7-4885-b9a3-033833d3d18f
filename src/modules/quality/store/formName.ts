import { defineStore } from 'pinia';
import { ref } from 'vue';
import { uuid } from '/@/modules/flow/utils/flow';
import { storage } from '/@/cool/utils';

import { useCool } from '/@/cool';
export const useFormName = defineStore('formNameStore', function () {
	const { service } = useCool();
	const formNameList = ref(storage.get('form_name_list') || []);
	const addFormNameList = (data: any) => {
		//	console.log("添加addFormFlowData", data);
		formNameList.value = data;
		storage.set('form_name_list', formNameList.value);
	};
	//const save
	//获取所有表的名字 data库的表+cloud_db里的表
	const getFormNameList = async () => {
		return new Promise(async (resolve, reject) => {
			//console.log("获取formFlowUserList", formFlowUserList.value);
			if (formNameList.value.length === 0) {
				const res = await service.cloud.db.list();
				formNameList.value = res.map((item: any) => item.tableName);
				const res2 = await service.quality.check.outterSchemas();
				formNameList.value = formNameList.value.concat(
					res2.map((item: any) => item.tablename)
				);
				//console.log("获取formFlowUserList11", formFlowUserList.value);
			}
			resolve(formNameList.value);
		});
	};

	return {
		addFormNameList,
		getFormNameList
	};
});
