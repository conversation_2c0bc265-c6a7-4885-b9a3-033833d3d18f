<template>
	<cl-view-group ref="ViewGroup">
		<template #left>
			<!-- 部门列表 -->
			<form-list @refresh="refresh" @formType-add="onFormTypeAdd" />
		</template>

		<template #right>
			<cl-crud ref="Crud">
				<cl-row>
					<!-- 刷新按钮 -->
					<cl-refresh-btn />
					<!-- 新增按钮 -->
					<el-button v-if="selectedFormId.length <= 1" type="primary" @click="addRules()"
						>新增</el-button
					>

					<cl-flex1 />
					<cl-search-key :placeholder="$t('搜索')" />
				</cl-row>

				<cl-row>
					<!-- 数据表格 -->
					<cl-table ref="Table">
						<template #column-noticeTemplate="{ scope }">
							<div
								:title="scope.row.noticeTemplate"
								style="
									overflow: hidden;
									text-overflow: ellipsis;
									white-space: nowrap;
								"
							>
								{{ formatContent(scope.row.noticeTemplate) }}
							</div>
						</template>
						<template #column-status="{ scope }">
							<el-switch
								v-model="scope.row.status"
								:active-value="1"
								:inactive-value="0"
								@change="changeStatus(scope.row)"
							/>
						</template>
						<template #column-checkSql="{ scope }">
							<el-tooltip
								:content="scope.row.checkSql"
								placement="top"
								popper-class="sql-tooltip"
							>
								<div
									style="
										overflow: hidden;
										text-overflow: ellipsis;
										white-space: nowrap;
									"
								>
									{{
										scope.row.checkSql?.length > 50
											? scope.row.checkSql.slice(0, 50) + '...'
											: scope.row.checkSql
									}}
								</div>
							</el-tooltip>
						</template>
						<template #slot-btn="{ scope }">
							<el-tooltip content="执行" placement="top">
								<el-button
									type="success"
									:icon="VideoPlay"
									circle
									size="small"
									@click="doSql(scope.row)"
								/>
							</el-tooltip>

							<el-tooltip content="编辑" placement="top">
								<el-button
									type="primary"
									:icon="Edit"
									circle
									size="small"
									@click="addRules(scope.row)"
								/>
							</el-tooltip>

							<el-tooltip content="删除" placement="top">
								<el-button
									type="danger"
									:icon="Delete"
									circle
									size="small"
									@click="deleteRules(scope.row)"
								/>
							</el-tooltip>
						</template>
					</cl-table>
				</cl-row>

				<cl-row>
					<cl-flex1 />
					<!-- 分页控件 -->
					<cl-pagination />
				</cl-row>

				<!-- 新增、编辑 -->
				<cl-upsert ref="Upsert" />

				<!-- 移动 -->
				<user-move :ref="setRefs('userMove')" />
			</cl-crud>
		</template>
	</cl-view-group>
</template>

<script lang="ts" setup name="dataCheck">
import { ref, onActivated } from 'vue';
import { useCrud, useTable, useUpsert, useSearch } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { useRouter } from 'vue-router';
import { ElMessageBox } from 'element-plus';
import FormList from '/@/modules/quality/components/formList.vue';

import { useViewGroup } from '/@/plugins/view';
import { useI18n } from 'vue-i18n';
import { Plugins } from '/#/crud';
import { ElMessage } from 'element-plus';
import { VideoPlay, Edit, Delete } from '@element-plus/icons-vue';

const { service, refs, setRefs } = useCool();
const { t } = useI18n();
const { ViewGroup } = useViewGroup({
	title: '表单',
	label: '类型'
	// onSelect(item) {
	// 	console.log('点击item', item);
	// 	selectedFormId.value = item.id;
	// 	//typeName.value = item.name;
	// 	// refresh({
	// 	// 	formType: item.id,
	// 	// 	page: 1
	// 	// });
	// },
	// onData(list) {
	// 	console.log('list', list);
	// 	return list;
	// }
});
const router = useRouter();
const selectedFormId = ref<number[]>([]);

const addRules = (data?) => {
	console.log('打开', data);
	//addRulesDialogRef.value?.open();
	if (data) {
		console.log('跳转1-编辑', data);
		router.push({
			path: '/dataCheck/rulesEdit',
			state: {
				type: 'edit',
				data: JSON.stringify(data)
			}
		});
	} else {
		console.log('跳转-新增', selectedFormId.value);
		router.push({
			path: '/dataCheck/rulesEdit',
			//path: '/luruTable/data',
			state: {
				type: 'add',
				data: selectedFormId.value[0]
			}
		});
	}
};
function changeStatus(row) {
	console.log('changeStatus', row);
	service.quality.check.update({
		id: Number(row.id),
		status: row.status ? true : false
	});
}
onActivated(() => {
	console.log('selectedFormId', selectedFormId.value);
	refresh(selectedFormId.value);
});

function doSql(row) {
	console.log('执行', row);
	service.quality.check
		.handleOnce({ ruleId: row.id })
		.then(res => {
			console.log('res', res);
			ElMessage.success('执行成功');
		})
		.catch(err => {
			ElMessage.error(err.message);
		});
}

function deleteRules(row) {
	console.log('删除', row);
	ElMessageBox.confirm('确定删除该规则吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	})
		.then(() => {
			console.log('删除', row);
			service.quality.check
				.delete({ ids: row.id })
				.then(res => {
					console.log('res', res);
					ElMessage.success('删除成功');
					refresh();
				})
				.catch(() => {
					ElMessage.error('删除失败');
				});
		})
		.catch(() => {
			ElMessage.error('删除失败');
		});
}

function getUsers(data) {
	let res = '';
	if (data?.sources?.user?.length > 0) {
		res = '用户:';
		data.sources.user.map(item => {
			res = res + item.name + ' ';
		});
		res = res + ';';
	}

	if (data?.sources?.role?.length > 0) {
		res = '角色:';
		data.sources.role.map(item => {
			res = res + item.name + ' ';
		});
		res = res + ';';
	}
	if (data?.sources?.form?.length > 0) {
		res = '表单:';
		data.sources.form.map(item => {
			console.log('item', item);
			res = res + item.name.externalFormName;
		});
		res = res + ';';
	}
	return res;
}

function formatContent(content) {
	//console.log('content', content);
	const div = document.createElement('div');
	div.innerHTML = content;
	const text = div.textContent || div.innerText || '';
	return text.length > 50 ? text.slice(0, 50) + '...' : text;
}

function onFormTypeAdd({ id }: Eps.BaseSysDepartmentEntity) {
	console.log('onFormTypeAdd', id);
	if (id) {
		selectedFormId.value = [id];
		Crud.value?.rowAppend({
			formTypeId: id
		});
	}
}
// cl-table
const Table = useTable({
	columns: [
		{
			label: '规则名称',
			prop: 'name',
			width: 120
		},
		{
			prop: 'status',
			label: '启用状态'
		},
		{
			label: '主表名称',
			prop: 'formName',
			width: 120
		},
		{
			label: '校验SQL',
			prop: 'checkSql'
		},
		{
			label: '数据治理通知模板',
			prop: 'noticeTemplate'
		},
		{
			label: '处理人参数表',
			prop: 'handlerQualityConfig',
			formatter: row => {
				//console.log('row', row);
				return getUsers(row.handlerQualityConfig);
			}
		},
		{
			type: 'op',
			minWidth: 80,
			buttons({ scope }) {
				return ['slot-btn'];
			}
		}
	]
});

// cl-search
const Search = useSearch();

// cl-crud
const Crud = useCrud(
	{
		service: service.quality.check
	},
	app => {
		app.refresh();
	}
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
	console.log('刷新params', params);
	if (!params?.formId) {
		console.log('无id', selectedFormId.value);
	} else {
		selectedFormId.value = params?.formId || [];
	}
	//
}
</script>
<style>
.cl-table__op {
	margin-bottom: 0px;
}
</style>
<style>
.sql-tooltip {
	max-width: 400px !important; /* 设置最大宽度 */
	white-space: pre-wrap !important; /* 允许文本换行 */
	word-break: break-all !important; /* 允许在任意字符间断行 */
}
.el-button.is-circle {
	margin: 0 4px; /* 给图标按钮添加间距 */
}
</style>
