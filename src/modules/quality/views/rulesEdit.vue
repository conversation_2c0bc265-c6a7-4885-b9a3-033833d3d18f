<template>
	<div class="cl-crud rules-edit-container">
		<el-steps :active="activeStep" finish-status="success" class="mb-20" simple>
			<el-step title="规则信息" :icon="Edit" />
			<el-step title="通知配置" :icon="Document" />
		</el-steps>

		<div v-if="activeStep === 0">
			<el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="表名">
							<span style="color: rgb(129 129 129)">{{ cloudFormName }}</span>
							<!-- <el-input
								v-model="cloudFormName"
								class="itemWidth"
								placeholder="请输入规则名称"
								readonly
							/> -->
						</el-form-item>
						<el-form-item label="规则名称" prop="name">
							<el-input
								v-model="form.name"
								class="itemWidth"
								placeholder="请输入规则名称"
							/>
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="联合主键配置" prop="compositeKeyConfig">
					<el-select
						v-model="form.compositeKeyConfig.keyFields"
						placeholder="请选择主键"
						style="width: 300px"
						multiple
						clearable
					>
						<el-option
							v-for="item in colInfoList"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
					<div class="composite-key-tip">
						<el-popover :width="300" trigger="hover" placement="right">
							<template #reference>
								<div class="tip-trigger">
									<el-icon><warning-filled /></el-icon>
									<span>重要提示</span>
								</div>
							</template>
							<template #default>
								<div class="tip-content">
									<div class="tip-item">
										<el-icon><info-filled /></el-icon>
										<span
											>联合主键用于唯一标识一条记录，请选择能够唯一确定数据的字段组合</span
										>
									</div>
									<div class="tip-item">
										<el-icon><sort /></el-icon>
										<span
											>选择主键的顺序必须与SQL中concat_ws的id字段顺序保持一致</span
										>
									</div>
								</div>
							</template>
						</el-popover>
					</div>
				</el-form-item>

				<el-form-item label="校验SQL" prop="checkSql">
					<div class="sql-container">
						<div class="editor-header">
							<div class="editor-title">
								<!-- <el-icon><document /></el-icon>
								<span>SQL编辑器</span> -->
							</div>
						</div>
						<div ref="editorContainer" class="editor"></div>
					</div>
				</el-form-item>

				<el-form-item>
					<div class="button-container">
						<div class="left-buttons">
							<el-button type="primary" @click="previewSql">预览sql</el-button>
							<el-button type="primary" @click="nextStep">下一步</el-button>
						</div>
						<div class="editor-actions">
							<el-tooltip content="格式化SQL" placement="top">
								<el-button type="primary" text @click="formatSql">
									<el-icon><magic-stick /></el-icon>
								</el-button>
							</el-tooltip>

							<el-tooltip content="SQL帮助" placement="top">
								<el-button
									type="primary"
									text
									@click="showSqlHelp"
									class="sql-help-button"
								>
									<el-icon><question-filled /></el-icon>
								</el-button>
							</el-tooltip>
						</div>
					</div>
				</el-form-item>

				<!-- SQL预览结果 -->
				<div v-if="previewData.length" class="preview-section">
					<div class="preview-header">
						<div class="preview-title">
							<el-icon><data-analysis /></el-icon>
							<span>预览结果</span>
						</div>
						<span class="preview-count">共 {{ previewData.length }} 条数据</span>
					</div>
					<div class="preview-container">
						<el-table :data="previewData" stripe border class="custom-table">
							<el-table-column
								v-for="(col, index) in previewColumns"
								:key="index"
								:prop="col"
								:label="col"
								:min-width="100"
							/>
						</el-table>
					</div>
				</div>
			</el-form>
		</div>

		<div v-else>
			<el-form :model="form" label-width="120px">
				<el-form-item label="处理人" prop="handlerQualityConfig">
					<div class="field-config-section">
						<div class="field-config-card">
							<div class="source-options">
								<div class="source-item">
									<div class="source-header">
										<span class="source-title">
											<el-icon><user-filled /></el-icon>角色
										</span>
										<el-button type="primary" link @click="selectRole">
											选择角色
										</el-button>
									</div>
									<div
										v-if="
											form.handlerQualityConfig &&
											form.handlerQualityConfig.sources.role.length
										"
										class="tags-wrapper"
									>
										<el-tag
											v-for="item in form.handlerQualityConfig.sources.role"
											:key="item.id"
											closable
											size="small"
											@close="removeSource('role', item.id)"
										>
											{{ item.name }}
										</el-tag>
									</div>
								</div>

								<div class="source-item">
									<div class="source-header">
										<span class="source-title">
											<el-icon><avatar /></el-icon>用户
										</span>
										<el-button type="primary" link @click="selectUser">
											选择用户
										</el-button>
									</div>
									<div
										v-if="
											form.handlerQualityConfig &&
											form.handlerQualityConfig.sources.user.length
										"
										class="tags-wrapper"
									>
										<el-tag
											v-for="item in form.handlerQualityConfig.sources.user"
											:key="item.id"
											closable
											size="small"
											@close="removeSource('user', item.id)"
										>
											{{ item.name }}
										</el-tag>
									</div>
								</div>
							</div>
						</div>
					</div>
				</el-form-item>
				<el-form-item label="选择处理周期">
					<el-select v-model="form.internalConfig.period" style="width: 200px">
						<el-option
							v-for="item in periodOptions"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
					<el-date-picker
						v-model="form.internalConfig.date"
						type="datetime"
						placeholder="选择日期时间"
						format="YYYY-MM-DD HH:mm:ss"
						style="width: 200px"
					/>
				</el-form-item>
				<el-form-item label="通知内容">
					<cl-editor-wang
						:isAll="false"
						:isSpace="false"
						:filePath="filePath"
						v-model="form.noticeTemplate"
					/>
				</el-form-item>
				<el-form-item>
					<el-button @click="prevStep">上一步</el-button>
					<el-button type="primary" @click="handleSubmit">提交</el-button>
				</el-form-item>
			</el-form>
		</div>
		<user-select ref="userSelectRef" @userSelectData="userSelectData"></user-select>
		<role-select ref="roleSelectRef" @roleSelectData="roleSelectData"></role-select>
		<form-select ref="formSelectRef" @formSelectData="formSelectData"></form-select>
	</div>

	<!-- 添加SQL帮助对话框 -->
	<el-dialog v-model="sqlHelpVisible" title="SQL帮助" width="600px">
		<div class="sql-help-content">
			<h4>常用SQL示例：</h4>
			<div class="help-item">
				<!-- <div class="help-title">LEFT JOIN 示例</div>
				<div
					style="font-size: 12px; color: #ce4300; font-style: italic; margin-bottom: 8px"
				>
					注意：select只允许显示主表(a表)id，不能显示b表id，否则会有冲突
				</div>
				<pre class="help-code">
SELECT a.*, b.field_name 
FROM table_a a 
LEFT JOIN table_b b ON a.id = b.table_a_id
				</pre
				> -->
				<div class="help-title">使用联合主键</div>
				<div
					style="font-size: 12px; color: #ce4300; font-style: italic; margin-bottom: 8px"
				>
					注意：sql必须包含id字段和user字段，否则无法执行
				</div>
				<pre class="help-code">
					<span>
SELECT concat_ws(',',a.projectCode) AS id, 
concat_ws(',',a.`项目经理`,a.`财务部长`) AS user 
FROM `func_xmcx` a
LEFT JOIN func_关联表4 b ON a.projectCode = b.projectCode
</span>
				</pre>
			</div>
			<!-- 可以添加更多帮助项 -->
		</div>
	</el-dialog>
</template>

<script lang="ts" setup name="dataCheck-rulesEdit">
import {
	ref,
	onMounted,
	onBeforeUnmount,
	onActivated,
	onUnmounted,
	toRaw,
	watch,
	nextTick
} from 'vue';
import { ElMessage } from 'element-plus';
import {
	Check,
	MagicStick,
	View,
	QuestionFilled,
	Document,
	DataAnalysis,
	InfoFilled,
	WarningFilled,
	Sort,
	Edit
} from '@element-plus/icons-vue';
import { service } from '/@/cool';
import UserSelect from '/@/modules/flow/components/design/config/userConfig/userSelect.vue';
import RoleSelect from '/@/modules/flow/components/design/config/userConfig/roleSelect.vue';
import FormSelect from '/@/modules/flow/components/design/config/formConfig/formSelect.vue';
// import { EditorView, basicSetup } from 'codemirror';
// import { sql } from '@codemirror/lang-sql';
import { cloneDeep } from 'lodash-es';
import { useRouter } from 'vue-router';
import * as monaco from 'monaco-editor';
import 'monaco-editor/min/vs/editor/editor.main.css';
import { useStore } from '/@/modules/quality/store';
import { changeRules } from '/@/modules/luru/utils/luruTable';

const { formNameStore } = useStore();
const editorContainer = ref<HTMLElement | null>(null);
const clEditorRef = ref();

const userSelectRef = ref();
const roleSelectRef = ref();
const formSelectRef = ref();

const dialogVisible = ref(false);
const loading = ref(false);
const validating = ref(false);
const isValid = ref(false);
const formList = ref([]);
const previewData = ref([]);
const previewColumns = ref([]);
const activeStep = ref(0);
const editorView = ref();
const colInfoList: any = ref([]);
const cloudFormName = ref('');
const period = ref('');
const filePath = ref('');
const periodOptions = [
	{
		label: '年',
		value: 'YEAR'
	},
	{
		label: '半年',
		value: 'HALF_YEAR'
	},
	{
		label: '季度',
		value: 'QUARTER'
	},
	{
		label: '月',
		value: 'MONTH'
	},
	{
		label: '周',
		value: 'WEEK'
	},
	{
		label: '日',
		value: 'DAY'
	}
];
// 定义数据源接口
interface Sources {
	user: any[];
	role: any[];
	form: any[];
}

// 定义处理质量配置接口
interface HandlerQualityConfig {
	sources: Sources;
}

// 定义复合键配置接口
interface CompositeKeyConfig {
	keyFields: string[];
}

// 定义表单数据接口
interface FormState {
	name: string;
	formId: number | null;
	formName: string;
	checkSql: string;
	compositeKeyConfig: CompositeKeyConfig;
	noticeTemplate: string;
	handlerQualityConfig: HandlerQualityConfig | null;
	status: boolean;
	internalConfig: {
		period: string;
		date: string;
	};
}

// 使用接口定义form
const form = ref<FormState>({
	name: '',
	formId: null,
	formName: '',
	checkSql: '',
	compositeKeyConfig: {
		keyFields: []
	},
	noticeTemplate: '',
	internalConfig: {
		period: '',
		date: ''
	},
	handlerQualityConfig: {
		sources: {
			user: [],
			role: [],
			form: []
		}
	},
	status: true
});

const router = useRouter();
const type = ref();
const formRef = ref();
const rules = ref({
	name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }]
	//checkSql: [{ required: true, message: '请输入校验SQL', trigger: 'blur' }]
});

// 添加帮助对话框控制变量
const sqlHelpVisible = ref(false);

onActivated(() => {
	console.log('编辑', router.options.history, router.options.history.state.type);
	type.value = router.options.history.state.type;
	console.log('router.options.history.state.data', router.options.history.state.data);
	if (type.value === 'edit') {
		form.value = JSON.parse(router.options.history.state.data);
		filePath.value = `通知/${form.value.formId}`;
		if (!form.value.handlerQualityConfig) {
			form.value.handlerQualityConfig = {
				sources: {
					user: [],
					role: [],
					form: []
				}
			};
		}
		if (!form.value.internalConfig) {
			form.value.internalConfig = {
				period: '',
				date: ''
			};
		}
		if (!form.value.compositeKeyConfig) {
			form.value.compositeKeyConfig = {
				keyFields: []
			};
		}
		console.log('编辑123', form.value);
	} else {
		filePath.value = `通知/${router.options.history.state.data}`;
		activeStep.value = 0;
		form.value = {
			name: '',
			formId: null,
			formName: '',
			checkSql: '',
			compositeKeyConfig: {
				keyFields: []
			},
			noticeTemplate: '',
			handlerQualityConfig: {
				sources: {
					user: [],
					role: [],
					form: []
				}
			},
			internalConfig: {
				period: '',
				date: ''
			},
			status: true
		};
		form.value.formId = router.options.history.state.data;
	}
	console.log('filePath', filePath.value);
	initEditor();
	getFormList();
});

// 获取表单列表
const getFormList = async () => {
	loading.value = true;
	try {
		if (Number(form.value.formId)) {
			const res = await service.cloud.db.list();
			if (res.length > 0) {
				formList.value = res.map(item => {
					return {
						name: item.name,
						id: item.id
					};
				});
				console.log('res', res, 'form.value.formId', form.value, form.value.formId);
				const cols = res.filter(item => item.id == form.value.formId);
				cloudFormName.value = cols[0].tableName;
				console.log('cols', cols, cols[0].colInfo);
				const colList = changeRules(cols[0].colInfo).filter(item => item.selected);
				console.log('cols', cols, 'colInfoList.value', colInfoList.value);
				colInfoList.value = [];
				colList.map(item => {
					if (item.hasDict) {
						if (item.isProject) {
							colInfoList.value.push({
								label: item.name,
								value: item.name
							});
							colInfoList.value.push({
								label: 'projectCode',
								value: 'projectCode'
							});
						} else {
							colInfoList.value.push({
								label: item.name,
								value: item.name
							});
							colInfoList.value.push({
								label: `${item.name}代码`,
								value: `${item.name}代码`
							});
						}
					} else {
						colInfoList.value.push({
							label: item.name,
							value: item.name
						});
					}
				});
			}
		} else {
			cloudFormName.value = form.value.formId;
			const res = await service.quality.check.outterSchemas();
			const selectedForm = res.filter(item => item.tablename == form.value.formId);
			formList.value = res.map(item => {
				return {
					name: item.tablename,
					id: item.tablename
				};
			});
			colInfoList.value = [];
			selectedForm[0].schema_info.split(',').map(item => {
				colInfoList.value.push({
					label: item,
					value: item
				});
			});
		}
	} catch (err) {
		console.error(err);
		ElMessage.error('获取表单失败' + err.message);
	}
	loading.value = false;
};

// 表单选择改变
const handleFormChange = (id: number) => {
	const selected = formList.value.find(item => item.id === id);
	if (selected) {
		form.value.formName = selected.name;
	}
};

// 预览SQL
const previewSql = async () => {
	if (!toRaw(editorView.value).getValue()) {
		ElMessage.warning('请输入校验SQL');
		return;
	}

	try {
		const rule = {
			sql: toRaw(editorView.value).getValue()
			//formId: form.value.formId
		};
		const res = await service.quality.check.doQuerySql(rule);
		console.log('预览', res);
		if (res.length > 0) {
			previewColumns.value = Object.keys(res[0]);

			console.log('res[0]', res[0], 'values', Object.values(res[0]));
			if (res.every(obj => Object.values(obj).every(val => val === null))) {
				ElMessage.warning('sql验证成功，数据结果为空');
				//previewData.value = [];
				previewData.value = res;
			} else {
				ElMessage.success('sql验证成功，已显示结果(' + res.length + '条)');
				previewData.value = res;
			}
		} else {
			ElMessage.warning('sql验证成功，数据结果为空');
			previewData.value = [];
			//	previewColumns.value = [];
		}
	} catch (err) {
		ElMessage.error('预览失败：' + err.message);
		previewData.value = [];
		previewColumns.value = [];
	}
};

// 选择角色
const selectRole = () => {
	roleSelectRef.value.open();
};

// 选择用户
const selectUser = () => {
	userSelectRef.value.open();
};

// 选择表单
const selectForm = () => {
	formSelectRef.value.open({ formId: form.value.formId });
};
const userSelectData = data => {
	console.log(data);
	const users = data.map(item => {
		return {
			id: item.id,
			name: item.username
		};
	});
	form.value.handlerQualityConfig.sources.user.push(...users);
};
// 移除数据源中的项
const removeSource = (sourceType, itemId) => {
	if (sourceType === 'form') {
		console.log('移除表单', itemId);
		form.value.handlerQualityConfig.sources.form.splice(itemId, 1);
	} else {
		form.value.handlerQualityConfig.sources[sourceType] =
			form.value.handlerQualityConfig.sources[sourceType].filter(item => item.id !== itemId);
	}
};

// 处理角色选择回调
const roleSelectData = (data, index) => {
	const roles = data.map(item => ({
		id: item.id,
		name: item.name
	}));
	form.value.handlerQualityConfig.sources.role.push(...roles);
};

// 处理表单选择回调

const formSelectData = (data, index) => {
	const forms: any = data;
	console.log('forms', forms);
	console.log('formData.value', form.value.handlerQualityConfig.sources);
	form.value.handlerQualityConfig.sources.form.push({
		id: forms.externalFormId,
		name: cloneDeep(forms)
	});
	console.log('push后', form.value.handlerQualityConfig.sources.form);
};

const addRules = async () => {
	if (!form.value.compositeKeyConfig || form.value.compositeKeyConfig.keyFields.length === 0) {
		ElMessage.error('请选择联合主键');
		return;
	}

	if (!form.value.internalConfig.period || !form.value.internalConfig.date) {
		ElMessage.error('请选择处理周期和日期');
		return;
	}
	if (type.value == 'add') {
		form.value.formName = formList.value.filter(item => item.id === form.value.formId)[0].name;
		form.value.checkSql = toRaw(editorView.value).getValue();
		try {
			console.log('form.value', form.value);

			await service.quality.check.add(form.value);
			ElMessage.success('规则添加成功');
			activeStep.value = 0;
			router.push('/dataCheck');
		} catch (err) {
			ElMessage.error('规则添加失败：' + err.message);
		}
	} else {
		form.value.checkSql = toRaw(editorView.value).getValue();
		form.value.status = true;
		try {
			console.log('form.value', form.value);
			await service.quality.check.update(form.value);
			ElMessage.success('规则修改成功');
			router.push('/dataCheck');
			activeStep.value = 0;
		} catch (err) {
			ElMessage.error('规则修改失败：' + err.message);
		}
	}
};

const nextStep = async () => {
	if (!formRef.value) return;

	try {
		const res = formList.value.filter(item => item.id == form.value.formId);

		if (form.value.noticeTemplate == '') {
			form.value.noticeTemplate = `<p>表单 '<span style="color: #ce4300;">${res[0].name}</span>' 需要 补录 行主键为<span style="color:#ff6400;">{id}</span> 的信息，请处理!</p>`;
		}

		await formRef.value.validate();
		// 在切换步骤前保存编辑器内容到表单
		form.value.checkSql = toRaw(editorView.value).getValue();
		activeStep.value++;
		previewData.value = [];
		previewColumns.value = [];
	} catch (error) {
		ElMessage.error('请填写完整信息');
		return;
	}
};

const prevStep = () => {
	activeStep.value--;
	// 在返回第一步时，需要等待DOM更新后重新设置编辑器内容
	setTimeout(() => {
		if (editorView.value && form.value.checkSql) {
			console.log('配置编辑器内容', form.value.checkSql, toRaw(editorView.value).getValue());
			toRaw(editorView.value).setValue(form.value.checkSql);
		}
	}, 0);
};

const handleSubmit = async () => {
	// 检查处理人是否存在
	if (form.value.handlerQualityConfig) {
		const sources = form.value.handlerQualityConfig.sources;
		const hasHandler =
			sources.user.length > 0 || sources.role.length > 0 || sources.form.length > 0;

		if (!hasHandler) {
			form.value.handlerQualityConfig = null;
		}
		//  else  {
		// 	ElMessage.error('请至少选择一个处理人');
		// 	return;
		// }
	}
	// 处理提交逻辑
	addRules();
};

// 添加一个方法来处理编辑器的初始化
const initEditor = () => {
	if (editorContainer.value) {
		try {
			if (editorView.value) {
				const editor = toRaw(editorView.value);
				if (editor && typeof editor.dispose === 'function') {
					editor.dispose();
				}
				editorView.value = null;
			}

			if (editorContainer.value.children.length > 0) {
				editorContainer.value.innerHTML = '';
			}

			// 增强的编辑器配置
			editorView.value = monaco.editor.create(editorContainer.value, {
				value: form.value.checkSql || '',
				language: 'sql',
				theme: 'vs',
				automaticLayout: true,
				fontSize: 14,
				lineHeight: 22,
				minimap: { enabled: false },
				scrollbar: {
					vertical: 'visible',
					horizontal: 'visible',
					useShadows: false,
					verticalScrollbarSize: 10,
					horizontalScrollbarSize: 10
				},
				quickSuggestions: {
					other: true,
					comments: true,
					strings: true
				},
				suggestSelection: 'first',
				suggestOnTriggerCharacters: true,
				snippetSuggestions: 'inline',
				wordBasedSuggestions: true,
				parameterHints: {
					enabled: true,
					cycle: true
				},
				placeholder: `请输入SQL语句
-- 1、查询data库的表时，需要加上'data.'，如select * from data.cc
-- 2、查询数据不存在类型时，sql语句固定为：
-- 第一个固定为id（联合主键，保证唯一性，必须和上方选择的联合主键顺序一致）
-- 第二个固定user（对应处理人列表），可被下一步配置覆盖
-- 示例请点击下方sql帮助图标`,
				suggest: {
					localityBonus: true,
					snippetsPreventQuickSuggestions: false,
					showIcons: true,
					maxVisibleSuggestions: 12,
					showMethods: true,
					showFunctions: true,
					showVariables: true
				},
				folding: true,
				lineNumbers: 'on',
				roundedSelection: true,
				selectOnLineNumbers: true,
				padding: { top: 8, bottom: 8 },
				renderLineHighlight: 'all'
			});

			// 注册SQL语言特性，只添加自定义表名提示
			monaco.languages.registerCompletionItemProvider('sql', {
				provideCompletionItems: async () => {
					const formNameList = await formNameStore.getFormNameList();
					const suggestions = formNameList.map(item => {
						return {
							label: item,
							kind: monaco.languages.CompletionItemKind.Class,
							insertText: item
						};
					});

					// const suggestions = [
					// 	// 只保留自定义表名
					// 	{
					// 		label: 'func_财务专用章',
					// 		kind: monaco.languages.CompletionItemKind.Class,
					// 		insertText: 'func_财务专用章'
					// 	},
					// 	{
					// 		label: 'func_坏账计提比例',
					// 		kind: monaco.languages.CompletionItemKind.Class,
					// 		insertText: 'func_坏账计提比例'
					// 	},
					// 	{
					// 		label: 'func_授信',
					// 		kind: monaco.languages.CompletionItemKind.Class,
					// 		insertText: 'func_授信'
					// 	}
					// ];

					return { suggestions };
				},
				triggerCharacters: ['_', '.', ' ']
			});

			// 添加SQL格式化提供程序
			monaco.languages.registerDocumentFormattingEditProvider('sql', {
				provideDocumentFormattingEdits(model) {
					const text = model.getValue();
					// 这里实现SQL格式化逻辑
					const formatted = formatSqlString(text);
					return [
						{
							range: model.getFullModelRange(),
							text: formatted
						}
					];
				}
			});
		} catch (error) {
			console.error('编辑器初始化错误:', error);
			editorView.value = null;
		}
	}
};

// SQL格式化具体实现
const formatSqlString = (sql: string) => {
	if (!sql.trim()) return sql;

	// 简单的SQL格式化逻辑
	const keywords = [
		'SELECT',
		'FROM',
		'WHERE',
		'AND',
		'OR',
		'ORDER BY',
		'GROUP BY',
		'HAVING',
		'JOIN',
		'LEFT JOIN',
		'RIGHT JOIN',
		'INNER JOIN',
		'LIMIT',
		'OFFSET'
	];

	// 将SQL转换为大写关键字
	let formatted = sql.replace(
		/\b(SELECT|FROM|WHERE|AND|OR|ORDER BY|GROUP BY|HAVING|JOIN|LEFT JOIN|RIGHT JOIN|INNER JOIN|LIMIT|OFFSET)\b/gi,
		keyword => keyword.toUpperCase()
	);

	// 在关键字前添加换行
	keywords.forEach(keyword => {
		const regex = new RegExp(`\\b${keyword}\\b`, 'g');
		formatted = formatted.replace(regex, `\n${keyword}`);
	});

	// 处理缩进
	const lines = formatted.split('\n').filter(line => line.trim());
	let indent = 0;
	formatted = lines
		.map(line => {
			line = line.trim();
			// 减少缩进的情况
			if (line.startsWith(')')) {
				indent = Math.max(0, indent - 1);
			}
			const result = ' '.repeat(indent * 4) + line;
			// 增加缩进的情况
			if (line.includes('(')) {
				indent++;
			}
			return result;
		})
		.join('\n');

	return formatted;
};

// 格式化SQL
const formatSql = () => {
	if (!editorView.value) return;

	const editor = toRaw(editorView.value);
	// 获取当前编辑器中的所有内容范围
	const model = editor.getModel();
	if (!model) return;

	const range = model.getFullModelRange();

	// 触发格式化
	editor.trigger('formatter', 'editor.action.formatDocument', {});
};

// 监听 activeStep 的变化
watch(activeStep, newVal => {
	if (newVal === 0) {
		console.log('activeStep初始化编辑器', newVal);
		// 假设 SQL 编辑器在第二步
		nextTick(() => {
			console.log('重新初始化');
			initEditor();
		});
	}
});

// 在组件卸载时清理编辑器实例
onBeforeUnmount(() => {
	console.log('卸载组件');
	const editor = toRaw(editorView.value);
	if (editor && typeof editor.dispose === 'function') {
		editor.dispose();
		console.log('编辑器销毁完成');
	}
});

defineExpose({
	open: () => {
		dialogVisible.value = true;
	}
});

// 显示SQL帮助
const showSqlHelp = () => {
	sqlHelpVisible.value = true;
};
</script>

<style lang="scss" scoped>
.itemWidth {
	width: 300px;
}
.rules-edit-container {
	padding: 15px 20px 5px 20px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

	.mb-20 {
		margin-bottom: 20px;
	}
}

.sql-container {
	display: flex;
	flex-direction: column;
	width: 100%;
	max-width: 1000px;
	background: #fff;
	border-radius: 8px;

	.editor-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 5px 16px;
		// background: #f8fafc;
		// border: 1px solid #e4e7ed;
		// border-bottom: none;
		// border-radius: 8px 8px 0 0;

		.editor-title {
			display: flex;
			align-items: center;
			gap: 8px;
			font-size: 14px;
			font-weight: 500;
			color: #1f2937;

			.el-icon {
				font-size: 16px;
				color: var(--el-color-primary);
			}
		}

		.editor-actions {
			display: flex;
			gap: 12px;
			margin-left: 160px;

			.el-button {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 32px;
				height: 22px;
				padding: 0;
				border-radius: 6px;
				transition: all 0.2s;

				&:hover {
					background-color: var(--el-color-primary-light-9);
					.el-icon {
						color: var(--el-color-primary);
					}
				}

				.el-icon {
					font-size: 16px;
					color: #606266;
				}
			}
		}
	}

	.editor {
		height: 200px;
		border: 1px solid #e4e7ed;
		border-radius: 8px;
		overflow: hidden;

		:deep(.monaco-editor) {
			.margin {
				background: #f8fafc !important;
			}

			.monaco-scrollable-element {
				border-radius: 0 0 8px 8px;
			}
		}
	}
}

.preview-section {
	//margin-top: 24px;
	width: 100%;

	.preview-header {
		display: flex;
		height: 40px;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 8px;

		.preview-title {
			display: flex;
			align-items: center;
			gap: 8px;
			font-size: 16px;
			font-weight: 500;
			color: #1f2937;

			.el-icon {
				font-size: 18px;
				color: var(--el-color-primary);
			}
		}

		.preview-count {
			font-size: 14px;
			color: #606266;
		}
	}

	.preview-container {
		background: #fff;
		border-radius: 8px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
		padding: 16px;

		.custom-table {
			:deep(.el-table__header) {
				th {
					background-color: #f8fafc;
					color: #1f2937;
					font-weight: 500;
					padding: 12px 8px;
				}
			}

			:deep(.el-table__body) {
				td {
					padding: 12px 8px;
				}
			}
		}
	}
}

.valid-icon {
	color: #67c23a;
	margin-left: 5px;
}
.preview-title {
	font-size: 16px;
	color: #606266;
	margin: 24px 0 16px;
	padding-left: 12px;
	border-left: 4px solid var(--el-color-primary);
}

.field-config-section {
	background-color: var(--el-fill-color-lighter);
	border-radius: 6px;
	padding: 6px;
}
.field-config-card {
	background-color: white;
	border-radius: 6px;
	padding: 12px;
}

.source-options {
	display: flex;
	gap: 12px;
	flex-wrap: wrap;
}

.source-item {
	flex: 1;
	min-width: 200px;
	border: 1px solid var(--el-border-color-lighter);
	border-radius: 4px;
	padding: 8px;
	background-color: var(--el-fill-color-lighter);
}

.source-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 6px;
}

.source-header .el-checkbox {
	display: flex;
	align-items: center;
}

.source-header .el-checkbox .el-icon {
	margin-right: 4px;
}

.el-form-item {
	.el-button + .el-button {
		margin-left: 10px;
	}
}
.preview-container {
	padding: 10px;
	width: 95%;
	background-color: #f9f9f9;
	border-radius: 8px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.custom-table {
	width: 100%;
}

.custom-table .el-table__body td {
	padding: 12px 0;
}

// 添加编辑器主题相关样式
:deep(.monaco-editor) {
	.current-line {
		border: none !important;
		background-color: #f8f9fa !important;
	}

	.line-numbers {
		color: #999 !important;
	}

	.suggest-widget {
		border-radius: 6px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
	}
}

.sql-help-content {
	padding: 8px;

	h4 {
		color: #1f2937;
		font-size: 16px;
		margin-bottom: 20px;
		padding-bottom: 12px;
		border-bottom: 1px solid #e5e7eb;
	}

	.help-item {
		margin-bottom: 24px;

		.help-title {
			font-size: 14px;
			font-weight: 500;
			color: #374151;
			margin-bottom: 8px;
		}

		.help-code {
			background: #f8fafc;
			padding: 16px;
			border-radius: 6px;
			font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
			font-size: 13px;
			line-height: 1.6;
			color: #374151;
			margin-bottom: 10px;
		}
	}
}

.composite-key-tip {
	margin-top: 8px;
	margin-left: 4px;

	.tip-trigger {
		display: flex;
		align-items: center;
		gap: 4px;
		color: var(--el-color-warning);
		cursor: pointer;

		.el-icon {
			font-size: 16px;
		}

		span {
			font-size: 13px;
		}
	}
}

.tip-content {
	padding: 4px;

	.tip-item {
		display: flex;
		align-items: flex-start;
		gap: 8px;
		padding: 8px 0;

		&:not(:last-child) {
			border-bottom: 1px solid var(--el-border-color-lighter);
		}

		.el-icon {
			font-size: 16px;
			color: var(--el-color-primary);
			margin-top: 2px;
		}

		span {
			font-size: 13px;
			line-height: 1.5;
			color: var(--el-text-color-regular);
		}
	}
}

.button-container {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.left-buttons {
	display: flex;
	gap: 10px;
}

.editor-actions {
	display: flex;
	gap: 12px;
}
</style>
