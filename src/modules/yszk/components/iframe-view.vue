<template>
	<div class="iframe-container">
		<!-- Loading state -->
		<div v-if="loading" class="loading-overlay">
			<el-skeleton :rows="10" animated />
		</div>

		<!-- Error state -->
		<div v-if="error" class="error-overlay">
			<el-result
				icon="error"
				:title="error.message || '加载失败'"
				:sub-title="'请检查登录状态或刷新重试'"
			>
				<template #extra>
					<el-button type="primary" @click="reload">重新加载</el-button>
					<el-button @click="relogin">重新登录</el-button>
				</template>
			</el-result>
		</div>

		<!-- Iframe content -->
		<iframe
			v-show="!error"
			ref="iframeRef"
			:src="url"
			:class="{ 'iframe-loaded': !loading }"
			frameborder="0"
			class="iframe"
			@load="onLoad"
			@error="onError"
			crossorigin="anonymous"
			allow="*"
			sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-downloads"
		></iframe>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useIframeUrls } from '../hooks/useIframeUrls';
import { useBase } from '/$/base';

const { user } = useBase();

defineOptions({
	name: 'iframe-view'
});

const props = defineProps({
	url: {
		type: String,
		required: true
	}
});

const router = useRouter();
const loading = ref(true);
const error = ref<Error | null>(null);
const iframeRef = ref<HTMLIFrameElement | null>(null);
const { processUrl } = useIframeUrls('');

// 监听 URL 变化
watch(
	() => props.url,
	newUrl => {
		if (newUrl && iframeRef.value) {
			loading.value = true;
			error.value = null;
			//const tokenUrl = newUrl + '?lurutoken=' + user.token;
			//console.log('xinUrl-0', tokenUrl);
			iframeRef.value.src = processUrl(newUrl);
		}
	}
);

// Handle iframe load event
function onLoad() {
	loading.value = false;
	error.value = null;

	// 尝试与 iframe 内容通信
	try {
		const iframe = iframeRef.value;
		if (iframe?.contentWindow) {
			iframe.contentWindow.postMessage({ type: 'init', from: 'parent' }, '*');
		}
	} catch (err) {
		console.warn('Post message failed:', err);
	}
}

// Handle iframe error
function onError(e: Event) {
	loading.value = false;
	error.value = new Error('加载失败，请检查登录状态');
}

// Reload iframe
function reload() {
	if (!iframeRef.value || !props.url) return;
	//const tokenUrl = props.url + '?lurutoken=' + user.token;
	//console.log('xinUrl', tokenUrl);
	loading.value = true;
	error.value = null;
	iframeRef.value.src = processUrl(props.url);
}

// 重新登录
function relogin() {
	// 清除当前登录信息
	localStorage.removeItem('token');
	// 跳转到登录页
	router.push('/login');
}

// 监听来自 iframe 的消息
function handleMessage(event: MessageEvent) {
	try {
		const data = event.data;
		if (data.type === 'auth-error') {
			error.value = new Error('认证失败，请重新登录');
		}
	} catch (err) {
		console.warn('Handle message error:', err);
	}
}

onMounted(() => {
	window.addEventListener('message', handleMessage);
});

// Clean up
onBeforeUnmount(() => {
	window.removeEventListener('message', handleMessage);
	if (iframeRef.value) {
		iframeRef.value.src = 'about:blank';
	}
});
</script>

<style lang="scss" scoped>
.iframe-container {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;

	.loading-overlay,
	.error-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: #fff;
		z-index: 2;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20px;
	}

	.iframe {
		width: 100%;
		height: 100%;
		border: none;
		opacity: 0;
		transition: opacity 0.3s ease;

		&.iframe-loaded {
			opacity: 1;
		}
	}
}
</style>
