<template>
	<div class="selectMonthBoxSquare rel clearFixed" id="boxArea">
		<!-- el-input输入框：readonly和clearable属性不能同时使用 -->
		<el-input
			class="inputStyle"
			v-model="inputValue"
			type="text"
			placeholder="请选择查询月份"
			readonly
			@focus="showBox = true"
			@clear="resetMonth"
		>
			<template #prefix>
				<el-icon><calendar /></el-icon>
			</template>
			<template #suffix>
				<el-icon v-if="showClear" @click="resetMonth"><circle-close /></el-icon>
			</template>
		</el-input>
		<!-- 年份月份选择弹框 -->
		<div class="selectContentBox" v-if="showBox">
			<div class="contentArea">
				<!-- 年份 -->
				<div
					class="flex flex-wrap flex-around"
					style="padding: 15px 0; border-bottom: 1px solid #e5e5e5"
				>
					<div class="cursor" v-if="curIndex == DateList.length - 1" style="width: 15%">
						<el-icon><d-arrow-left /></el-icon>
					</div>
					<div class="cursor" v-else @click="reduceYear" style="width: 15%">
						<el-icon><d-arrow-left /></el-icon>
					</div>
					<div>{{ OneY }}年</div>
					<div class="cursor t-r" v-if="curIndex == 0" style="width: 15%">
						<el-icon><d-arrow-right /></el-icon>
					</div>
					<div class="cursor t-r" v-else @click="addYear" style="width: 15%">
						<el-icon><d-arrow-right /></el-icon>
					</div>
				</div>
				<!-- 月份 -->
				<div class="conterList">
					<el-checkbox-group
						class="flex flex-wrap"
						v-model="optTime[curIndex].queryTime"
						@change="onChange"
					>
						<el-checkbox
							class="onSelect flex-x-center"
							v-for="(item, index) in DateList[curIndex].queryTime"
							:key="index"
							:label="`${DateList[curIndex].TimeYear}-${item <= 9 ? `0${item}` : item}`"
						>
							{{ monthMap[item] }}月
						</el-checkbox>
					</el-checkbox-group>
				</div>
			</div>
			<!-- 按钮 -->
			<div class="buttonBox t-r">
				<el-button
					class="buttonStyle"
					size="mini"
					type="primary"
					plain
					@click.stop="handleSubmit"
					>确定</el-button
				>
				<el-button
					class="buttonStyle"
					size="mini"
					type="success"
					plain
					@click.stop="selectedyear"
					>整年</el-button
				>
				<el-button class="buttonStyle" size="mini" plain @click.stop="resetMonth"
					>重置</el-button
				>
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
import { ref, onMounted, toRefs, computed } from "vue";
import { Calendar, CircleClose, DArrowLeft, DArrowRight } from "@element-plus/icons-vue";

const props = defineProps({
	modelValue: {
		// 改用 modelValue 作为 prop 名称
		type: String,
		default: ""
	}
});
const inputValue = computed({
	get: () => props.modelValue,
	set: value => emit("update:modelValue", value)
});
// 响应式状态声明
const DateList = ref([]);
const optTime = ref([]);
const OneY = ref("");
const curIndex = ref(0);
const optTimes = ref([]);
const resultTimes = ref([]);
const showBox = ref(false);

const showClear = ref(false);

// 月份映射
const monthMap = {
	1: "一",
	2: "二",
	3: "三",
	4: "四",
	5: "五",
	6: "六",
	7: "七",
	8: "八",
	9: "九",
	10: "十",
	11: "十一",
	12: "十二"
};

// emit 声明
const emit = defineEmits(["update:modelValue", "submitBtn", "resetBtn"]);

// 方法定义
const init = () => {
	const _opt = [];
	const _optTime = [];
	const arr = new Array(12);
	const optDate = getDateList();

	optDate.map((item, index) => {
		_optTime[index] = {
			TimeYear: item,
			queryTime: []
		};
		_opt[index] = {
			TimeYear: item,
			queryTime: []
		};
		for (let i = 1; i <= arr.length; i++) {
			_opt[index].queryTime.push(i);
		}
	});

	optTime.value = _optTime;
	DateList.value = _opt;

	// 添加初始值处理
	if (props.modelValue) {
		const initialValues = props.modelValue.split(",");
		optTimes.value = initialValues;
		resultTimes.value = initialValues;
		showClear.value = true;

		// 设置选中状态
		initialValues.forEach(value => {
			const year = value.substring(0, 4);
			const month = parseInt(value.substring(5));
			const yearIndex = DateList.value.findIndex(item => item.TimeYear === parseInt(year));
			if (yearIndex !== -1) {
				optTime.value[yearIndex].queryTime.push(
					`${year}-${month <= 9 ? `0${month}` : month}`
				);
			}
		});
	}
};

const getDateList = () => {
	const Dates = new Date();
	const year = Dates.getFullYear();
	OneY.value = year;
	const optDate = [];
	for (let i = year - 20; i <= year; i++) {
		optDate.push(i);
	}
	return optDate.reverse();
};

// 确定
const handleSubmit = () => {
	inputValue.value = optTimes.value.join(",");
	showClear.value = inputValue.value !== "";
	resultTimes.value = optTimes.value;
	showBox.value = false;
	emit("submitBtn", resultTimes.value);
};

// 重置
const resetMonth = () => {
	const Dates = new Date();
	const year = Dates.getFullYear();
	OneY.value = year;
	optTimes.value = [];
	for (const i in optTime.value) {
		optTime.value[i].queryTime = [];
	}
	inputValue.value = "";
	showClear.value = false;
	resultTimes.value = [];
	showBox.value = false;
	const input = document.querySelector(".inputStyle input");
	if (input) {
		input.blur();
	}
	emit("resetBtn");
};

//选择年
const selectedyear = () => {
	console.log("年", OneY.value, optTime.value, resultTimes.value);
	optTimes.value = [OneY.value];

	inputValue.value = OneY.value;
	showBox.value = false;
	emit("submitBtn", resultTimes.value);
};

// 左上角年份减少
const reduceYear = () => {
	if (curIndex.value == DateList.value.length - 1) return;
	curIndex.value = curIndex.value + 1;
	OneY.value = DateList.value[curIndex.value].TimeYear;
};

// 左上角年份增加
const addYear = () => {
	if (curIndex.value == 0) return;
	curIndex.value = curIndex.value - 1;
	OneY.value = DateList.value[curIndex.value].TimeYear;
};

// 选择月份
const onChange = () => {
	const _opt = optTime.value;
	const arr = [];
	for (const item in _opt) {
		if (_opt[item].queryTime.length > 0)
			_opt[item].queryTime.filter(v => {
				arr.push(v);
			});
	}
	optTimes.value = arr;
	inputValue.value = optTimes.value.join(",");
	showClear.value = inputValue.value == "" ? false : true;
};

// 生命周期钩子
onMounted(() => {
	document.addEventListener("click", e => {
		const boxArea = document.getElementById("boxArea");
		if (boxArea && !boxArea.contains(e.target)) {
			const equalArr =
				resultTimes.value.sort().toString() == optTimes.value.sort().toString();
			if (!equalArr) {
				optTimes.value = resultTimes.value;
				inputValue.value = optTimes.value.join(",");
				showClear.value = inputValue.value == "" ? false : true;
				const _opt = resultTimes.value.map(v => {
					return v.substring(0, 4);
				});
				for (const item in optTime.value) {
					optTime.value[item].queryTime = [];
					_opt.map((items, indexs) => {
						if (items == optTime.value[item].TimeYear) {
							optTime.value[item].queryTime.push(resultTimes.value[indexs]);
						}
					});
				}
			}
			showBox.value = false;
		}
	});
});

// 初始化调用
init();
</script>
<style lang="scss">
.flex {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
}
.flex-wrap {
	flex-wrap: wrap;
}
.flex-around {
	justify-content: space-around;
}
.flex-x-center {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
}
.selectMonthBoxSquare {
	margin-top: 10px;
	width: 120px;
	position: relative;
	.inputStyle {
		width: 120px;
	}
	.clearIconStyle {
		display: none;
	}
	.inputStyle:hover .clearIconStyle {
		display: block;
	}
	.selectContentBox {
		position: absolute;
		top: 30px;
		left: 0;
		z-index: 2021;
		background: #ffffff;
		border: 1px solid #e5e5e5;
		border-radius: 3px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
		.contentArea {
			width: 330px;
		}
	}
	.conterList {
		.onSelect {
			width: 25% !important;
			margin: 20px 0 !important;
		}
		.columWidth {
			width: 33.33%;
		}
		.el-checkbox__input {
			display: none !important;
		}
		.el-checkbox__label {
			padding-left: 0px !important;
		}
	}
	.selectBox {
		width: 100px;

		input {
			height: 25px;
			line-height: 25px;
		}
		.el-input .el-input__icon {
			line-height: 25px;
		}
	}
	.tagStyle {
		margin-right: 10px;
		height: 25px;
		line-height: 25px;
	}
	.lableStyle {
		font-size: 14px;
	}
	.el-button--mini {
		padding: 5px 15px;
		font-size: 12px;
		border-radius: 3px;
	}
	.buttonBox {
		border-top: 1px solid #e5e5e5;
		padding: 10px 10px 10px 0;
	}
}
</style>
