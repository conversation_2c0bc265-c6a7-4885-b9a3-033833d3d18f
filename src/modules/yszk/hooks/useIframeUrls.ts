import { ref, onMounted } from 'vue';
import { useCool } from '/@/cool';

export function useIframeUrls(key: string) {
	const { service } = useCool();
	const iframeUrls = ref<{ [key: string]: string }>({});
	const loading = ref(true);
	const error = ref<Error | null>(null);

	// 处理 URL，添加必要的参数
	function processUrl(url: string) {
		if (!url) return '';

		try {
			const urlObj = new URL(url);

			// 获取当前用户的 token
			const token = localStorage.getItem('token');

			// 添加必要的参数
			urlObj.searchParams.set('token', token || '');
			urlObj.searchParams.set('timestamp', Date.now().toString());
			urlObj.searchParams.set('from', 'microApp');

			// 如果需要，可以添加更多参数
			// urlObj.searchParams.set('otherParam', 'value');

			return urlObj.toString();
		} catch (err) {
			console.error('URL processing error:', err);
			return url;
		}
	}

	// 预加载函数
	function preloadIframe(url: string) {
		if (!url) return;

		const processedUrl = processUrl(url);

		// 创建 link prefetch
		const link = document.createElement('link');
		link.rel = 'prefetch';
		link.href = processedUrl;
		document.head.appendChild(link);

		// 创建隐藏的 iframe 进行预加载
		const hiddenIframe = document.createElement('iframe');
		hiddenIframe.style.display = 'none';
		hiddenIframe.src = processedUrl;

		// 添加跨域属性
		hiddenIframe.setAttribute('crossorigin', 'anonymous');
		hiddenIframe.setAttribute('allow', '*');

		document.body.appendChild(hiddenIframe);

		// 30秒后移除预加载 iframe
		setTimeout(() => {
			document.body.removeChild(hiddenIframe);
		}, 30000);
	}

	onMounted(async () => {
		try {
			loading.value = true;
			const res = await service.base.sys.param.page({ keyName: 'biChart' });
			if (res.list?.[0]?.data) {
				const data = JSON.parse(res.list[0].data);
				// 处理所有 URL
				const urls = data[key] || {};
				Object.keys(urls).forEach(k => {
					iframeUrls.value[k] = processUrl(urls[k]);
				});

				// 预加载所有 iframe URL
				// Object.values(iframeUrls.value).forEach(url => {
				// 	if (url) preloadIframe(url);
				// });
			}
		} catch (err) {
			console.error(`Failed to load iframe URLs for ${key}:`, err);
			error.value = err as Error;
		} finally {
			loading.value = false;
		}
	});

	return {
		iframeUrls,
		loading,
		error,
		preloadIframe,
		processUrl
	};
}
