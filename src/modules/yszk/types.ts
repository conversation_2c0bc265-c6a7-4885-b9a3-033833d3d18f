export interface DesignTable {
	tableName: string;
	type?: string;
	tableColumn: CustomizeColumn[];
	remark: string;
}
export interface CustomizeColumn {
	name: string;
	showName: string;
	type: string;
	hasDict: boolean;
	dictId?: number;
	isLeaf?: boolean;
	parentId?: number;
	parentName?: string;
	dictName?: string;
	dictField?: string;
	dictFieldName?: string;
	dictCode?: string;
	disabled?: boolean;
	typeDisabled?: boolean;
	isCode?: string;
	isProject?: boolean;
	decimals?: number;
	dateFormat?: string;
	dateType?: string;
	isPercent?: boolean;
	isNull?: boolean;
}
export interface DesignRow {
	id: number;
	createTime: string;
	updateTime: string;
	name: string;
	info: DesignTable[];
	remark: string;
}

export interface SelectDict {
	label: string;
	key: string;
}

export interface SearchData {
	selectValue: string;
	operation: string;
	inputValue: string | number;
}
