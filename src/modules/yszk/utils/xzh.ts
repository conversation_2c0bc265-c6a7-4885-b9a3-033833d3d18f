export function yearToMonth(date) {
	let months;
	console.log("date", date, typeof date);
	if (typeof date == "number") date = date.toString();
	if (date.indexOf("-") == -1) {
		const year = date;
		console.log("year", year);
		months = Array.from({ length: 12 }, (_, i) => {
			const month = (i + 1).toString().padStart(2, "0");
			return `${year}-${month}`;
		});
	} else {
		// 如果已经是月份格式，直接使用
		months = date.split(",");
	}
	return months;
}

//根据props的顺序，对ts进行排序和隐藏
export function sortAndHideColumns(ts, props) {
	// 创建一个映射，方便根据 name 快速查找 prop 的配置
	const propMap = {};
	props.forEach(prop => {
		propMap[prop.name] = prop;
	});

	// 排序函数，根据 props 的顺序排序
	const sortOrder = props.map(prop => prop.name);
	ts.sort((a, b) => {
		const indexA = sortOrder.indexOf(a.prop);
		const indexB = sortOrder.indexOf(b.prop);
		return indexA - indexB;
	});

	// 调整特殊字段对的位置
	const specialPairs = [
		{ code: "projectCode", name: "所属项目名称" },
		{ code: "客商名称代码", name: "客商名称" },
		{ code: "合同代码", name: "合同名称" }
	];

	for (const pair of specialPairs) {
		const codeIndex = ts.findIndex(item => item.prop === pair.code);
		const nameIndex = ts.findIndex(item => item.prop === pair.name);
		if (codeIndex !== -1 && nameIndex !== -1) {
			// 如果找到了配对的字段
			if (codeIndex > nameIndex) {
				// 如果代码字段在名称字段后面，将代码字段移到名称字段前面
				const codeItem = ts.splice(codeIndex, 1)[0];
				ts.splice(nameIndex, 0, codeItem);
			} else if (codeIndex + 1 !== nameIndex) {
				// 如果代码字段在名称字段前面但不相邻，将代码字段移到名称字段前面
				const codeItem = ts.splice(codeIndex, 1)[0];
				ts.splice(nameIndex - 1, 0, codeItem);
			}
		}
	}

	// 遍历 ts，根据 isNotShow 添加 hidden 属性
	ts.forEach((column, index) => {
		const specialFieldMap = {
			projectCode: "所属项目名称",
			客商名称代码: "客商名称",
			合同代码: "合同名称"
		};

		const propConfig = propMap[column.prop];
		if (propConfig) ts[index].label = propConfig.showName;

		// 处理普通字段和特殊字段的隐藏逻辑
		const relatedField = specialFieldMap[column.prop];
		ts[index] = {
			...column,
			hidden: relatedField
				? (propMap[relatedField]?.isNotShowCode ?? false)
				: (propConfig?.isNotShow ?? false)
		};
	});

	return ts;
}
