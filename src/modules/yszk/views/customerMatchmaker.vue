<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 关键字搜索 -->
			<cl-search-key />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" name="菜单名称" setup>
import { onMounted, ref } from 'vue';
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { useStore } from '/@/modules/base/store';
import { SystemDict, PermissionInfo } from '/@/modules/luru/utils/constants';
import { ElMessage } from 'element-plus';
import { AUDIT_STATUS } from '/@/modules/luru/utils/constants';

const { service } = useCool();
const { user } = useStore();
const projectNameList = ref<any[]>([]);
const userNameList = ref<any[]>([]);
const projectDictName = SystemDict.Project;
const userDictName = SystemDict.User;
const CustomerDictId = PermissionInfo.CustomerDictId;
const customerDictName = SystemDict.Customer;
const customerNameList = ref<any[]>([]);
onMounted(async () => {
	const res = await service.dict.info.data({ types: [projectDictName] });
	projectNameList.value = res[projectDictName]
		.filter(dict => {
			return dict.parentId === null;
		})
		.map(item => {
			return { label: item.name, value: item.name + '&' + item.value };
		});
	//projectNameList.value.unshift({ label: "所有项目", value: "ALL&ALL" });
	const customerRes = await service.dict.info.data({ types: [customerDictName] });
	customerNameList.value = customerRes[customerDictName]
		.filter(dict => {
			return dict.parentId === null;
		})
		.map(item => {
			return { label: item.name, value: item.name + '&' + item.value };
		});

	const userNameRes = await service.dict.info.data({
		types: [userDictName]
	});
	userNameList.value = userNameRes[userDictName].map(item => {
		return { label: item.name, value: item.value };
	});
});

const loading = ref(false);
const filteredUserList = ref([]);

const remoteSearchForms = async (query: string) => {
	if (query !== '') {
		loading.value = true;
		// 模拟API调用，实际使用时替换为真实的API调用
		await new Promise(resolve => setTimeout(resolve, 200));
		filteredUserList.value = userNameList.value
			.filter(item => {
				return item.label.toLowerCase().includes(query.toLowerCase());
			})
			.slice(0, 20); // 只返回前20个匹配结果
		loading.value = false;
	} else {
		filteredUserList.value = [];
	}
};
// cl-upsert
const Upsert = useUpsert({
	async onInfo(data, { done, next }) {
		const newData = await next({
			...data,
			status: false
		});
		newData.permissionName = data.permissionName.split(',');
		newData.projectName = [data.projectName + '&' + data.projectCode];

		done(newData);
	},
	items: [
		{
			label: '项目名',
			prop: 'projectName',
			component: {
				name: 'el-select',
				props: {
					clearable: true,
					filterable: true
				},
				options: projectNameList
			},
			rules: [{ required: true, message: '请输入项目名', trigger: 'blur' }]
		},
		{
			label: '客商名称',
			prop: '客商名称',
			component: {
				name: 'el-select',
				props: {
					clearable: true,
					filterable: true
				},
				options: customerNameList
			},
			rules: [{ required: true, message: '请输入客商名称', trigger: 'blur' }]
		},
		{
			label: '对接人',
			prop: '对接人',
			component: {
				name: 'el-select',
				props: {
					clearable: true,
					placeholder: '键入用户名搜索',
					filterable: true,
					remote: true,
					remoteMethod: remoteSearchForms,
					loading: loading
				},
				options: filteredUserList
			},
			rules: [{ required: true, message: '请输入对接人', trigger: 'blur' }]
		},
		{
			label: '对接人邮箱',
			prop: '对接人邮箱',
			component: {
				name: 'el-input',
				props: {}
			},
			rules: [
				{ required: true, message: '请输入对接人邮箱', trigger: 'blur' },
				{ type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
			]
		},
		{
			label: '对接人手机号码',
			prop: '对接人手机号码',
			component: {
				name: 'el-input',
				props: {
					maxlength: 11
				}
			},
			rules: [
				{ required: true, message: '请输入手机号码', trigger: 'blur' },
				{ pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' }
			]
		},
		{
			label: '客商联系人',
			prop: '客商联系人',
			component: {
				name: 'el-input',
				props: {}
			},
			rules: [{ required: true, message: '请输入客商联系人', trigger: 'blur' }]
		},
		{
			label: '客商联系人邮箱',
			prop: '客商联系人邮箱',
			component: {
				name: 'el-input',
				props: {}
			},
			rules: [
				{ required: true, message: '请输入客商联系人邮箱', trigger: 'blur' },
				{ type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
			]
		},
		{
			label: '客商联系人手机号码',
			prop: '客商联系人手机号码',
			component: {
				name: 'el-input',
				props: {
					maxlength: 11
				}
			},
			rules: [
				{ required: true, message: '请输入手机号码', trigger: 'blur' },
				{ pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' }
			]
		}
	],
	props: {
		// 标签宽度
		labelWidth: '160px',

		// 标签位置
		labelPosition: 'left'
	},
	async onSubmit(data, { done, close, next }) {
		const newData: any = [];
		let project = [];
		let customer = [];
		console.log('data', data);
		project = data.projectName.split('&');
		customer = data.客商名称.split('&');

		console.log('project', project, data.对接人, customer);
		newData.push({
			录入人: user.info?.username,
			对接人: data.对接人.trim(),
			对接人代码: data.对接人.trim(),
			projectCode: project[1],
			项目名称: project[0],
			客商名称: customer[0],
			客商名称代码: customer[1],
			对接人邮箱: data.对接人邮箱,
			对接人手机号码: data.对接人手机号码,
			客商联系人: data.客商联系人,
			客商联系人邮箱: data.客商联系人邮箱,
			客商联系人手机号码: data.客商联系人手机号码,
			审核状态: AUDIT_STATUS.UNAUDITED,
			审核说明: ' '
		});
		//});
		data = newData;
		console.log('data', data);
		try {
			await service.cloud.db.data({
				id: CustomerDictId,
				method: 'add',
				params: data
			});
		} catch (error) {
			ElMessage.error(error.message);
		}
		refresh();
		//next([...data]);
		done();
		close();
	}
});

// cl-table
const Table = useTable({
	contextMenu: [],
	columns: [
		{ label: '录入人', prop: '录入人' },

		{ label: '项目代码', prop: 'projectCode' },
		{ label: '项目名称', prop: '项目名称' },
		{ label: '客商名称', prop: '客商名称' },
		{ label: '客商名称代码', prop: '客商名称代码' },
		{ label: '对接人', prop: '对接人' },
		{ label: '对接人代码', prop: '对接人代码' },
		{ label: '对接人邮箱', prop: '对接人邮箱' },
		{ label: '对接人手机号码', prop: '对接人手机号码' },
		{ label: '客商联系人', prop: '客商联系人' },
		{ label: '客商联系人邮箱', prop: '客商联系人邮箱' },
		{ label: '客商联系人手机号码', prop: '客商联系人手机号码' },
		{ label: '审核人', prop: '审核人' },
		{ label: '审核时间', prop: '审核时间' },
		{
			label: '审核状态',
			prop: '审核状态',
			minWidth: 140,
			dict: [
				{
					label: '未审核',
					value: 0,
					type: 'warning'
				},
				{
					label: '审核通过',
					value: 1,
					type: 'success'
				},
				{
					label: '已驳回',
					value: 2,
					type: 'danger'
				},
				{
					label: '已归档',
					value: 5,
					type: 'info'
				}
			]
		},
		{
			label: '审核状态',
			prop: '审核状态',
			minWidth: 140,
			dict: [
				{
					label: '未审核',
					value: AUDIT_STATUS.UNAUDITED,
					type: 'warning'
				},
				{
					label: '审核通过',
					value: AUDIT_STATUS.APPROVED,
					type: 'success'
				},
				{
					label: '已驳回',
					value: AUDIT_STATUS.REJECTED,
					type: 'danger'
				},
				{
					label: '已作废',
					value: AUDIT_STATUS.INVALID,
					type: 'danger'
				},
				{
					label: '已暂存',
					value: AUDIT_STATUS.DRAFT,
					type: 'danger'
				},
				{
					label: '已归档',
					value: AUDIT_STATUS.ARCHIVED,
					type: 'info'
				}
			]
		},
		{ label: '审核说明', prop: '审核说明' }
	]
});

// cl-crud
const Crud = useCrud(
	{
		service: service.yszk.customermatchmaker
	},
	app => {
		app.refresh();
	}
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
<style></style>
