<template>
	<div class="hz-container">
		<el-container>
			<!-- 侧边栏 -->
			<el-aside width="200px" class="sidebar">
				<el-menu :default-active="activeMenu" class="menu" @select="handleMenuSelect">
					<el-menu-item index="xzh" class="menu-item">
						<span>询证函</span>
					</el-menu-item>

					<el-menu-item index="sendStrategyLog" class="menu-item">
						<span>发函日志</span>
					</el-menu-item>

					<el-menu-item index="rpaLog" class="menu-item">
						<span>解析日志</span>
					</el-menu-item>

					<el-menu-item index="子分公司" class="menu-item">
						<div class="menu-text">
							<div>子分公司应收账款询证函回函统计表</div>
						</div>
					</el-menu-item>
					<el-menu-item index="各单位" class="menu-item">
						<div class="menu-text">
							<div>各单位函证回函确权情况统计</div>
						</div>
					</el-menu-item>
					<el-menu-item index="询证函统计表" class="menu-item">
						<div class="menu-text">
							<div>询证函统计表</div>
						</div>
					</el-menu-item>
				</el-menu>
			</el-aside>

			<!-- 主内容区 -->
			<el-main class="main-content">
				<!-- 使用keep-alive包裹动态组件 -->
				<keep-alive>
					<component :is="currentComponent" />
				</keep-alive>
			</el-main>
		</el-container>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, defineComponent, h, watch } from 'vue';
import { useCool } from '/@/cool';
import { Setting } from '@element-plus/icons-vue';
import xzh from '/@/modules/luru/views/xzhView.vue';
import sendStrategyLog from './sendStrategyLog.vue';
import rpaLog from './rpaLog.vue';
import IframeView from '../components/iframe-view.vue';
import { useIframeUrls } from '../hooks/useIframeUrls';
import { useBase } from '/$/base';

const { user } = useBase();

// 当前激活的菜单项
const activeMenu = ref('xzh');

// 使用共享的 iframe URLs hook
const { iframeUrls, loading, error, preloadIframe } = useIframeUrls('hz');

// 当前显示的组件
const currentComponent = computed(() => {
	switch (activeMenu.value) {
		case 'xzh':
			return xzh;
		case 'sendStrategyLog':
			return sendStrategyLog;
		case 'rpaLog':
			return rpaLog;
		case '子分公司':
		case '各单位':
		case '询证函统计表':
			const url = iframeUrls.value[activeMenu.value] + '&lurutoken=' + user.token;
			console.log('发出的url', url);
			return defineComponent({
				setup() {
					return () => (url ? h(IframeView, { url }) : null);
				}
			});
		default:
			return xzh;
	}
});

// 菜单选择处理
function handleMenuSelect(index: string) {
	activeMenu.value = index;
}
</script>

<style lang="scss" scoped>
.hz-container {
	height: 100%;
	background-color: #f5f7fa;

	.el-container {
		height: 100%;
	}

	.sidebar {
		background-color: #fff;
		border-right: 1px solid #e6e6e6;

		.menu {
			border-right: none;

			.menu-item {
				height: auto;
				line-height: 1.5;
				min-height: 56px;
				padding: 10px 20px;
				white-space: normal;

				.menu-text {
					div {
						line-height: 1.4;
						margin: 4px 0;
					}
				}

				:deep(.el-menu-item-content) {
					white-space: normal;
					height: auto;
					line-height: 1.5;
				}
			}
		}
	}

	.main-content {
		padding: 10px;
		height: 100%;
		overflow: hidden;

		:deep(.xzh-crud) {
			height: 100%;
		}
	}
}
</style>
