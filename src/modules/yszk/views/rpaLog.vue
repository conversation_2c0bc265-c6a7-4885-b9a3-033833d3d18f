<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<cl-column-custom :columns="Table?.columns" />

			<cl-flex1 />
			<!-- 导出按钮 -->

			<!-- 关键字搜索 -->
			<cl-search-key />
			<el-button :icon="Download" :loading="exporting" @click="exportData">导出</el-button>
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" name="rpalog" setup>
import { onMounted, ref } from 'vue';
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { ElMessage } from 'element-plus';
import { exportExcel } from '/@/plugins/excel/utils';
import { Download } from '@element-plus/icons-vue';
const { service } = useCool();
// 导出状态
const exporting = ref(false);

// cl-table
const Table = useTable({
	contextMenu: [],
	columns: [
		{ label: '更新日期', prop: 'updateTime', width: 180 },
		{ label: '询证函单号', prop: 'qrcode_decoded', width: 220 },
		{ label: '项目代码', prop: 'projectCode', width: 220 },
		{ label: '项目名称', prop: '项目名称', width: 320 },
		{ label: '应收账款会计期', prop: '应收账款会计期', width: 130 },
		{ label: '客商名称代码', prop: '客商名称代码', width: 220 },
		{ label: '客商名称', prop: '客商名称', width: 390 },
		{ label: '回函人邮箱', prop: 'sender_email', width: 220 },
		{ label: '回函时间', prop: 'receive_time', width: 220 },
		{ label: '回函主题', prop: 'subject', showOverflowTooltip: true, width: 300 },
		{
			label: '回函正文',
			prop: 'email_body',
			showOverflowTooltip: true,
			width: 300
		},
		{
			label: '有无二维码',
			prop: 'has_qrcode',
			dict: [
				{
					label: '有',
					value: 1,
					type: 'success'
				},
				{
					label: '无',
					value: 0,
					type: 'danger'
				}
			],
			width: 120
		},
		{
			label: '印章识别结果',
			prop: 'seal_ocr_result',
			showOverflowTooltip: true,
			width: 230
		},
		{
			label: '打勾识别结果',
			prop: 'checkbox_ocr_result',
			showOverflowTooltip: true,
			width: 230
		},
		{
			label: 'ocr结果',
			prop: 'ocr_result',
			showOverflowTooltip: true,
			width: 220
		},
		{
			label: '最终确权状态',
			prop: 'ocr_status',
			dict: [
				{
					label: '金额一致',
					value: 1,
					type: 'success'
				},
				{
					label: '金额不一致',
					value: 2,
					type: 'danger'
				},
				{
					label: '无法识别打勾状态',
					value: 0,
					type: 'danger'
				}
			],
			width: 130
		}
	]
});

// cl-crud
const Crud = useCrud(
	{
		service: service.yszk.xzhRpaLog
	},
	app => {
		app.refresh();
	}
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}

// 导出数据
async function exportData() {
	if (!Table.value?.data.length) {
		ElMessage.warning('暂无数据可导出');
		return;
	}

	try {
		exporting.value = true;
		// 准备列配置
		const columnList = Table.value.columns.map(col => {
			const item = {
				name: col.label,
				type: 'string'
			};

			// 处理不同类型的列
			if (col.dict) {
				item.type = 'dict';
				item.values = col.dict.map(d => d.label);
				item.codes = col.dict.map(d => d.value);
			}

			return item;
		});

		// 导出Excel
		await exportExcel(columnList, Table.value.data, 'RPA日志数据');
		ElMessage.success('导出成功');
	} catch (error) {
		console.error('导出失败', error);
		ElMessage.error('导出失败：' + (error.message || '未知错误'));
	} finally {
		exporting.value = false;
	}
}
</script>
