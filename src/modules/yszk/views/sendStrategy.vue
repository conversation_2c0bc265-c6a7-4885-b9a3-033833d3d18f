<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 关键字搜索 -->
			<cl-search-key />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" name="菜单名称" setup>
import { onMounted, ref } from 'vue';
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { useStore } from '/@/modules/base/store';
import { SystemDict, PermissionInfo } from '/@/modules/luru/utils/constants';
import { ElMessage } from 'element-plus';
import { AUDIT_STATUS } from '/@/modules/luru/utils/constants';

const { service } = useCool();
const { user } = useStore();
const projectNameList = ref<any[]>([]);
const userNameList = ref<any[]>([]);
const projectDictName = SystemDict.Project;
const userDictName = SystemDict.User;
const SendStrategyDictId = PermissionInfo.SendStrategyDictId;
const customerDictName = SystemDict.Customer;
const SendStrategyDictName = SystemDict.SendStrategy;

const SendStrategyPeriodDictName = SystemDict.SendStrategyPeriod;
const customerNameList = ref<any[]>([]);
const SendStrategyNameList = ref<any[]>([]);
const SendStrategyPeriodNameList = ref<any[]>([]);
const sendStrategyPeriodList = ref([
	{
		label: '天',
		value: '天'
	},
	{
		label: '周',
		value: '周'
	},
	{
		label: '月',
		value: '月'
	},
	{
		label: '年',
		value: '年'
	}
]);

onMounted(async () => {
	const res = await service.dict.info.data({ types: [projectDictName] });
	projectNameList.value = res[projectDictName]
		.filter(dict => {
			return dict.parentId === null;
		})
		.map(item => {
			return { label: item.name, value: item.name + '&' + item.value };
		});
	//projectNameList.value.unshift({ label: "所有项目", value: "ALL&ALL" });
	//发函策略
	const SendStrategyDictRes = await service.dict.info.data({ types: [SendStrategyDictName] });
	SendStrategyNameList.value = SendStrategyDictRes[SendStrategyDictName].filter(dict => {
		return dict.parentId === null;
	}).map(item => {
		return { label: item.name, value: item.name + '&' + item.value };
	});
	//周期
	const SendStrategyPeriodDictRes = await service.dict.info.data({
		types: [SendStrategyPeriodDictName]
	});

	SendStrategyPeriodNameList.value = SendStrategyPeriodDictRes[SendStrategyPeriodDictName].filter(
		dict => {
			return dict.parentId === null;
		}
	).map(item => {
		return { label: item.name, value: item.name + '&' + item.value };
	});
	//客商
	const customerRes = await service.dict.info.data({ types: [customerDictName] });
	customerNameList.value = customerRes[customerDictName]
		.filter(dict => {
			return dict.parentId === null;
		})
		.map(item => {
			return { label: item.name, value: item.name + '&' + item.value };
		});

	const userNameRes = await service.dict.info.data({
		types: [userDictName]
	});
	userNameList.value = userNameRes[userDictName].map(item => {
		return { label: item.name, value: item.value };
	});
});

const loading = ref(false);
const filteredUserList = ref([]);

const remoteSearchForms = async (query: string) => {
	if (query !== '') {
		loading.value = true;
		// 模拟API调用，实际使用时替换为真实的API调用
		await new Promise(resolve => setTimeout(resolve, 200));
		filteredUserList.value = userNameList.value
			.filter(item => {
				return item.label.toLowerCase().includes(query.toLowerCase());
			})
			.slice(0, 20); // 只返回前20个匹配结果
		loading.value = false;
	} else {
		filteredUserList.value = [];
	}
};

// cl-upsert
const Upsert = useUpsert({
	async onInfo(data, { done, next }) {
		const newData = await next({
			...data,
			status: false
		});
		newData.projectName = [data.项目名称 + '&' + data.项目代码];
		newData.customerName = [data.客商名称 + '&' + data.客商名称代码];
		newData.strategyName = [data.发函策略名称 + '&' + data.发函策略代码];
		newData.strategyCycle = [data.发函策略周期 + '&' + data.发函策略周期代码];

		done(newData);
	},
	items: [
		{
			label: '项目名称',
			prop: '项目名称',
			component: {
				name: 'el-select',
				props: {
					clearable: true,
					filterable: true
				},
				options: projectNameList
			},
			rules: [{ required: true, message: '请选择项目名称', trigger: 'change' }]
		},
		{
			label: '客商名称',
			prop: '客商名称',
			component: {
				name: 'el-select',
				props: {
					clearable: true,
					filterable: true
				},
				options: customerNameList
			},
			rules: [{ required: true, message: '请选择客商名称', trigger: 'change' }]
		},
		{
			label: '发函策略名称',
			prop: '发函策略名称',
			component: {
				name: 'el-select',
				props: {
					clearable: true,
					filterable: true
				},
				options: SendStrategyNameList
			},
			rules: [{ required: true, message: '请选择发函策略名称', trigger: 'change' }]
		},
		{
			label: '发函策略周期',
			prop: '发函策略周期',
			component: {
				name: 'el-input-number',
				props: {
					min: 1,
					max: 100,
					step: 1,
					precision: 0
				}
			},

			visible: formData => formData.发函策略名称 && formData.发函策略名称.includes('定时发函')
			//rules: [{ required: true, message: "请选择发函策略周期", trigger: "change" }]
		},
		{
			label: '发函策略周期类型',
			prop: '发函策略周期类型',
			component: {
				name: 'el-select',
				props: {
					clearable: true,
					filterable: true
				},
				options: sendStrategyPeriodList
			},
			rules: [{ required: true, message: '请选择发函策略周期', trigger: 'change' }],
			visible: formData => formData.发函策略名称 && formData.发函策略名称.includes('定时发函')
		}
	],
	props: {
		labelWidth: '160px',
		labelPosition: 'left'
	},
	async onSubmit(data, { done, close, next }) {
		console.log('data', data);
		const newData: any = [];
		const project = data.项目名称.split('&');
		const customer = data.客商名称.split('&');
		const strategy = data.发函策略名称.split('&');
		let cycle = ['', ''];
		if (data.发函策略周期) {
			cycle = data.发函策略周期.split('&');
		}

		newData.push({
			录入人: user.info?.username,
			projectCode: project[1],
			项目名称: project[0],
			客商名称: customer[0],
			客商名称代码: customer[1],
			发函策略代码: strategy[1],
			发函策略名称: strategy[0],
			发函策略周期: cycle[0],
			发函策略周期代码: cycle[1],
			发函策略时间: data.发函策略时间,
			审核状态: AUDIT_STATUS.UNAUDITED,
			审核说明: ' '
		});

		data = newData;
		console.log('data', data);
		try {
			await service.cloud.db.data({
				id: SendStrategyDictId,
				method: 'add',
				params: data
			});
		} catch (error) {
			ElMessage.error(error.message);
		}
		refresh();
		done();
		close();
	}
});

// cl-table
const Table = useTable({
	contextMenu: [],
	columns: [
		{ label: '录入人', prop: '录入人' },
		{ label: '项目代码', prop: 'projectCode' },
		{ label: '项目名称', prop: '项目名称' },
		{ label: '客商名称', prop: '客商名称' },
		{ label: '客商名称代码', prop: '客商名称代码' },
		{ label: '发函策略名称', prop: '发函策略代码' },
		{ label: '发函策略名称', prop: '发函策略名称' },
		{ label: '发函策略周期', prop: '发函策略周期' },
		{ label: '发函策略周期代码', prop: '发函策略周期代码' },
		{ label: '发函策略时间', prop: '发函策略时间' },
		{ label: '审核人', prop: '审核人' },
		{ label: '审核时间', prop: '审核时间' },
		{
			label: '审核状态',
			prop: '审核状态',
			minWidth: 140,
			dict: [
				{
					label: '未审核',
					value: AUDIT_STATUS.UNAUDITED,
					type: 'warning'
				},
				{
					label: '审核通过',
					value: AUDIT_STATUS.APPROVED,
					type: 'success'
				},
				{
					label: '已驳回',
					value: AUDIT_STATUS.REJECTED,
					type: 'danger'
				},
				{
					label: '已作废',
					value: AUDIT_STATUS.INVALID,
					type: 'danger'
				},
				{
					label: '已暂存',
					value: AUDIT_STATUS.DRAFT,
					type: 'danger'
				},
				{
					label: '已归档',
					value: AUDIT_STATUS.ARCHIVED,
					type: 'info'
				}
			]
		},
		{ label: '审核说明', prop: '审核说明', minWidth: 190 }
	]
});

// cl-crud
const Crud = useCrud(
	{
		service: service.yszk.sendstrategy
	},
	app => {
		app.refresh();
	}
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
