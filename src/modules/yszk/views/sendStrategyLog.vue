<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<cl-column-custom :columns="Table?.columns" />

			<cl-flex1 />
			<!-- 关键字搜索 -->
			<cl-search-key />
			<el-button :icon="Download" :loading="exporting" @click="exportData">导出</el-button>
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" name="fhrz" setup>
import { onMounted, ref } from 'vue';
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { ElMessage } from 'element-plus';
import { Download } from '@element-plus/icons-vue';
import { exportExcel } from '/@/plugins/excel/utils';

const { service } = useCool();
const exporting = ref(false);
// cl-table
const Table = useTable({
	contextMenu: [],
	columns: [
		{ label: 'id', prop: 'id', width: 120 },
		{ label: '创建日期', prop: 'createTime', width: 160 },
		{ label: '询证函单号', prop: '询证函单号', width: 220 },
		{ label: '项目名称', prop: '项目名称', width: 320 },
		{ label: '应收账款会计期', prop: '应收账款会计期', width: 130 },
		{ label: '发函人', prop: '发函人', width: 120 },
		{ label: '客商名称', prop: '客商名称', width: 380 },
		{ label: '发函策略代码', prop: '发函策略代码', width: 120 },
		{ label: '发函策略', prop: '发函策略', width: 120 },
		{ label: '发函策略周期代码', prop: '发函策略周期代码', width: 160 },
		{ label: '发函策略周期', prop: '发函策略周期', width: 180 },
		{ label: '发函策略时间', prop: '发函策略时间', width: 180 },
		{ label: '下次发函时间', prop: '下次发函时间', width: 180 },

		{
			label: '发送状态',
			prop: '发送状态',
			dict: [
				{
					label: '未发函',
					value: 0,
					type: 'danger'
				},
				{
					label: '已发函',
					value: 1,
					type: 'success'
				},
				{
					label: '发函失败',
					value: -1,
					type: 'danger'
				}
			],
			width: 120
		},
		{ label: '接收邮箱', prop: '接收邮箱', width: 350 },
		{ label: '发函日志', prop: '发函日志', width: 400 }
	]
});

// cl-crud
const Crud = useCrud(
	{
		service: service.yszk.xzhSendLog
	},
	app => {
		app.refresh();
	}
);
// 导出数据
async function exportData() {
	if (!Table.value?.data.length) {
		ElMessage.warning('暂无数据可导出');
		return;
	}

	try {
		exporting.value = true;
		// 准备列配置
		const columnList = Table.value.columns.map(col => {
			const item = {
				name: col.label,
				type: 'string'
			};

			// 处理不同类型的列
			if (col.dict) {
				item.type = 'dict';
				item.values = col.dict.map(d => d.label);
				item.codes = col.dict.map(d => d.value);
			}

			return item;
		});

		// 导出Excel
		await exportExcel(columnList, Table.value.data, '发函日志');
		ElMessage.success('导出成功');
	} catch (error) {
		console.error('导出失败', error);
		ElMessage.error('导出失败：' + (error.message || '未知错误'));
	} finally {
		exporting.value = false;
	}
}
// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
