<template>
	<cl-crud ref="Crud" v-loading="loading2" element-loading-text="发送中...">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<cl-column-custom :columns="dynamicColumns" name="xzh" />
			<cl-flex1 />
			<span class="cl-dateText">应收账款会计期选择：</span
			><monthMultiSelect
				v-model="selectedMonths"
				format="YYYY-MM"
				@submitBtn="searchMonths"
				@resetBtn="searchMonths"
			></monthMultiSelect>
			<!-- 关键字搜索 -->

			<cl-search-key field="选择筛选列" placeholder="请输入" :field-list="searchFieldsList" />
			<el-button :icon="Download" @click="exportExcel">导出</el-button>
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table
				ref="Table"
				v-loading="loading"
				element-loading-text="加载中..."
				:columns="dynamicColumns"
			>
				<template #column-询证函的电子原件="{ scope }">
					<cl-row type="flex" align="middle">
						<el-button type="text" @click="openPDF(scope.row.询证函的电子原件)"
							>查看</el-button
						>
					</cl-row>
				</template>
				<template #column-回函确认状态="{ scope }">
					<cl-row type="flex" align="middle">
						<el-tooltip :content="getStatusText(scope.row)" placement="top">
							<el-tag :type="getStatusType(scope.row)" effect="dark">
								{{ getStatusText(scope.row) }}
							</el-tag>
						</el-tooltip>

						<el-progress
							:text-inside="true"
							:color="getProgress(scope.row)"
							:percentage="getPercentage(scope.row)"
							:stroke-width="16"
							style="margin-top: 5px; width: 140px"
						>
							<template #default="{ percentage }">
								<span style="color: #77797d">{{ percentage }}%</span>
							</template>
						</el-progress>
					</cl-row>
				</template>
				<template #column-回函附件="{ scope }">
					<cl-row type="flex" align="middle">
						<el-button type="text" @click="openPDF(scope.row.回函附件)">查看</el-button>
					</cl-row>
				</template>
			</cl-table>
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->

		<el-dialog v-model="isPdfPreviewVisible" title="函证 预览" width="80%" top="2vh">
			<img
				v-if="isImageFile(pdfUrl)"
				:src="pdfUrl"
				style="width: 100%; height: auto"
				alt="预览图片"
			/>
			<iframe v-else :src="pdfUrl" style="width: 100%; height: 80vh"></iframe>
		</el-dialog>
	</cl-crud>
</template>

<script lang="ts" name="xzh" setup>
import { ref, onMounted, onActivated, watch, computed } from 'vue';
import { useCrud, useTable, useUpsert, useForm } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Download } from '@element-plus/icons-vue';
import { formatNumber } from '/@/modules/luru/utils/formatTableData';
import monthMultiSelect from '../components/monthMultiSelect.vue';
import dayjs from 'dayjs';
import { yearToMonth } from '../utils/xzh';
import { ExportExcel } from '/@/modules/luru/utils/exportExcel';
import { searchFieldsList } from '../utils/xzhFields';
import { sortAndHideColumns } from '../utils/xzh';
import { storage } from '/@/cool';

const { service, router } = useCool();

const lastMonth = dayjs().subtract(1, 'month').format('YYYY-MM');
const selectedMonths = ref(lastMonth);
const isPdfPreviewVisible = ref(false);
const loading = ref<boolean>(false);
const loading2 = ref<boolean>(false);
const pdfUrl = ref('');
// cl-upsert

const SearchKey = ref(null);
const exportCol: any = ref([]);
const dynamicColumns = ref<any[]>([]);

//type中的“-1”等数字对应腾讯文档中约定的状态类型
const items = ref([
	{
		label: '初始状态',
		value: 0,
		type: 'danger' //对应el-tag的type自带颜色
	},
	{
		label: '金额不一致，部分正式回函',
		value: 1,
		type: 'status-1' //对应css里配置的颜色
	},
	{
		label: '金额为0，正式回函',
		value: 2,
		type: 'status-2'
	},
	{
		label: '金额一致，其它方式回函',
		value: 3,
		type: 'status-3'
	},
	{
		label: '金额一致，正式回函',
		value: 4,
		type: 'success' //对应el-tag的type自带颜色
	},
	{
		label: '金额不一致，其它方式回函',
		value: 5,
		type: 'status-5'
	},
	{
		label: '金额为0，其它方式回函',
		value: 6,
		type: 'status-6'
	},
	{
		label: '金额一致，部分正式回函',
		value: 7,
		type: 'status-0'
	},
	{
		label: '金额不一致，正式回函',
		value: 8,
		type: 'status-8'
	},
	{
		label: '金额为0，部分正式回函',
		value: 9,
		type: 'status-9'
	},
	{
		label: '正式回函，ocr无法正确识别回复意见，待人工复核',
		value: 10,
		type: 'status-10'
	},
	{
		label: '回函客商与询证函不一致，待人工复核',
		value: 11,
		type: 'status-11'
	},
	{
		label: '回函总金额与询证函不一致，待人工复核',
		value: 12,
		type: 'status-12'
	}
]);
const columns = ref([
	{ label: '询证函单号', prop: '询证函单号', width: 120 },
	{ label: '应收账款会计期', prop: '应收账款会计期', width: 90 },
	{ label: '所属项目代码', prop: 'projectCode', width: 100 },
	{ label: '所属项目名称', prop: '所属项目名称', width: 200 },
	{ label: '客商名称代码', prop: '客商名称代码', width: 100 },
	{ label: '客商名称', prop: '客商名称', width: 200 },
	{
		label: '应收账款总金额',
		prop: '应收账款总金额',
		formatter: (row, column, value) => formatNumber(value, 2)
	},
	{
		label: '已确权总金额',
		prop: '已确权总金额',
		formatter: (row, column, value) => formatNumber(value, 2)
	},
	{
		label: '未回函总金额',
		prop: '未回函总金额',
		formatter: (row, column, value) => formatNumber(value, 2)
	},
	{
		label: '已回函总金额',
		prop: '已回函总金额',
		formatter: (row, column, value) => formatNumber(value, 2)
	},
	{ label: '责任人', prop: '责任人' },
	{ label: '责任人手机号码', prop: '责任人手机号码' },
	{ label: '责任人邮箱', prop: '责任人邮箱' },
	{ label: '客商联系人', prop: '客商联系人' },
	{ label: '客商联系人手机号码', prop: '客商联系人手机号码' },
	{ label: '客商联系人邮箱', prop: '客商联系人邮箱' },
	{
		label: '回函确认状态',
		prop: '回函确认状态',
		width: 220
	},
	{
		label: '发函状态',
		prop: '发函状态',
		dict: [
			{
				label: '未发函',
				value: 0,
				type: 'danger'
			},
			{
				label: '已发函',
				value: 1,
				type: 'success'
			},
			{
				label: '发函失败',
				value: -1,
				type: 'danger'
			}
		]
	},
	{ label: '询证函的电子原件', prop: '询证函的电子原件' },
	{ label: '预计发函时间', prop: '预计发函时间' },
	{ label: '实际发函时间', prop: '实际发函时间' },
	{ label: '回函时间', prop: '回函时间' },
	{ label: '回函人邮箱', prop: '回函人邮箱' },
	{ label: '回函附件', prop: '回函附件' }
]);

onActivated(async () => {
	await initializeColumns();
	//Crud.value?.refresh();
});

function getStatusText(row) {
	if (row.应收账款总金额 == 0) {
		return '金额一致，其它方式回函';
	}
	return items.value.find(item => item.value === row.回函确认状态)?.label || '';
}
function getStatusType(row) {
	if (row.应收账款总金额 == 0) {
		return 'status-3';
	}
	return items.value.find(item => item.value === row.回函确认状态)?.type || '';
}

function getPercentage(row) {
	let result = 0;
	if (row.应收账款总金额 == 0) {
		result = 100;
	} else {
		result = Number(((row.已确权总金额 * 100) / row.应收账款总金额).toFixed(1)) || 0;
	}
	return result;
}

function getProgress(row) {
	// if (row.回函确认状态 == 0) {
	// 	return "rgb(224, 84, 84)";
	// } else if (row.回函确认状态 == 1) {
	// 	return "#ffb074";
	// } else if (row.回函确认状态 == 2) {
	// 	return "#e4e25f";
	// } else if (row.回函确认状态 == 3) {
	// 	return "#99d4ef";
	// } else if (row.回函确认状态 == 4) {
	// 	return "#8cd569";
	// } else if (row.回函确认状态 == 5) {
	// 	return "#c9a8c8";
	// } else if (row.回函确认状态 == 6) {
	// 	return "#fdd5e7";
	// } else if (row.回函确认状态 == 7) {
	// 	return "#df6060";
	// }

	if ((Number(((row.已确权总金额 * 100) / row.应收账款总金额).toFixed(1)) || 0) < 25) {
		//console.log("1", Number(((row.已确权总金额 * 100) / row.应收账款总金额).toFixed(1)));
		return '#f56c6c';
	} else if (Number(((row.已确权总金额 * 100) / row.应收账款总金额).toFixed(1)) < 75) {
		//console.log("2", Number(((row.已确权总金额 * 100) / row.应收账款总金额).toFixed(1)));
		return '#ffbd7a';
	} else if (Number(((row.已确权总金额 * 100) / row.应收账款总金额).toFixed(1)) < 100) {
		//console.log("3", Number(((row.已确权总金额 * 100) / row.应收账款总金额).toFixed(1)));
		return '#b2ec97';
	} else {
		//console.log("4", Number(((row.已确权总金额 * 100) / row.应收账款总金额).toFixed(1)));
		return '#8cd569';
	}
}

function isImageFile(url: string) {
	const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
	return imageExtensions.some(ext => url.toLowerCase().includes(ext));
}

function openPDF(url: string) {
	console.log('附件', url);
	pdfUrl.value = '';
	if (url) {
		pdfUrl.value = url;
		isPdfPreviewVisible.value = true;
	} else {
		ElMessage.error('暂无附件');
	}
}

function getPDFUrl(row) {
	loading.value = true;
	service.yszk.xzh
		.sendFilePreview({ xzh_no: row.询证函单号 })
		.then(res => {
			const uint8Array = new Uint8Array(res.buffer.data);
			const blob = new Blob([uint8Array], { type: 'application/pdf' });
			const url = URL.createObjectURL(blob);
			openPDF(url);
			loading.value = false;
		})
		.catch(error => {
			loading.value = false;
			ElMessage.error(`获取PDF失败，${error.message}`);
			console.error('获取PDF错误:', error);
		})
		.finally(() => {
			loading.value = false;
		});
	//return url;
}
function sendXzh(row) {
	ElMessageBox.confirm('确认重新发函？', '提示', {
		type: 'warning'
		//	confirmButtonText: '确认',
	}).then(() => {
		loading2.value = true;
		service.yszk.xzh
			.sendXzh({ xzh_no: row.询证函单号 })
			.then(res => {
				ElMessage.success('发送成功');
				loading2.value = false;
			})
			.catch(error => {
				ElMessage.error(`发送失败，${error.message}`);
				loading2.value = false;
				console.error('发送错误:', error);
			})
			.finally(() => {
				loading2.value = false;
				Crud.value?.refresh();
			});
	});
}

async function exportExcel() {
	const data = await service.yszk.xzh.list();
	const columns = Table.value?.columns.map(item => item.prop);
	ExportExcel(data, columns, '询证函');
}

const newColumns = ref([]);
async function getColumns() {
	try {
		console.log('获取getColumns', Table.value?.columns);

		const res = await service.cloud.db.page({
			tableName: 'func_xzh_record',
			page: 1
		});

		if (res.list?.[0]?.colInfo) {
			const newColumns = sortAndHideColumns(columns.value, res.list[0].colInfo);
			console.log('newColumns', newColumns);
			return [
				...newColumns,
				{
					type: 'op',
					width: 160,
					buttons({ scope }) {
						//const detail = ;
						if (scope.row.询证函的电子原件) {
							return [
								{
									label: '详情（手动确权）',
									type: 'success',
									onClick({ scope }) {
										console.log('row', scope.row);
										router.push({
											path: '/xzhDetail',
											state: {
												客商名称代码: scope.row.客商名称代码,
												projectCode: scope.row.projectCode,
												询证函单号: scope.row.询证函单号,
												所属项目名称: scope.row.所属项目名称,
												责任人: scope.row.责任人,
												客商名称: scope.row.客商名称,
												searchFields: {
													询证函单号: scope.row.询证函单号
												}
											}
										});
									}
								},
								{
									label: '重新发函',
									onClick({ scope }) {
										console.log('row', scope.row);
										sendXzh(scope.row);
									}
								}
							];
						} else {
							return [
								{
									label: '预览',
									onClick({ scope }) {
										console.log('row', scope.row);
										getPDFUrl(scope.row);
									}
								},
								{
									label: '详情（手动确权）',
									type: 'success',
									onClick({ scope }) {
										console.log(
											'row',
											scope.row,
											'Table',
											Table?.value?.columns
										);
										router.push({
											path: '/xzhDetail',
											state: {
												客商名称代码: scope.row.客商名称代码,
												projectCode: scope.row.projectCode,
												询证函单号: scope.row.询证函单号,
												所属项目名称: scope.row.所属项目名称,
												责任人: scope.row.责任人,
												客商名称: scope.row.客商名称,
												searchFields: {
													询证函单号: scope.row.询证函单号
												}
												// selectedMonths.value
												// 	? {
												// 			所属项目代码: scope.row.projectCode,
												// 			客商名称代码: scope.row.客商名称代码
												// 		}
											}
										});
									}
								},
								{
									label: '重新发函',
									onClick({ scope }) {
										console.log('row', scope.row);
										sendXzh(scope.row);
									}
								}
							];
						}
					}
				}
			];
		}
		return [];
	} catch (error) {
		console.error('获取列配置失败:', error);
		return [];
	}
}

// cl-table
const Table = useTable({
	contextMenu: []
});

// 初始化和更新列
async function initializeColumns() {
	//loading.value = true;
	try {
		const columns = await getColumns();
		dynamicColumns.value = columns;
		// 更新 Table 的列配置
		if (Table.value) {
			Table.value.columns = columns;
		}
		//	loading.value = false;
	} catch (error) {
		ElMessage.error('初始化列失败:' + error.message);
		console.error('初始化列失败:', error);
	} finally {
		//	loading.value = false;
	}
}

// 监听动态列变化
watch(
	dynamicColumns,
	newColumns => {
		if (Table.value) {
			Table.value.columns = newColumns;
		}
	},
	{ deep: true }
);

function searchMonths() {
	console.log('selectedMonths.value', selectedMonths.value);
	const months = yearToMonth(selectedMonths.value);
	console.log('months', months);
	Crud.value?.refresh({
		应收账款会计期: selectedMonths.value ? months : undefined
	});
}

// cl-crud
const Crud = useCrud(
	{
		service: service.yszk.xzh,
		onRefresh(params, { next }) {
			console.log('params', params);
			console.log('Table', Table?.value?.columns);

			if (params.发函状态) {
				console.log('发函状态');
				if (params.发函状态 === '已发函') {
					console.log('依发函');
					params.发函状态 = 1;
				} else if (params.发函状态 === '未发函') {
					params.发函状态 = 0;
				} else if (params.发函状态 === '发函失败') {
					params.发函状态 = 2;
				}
			}
			// 处理回函确认状态
			if (params.回函确认状态) {
				if (params.回函确认状态 === '已确认') {
					params.回函确认状态 = 1;
				} else if (params.回函确认状态 === '未确认') {
					params.回函确认状态 = 0;
				}
			}
			next({
				...params
			});
		}
	},
	app => {
		app.refresh({
			应收账款会计期: selectedMonths.value ? yearToMonth(selectedMonths.value) : undefined
		});
	}
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>
<style>
.cl-search-key__select.el-select {
	width: 130px !important;
}
.cl-dateText {
	font-size: 14px;
	color: #606266;
	display: flex;
	align-items: center;
}

/* 添加自定义状态样式 */
.el-tag.el-tag--status-6 {
	--el-tag-bg-color: #f9b7d2;
	--el-tag-border-color: #fdd5e7;
	--el-tag-text-color: #ffffff;
}

.el-tag.el-tag--status-5 {
	--el-tag-bg-color: rgb(140, 74, 165);
	--el-tag-border-color: rgb(140, 74, 165);
	--el-tag-text-color: #ffffff;
}

.el-tag.el-tag--status-4 {
	--el-tag-bg-color: #f198a5;
	--el-tag-border-color: #e57373;
	--el-tag-text-color: #ffffff;
}

.el-tag.el-tag--status-3 {
	--el-tag-bg-color: rgb(138, 151, 123);
	--el-tag-border-color: rgb(138, 151, 123);
	--el-tag-text-color: #ffffff;
}

.el-tag.el-tag--status-2 {
	--el-tag-bg-color: rgb(244, 208, 0);
	--el-tag-border-color: rgb(244, 208, 0);
	--el-tag-text-color: #ffffff;
}

.el-tag.el-tag--status-1 {
	--el-tag-bg-color: rgb(249, 187, 18);
	--el-tag-border-color: rgb(249, 187, 18);
	--el-tag-text-color: #fff;
}

.el-tag.el-tag--status-0 {
	--el-tag-bg-color: #ca4646;
	--el-tag-border-color: #df6060;
	--el-tag-text-color: #ffffff;
}
.el-tag.el-tag--status-8 {
	--el-tag-bg-color: rgb(229, 131, 8);
	--el-tag-border-color: rgb(229, 131, 8);
	--el-tag-text-color: #ffffff;
}
.el-tag.el-tag--status-9 {
	--el-tag-bg-color: rgb(137, 190, 178);
	--el-tag-border-color: rgb(137, 190, 178);
	--el-tag-text-color: #ffffff;
}
.el-tag.el-tag--status-10 {
	--el-tag-bg-color: rgb(128, 21, 0);
	--el-tag-border-color: rgb(128, 21, 0);
	--el-tag-text-color: #ffffff;
}
.el-tag.el-tag--status-11 {
	--el-tag-bg-color: rgb(14, 45, 115);
	--el-tag-border-color: rgb(0, 38, 128);
	--el-tag-text-color: #ffffff;
}
.el-tag.el-tag--status-12 {
	--el-tag-bg-color: rgb(13, 107, 96);
	--el-tag-border-color: rgb(13, 107, 96);
	--el-tag-text-color: #ffffff;
}
</style>
