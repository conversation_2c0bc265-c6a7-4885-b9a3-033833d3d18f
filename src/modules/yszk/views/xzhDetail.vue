<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />

			<cl-column-custom :columns="dynamicColumns" name="xzhDetail" />

			<cl-flex1 />
			<span class="cl-dateText">账期选择：</span>
			<monthMultiSelect
				v-model="selectedMonths"
				format="YYYY-MM"
				@submitBtn="searchMonths"
				@resetBtn="searchMonths"
			></monthMultiSelect>
			<!-- 关键字搜索 -->
			<cl-search-key />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" :columns="dynamicColumns">
				<template #column-合同函证影像="{ scope }">
					<cl-row type="flex" align="middle">
						<el-button type="text" @click="openPDF(scope.row.合同函证影像)"
							>预览</el-button
						>
					</cl-row>
				</template>
			</cl-table>
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>
		<el-dialog v-model="isPdfPreviewVisible" title="函证 预览" width="80%" top="2vh">
			<img
				v-if="isImageFile(pdfUrl)"
				:src="pdfUrl"
				style="width: 100%; height: auto"
				alt="预览图片"
			/>
			<iframe v-else :src="pdfUrl" style="width: 100%; height: 80vh"></iframe>
		</el-dialog>
		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
		<viewer :ref="setRefs('viewer')" />
	</cl-crud>
</template>

<script lang="ts" name="xzhDetail" setup>
import { ref, onMounted, onActivated, watch } from 'vue';
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatNumber } from '/@/modules/luru/utils/formatTableData';
import monthMultiSelect from '../components/monthMultiSelect.vue';
import { yearToMonth } from '../utils/xzh';
import { useBase } from '/$/base';
import { isDev } from '/@/config';
import { sortAndHideColumns } from '../utils/xzh';
import viewer from '/@/plugins/upload/components/upload-item/viewer.vue';
import { getType } from '/@/plugins/upload/utils';

const { service, router, setRefs, refs } = useCool();
const contractNameList = ref<any[]>([]);
const isPdfPreviewVisible = ref(false);
const pdfUrl = ref('');
const searchField = ref();
const selectedMonths = ref();
const projectCode = ref();
const ksdm = ref();
const rowInfo = ref();
const row = ref();
const { user } = useBase();
const isEditVisible = ref(false);
const dynamicColumns = ref<any[]>([]);
const columns = ref([
	{ label: '询证函单号', prop: '询证函单号', width: 100 },
	{ label: '所属项目代码', prop: 'projectCode', width: 100 },
	{ label: '所属项目名称', prop: '所属项目名称', width: 200 },
	{ label: '科目', prop: '科目' },
	{ label: '客商名称代码', prop: '客商名称代码', width: 100 },
	{ label: '客商名称', prop: '客商名称', width: 200 },
	{ label: '账期', prop: '账期' },
	{ label: '账龄', prop: '账龄' },
	{ prop: '凭证日期', label: '凭证日期', width: 150 },
	{ prop: '凭证类别', label: '凭证类别', width: 150 },
	{ prop: '凭证号', label: '凭证号', width: 100 },
	{
		label: '确权状态',
		prop: '确权状态',
		hidden: false,
		index: 2,
		orderNum: 6,
		minWidth: 100,
		dict: [
			{
				label: '已确权',
				value: 1,
				type: 'success'
			},
			{
				label: '未确权',
				value: 0,
				type: 'danger'
			}
		]
	},
	{ prop: '确权时间', label: '确权时间', width: 150 },
	{ label: '合同代码', prop: '合同代码', width: 100 },
	{ label: '合同名称', prop: '合同名称', width: 200 },
	{ label: '责任人', prop: '责任人' },

	{
		label: '待确权金额',
		prop: '待确权金额',
		formatter: (row, column, value) => formatNumber(value, 2)
	},
	{
		label: '已确权金额',
		prop: '已确权金额',
		formatter: (row, column, value) => formatNumber(value, 2)
	},
	{
		prop: '合同函证影像',
		label: '合同函证影像',
		minWidth: 90
	},
	{
		label: '回函确认状态',
		prop: '回函确认状态',
		minWidth: 120,
		dict: [
			{ label: '其它方式回函', value: 1, type: 'primary' },
			{ label: '正式回函', value: 2, type: 'success' },
			{ label: '未回函', value: 0, type: 'danger' }
		]
	},
	{ label: '确权人', prop: '确权人' },
	{ label: '备注', prop: '备注' },
	{
		type: 'op',
		width: 160,
		buttons({ scope }) {
			return ['edit'];
		}
	}
]);

onActivated(async () => {
	await initializeColumns();
	if (router.options.history.state.询证函单号) {
		console.log('onActivated', router.options.history.state);
		searchField.value = router.options.history.state.searchFields;
		projectCode.value = router.options.history.state.projectCode;
		ksdm.value = router.options.history.state.客商名称代码;
		console.log('projectCode.value', projectCode.value);
		console.log('ksdm.value', ksdm.value);
		refresh();
		console.log('row', searchField.value, router.options.history.state);
		service.dict.info.data({ types: ['合同字典'] }).then(res => {
			console.log('res', res);
			contractNameList.value = res['合同字典'].map(item => {
				return { label: item.value + '&' + item.name, value: item.value + '&' + item.name };
			});
		});
	}
	//refresh();
});

// 监听动态列变化
watch(
	dynamicColumns,
	newColumns => {
		if (Table.value) {
			Table.value.columns = newColumns;
		}
	},
	{ deep: true }
);

async function getColumns() {
	try {
		const res = await service.cloud.db.page({
			tableName: 'func_xzh_detail',
			page: 1
		});

		if (res.list?.[0]?.colInfo) {
			return sortAndHideColumns(columns.value, res.list[0].colInfo);
		}
		return [];
	} catch (error) {
		console.error('获取列配置失败:', error);
		return [];
	}
}

function isImageFile(url: string) {
	const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
	return imageExtensions.some(ext => url.toLowerCase().includes(ext));
}
function openPDF(url: string) {
	console.log('dakai');
	if (url) {
		//window.open(url, "_blank");
		const type = getType(url);
		console.log('type', type);
		refs.viewer.open({ url, type });
		console.log('打开');
		pdfUrl.value = url;
		//isPdfPreviewVisible.value = true;
	} else {
		ElMessage.error('暂无附件');
	}
}

function searchMonths() {
	console.log('selectedMonths.value', selectedMonths.value);
	const months = yearToMonth(selectedMonths.value);
	console.log('months', months);
	Crud.value?.refresh();
}

async function initializeColumns() {
	//loading.value = true;
	try {
		const columns = await getColumns();
		dynamicColumns.value = columns;
		// 更新 Table 的列配置
		if (Table.value) {
			console.log('添加操作columns', columns);
			Table.value.columns = [...columns];
			console.log('添加成功', Table.value.columns);
		}
		//	loading.value = false;
	} catch (error) {
		console.error('初始化列失败:', error);
	} finally {
		//	loading.value = false;
	}
}

function saveEdit() {
	console.log('row', row.value);
	service.yszk.xzhDetail
		.update({ id: row.value.id, 待确权金额: row.value.待确权金额 })
		.then(res => {
			console.log('res', res);
			ElMessage.success('保存成功');
			isEditVisible.value = false;
			refresh();
		})
		.catch(err => {
			ElMessage.error('保存失败,请联系管理员');
		});
}

// cl-upsert
const Upsert = useUpsert({
	async onInfo(data, { next, done }) {
		console.log('data', data);
		rowInfo.value = `询证函回函_合同函证影像_${data.询证函单号}_${data.id}&${data.账期}`;
		if (isDev) {
			rowInfo.value = `询证函回函_合同函证影像_${data.询证函单号}_${data.id}_dev&${data.账期}`;
		}
		const newData = await next({
			...data
		});
		if (newData.回函确认状态 == 0) {
			newData.回函确认状态 = 1;
		}

		done(newData);
		//console.log("rowInfo.value", rowInfo.value);
		//next(data);
	},
	props: {
		labelWidth: '125px'
	},
	items: [
		() => {
			return {
				label: '待确权金额',
				prop: '待确权金额',
				component: {
					name: 'el-input-number',
					props: {
						precision: 2,
						disabled: Upsert.value?.mode == 'update'
					}
				}
			};
		},
		{
			label: '已确权金额',
			prop: '已确权金额',
			component: {
				name: 'el-input-number',
				props: {
					precision: 2
				}
			}
		},
		() => {
			return {
				label: '合同函证影像',
				prop: '合同函证影像',

				required: true,
				component: {
					name: 'cl-upload',
					props: () => ({
						// 将 props 改为函数形式
						type: 'file',
						accept: '.pdf,.png,.jpg,.jpeg',
						text: '上传影像',
						fileName: rowInfo.value
					})
				}
			};
		},
		{
			label: '回函确认状态',
			prop: '回函确认状态',
			component: {
				name: 'el-select',
				props: {
					required: true
				},
				options: [
					{ label: '正式回函', value: 2 },
					{ label: '其它方式回函', value: 1 }
				]
			}
		},
		{
			label: '备注',
			prop: '备注',
			component: {
				name: 'el-input'
			}
		}
	],

	op: {
		saveButtonText: '仅保存',
		buttons: [
			'close',
			{
				label: '保存并确权',
				type: 'primary',
				onClick: () => {
					console.log('保存并确权', Upsert.value?.form);
					if (Upsert.value?.form) {
						if (!Upsert.value?.form.合同函证影像) {
							ElMessage.error('请上传合同函证影像');
							return;
						} else {
							if (
								Number(Upsert.value.form.已确权金额) >
								Number(Upsert.value.form.待确权金额)
							) {
								ElMessage.error('已确权金额不能大于待确权金额');
								return;
							}
							Upsert.value.form.确权状态 = 1;
							Upsert.value.form.确权时间 = new Date();
							Upsert.value.submit({
								...Upsert.value.form,
								确权人: user.info?.username
							});
						}
					}
				}
			}
		]
	},
	async onSubmit(data, { done, close, next }) {
		console.log('data', data);

		next({
			...data
		});
	}
});

// cl-table
const Table = useTable({
	contextMenu: []
});

// cl-crud
const Crud = useCrud(
	{
		service: service.yszk.xzhDetail,
		dict: {
			label: {
				op: '操作',
				update: '确权'
			}
		},
		onRefresh(params, { next }) {
			console.log('刷新', Table.value?.columns);
			console.log(
				'params',
				params,
				selectedMonths.value,
				selectedMonths.value
					? { projectCode: projectCode.value, 客商名称代码: ksdm.value }
					: searchField.value
			);
			// 默认使用 next(params)，也可以自己对数据进行处理
			params = selectedMonths.value
				? {
						projectCode: projectCode.value,
						客商名称代码: ksdm.value,
						账期: yearToMonth(selectedMonths.value),
						page: params.page,
						size: params.size
					}
				: { ...searchField.value, page: params.page, size: params.size };
			next({
				...params
			});
		}
	},

	app => {
		app.refresh(
			selectedMonths.value
				? { projectCode: projectCode.value, 客商名称代码: ksdm.value }
				: searchField.value
		);
	}
);

// 刷新
function refresh() {
	Crud.value?.refresh();
}
</script>
<style>
.cl-dateText {
	font-size: 14px;
	color: #606266;
	display: flex;
	align-items: center;
}
</style>
