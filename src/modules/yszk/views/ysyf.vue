<template>
	<div class="hz-container">
		<el-container>
			<!-- 侧边栏 -->
			<el-aside width="200px" class="sidebar">
				<el-menu :default-active="activeMenu" class="menu" @select="handleMenuSelect">
					<el-menu-item index="应收应付抵账" class="menu-item">
						<span>应收应付抵账 </span>
					</el-menu-item>
				</el-menu>
			</el-aside>

			<!-- 主内容区 -->
			<el-main class="main-content">
				<!-- 使用keep-alive包裹动态组件 -->
				<keep-alive>
					<component :is="currentComponent" />
				</keep-alive>
			</el-main>
		</el-container>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, defineComponent, h, watch } from 'vue';
import { Setting } from '@element-plus/icons-vue';
import IframeView from '../components/iframe-view.vue';
import { useIframeUrls } from '../hooks/useIframeUrls';
import { useBase } from '/$/base';

const { user } = useBase();
// 当前激活的菜单项
const activeMenu = ref('应收应付抵账');

// 使用共享的 iframe URLs hook
const { iframeUrls, loading, error, preloadIframe } = useIframeUrls('ysyf');

// 当前显示的组件
const currentComponent = computed(() => {
	const url = iframeUrls.value[activeMenu.value] + '&lurutoken=' + user.token;
	return defineComponent({
		setup() {
			return () => (url ? h(IframeView, { url }) : null);
		}
	});
});

// 菜单选择处理
function handleMenuSelect(index: string) {
	activeMenu.value = index;
}
</script>

<style lang="scss" scoped>
.hz-container {
	height: 100%;
	background-color: #f5f7fa;

	.el-container {
		height: 100%;
	}

	.sidebar {
		background-color: #fff;
		border-right: 1px solid #e6e6e6;

		.menu {
			border-right: none;

			.menu-item {
				height: auto;
				line-height: 1.5;
				min-height: 56px;
				padding: 10px 20px;
				white-space: normal;

				.menu-text {
					div {
						line-height: 1.4;
						margin: 4px 0;
					}
				}

				:deep(.el-menu-item-content) {
					white-space: normal;
					height: auto;
					line-height: 1.5;
				}
			}
		}
	}

	.main-content {
		padding: 10px;
		height: 100%;
		overflow: hidden;

		:deep(.xzh-crud) {
			height: 100%;
		}
	}
}
</style>
