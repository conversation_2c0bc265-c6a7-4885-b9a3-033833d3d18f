<template>
	<el-row>
		<cl-flex1 />
		<el-select
			v-model="selected"
			filterable
			clearable
			placeholder="请选择组织"
			style="height: 24px; width: 400px"
		>
			<el-option
				v-for="item in org.listOrg"
				:key="item.name"
				:label="item.name"
				:value="item.name"
			/>
		</el-select>
		<cl-flex1 />
	</el-row>
</template>

<script lang="ts" setup>
import { useCool } from '/@/cool';
import { useBase } from '/$/base';
import { ref, watch } from 'vue';

const selected = ref('');
const { service } = useCool();
const { org } = useBase();

const treeOptions = ref<TreeNode[]>([]);

let xiangmu: any;

interface TreeNode {
	id: number;
	name: string;
	value: string;
	children: TreeNode[];
}

// 递归获取树形数据
async function getChildNodes(parentId: number | undefined, childrens): Promise<TreeNode[]> {
	const result: TreeNode[] = [];
	for (const child of childrens) {
		if (child.parentId == parentId) {
			result.push({
				id: child.id,
				name: child.name,
				value: child.value,
				children: await getChildNodes(child.id, childrens)
			});
		}
	}
	return result;
}

// 初始化树形数据
async function initTreeData(childrens, parentId_list) {
	// 直接获取根节点的子节点作为顶层节点
	let allChildren: TreeNode[] = [];
	for (const root of parentId_list) {
		const children = await getChildNodes(root.id, childrens);
		allChildren = [...allChildren, ...children];
	}
	treeOptions.value = allChildren;
}

watch(selected, async (newselected, oldselected) => {
	org.setOrg(selected.value);

	const dictList = await service.dict.type.list();
	const xmb = dictList.filter(item => item.name === '项目表');
	const xmb_id: number = xmb[0]?.id as number;
	const result = await service.dict.info.list({ typeId: xmb_id });

	const alldepList = await service.dict.info.getAllChildren({ typeId: xmb_id, status: 1 });

	for (let i = 0; i < result.length; i++) {
		const row = result[i];
		if (row.name == org.name) {
			xiangmu = row.value;
		}
	}
	org.setCode(xiangmu);

	const parentId_list = result.filter(item => item.name === org.name);

	await initTreeData(alldepList[xmb_id], parentId_list);

	org.listpart = [];
	org.setListpart(
		treeOptions.value.map((a: any) => {
			return { id: a.id, name: a.name, value: a.value, children: a.children };
		})
	);
});
</script>

<style lang="scss" scoped></style>
