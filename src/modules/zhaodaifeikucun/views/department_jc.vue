<template>
	<cl-crud ref="Crud">
		<el-row>
			<cl-refresh-btn />
			<cl-add-btn />
			<cl-multi-delete-btn />
			<cl-flex1 />
			<cl-search-key />
		</el-row>

		<el-row>
			<cl-table
				ref="Table"
				:default-sort="{
					prop: 'createTime',
					order: 'descending'
				}"
			/>
		</el-row>

		<el-row>
			<cl-flex1 />
			<cl-pagination />
		</el-row>

		<cl-upsert ref="Upsert"></cl-upsert>
	</cl-crud>
</template>

<script lang="ts" setup>
import { useTable, useUpsert, useCrud } from '@cool-vue/crud';
import { useCool } from '/@/cool';

const { service } = useCool();

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.zhongtie.department_jc
	},
	app => {
		app.refresh();
	}
);

// cl-upsert 配置
const Upsert = useUpsert({
	dialog: {
		width: '500px'
	},

	items: [
		{
			prop: 'xm_name',
			label: '项目名称',
			// span: 10,
			required: true,
			component: {
				name: 'el-input'
			}
		},
		{
			prop: 'xm_code',
			label: '项目代码',
			// span: 24,
			required: true,
			component: {
				name: 'el-input'
			}
		},
		{
			prop: 'part_name',
			label: '部门名称',
			// span: 10,
			required: true,
			component: {
				name: 'el-input'
			}
		},
		{
			prop: 'part_code',
			label: '部门代码',
			// span: 24,
			required: true,
			component: {
				name: 'el-input'
			}
		},
		{
			prop: 'part_name_jc',
			label: '部门简称',
			// span: 24,
			required: true,
			component: {
				name: 'el-input'
			}
		}
	]
});

// cl-table 配置
const Table = useTable({
	columns: [
		{
			type: 'selection',
			width: 60
		},
		{
			prop: 'xm_name',
			label: '项目名称',
			minWidth: 220
		},
		{
			prop: 'xm_code',
			label: '项目代码',
			minWidth: 220
		},
		{
			prop: 'part_name',
			label: '部门名称',
			minWidth: 120
		},
		{
			prop: 'part_code',
			label: '部门代码',
			minWidth: 220
		},
		{
			prop: 'part_name_jc',
			label: '部门名称(简称)',
			minWidth: 120
		},
		{
			label: '操作',
			type: 'op',
			buttons: ['edit', 'delete']
		}
	]
});
</script>
