<template>
	<cl-crud ref="Crud">
		<el-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
		</el-row>
		<el-row>
			<cl-table ref="Table">
				<template #slot-btn1="{ scope }">
					<el-button
						type="primary"
						@click="Check(scope.row)"
						text
						bg
						style="margin-bottom: 10px; width: 140px"
						>数据审核
					</el-button>
				</template>
			</cl-table>
		</el-row>
		<el-row>
			<cl-flex1 />
			<!-- 分页展示 -->
			<!-- <cl-pagination /> -->
		</el-row>
	</cl-crud>
</template>

<script lang="ts" setup>
import { useCrud, useTable } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { useBase } from '/$/base';
const { org, user } = useBase();
const { service, router } = useCool();

const username = user.info?.username;

// cl-table 配置
const Table = useTable({
	columns: [
		{ label: '入库单号', prop: 'danhao' },
		{ label: '出库单号', prop: 'ck_danhao' },
		{ label: '项目', prop: 'xiangmu' },
		{ label: '保管部门', prop: 'baoguan_part' },
		{ label: '领用部门', prop: 'lingyong_part' },
		{ label: '出库时间', prop: 'time_chuku' },
		{
			// label: "数据查看",
			type: 'op',
			buttons: ['slot-btn1']
		}
	]
});

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.zhongtie.zhongtie_kucun,
		async onRefresh(params, { next, done, render }) {
			// 获取原始的数据列表
			if (username == 'admin') {
				const originalData = await next({
					...params,
					states: '出库审核',
					xiangmu: org.code
				});
				// 使用 Set 和 map 方法提取不重复的属性值
				const uniqueValues = [
					...new Set(originalData.list.map((item: { danhao: string }) => item.danhao))
				];
				// 构建去重后的展示数据
				const deduplicatedData = uniqueValues.map(value => {
					const matchingItem = originalData.list.find(
						(item: { danhao: string }) => item.danhao === value
					);
					return matchingItem;
				});
				// 调用 render 方法更新 cl-crud 的展示数据
				render(deduplicatedData);
			} else {
				const originalData = await next({
					...params,
					states: '出库审核',
					xiangmu: org.code,
					checker: username
				});
				// 使用 Set 和 map 方法提取不重复的属性值
				const uniqueValues = [
					...new Set(originalData.list.map((item: { danhao: string }) => item.danhao))
				];
				// 构建去重后的展示数据
				const deduplicatedData = uniqueValues.map(value => {
					const matchingItem = originalData.list.find(
						(item: { danhao: string }) => item.danhao === value
					);
					return matchingItem;
				});
				// 调用 render 方法更新 cl-crud 的展示数据
				render(deduplicatedData);
			}
		}
	},
	app => {
		app.refresh({});
	}
);

const Check = (data: any) => {
	// console.log("得到", data);
	router.push({
		path: '/zdf/check/detail/CK',
		query: {
			ck_danhao: data.ck_danhao,
			danhao: data.danhao,
			huowu_name: data.huowu_name
		}
	});
};
</script>
