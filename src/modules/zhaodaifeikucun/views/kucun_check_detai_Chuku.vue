<template>
	<cl-crud ref="Crud">
		<el-button type="primary" @click="Pass()" text bg style="margin-bottom: 10px; width: 100px"
			>全部通过
		</el-button>
		<el-button type="danger" @click="Reject()" text bg style="margin-bottom: 10px; width: 100px"
			>全部驳回
		</el-button>
		<el-row>
			<!-- 数据表格 -->
			<cl-table ref="Table">
				<template #slot-btn1="{ scope }">
					<el-button type="primary" @click="pass(scope.row)" text bg>通过 </el-button>
				</template>
				<template #slot-btn2="{ scope }">
					<el-button type="primary" @click="reject(scope.row)" text bg>驳回 </el-button>
				</template>
			</cl-table>
			<el-dialog v-model="dialogFormVisible" title="请填写驳回原因" draggable>
				<el-form :model="form" center>
					<el-form-item :label-width="140">
						<el-input size="large" v-model="form.reject_reason" style="width: 80%" />
					</el-form-item>
				</el-form>
				<template #footer>
					<span class="dialog-footer">
						<el-button type="primary" @click="submit">提交</el-button>
						<el-button type="primary" @click="dialogFormVisible = false">
							取消
						</el-button>
					</span>
				</template>
			</el-dialog>
			<el-dialog v-model="dialogFormVisible_all" title="请填写驳回原因" draggable>
				<el-form :model="form" center>
					<el-form-item :label-width="140">
						<el-input size="large" v-model="form.all_reason" style="width: 80%" />
					</el-form-item>
				</el-form>
				<template #footer>
					<span class="dialog-footer">
						<el-button type="primary" @click="submit_all">提交</el-button>
						<el-button type="primary" @click="dialogFormVisible_all = false">
							取消
						</el-button>
					</span>
				</template>
			</el-dialog>
		</el-row>

		<el-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</el-row>
	</cl-crud>
</template>

<script lang="ts" setup>
import { useCrud, useTable } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useBase } from "/$/base";
import moment from "moment";
import { ref, reactive } from "vue";
import * as XLSX from "xlsx";
const { org } = useBase();
const { service, route, router } = useCool();

const rowId = ref();
const dialogFormVisible = ref(false);
const dialogFormVisible_all = ref(false);
const form = reactive({
	reject_reason: "",
	all_reason: ""
});

// cl-table 配置
const Table = useTable({
	columns: [
		{ label: "入库单号", prop: "danhao", minWidth: 160 },
		{ label: "出库单号", prop: "ck_danhao", minWidth: 160 },
		{ label: "项目", prop: "xiangmu", minWidth: 200 },
		{ label: "保管部门", prop: "baoguan_part", minWidth: 100 },
		{ label: "领用部门", prop: "lingyong_part", minWidth: 100 },
		{ label: "货物名称", prop: "huowu_name", minWidth: 250 },
		{ label: "货物单位", prop: "huowu_danwei", minWidth: 90 },
		{ label: "出库时间", prop: "time_chuku", minWidth: 130 },
		{ label: "出库单价", prop: "chuku_danjia", minWidth: 90 },
		{ label: "出库数量", prop: "chuku_num", minWidth: 90 },
		{ label: "出库金额", prop: "chuku_money", minWidth: 120 },
		{ label: "入库人", prop: "checker", minWidth: 120 },
		{ label: "出库人", prop: "ck_person", minWidth: 120 },
		{
			type: "op",
			buttons: ["slot-btn1", "slot-btn2"]
		}
	]
});

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.zhongtie.zhongtie_kucun,
		async onRefresh(params, { next, done, render }) {
			// 1 默认调用
			const { list } = await next({
				...params,
				danhao: route.query.danhao,
				states: "出库审核",
				xiangmu: org.code
			});
			// console.log(list, "传来的params", route.query.danhao);
		}
	},
	app => {
		if (route.query.danhao) {
			app.refresh();
		}
	}
);

const downloadFileClick = async (id: any) => {
	const arr: any[] = [];
	const excel_name = "出库单明细.xlsx";
	arr[0] = [
		"入库单号",
		"出库单号",
		"项目",
		"保管部门",
		"领用部门",
		"货物名称",
		"货物单位",
		"出库时间",
		"出库单价",
		"出库数量",
		"出库金额",
		"入库人",
		"出库人"
	];
	if (id == null) {
		const result = await service.zhongtie.zhongtie_kucun.list({
			danhao: route.query.danhao,
			xiangmu: org.code,
			states: "出库审核"
		});
		result.forEach(item => {
			arr.push([
				item.danhao,
				item.ck_danhao,
				item.xiangmu,
				item.baoguan_part,
				item.lingyong_part,
				item.huowu_name,
				item.huowu_danwei,
				item.time_chuku,
				item.chuku_danjia,
				item.chuku_num,
				item.chuku_money,
				item.checker,
				item.ck_person
			]);
		});
	} else {
		const result = await service.zhongtie.zhongtie_kucun.list({ id: id, states: "出库审核" });
		result.forEach(item => {
			arr.push([
				item.danhao,
				item.ck_danhao,
				item.xiangmu,
				item.baoguan_part,
				item.lingyong_part,
				item.huowu_name,
				item.huowu_danwei,
				item.time_chuku,
				item.chuku_danjia,
				item.chuku_num,
				item.chuku_money,
				item.checker,
				item.ck_person
			]);
		});
	}
	//创建一个新的工作表
	const workBook = XLSX.utils.book_new();
	//将表格转换为工作表
	const worksheet = XLSX.utils.aoa_to_sheet(arr);
	//将工作表添加到workBook中
	XLSX.utils.book_append_sheet(workBook, worksheet, "Sheet1");
	XLSX.writeFile(workBook, excel_name);
};

// 审核全部通过
async function Pass() {
	const list_all = await service.zhongtie.zhongtie_kucun.list({
		danhao: route.query.danhao,
		xiangmu: org.code,
		states: "出库审核"
	});
	// 定义结存金额、结存单价
	let result_residue_money = 0;
	let result_residue_danjia = 0;

	ElMessageBox.confirm("确认审核通过？", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning"
	}).then(async () => {
		downloadFileClick(null);
		for (let j = 0; j < list_all.length; j++) {
			const item = list_all[j];
			// 添加验证
			if (item.chuku_num === undefined || item.chuku_num === null) {
				ElMessage({
					type: "error",
					message: "出库数量不能为空!"
				});
				continue;
			}
			let result_residue_num = -1;

			// 筛选库存信息
			const list_kucun = await service.zhongtie.zhongtie_kucun_details.list({
				danhao: route.query.danhao,
				xiangmu: org.code,
				huowu_name: route.query.huowu_name
			});
			// 单号货物库存为0进行出库红冲
			if (list_kucun.length == 0) {
				if (item.chuku_money === undefined || item.chuku_money === null) {
					ElMessage({
						type: "error",
						message: "出库金额不能为空!"
					});
					continue;
				}
				await service.zhongtie.zhongtie_kucun_details.add({
					danhao: item.danhao,
					xiangmu: item.xiangmu,
					baoguan_part: item.baoguan_part,
					huowu_name: item.huowu_name,
					huowu_danwei: item.huowu_danwei,
					residue_num: item.residue_num,
					residue_danjia: item.chuku_danjia,
					residue_money: -item.chuku_money,
					finally_time: item.time_chuku
				});
				await service.zhongtie.zhongtie_kucun.update({
					id: item.id,
					states: "审核通过",
					reject_reason: "红冲数据"
				});
			}
			// 正常出库
			else {
				for (let i = 0; i < list_kucun.length; i++) {
					const row_kucun = list_kucun[i];
					if (
						!row_kucun ||
						row_kucun.residue_num === undefined ||
						row_kucun.residue_num === null
					) {
						ElMessage({
							type: "error",
							message: "库存数量数据异常!"
						});
						continue;
					}
					if (
						item.baoguan_part == row_kucun.baoguan_part &&
						item.huowu_name == row_kucun.huowu_name
					) {
						result_residue_num = row_kucun.residue_num - item.chuku_num;
						if (item.chuku_money === undefined || item.chuku_money === null) {
							ElMessage({
								type: "error",
								message: "出库金额不能为空!"
							});
							continue;
						}
						result_residue_money =
							row_kucun.residue_money - parseFloat(item.chuku_money);

						if (result_residue_num == 0) {
							await service.zhongtie.zhongtie_kucun.update({
								id: item.id,
								states: "审核通过",
								residue_num: 0
							});
							await service.zhongtie.zhongtie_kucun_details.delete({
								ids: [row_kucun.id]
							});
						} else if (result_residue_num < 0) {
							ElMessage({
								type: "error",
								message: "出库失败，结余数量不能为负数!"
							});
							await service.zhongtie.zhongtie_kucun.update({
								id: item.id,
								states: "出库驳回",
								reject_reason: "结余数量为负数"
							});
						} else {
							if (item.chuku_num < 0) {
								// 红冲数据
								await service.zhongtie.zhongtie_kucun.update({
									id: item.id,
									states: "审核通过",
									residue_num: result_residue_num,
									reject_reason: "红冲数据"
								});
							} else {
								// 正常出库
								await service.zhongtie.zhongtie_kucun.update({
									id: item.id,
									states: "审核通过",
									residue_num: result_residue_num
								});
							}
							result_residue_danjia = result_residue_money / result_residue_num;
							// 判断那个时间靠后
							const parsedDate1 = moment(item.time_chuku, "YYYY年M月D日");
							const parsedDate2 = moment(row_kucun.finally_time, "YYYY年M月D日");
							if (parsedDate1 >= parsedDate2) {
								await service.zhongtie.zhongtie_kucun_details.update({
									id: row_kucun.id,
									residue_num: result_residue_num,
									residue_money: result_residue_money,
									residue_danjia: result_residue_danjia,
									finally_time: item.time_chuku
								});
							} else {
								await service.zhongtie.zhongtie_kucun_details.update({
									id: row_kucun.id,
									residue_num: result_residue_num,
									residue_money: result_residue_money,
									residue_danjia: result_residue_danjia,
									finally_time: row_kucun.finally_time
								});
							}
						}
						break;
					}
				}
			}
		}
		ElMessage({
			type: "success",
			message: "已完成出库!"
		});
		router.push({
			path: "/zdf/check/chuku"
		});
	});
}

// 全部驳回
const Reject = () => {
	dialogFormVisible_all.value = true;
};

const submit_all = () => {
	ElMessageBox.confirm("确认审核驳回？", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning"
	}).then(async () => {
		const list_all = await service.zhongtie.zhongtie_kucun.list({
			states: "出库审核",
			ck_danhao: route.query.ck_danhao
		});
		list_all.forEach((item: any) => {
			service.zhongtie.zhongtie_kucun.update({
				id: item.id,
				states: "出库驳回",
				reject_reason: form.all_reason
			});
		});
		ElMessage({
			type: "success",
			message: "已驳回!"
		});
		dialogFormVisible_all.value = false;
		router.push({
			path: "zdf/check/chuku"
		});
	});
};

// 单条数据驳回
const reject = (row: any) => {
	rowId.value = row.id;
	form.reject_reason = row.reject_reason;
	dialogFormVisible.value = true;
};
// 单条数据通过
const pass = (row: any) => {
	// 定义结存金额、结存单价
	let result_residue_money = 0;
	let result_residue_danjia = 0;
	ElMessageBox.confirm("确认审核通过？", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning"
	}).then(async () => {
		downloadFileClick(row.id);
		let result_residue_num = -1;
		// 筛选库存信息
		const list_kucun = await service.zhongtie.zhongtie_kucun_details.list({
			danhao: route.query.danhao,
			xiangmu: org.code,
			huowu_name: route.query.huowu_name
		});
		// 单号货物库存为0进行出库红冲
		if (list_kucun.length == 0) {
			await service.zhongtie.zhongtie_kucun_details.add({
				danhao: row.danhao,
				xiangmu: row.xiangmu,
				baoguan_part: row.baoguan_part,
				huowu_name: row.huowu_name,
				huowu_danwei: row.huowu_danwei,
				residue_num: row.residue_num,
				residue_danjia: row.chuku_danjia,
				residue_money: -row.chuku_money,
				finally_time: row.time_chuku
			});
			await service.zhongtie.zhongtie_kucun.update({
				id: row.id,
				states: "审核通过",
				reject_reason: "红冲数据"
			});
		}
		// 正常出库
		else {
			for (let i = 0; i < list_kucun.length; i++) {
				const row_kucun = list_kucun[i];
				if (
					!row_kucun ||
					row_kucun.residue_num === undefined ||
					row_kucun.residue_num === null
				) {
					ElMessage({
						type: "error",
						message: "库存数量数据异常!"
					});
					continue;
				}
				if (
					row.baoguan_part == row_kucun.baoguan_part &&
					row.huowu_name == row_kucun.huowu_name
				) {
					result_residue_num = row_kucun.residue_num - row.chuku_num;

					result_residue_money = row_kucun.residue_money - parseFloat(row.chuku_money);
					if (result_residue_num == 0) {
						await service.zhongtie.zhongtie_kucun.update({
							id: row.id,
							states: "审核通过",
							residue_num: 0
						});
						await service.zhongtie.zhongtie_kucun_details.delete({
							ids: [row_kucun.id]
						});
					} else if (result_residue_num < 0) {
						ElMessage({
							type: "error",
							message: "出库失败，结余数量不能为负数!"
						});
						await service.zhongtie.zhongtie_kucun.update({
							id: row.id,
							states: "出库驳回",
							reject_reason: "结余数量为负数"
						});
					} else {
						if (row.chuku_num < 0) {
							// 红冲数据
							await service.zhongtie.zhongtie_kucun.update({
								id: row.id,
								states: "审核通过",
								residue_num: result_residue_num,
								reject_reason: "红冲数据"
							});
						} else {
							// 正常出库
							await service.zhongtie.zhongtie_kucun.update({
								id: row.id,
								states: "审核通过",
								residue_num: result_residue_num
							});
						}
						result_residue_danjia = result_residue_money / result_residue_num;
						// 判断那个时间靠后
						const parsedDate1 = moment(row.time_chuku, "YYYY年M月D日");
						const parsedDate2 = moment(row_kucun.finally_time, "YYYY年M月D日");
						if (parsedDate1 >= parsedDate2) {
							await service.zhongtie.zhongtie_kucun_details.update({
								id: row_kucun.id,
								residue_num: result_residue_num,
								residue_money: result_residue_money,
								residue_danjia: result_residue_danjia,
								finally_time: row.time_chuku
							});
						} else {
							await service.zhongtie.zhongtie_kucun_details.update({
								id: row_kucun.id,
								residue_num: result_residue_num,
								residue_money: result_residue_money,
								residue_danjia: result_residue_danjia,
								finally_time: row_kucun.finally_time
							});
						}
					}
				}
			}
		}
		ElMessage({
			type: "success",
			message: "已完成出库!"
		});
		router.push({
			path: "/zdf/check/chuku"
		});
	});
};

// 驳回原因保存
const submit = () => {
	ElMessageBox.confirm("确认审核驳回？", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning"
	}).then(() => {
		service.zhongtie.zhongtie_kucun
			.update({ id: rowId.value, reject_reason: form.reject_reason, states: "出库驳回" })
			.then(() => {
				ElMessage({
					type: "success",
					message: "已驳回!"
				});
				setTimeout(() => {
					dialogFormVisible.value = false;
					Crud.value?.refresh();
				}, 200);
			});
	});
};
</script>
