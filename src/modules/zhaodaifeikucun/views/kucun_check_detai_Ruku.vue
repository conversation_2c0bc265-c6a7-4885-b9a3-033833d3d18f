<template>
	<cl-crud ref="Crud">
		<el-button type="primary" @click="Pass()" text bg style="margin-bottom: 10px; width: 100px"
			>全部通过
		</el-button>
		<el-button type="danger" @click="Reject()" text bg style="margin-bottom: 10px; width: 100px"
			>全部驳回
		</el-button>
		<el-row>
			<!-- 数据表格 -->
			<cl-table ref="Table">
				<template #slot-btn1="{ scope }">
					<el-button type="primary" @click="pass(scope.row)" text bg>通过 </el-button>
				</template>
				<template #slot-btn2="{ scope }">
					<el-button type="primary" @click="reject(scope.row)" text bg>驳回 </el-button>
				</template>
			</cl-table>
			<el-dialog v-model="dialogFormVisible" title="请填写驳回原因" draggable>
				<el-form :model="form" center>
					<el-form-item :label-width="140">
						<el-input size="large" v-model="form.reject_reason" style="width: 80%" />
					</el-form-item>
				</el-form>
				<template #footer>
					<span class="dialog-footer">
						<el-button type="primary" @click="submit">提交</el-button>
						<el-button type="primary" @click="dialogFormVisible = false">
							取消
						</el-button>
					</span>
				</template>
			</el-dialog>
			<el-dialog v-model="dialogFormVisible_all" title="请填写驳回原因" draggable>
				<el-form :model="form" center>
					<el-form-item :label-width="140">
						<el-input size="large" v-model="form.all_reason" style="width: 80%" />
					</el-form-item>
				</el-form>
				<template #footer>
					<span class="dialog-footer">
						<el-button type="primary" @click="submit_all">提交</el-button>
						<el-button type="primary" @click="dialogFormVisible_all = false">
							取消
						</el-button>
					</span>
				</template>
			</el-dialog>
		</el-row>

		<el-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</el-row>
	</cl-crud>
</template>

<script lang="ts" setup>
import { useTable, useCrud } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useBase } from "/$/base";
import moment from "moment";
import { ref, reactive } from "vue";
const { org } = useBase();
const { service, route, router } = useCool();

const rowId = ref();
const dialogFormVisible = ref(false);
const dialogFormVisible_all = ref(false);
const form = reactive({
	reject_reason: "",
	all_reason: ""
});

// cl-table 配置
const Table = useTable({
	columns: [
		{ label: "入库单号", prop: "danhao", minWidth: 190 },
		{ label: "项目", prop: "xiangmu", minWidth: 200 },
		{ label: "保管部门", prop: "baoguan_part", minWidth: 100 },
		{ label: "货物名称", prop: "huowu_name", minWidth: 250 },
		{ label: "货物单位", prop: "huowu_danwei", minWidth: 90 },
		{ label: "入库时间", prop: "time_ruku", minWidth: 130 },
		{ label: "入库单价", prop: "ruku_danjia", minWidth: 90 },
		{ label: "入库数量", prop: "ruku_num", minWidth: 90 },
		{ label: "入库金额", prop: "ruku_money", minWidth: 120 },
		{ label: "入库人", prop: "username", minWidth: 120 },
		{
			type: "op",
			buttons: ["slot-btn1", "slot-btn2"]
		}
	]
});

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.zhongtie.zhongtie_kucun,
		async onRefresh(params, { next, done, render }) {
			// 查找相同单号且状态为入库审核的数据
			const { list } = await next({
				...params,
				danhao: route.query.danhao,
				states: "入库审核",
				xiangmu: org.code
			});
		}
	},
	app => {
		if (route.query.danhao) {
			app.refresh();
		}
	}
);

// 审核通过
async function Pass() {
	const list_all = await service.zhongtie.zhongtie_kucun.list({
		danhao: route.query.danhao,
		xiangmu: org.code,
		states: "入库审核"
	});
	const list_kucun = await service.zhongtie.zhongtie_kucun_details.list({
		danhao: route.query.danhao,
		xiangmu: org.code
	});
	// 定义一个标记变量 用来判断多条数据导入
	let result_residue_num = -1;
	// 定义一个货物名称变量 用来判断多条数据导入
	let hw_flag: any;
	// 定义结存金额、结存单价
	let result_residue_money = 0;
	let result_residue_danjia = 0;
	ElMessageBox.confirm("确认审核通过？", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning"
	}).then(() => {
		list_all.forEach((item: any) => {
			service.zhongtie.zhongtie_kucun.update({ id: item.id, states: "审核通过" });
			if (hw_flag == item.huowu_name) {
				result_residue_num = result_residue_num;
				result_residue_money = result_residue_money;
			} else {
				result_residue_num = -1;
				hw_flag = item.huowu_name;
			}
			// 定义变量，存放是否有相同的库存货物
			let flag = 0;
			for (let i = 0; i < list_kucun.length; i++) {
				const row_kucun = list_kucun[i];
				if (
					item.baoguan_part == row_kucun.baoguan_part &&
					item.huowu_name == row_kucun.huowu_name
				) {
					flag = 1;
					if (result_residue_num == -1) {
						result_residue_num = row_kucun.residue_num + item.ruku_num;
						result_residue_money =
							row_kucun.residue_money + parseFloat(item.ruku_money);
					} else {
						result_residue_num = result_residue_num + item.ruku_num;
						result_residue_money = parseFloat(item.ruku_money) + result_residue_money;
					}
					result_residue_danjia = result_residue_money / result_residue_num;
					const parsedDate1 = moment(item.time_ruku, "YYYY年M月D号");
					const parsedDate2 = moment(row_kucun.finally_time, "YYYY年M月D号");

					if (parsedDate1 >= parsedDate2) {
						service.zhongtie.zhongtie_kucun_details.update({
							id: row_kucun.id,
							residue_num: result_residue_num,
							residue_money: result_residue_money,
							residue_danjia: result_residue_danjia,
							finally_time: item.time_ruku
						});
					} else {
						service.zhongtie.zhongtie_kucun_details.update({
							id: row_kucun.id,
							residue_num: result_residue_num,
							residue_money: result_residue_money,
							residue_danjia: result_residue_danjia,
							finally_time: row_kucun.finally_time
						});
					}
				}
			}
			if (flag == 0) {
				// 设置结存单价、结存金额、更新时间
				item.residue_danjia = item.ruku_danjia;
				item.residue_money = item.ruku_money;
				item.finally_time = item.time_ruku;
				item.checker = item.checker;
				service.zhongtie.zhongtie_kucun_details.add(item);
			}
		});
		ElMessage({
			type: "success",
			message: "已完成入库!"
		});
		router.push({
			path: "/zdf/check/ruku",
			query: {
				flag: 1
			}
		});
	});
}

// 全部驳回
const Reject = () => {
	dialogFormVisible_all.value = true;
};

const submit_all = () => {
	ElMessageBox.confirm("确认审核驳回？", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning"
	}).then(async () => {
		const list_all = await service.zhongtie.zhongtie_kucun.list({
			states: "入库审核",
			danhao: route.query.danhao
		});
		list_all.forEach((item: any) => {
			service.zhongtie.zhongtie_kucun.update({
				id: item.id,
				states: "入库驳回",
				reject_reason: form.all_reason
			});
		});
		ElMessage({
			type: "success",
			message: "已驳回!"
		});
		dialogFormVisible_all.value = false;
		router.push({
			path: "/zdf/check/ruku"
		});
	});
};

// 单条数据通过
const pass = (row: any) => {
	ElMessageBox.confirm("确认审核通过？", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning"
	}).then(async () => {
		await service.zhongtie.zhongtie_kucun.update({ id: row.id, states: "审核通过" });
		const item = {
			danhao: row.danhao,
			xiangmu: row.xiangmu,
			baoguan_part: row.baoguan_part,
			huowu_name: row.huowu_name,
			huowu_danwei: row.huowu_danwei,
			residue_danjia: row.ruku_danjia,
			residue_money: row.ruku_money,
			finally_time: row.time_ruku,
			residue_num: row.ruku_num,
			checker: row.checker
		};
		await service.zhongtie.zhongtie_kucun_details.add(item);
		ElMessage({
			type: "success",
			message: "已完成入库!"
		});
		router.push({
			path: "/zdf/check/ruku"
		});
	});
};

// 单条数据驳回
const reject = (row: any) => {
	rowId.value = row.id;
	form.reject_reason = row.reject_reason;
	dialogFormVisible.value = true;
};

// 驳回原因保存
const submit = () => {
	ElMessageBox.confirm("确认审核驳回？", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning"
	}).then(() => {
		service.zhongtie.zhongtie_kucun
			.update({ id: rowId.value, reject_reason: form.reject_reason, states: "入库驳回" })
			.then(() => {
				ElMessage({
					type: "success",
					message: "已驳回!"
				});
				setTimeout(() => {
					dialogFormVisible.value = false;
					Crud.value?.refresh();
				}, 200);
			});
	});
};
</script>
