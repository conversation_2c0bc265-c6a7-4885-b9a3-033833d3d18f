<template>
	<div class="table-container">
		<table class="form-table">
			<thead>
				<tr>
					<th>入库单号</th>
					<th>项目</th>
					<th>保管部门</th>
					<th>领用部门</th>
					<th>货物名称</th>
					<th>单位</th>
					<th>出库时间</th>
					<th>出库单价</th>
					<th>出库数量</th>
					<th>出库金额</th>
					<th>结存数量</th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="(row, index) in formRows" :key="index">
					<td>
						<el-select
							v-model="row.danhao"
							filterable
							clearable
							type="text"
							placeholder=" "
							style="width: 180px"
							class="input-style"
						>
							<el-option
								v-for="item in kucun_danhao"
								:key="item.value"
								:label="item.value"
								:value="item.value"
							>
							</el-option>
						</el-select>
					</td>
					<td>
						<el-input
							v-model="row.xm_name"
							type="text"
							disabled
							style="height: 32px; width: 320px"
							class="input-style"
						/>
					</td>
					<td>
						<el-input
							v-model="row.baoguan_part"
							type="text"
							disabled
							style="height: 32px; width: 200px"
							class="input-style"
						/>
					</td>
					<td>
						<el-tree-select
							v-model="row.lingyong_part"
							:data="org.listpart"
							:props="{
								label: 'name',
								children: 'children',
								disabled: data => data.children && data.children.length > 0
							}"
							filterable
							check-strictly
							node-key="name"
							placeholder=" "
							style="width: 300px"
						/>
					</td>
					<td>
						<el-input
							v-model="row.huowu_name"
							type="text"
							disabled
							style="height: 32px; width: 150px"
							class="input-style"
						/>
					</td>
					<td>
						<el-input
							v-model="row.huowu_danwei"
							type="text"
							disabled
							style="height: 32px; width: 50px"
							class="input-style"
						/>
					</td>
					<td>
						<el-date-picker
							v-model="row.show_time"
							type="date"
							placeholder="请选择日期"
							format="YYYY年MM月DD日"
						/>
					</td>
					<td>
						<el-input
							v-model="row.chuku_danjia"
							type="text"
							disabled
							style="height: 32px; width: 70px"
							class="input-style"
						/>
						<!-- <el-select v-model="row.chuku_danjia" filterable clearable type="text" placeholder=" "
                            style="height: 24px; width: 120px; " class="input-style">
                            <el-option v-for="item in kucun_danjia" :key="item.value" :label="item.value" :value="item.value">
                            </el-option>
                        </el-select> -->
					</td>
					<td>
						<el-input
							v-model="row.chuku_num"
							type="number"
							min="1"
							step="1"
							oninput="if(!/^[0-9]+$/.test(value)) value=null;if(value<0)value=null"
							style="height: 32px; width: 70px"
							class="input-style"
						/>
					</td>
					<td>
						<el-input
							v-model="row.chuku_money"
							type="number"
							disabled
							style="height: 32px; width: 80px"
							class="input-style"
						/>
					</td>
					<td>
						<!-- 结存数量 -->
						<el-input
							v-model="row.residue_num"
							type="text"
							disabled
							style="height: 32px; width: 50px"
							class="input-style"
						/>
					</td>
					<td>
						<button @click="removeRow(index)" class="remove-button">删除</button>
					</td>
				</tr>
			</tbody>
		</table>
		<el-row>
			<button @click="addRow" class="add-button">新增一行</button>
		</el-row>
		<el-row>
			<button @click="saveForm" class="save-button">提交出库</button>
		</el-row>
		<el-row>
			<button @click="resetForm" class="reset-button">页面重置</button>
		</el-row>
	</div>
</template>

<script lang="ts" setup>
import { useCool } from '/@/cool';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useBase } from '/$/base';
import { watch, ref } from 'vue';
import { dateFormat } from '../utils/moment';
import moment from 'moment';
const { service, router } = useCool();
const { org, user } = useBase();

const kucun_danhao = ref<Array<{ value: string; label: string }>>([]);
const username = user.info?.username;

const rk_danhao = async () => {
	const kucun_list = await service.zhongtie.zhongtie_kucun_details.list({ xiangmu: org.code });
	kucun_danhao.value = [];
	for (let j = 0; j < kucun_list.length; j++) {
		const row_mx = kucun_list[j];
		if (row_mx.danhao !== undefined) {
			kucun_danhao.value.push({ value: row_mx.danhao, label: row_mx.danhao });
		}
	}
};

// 初始表格行
const formRows = ref([
	{
		danhao: '',
		ck_danhao: '',
		xiangmu: org.code,
		xm_name: org.name,
		baoguan_part: '',
		lingyong_part: '',
		show_time: new Date(),
		time_chuku: '',
		huowu_name: '',
		huowu_danwei: '',
		chuku_danjia: null,
		chuku_num: null,
		chuku_money: '',
		states: '出库审核',
		residue_num: null,
		username: username,
		checker: '',
		ck_person: username
	}
]);

// 添加新的表格行
const addRow = () => {
	formRows.value.push({
		danhao: '',
		ck_danhao: '',
		xiangmu: org.code,
		xm_name: org.name,
		baoguan_part: '',
		lingyong_part: '',
		show_time: new Date(),
		time_chuku: '',
		huowu_name: '',
		huowu_danwei: '',
		chuku_danjia: null,
		chuku_num: null,
		chuku_money: '',
		states: '出库审核',
		residue_num: null,
		username: username,
		checker: '',
		ck_person: username
	});
};
// 移除指定的表格行
const removeRow = (index: any) => {
	formRows.value.splice(index, 1);
};
// 在这里调用保存表单的逻辑
const saveForm = () => {
	// 判断出库信息是否完整
	let flag_complete = 0;
	for (const item of formRows.value) {
		if (item.danhao == '') {
			ElMessage({
				type: 'error',
				message: '入库单号未选择！'
			});
			flag_complete = 1;
		} else if (item.lingyong_part == '') {
			ElMessage({
				type: 'error',
				message: '领用部门未选择！'
			});
			flag_complete = 1;
		} else if (item.time_chuku == 'Invalid date') {
			ElMessage({
				type: 'error',
				message: '出库时间未选择！'
			});
			flag_complete = 1;
		} else if (item.chuku_num == null) {
			ElMessage({
				type: 'error',
				message: '出库数量未填写！'
			});
			flag_complete = 1;
		}
	}
	if (flag_complete == 0) {
		// 判断是否是非末级组织
		if (org.code[org.code.length - 1] != '0') {
			ElMessage({
				type: 'error',
				message: '对不起，您没有该权限'
			});
			resetForm();
		} else {
			ElMessageBox.confirm('确认信息无误进行出库？', '提示', {
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(async () => {
				for (const item of formRows.value) {
					const now = new Date();
					const year = now.getFullYear();
					const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始
					const day = String(now.getDate()).padStart(2, '0');
					const hours = String(now.getHours()).padStart(2, '0');
					const minutes = String(now.getMinutes()).padStart(2, '0');
					const seconds = String(now.getSeconds()).padStart(2, '0');
					const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
					const currentDateTime = `${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}`;
					item.ck_danhao = currentDateTime;
					await new Promise(resolve => setTimeout(resolve, 1));
				}
				// 调用出库接口
				service.zhongtie.zhongtie_kucun.add(formRows.value);
				ElMessage({
					type: 'success',
					message: '完成出库，等待审核'
				});
				// 重置数据
				formRows.value = [
					{
						danhao: '',
						ck_danhao: '',
						xiangmu: org.code,
						xm_name: org.name,
						baoguan_part: '',
						lingyong_part: '',
						show_time: new Date(),
						time_chuku: '',
						huowu_name: '',
						huowu_danwei: '',
						chuku_danjia: null,
						chuku_num: null,
						chuku_money: '',
						states: '出库审核',
						residue_num: null,
						username: username,
						checker: '',
						ck_person: username
					}
				];
				// router.push({
				//     path: "/check/chuku",
				// })
			});
		}
	}
};
// 重置表单
const resetForm = () => {
	formRows.value = [
		{
			danhao: '',
			ck_danhao: '',
			xiangmu: org.code,
			xm_name: org.name,
			baoguan_part: '',
			lingyong_part: '',
			show_time: new Date(),
			time_chuku: '',
			huowu_name: '',
			huowu_danwei: '',
			chuku_danjia: null,
			chuku_num: null,
			chuku_money: '',
			states: '出库审核',
			residue_num: null,
			username: username,
			checker: '',
			ck_person: username
		}
	];
};
// 获取库存里所有入库单号
rk_danhao();

// 监听
watch(formRows.value, async (newselected, oldselected) => {
	const kucun_list = await service.zhongtie.zhongtie_kucun_details.list({ xiangmu: org.code });
	if (kucun_list.length == 0) {
		ElMessage({
			message: '该组织无库存,无法进行出库操作',
			type: 'error'
		});
	} else {
		for (let i = 0; i < formRows.value.length; i++) {
			const row = formRows.value[i];
			row.xm_name = org.name;
			row.time_chuku = dateFormat(row.show_time);
			if (row.danhao) {
				for (let j = 0; j < kucun_list.length; j++) {
					const row_mx = kucun_list[j];
					if (row_mx.danhao == row.danhao) {
						row.baoguan_part = row_mx.baoguan_part ?? '';
						row.huowu_name = row_mx.huowu_name ?? '';
						row.huowu_danwei = row_mx.huowu_danwei ?? '';
						row.chuku_danjia = row_mx.residue_danjia;
						row.checker = row_mx.checker ?? '';
						break;
					}
				}
			}
			if (row.chuku_danjia && row.chuku_num) {
				// 计算出库金额
				row.chuku_money = parseFloat((row.chuku_danjia * row.chuku_num).toString()).toFixed(
					2
				);
				for (let j = 0; j < kucun_list.length; j++) {
					const row_mx = kucun_list[j];
					if (
						row_mx.baoguan_part == row.baoguan_part &&
						row_mx.xiangmu == row.xiangmu &&
						row_mx.huowu_name == row.huowu_name &&
						row_mx.residue_danjia == row.chuku_danjia &&
						row_mx.danhao == row.danhao
					) {
						row.residue_num = (row_mx.residue_num ?? 0) - row.chuku_num;
						if (row.residue_num < 0) {
							ElMessage({
								message: '结存数量不能为负数！',
								type: 'error'
							});
						}
						const parsedDate1 = moment(row.time_chuku, 'YYYY年M月D号');
						const parsedDate2 = moment(row_mx.finally_time, 'YYYY年M月D号');
						if (parsedDate1 < parsedDate2) {
							ElMessage({
								message: '出库时间不能早于入库时间，请重新输入！',
								type: 'error'
							});
						}
						break;
					}
				}
			}
		}
	}
});
</script>

<style scoped>
.table-container {
	width: 100%;
	overflow-x: auto;
}

.form-table {
	width: 100%;
	border-collapse: collapse;
	margin-bottom: 20px;
}

.form-table th,
.form-table td {
	padding: 10px;
	text-align: center;
	border-bottom: 0px solid #ccc;
}

.form-table th {
	background-color: #f0f0f0;
}

.input-style {
	text-align: center;
}

.remove-button {
	background-color: #f40509;
	color: #fff;
	border: none;
	/* 设置元素的内边距 */
	padding: 0px 2px;
	cursor: pointer;
}

.add-button,
.reset-button,
.save-button {
	background-color: #008000;
	color: #fff;
	border: none;
	padding: 10px 10px;
	cursor: pointer;
}

.save-button {
	margin-top: auto;
	margin-left: auto;
	margin-right: auto;
}
</style>
