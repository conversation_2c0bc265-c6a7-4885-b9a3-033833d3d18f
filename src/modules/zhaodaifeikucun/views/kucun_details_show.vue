<template>
	<cl-crud ref="Crud">
		<el-row>
			<cl-refresh-btn />
			<cl-search-key />
			<cl-flex1 />
			<el-button type="primary" @click="downloadFileClick">导出</el-button>
		</el-row>
		<el-row>
			<cl-table ref=" Table"> </cl-table>
		</el-row>
		<!-- 分页 -->
		<el-row>
			<cl-flex1 />
			<cl-pagination />
		</el-row>
	</cl-crud>
</template>

<script lang="ts" setup>
import { useCrud, useTable } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useBase } from "/$/base";
import * as XLSX from "xlsx";

const { service } = useCool();
const { org, user } = useBase();
// 获取用户名

// cl-table 配置
const Table = useTable({
	columns: [
		{ label: "更新时间", prop: "finally_time", minWidth: 130 },
		{ label: "入库单号", prop: "danhao", minWidth: 190 },
		{ label: "项目", prop: "xiangmu", minWidth: 200 },
		{ label: "保管部门", prop: "baoguan_part", minWidth: 100 },
		{ label: "货物名称", prop: "huowu_name", minWidth: 250 },
		{ label: "货物单位", prop: "huowu_danwei", minWidth: 90 },
		{ label: "结存数量", prop: "residue_num", minWidth: 90 },
		{ label: "结存单价", prop: "residue_danjia", minWidth: 120 },
		{ label: "结存金额", prop: "residue_money", minWidth: 120 },
		{ label: "保管人", prop: "checker", minWidth: 120 }
	]
});

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.zhongtie.zhongtie_kucun_details,
		async onRefresh(params, { next, done, render }) {
			// 1 默认调用

			const { list } = await next({ ...params, xiangmu: org.code });
		}
	},
	app => {
		app.refresh({});
	}
);

const downloadFileClick = async () => {
	const arr: any[] = [];
	const excel_name = org.name + "库存信息.xlsx";
	arr[0] = [
		"更新时间",
		"入库单号",
		"项目",
		"保管部门",
		"货物名称",
		"货物单位",
		"结存数量",
		"结存单价",
		"结存金额",
		"保管人"
	];
	const result = await service.zhongtie.zhongtie_kucun_details.list({ xiangmu: org.code });
	result.forEach(item => {
		arr.push([
			item.finally_time,
			item.danhao,
			item.xiangmu,
			item.baoguan_part,
			item.huowu_name,
			item.huowu_danwei,
			item.residue_num,
			item.residue_danjia,
			item.residue_money,
			item.checker
		]);
	});
	//创建一个新的工作表
	const workBook = XLSX.utils.book_new();
	//将表格转换为工作表
	const worksheet = XLSX.utils.aoa_to_sheet(arr);
	//将工作表添加到workBook中

	XLSX.utils.book_append_sheet(workBook, worksheet, "Sheet1");
	XLSX.writeFile(workBook, excel_name);
};
</script>
