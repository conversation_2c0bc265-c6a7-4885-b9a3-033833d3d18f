<template>
	<cl-crud ref="Crud">
		<el-row>
			<cl-refresh-btn />
			<el-button type="primary" @click="downloadFileClick">导出</el-button>
			<cl-multi-delete-btn />
			<cl-flex1 />
			<cl-search-key />
		</el-row>
		<el-row>
			<cl-table ref=" Table"> </cl-table>
		</el-row>
		<!-- 分页 -->
		<el-row>
			<cl-flex1 />
			<cl-pagination />
		</el-row>
		<cl-upsert ref="Upsert"></cl-upsert>
	</cl-crud>
</template>

<script lang="ts" setup>
import { useTable, useUpsert, useCrud } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useBase } from "/$/base";
import * as XLSX from "xlsx";
const { service } = useCool();
const { org, user } = useBase();

const username = user.info?.username;

// cl-upsert 配置
const Upsert = useUpsert({
	dialog: {
		width: "1300px"
	},
	// 一行24
	items: [
		{
			prop: "danhao",
			label: "单号",
			span: 6,
			required: true,
			component: {
				name: "el-input",
				align: "center"
			}
		},
		{
			prop: "baoguan_part",
			label: "保管部门",
			span: 4,
			required: true,
			component: {
				name: "el-input"
			}
		},
		{
			prop: "huowu_name",
			label: "货物名称",
			span: 8,
			required: true,
			component: {
				name: "el-input"
			}
		},
		{
			prop: "huowu_danwei",
			label: "货物单位",
			span: 3,
			required: true,
			component: {
				name: "el-input"
			}
		},
		{
			prop: "time_ruku",
			label: "入库时间",
			span: 5,
			component: {
				name: "el-input"
			}
		},
		{
			prop: "ruku_danjia",
			label: "入库单价",
			span: 6,
			component: {
				name: "el-input"
			}
		},
		{
			prop: "ruku_num",
			label: "入库数量",
			span: 4,
			component: {
				name: "el-input"
			}
		},
		{
			prop: "ruku_money",
			label: "入库金额",
			span: 7,
			component: {
				name: "el-input"
			}
		},
		{
			prop: "time_chuku",
			label: "出库时间",
			span: 5,
			component: {
				name: "el-input"
			}
		},
		{
			prop: "chuku_danjia",
			label: "出库单价",
			span: 6,
			component: {
				name: "el-input"
			}
		},
		{
			prop: "chuku_num",
			label: "出库数量",
			span: 4,
			component: {
				name: "el-input"
			}
		},
		{
			prop: "chuku_money",
			label: "出库金额",
			span: 7,
			component: {
				name: "el-input"
			}
		},
		{
			prop: "states",
			label: "状态",
			span: 5,
			required: true,
			component: {
				name: "el-select",
				options: [
					{
						value: "入库审核",
						label: "入库审核"
					},
					{
						value: "出库审核",
						label: "出库审核"
					}
				]
			}
		}
	]
});

// cl-table 配置
const Table = useTable({
	columns: [
		{
			type: "selection",
			width: 60
		},
		{
			label: "状态",
			prop: "states",
			minWidth: 90
		},
		{
			label: "驳回原因",
			prop: "reject_reason",
			minWidth: 200
		},
		{ label: "审核人", prop: "checker", minWidth: 120 },
		{
			label: "入库单号",
			prop: "danhao",
			minWidth: 190
		},
		{
			label: "出库单号",
			prop: "ck_danhao",
			minWidth: 190
		},
		{
			label: "项目",
			prop: "xiangmu",
			minWidth: 200
		},
		{
			label: "保管部门",
			prop: "baoguan_part",
			minWidth: 100
		},
		{
			label: "货物名称",
			prop: "huowu_name",
			minWidth: 250
		},
		{
			label: "货物单位",
			prop: "huowu_danwei",
			minWidth: 90
		},
		{
			label: "入库信息",
			children: [
				{
					label: "入库时间",
					prop: "time_ruku",
					minWidth: 130
				},
				{
					label: "入库单价",
					prop: "ruku_danjia",
					minWidth: 90
				},
				{
					label: "入库数量",
					prop: "ruku_num",
					minWidth: 90
				},
				{
					label: "入库金额",
					prop: "ruku_money",
					minWidth: 120
				}
			]
		},
		{
			label: "出库信息",
			children: [
				{
					label: "出库时间",
					prop: "time_chuku",
					minWidth: 130
				},
				{
					label: "出库单价",
					prop: "chuku_danjia",
					minWidth: 90
				},
				{
					label: "出库数量",
					prop: "chuku_num",
					minWidth: 90
				},
				{
					label: "出库金额",
					prop: "chuku_money",
					minWidth: 120
				}
			]
		},
		{
			label: "操作",
			type: "op",
			buttons: ["edit", "delete"]
		}
	]
});

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.zhongtie.zhongtie_kucun,
		async onRefresh(params, { next, done, render }) {
			// 1 默认调用
			// const users = await service.base.comm.person().then(data => {
			//     return (data.username)
			// })
			// const users = user.info?.username
			const { list } = await next({
				...params,
				states: ["出库驳回", "入库驳回"],
				xiangmu: org.code,
				username: username
			});
		}
	},
	app => {
		app.refresh({});
	}
);

const downloadFileClick = async () => {
	const arr: any[] = [];
	const excel_name = org.name + "驳回明细.xlsx";
	arr[0] = [
		"状态",
		"驳回原因",
		"审核人",
		"入库单号",
		"出库单号",
		"项目",
		"保管部门",
		"货物名称",
		"货物单位",
		"入库时间",
		"入库单价",
		"入库数量",
		"入库金额",
		"出库时间",
		"出库单价",
		"出库数量",
		"出库金额"
	];
	const result = await service.zhongtie.zhongtie_kucun.list({
		xiangmu: org.code,
		states: ["出库驳回", "入库驳回"]
	});
	result.forEach(item => {
		arr.push([
			item.states,
			item.reject_reason,
			item.checker,
			item.danhao,
			item.ck_danhao,
			item.xiangmu,
			item.baoguan_part,
			item.huowu_name,
			item.huowu_danwei,
			item.time_ruku,
			item.ruku_danjia,
			item.ruku_num,
			item.ruku_money,
			item.time_chuku,
			item.chuku_danjia,
			item.chuku_num,
			item.chuku_money
		]);
	});
	//创建一个新的工作表
	const workBook = XLSX.utils.book_new();
	//将表格转换为工作表
	const worksheet = XLSX.utils.aoa_to_sheet(arr);
	//将工作表添加到workBook中
	XLSX.utils.book_append_sheet(workBook, worksheet, "Sheet1");
	XLSX.writeFile(workBook, excel_name);
};
</script>
