<template>
	<el-row>
		<input
			type="file"
			ref="upload"
			@change="handleFileChange"
			accept=".xlsx"
			v-show="false"
			id="file1"
		/>
		<el-button type="primary" @click="handleFileChange">上传文件</el-button>
		<el-button type="primary" @click="readExcelData">提交入库</el-button>
		<cl-flex1 />
		<el-button type="primary" @click="Download">下载模板</el-button>
		<el-table :data="tableData" style="width: 100%" :border="true" table-layout="auto">
			<!-- <el-table-column prop='danhao' label="单号" minWidth="190" align="center" /> -->
			<el-table-column prop="xiangmu" label="项目" minWidth="200" align="center" />
			<el-table-column prop="baoguan_part" label="保管部门" minWidth="100" align="center" />
			<el-table-column prop="huowu_name" label="货物名称" minWidth="250" align="center" />
			<el-table-column prop="huowu_danwei" label="单位" minWidth="90" align="center" />
			<el-table-column prop="time_ruku" label="入库时间" minWidth="120" align="center" />
			<el-table-column prop="ruku_danjia" label="入库单价" minWidth="90" align="center" />
			<el-table-column prop="ruku_num" label="入库数量" minWidth="90" align="center" />
			<el-table-column prop="ruku_money" label="入库金额" align="center" />
		</el-table>
	</el-row>
	<div class="table-container">
		<table class="form-table">
			<thead>
				<tr>
					<th>项目</th>
					<th>保管部门</th>
					<th>货物名称</th>
					<th>单位</th>
					<th>入库时间</th>
					<th>入库单价</th>
					<th>入库数量</th>
					<th>入库金额</th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="(row, index) in formRows" :key="index">
					<td>
						<el-input
							v-model="row.xm_name"
							type="text"
							disabled
							style="height: 32px; width: 320px"
							class="input-style"
						/>
					</td>
					<td>
						<el-tree-select
							v-model="row.baoguan_part"
							:data="org.listpart"
							:props="{
								label: 'name',
								children: 'children',
								disabled: data => data.children && data.children.length > 0
							}"
							filterable
							check-strictly
							node-key="name"
							placeholder=" "
							style="width: 300px"
						/>
					</td>
					<td>
						<el-select
							v-model="row.huowu_name"
							filterable
							clearable
							type="text"
							placeholder=" "
							style="width: 250px"
							class="input-style"
						>
							<el-option
								v-for="item in org.listhw"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							>
							</el-option>
						</el-select>
					</td>
					<td>
						<el-select
							v-model="row.huowu_danwei"
							filterable
							clearable
							type="text"
							placeholder=" "
							style="width: 60px"
							class="input-style"
						>
							<el-option
								v-for="item in row.options"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							>
							</el-option>
						</el-select>
					</td>
					<td>
						<el-date-picker
							v-model="row.show_time"
							type="date"
							placeholder="请选择日期"
							format="YYYY年MM月DD日"
						/>
					</td>
					<td>
						<el-input
							v-model="row.ruku_danjia"
							type="number"
							min="0"
							step="0.01"
							oninput="if(!/^[0-9]+(.[0-9]{1,2})?$/.test(value)) value=null;if(value<0)value=null"
							style="height: 32px; width: 100px"
							class="input-style"
						/>
					</td>
					<td>
						<el-input
							v-model="row.ruku_num"
							type="number"
							min="1"
							step="1"
							oninput="if(!/^[0-9]+$/.test(value)) value=null;if(value<0)value=null"
							style="height: 32px; width: 60px"
							class="input-style"
						/>
					</td>
					<td>
						<el-input
							v-model="row.ruku_money"
							type="text"
							disabled
							style="height: 32px; width: 80px"
							class="input-style"
						/>
					</td>
					<td>
						<button @click="removeRow(index)" class="remove-button">删除</button>
					</td>
				</tr>
			</tbody>
		</table>
		<el-row>
			<button @click="addRow" class="add-button">新增一行</button>
		</el-row>
		<el-row>
			<button @click="saveForm" class="save-button">提交入库</button>
		</el-row>
		<el-row>
			<button @click="resetForm" class="reset-button">页面重置</button>
		</el-row>
	</div>
</template>

<script lang="ts" setup>
import { useCool } from "/@/cool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useBase } from "/$/base";
import * as XLSX from "xlsx";
import { watch, ref } from "vue";
import { dateFormat } from "../utils/moment";
const { service, router } = useCool();
const { user, org } = useBase();

let excelData = ref<any[]>([]);
let temporarylist: any = [];
const tableData = ref([]);
const url = `${import.meta.env.VITE_TEMPLATE_URL}/入库模板.xlsx`;
// const url = 'http://***************:9001/public/uploads/入库模板.xlsx';
const username = user.info?.username;
const xm_name = org.name;
const xiangmu = org.code;

const result: any = [];

function generateTimeStamp() {
	const now = new Date();
	return (
		now.getFullYear().toString() +
		(now.getMonth() + 1).toString().padStart(2, "0") +
		now.getDate().toString().padStart(2, "0") +
		now.getHours().toString().padStart(2, "0") +
		now.getMinutes().toString().padStart(2, "0") +
		now.getSeconds().toString().padStart(2, "0") +
		now.getMilliseconds().toString().padStart(3, "0")
	);
}

const handleFileChange = (event: Event) => {
	// 定义按钮
	const input = document.querySelector("#file1") as any;
	// 点击
	input.click();
	console.log(input.value);
	const target = event.target as HTMLInputElement;
	const file = target.files?.[0];
	if (file) {
		excelData = ref([]);
		tableData.value = [];
		temporarylist = [];
		const reader = new FileReader();
		reader.onload = e => {
			const data = new Uint8Array(e.target?.result as ArrayBuffer);
			const workbook = XLSX.read(data, { type: "array" });
			const worksheet = workbook.Sheets[workbook.SheetNames[0]];
			const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

			excelData.value = jsonData.map((row: any, index: number) => ({
				index: index + 1,
				data: row
			}));
		};
		reader.readAsArrayBuffer(file);
		ElMessage({
			type: "success",
			message: "上传文件成功!"
		});
		setTimeout(() => {
			showtable();
			// 重置文件输入的元素
			input.value = "";
		}, 500);
	}
};

// 展示查看数据
const showtable = async () => {
	// 判断是否有数据
	if (excelData.value.length > 1) {
		let xiangmu = -1;
		let baoguan_part = -1;
		let huowu_name = -1;
		let huowu_danwei = -1;
		let ruku_danjia = -1;
		let time_ruku = -1;
		let ruku_num = -1;
		let ruku_money = -1;
		const Result = excelData.value[0].data;
		for (let i = 0; i < Result.length; i++) {
			if ("项目" == Result[i]) {
				xiangmu = i;
			}
			if ("保管部门" == Result[i]) {
				baoguan_part = i;
			}
			if ("货物名称" == Result[i]) {
				huowu_name = i;
			}
			if ("单位" == Result[i]) {
				huowu_danwei = i;
			}
			if ("入库时间" == Result[i]) {
				time_ruku = i;
			}
			if ("入库单价" == Result[i]) {
				ruku_danjia = i;
			}
			if ("入库数量" == Result[i]) {
				ruku_num = i;
			}
			if ("入库金额" == Result[i]) {
				ruku_money = i;
			}
		}
		excelData.value.shift();
		excelData.value.forEach((item: any, index: number) => {
			const result = item.data;
			temporarylist.push({
				danhao: generateTimeStamp() + index,
				xiangmu: result[xiangmu],
				baoguan_part: result[baoguan_part],
				huowu_name: result[huowu_name],
				huowu_danwei: result[huowu_danwei],
				ruku_danjia: result[ruku_danjia],
				time_ruku: result[time_ruku],
				ruku_num: result[ruku_num],
				ruku_money: result[ruku_money]
			});
		});
		tableData.value = temporarylist;
	} else {
		ElMessage({
			message: "文件数据为空",
			type: "error"
		});
	}
};

const checkDepartment = (department: any, targetName: string): boolean => {
	// 如果是叶子节点，直接比较名称
	if (!department.children || department.children.length === 0) {
		return department.name === targetName;
	}

	// 递归检查子部门
	return department.children.some((child: any) => checkDepartment(child, targetName));
};

const readExcelData = async () => {
	// 读取 Excel 数据的逻辑
	const result_list: any[] = [];
	temporarylist.forEach((result: any) => {
		// console.log("###", result.time_ruku, typeof result.time_ruku);
		let flag_hw = 0;
		let flag_part = 0;
		for (let i = 0; i < org.listhw.length; i++) {
			if (result.huowu_name == org.listhw[i].value) {
				flag_hw = 1;
				break;
			}
		}
		for (let j = 0; j < org.listpart.length; j++) {
			if (checkDepartment(org.listpart[j], result.baoguan_part)) {
				flag_part = 1;
				break;
			}
		}
		if (flag_hw == 1 && flag_part == 1) {
			if (result.xiangmu == org.code) {
				// 判断入库价格是否等于入库单价乘以入库数量
				if (result.time_ruku == undefined) {
					ElMessage({
						message: "入库时间不能为空",
						type: "error"
					});
				} else {
					if (
						!result.time_ruku.includes("年") ||
						!result.time_ruku.includes("月") ||
						!result.time_ruku.includes("日")
					) {
						ElMessage({
							message: "入库时间格式无效,应为*年*月*日",
							type: "error"
						});
					} else {
						if (result.ruku_money == result.ruku_danjia * result.ruku_num) {
							result_list.push({
								danhao: result.danhao,
								xiangmu: result.xiangmu,
								baoguan_part: result.baoguan_part,
								huowu_name: result.huowu_name,
								huowu_danwei: result.huowu_danwei,
								ruku_danjia: result.ruku_danjia,
								time_ruku: result.time_ruku,
								ruku_num: result.ruku_num,
								ruku_money: result.ruku_money,
								states: "入库审核",
								residue_num: result.ruku_num,
								username: username,
								checker: username
							});
						} else {
							ElMessage({
								message:
									result.huowu_name +
									"入库金额不等于入库单价乘入库数量，请查看！",
								type: "error"
							});
						}
					}
				}
			} else {
				ElMessage({
					message: "项目代码不正确，请查看数据或切换组织，导入失败",
					type: "error"
				});
			}
		} else if (flag_hw == 0 && flag_part == 0) {
			ElMessage({
				message: result.huowu_name + "货物名称与部门都不匹配，导入失败",
				type: "error"
			});
		} else if (flag_hw == 0) {
			ElMessage({
				message: result.huowu_name + "货物名称不匹配，导入失败",
				type: "error"
			});
		} else if (flag_part == 0) {
			ElMessage({
				message: result.huowu_name + "部门不匹配，导入失败",
				type: "error"
			});
		}
	});
	if (result_list.length > 0) {
		service.zhongtie.zhongtie_kucun.add(result_list);
		ElMessage({
			message: "完成入库，等待审核",
			type: "success"
		});
	}
	// 将数据设置为空
	excelData = ref([]);
	tableData.value = [];
	temporarylist = [];
};

const Download = () => {
	window.location.href = url;
};

// 定义数组
const formRows = ref([
	{
		danhao: "",
		xiangmu: xiangmu,
		xm_name: xm_name,
		baoguan_part: "",
		show_time: new Date(),
		time_ruku: "",
		huowu_name: "",
		huowu_danwei: "",
		ruku_danjia: null,
		ruku_num: null,
		ruku_money: "",
		states: "入库审核",
		options: [
			{ value: "瓶", label: "瓶" },
			{ value: "斤", label: "斤" }
		],
		residue_num: null,
		username: username,
		checker: username
	}
]);

// 添加新的表格行
const addRow = () => {
	formRows.value.push({
		danhao: "",
		xiangmu: xiangmu,
		xm_name: xm_name,
		baoguan_part: "",
		show_time: new Date(),
		time_ruku: "",
		huowu_name: "",
		huowu_danwei: "",
		ruku_danjia: null,
		ruku_num: null,
		ruku_money: "",
		states: "入库审核",
		options: [
			{ value: "瓶", label: "瓶" },
			{ value: "斤", label: "斤" }
		],
		residue_num: null,
		username: username,
		checker: username
	});
};

// 删除行
const removeRow = (index: any) => {
	formRows.value.splice(index, 1);
};

// 在这里调用保存表单的逻辑
const saveForm = () => {
	// 判断入库信息是否完整
	let flag_complete = 0;
	for (const item of formRows.value) {
		if (item.baoguan_part == "") {
			ElMessage({
				type: "error",
				message: "保管部门未选择！"
			});
			flag_complete = 1;
		} else if (item.huowu_name == "") {
			ElMessage({
				type: "error",
				message: "货物名称未选择！"
			});
			flag_complete = 1;
		} else if (item.huowu_danwei == "") {
			ElMessage({
				type: "error",
				message: "货物单位未选择！"
			});
			flag_complete = 1;
		} else if (item.time_ruku == "Invalid date") {
			ElMessage({
				type: "error",
				message: "入库时间未选择！"
			});
			flag_complete = 1;
		} else if (item.ruku_danjia == null) {
			ElMessage({
				type: "error",
				message: "入库单价未填写！"
			});
			flag_complete = 1;
		} else if (item.ruku_num == null) {
			ElMessage({
				type: "error",
				message: "入库数量未填写！"
			});
			flag_complete = 1;
		}
	}
	if (flag_complete == 0) {
		if (xiangmu[xiangmu.length - 1] != "0") {
			ElMessage({
				type: "success",
				message: "对不起，您没有该权限"
			});
			resetForm();
		} else {
			ElMessageBox.confirm("确认信息无误进行入库？", "提示", {
				confirmButtonText: "确认",
				cancelButtonText: "取消",
				type: "warning"
			}).then(async () => {
				for (const item of formRows.value) {
					const now = new Date();
					const year = now.getFullYear();
					const month = String(now.getMonth() + 1).padStart(2, "0"); // 月份从0开始
					const day = String(now.getDate()).padStart(2, "0");
					const hours = String(now.getHours()).padStart(2, "0");
					const minutes = String(now.getMinutes()).padStart(2, "0");
					const seconds = String(now.getSeconds()).padStart(2, "0");
					const milliseconds = String(now.getMilliseconds()).padStart(3, "0");
					const currentDateTime = `${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}`;
					item.danhao = currentDateTime;
					await new Promise(resolve => setTimeout(resolve, 1));
				}
				// console.log('#########',formRows.value)
				// 调用入库接口
				await service.zhongtie.zhongtie_kucun.add(formRows.value);
				ElMessage({
					type: "success",
					message: "完成入库，等待审核"
				});
				// 重置数据
				formRows.value = [
					{
						danhao: "",
						xiangmu: xiangmu,
						xm_name: xm_name,
						baoguan_part: "",
						show_time: new Date(),
						time_ruku: "",
						huowu_name: "",
						huowu_danwei: "",
						ruku_danjia: null,
						ruku_num: null,
						ruku_money: "",
						states: "入库审核",
						options: [
							{ value: "瓶", label: "瓶" },
							{ value: "斤", label: "斤" }
						],
						residue_num: null,
						username: username,
						checker: username
					}
				];
				// router.push({
				//     path: "/check/ruku",
				// })
			});
		}
	}
};

// 重置数据
const resetForm = () => {
	formRows.value = [
		{
			danhao: "",
			xiangmu: xiangmu,
			xm_name: xm_name,
			baoguan_part: "",
			show_time: new Date(),
			time_ruku: "",
			huowu_name: "",
			huowu_danwei: "",
			ruku_danjia: null,
			ruku_num: null,
			ruku_money: "",
			states: "入库审核",
			options: [
				{ value: "瓶", label: "瓶" },
				{ value: "斤", label: "斤" }
			],
			residue_num: null,
			username: username,
			checker: username
		}
	];
};

// 监听每一行数据
watch(formRows.value, async (newselected, oldselected) => {
	for (let i = 0; i < formRows.value.length; i++) {
		const row = formRows.value[i];
		// row.xiangmu = org.code;
		if (row.ruku_danjia && row.ruku_num) {
			// 保留两位小数
			row.ruku_money = parseFloat((row.ruku_danjia * row.ruku_num).toString()).toFixed(2);
		} else {
			row.ruku_money = "";
		}
		row.residue_num = row.ruku_num;
		row.time_ruku = dateFormat(row.show_time);
	}
});
</script>

<style scoped>
.table-container {
	width: 100%;
	overflow-x: auto;
}

.form-table {
	width: 100%;
	border-collapse: collapse;
	margin-bottom: 20px;
}

.form-table th,
.form-table td {
	padding: 10px;
	text-align: center;
	border-bottom: 0px solid #ccc;
}

.form-table th {
	background-color: #f0f0f0;
}

.input-style {
	text-align: center;
}

.remove-button {
	background-color: #f40509;
	color: #fff;
	border: none;
	/* 设置元素的内边距 */
	padding: 0px 2px;
	cursor: pointer;
}

.add-button,
.reset-button,
.save-button {
	background-color: #008000;
	color: #fff;
	border: none;
	padding: 10px 10px;
	cursor: pointer;
}

.save-button {
	margin-top: auto;
	margin-left: auto;
	margin-right: auto;
}
</style>
