<template>
	<cl-crud ref="Crud">
		<el-row>
			<cl-refresh-btn />
			<cl-search-key />
			<cl-flex1 />
			<el-button type="primary" @click="downloadFileClick">导出</el-button>
		</el-row>
		<el-row>
			<cl-table ref=" Table" :row-style="tableRowClassName">
				<template #column-imgPath="{ scope }">
					<div @click="previewPic(scope.row)"></div>
				</template>
			</cl-table>
		</el-row>
		<!-- 分页 -->
		<el-row>
			<cl-flex1 />
			<cl-pagination />
		</el-row>
	</cl-crud>
</template>

<script lang="ts" setup>
import { useCrud, useTable } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useBase } from "/$/base";
import * as XLSX from "xlsx";
import randomColor from "randomcolor";

const { service } = useCool();
const { org } = useBase();

const color = randomColor({
	hue: "red",
	luminosity: "light"
});

// cl-table 配置
const Table = useTable({
	columns: [
		{
			label: "单据编号",
			prop: "danju_code",
			minWidth: 190
		},
		{
			label: "凭证号",
			prop: "pz_code",
			minWidth: 80
		},
		{
			label: "凭证类别",
			prop: "pz_type",
			minWidth: 100
		},
		{
			label: "凭证日期",
			prop: "pz_date",
			minWidth: 100
		},
		{
			label: "入库单号",
			prop: "danhao",
			minWidth: 190
		},
		{
			label: "出库单号",
			prop: "ck_danhao",
			minWidth: 190
		},
		{
			label: "项目",
			prop: "xiangmu",
			minWidth: 200
		},
		{
			label: "保管部门",
			prop: "baoguan_part",
			minWidth: 100
		},
		{
			label: "领用部门",
			prop: "lingyong_part",
			minWidth: 100
		},
		{
			label: "货物名称",
			prop: "huowu_name",
			minWidth: 250
		},
		{
			label: "货物单位",
			prop: "huowu_danwei",
			minWidth: 90
		},
		{ label: "入库人", prop: "checker", minWidth: 120 },
		{ label: "出库人", prop: "ck_person", minWidth: 120 },
		{ label: "审核人", prop: "checker", minWidth: 120 },
		{
			label: "入库信息",
			children: [
				{
					label: "入库时间",
					prop: "time_ruku",
					minWidth: 130
				},
				{
					label: "入库单价",
					prop: "ruku_danjia",
					minWidth: 90
				},
				{
					label: "入库数量",
					prop: "ruku_num",
					minWidth: 90
				},
				{
					label: "入库金额",
					prop: "ruku_money",
					minWidth: 120
				}
			]
		},
		{
			label: "出库信息",
			children: [
				{
					label: "出库时间",
					prop: "time_chuku",
					minWidth: 130
				},
				{
					label: "出库单价",
					prop: "chuku_danjia",
					minWidth: 90
				},
				{
					label: "出库数量",
					prop: "chuku_num",
					minWidth: 90
				},
				{
					label: "出库金额",
					prop: "chuku_money",
					minWidth: 120
				}
			]
		},
		{
			label: "单据状态",
			prop: "reject_reason",
			minWidth: 100
		}
	]
});

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.zhongtie.zhongtie_kucun,
		async onRefresh(params, { next, done, render }) {
			// 1 默认调用
			// const users = await service.base.comm.person().then(data => {
			//     return (data.username)
			// })
			const { list } = await next({ ...params, states: "审核通过", xiangmu: org.code });
		}
	},
	app => {
		app.refresh({});
	}
);

const downloadFileClick = async () => {
	const arr: any[] = [];
	const excel_name = org.name + "流水明细.xlsx";
	arr[0] = [
		"单据编号",
		"凭证号",
		"凭证类别",
		"凭证日期",
		"入库单号",
		"出库单号",
		"项目",
		"保管部门",
		"领用部门",
		"货物名称",
		"货物单位",
		"入库人",
		"出库人",
		"审核人",
		"入库时间",
		"入库单价",
		"入库数量",
		"入库金额",
		"出库时间",
		"出库单价",
		"出库数量",
		"出库金额"
	];
	const result = await service.zhongtie.zhongtie_kucun.list({
		xiangmu: org.code,
		states: "审核通过"
	});
	result.forEach(item => {
		arr.push([
			item.danju_code,
			item.pz_code,
			item.pz_type,
			item.pz_date,
			item.danhao,
			item.ck_danhao,
			item.xiangmu,
			item.baoguan_part,
			item.lingyong_part,
			item.huowu_name,
			item.huowu_danwei,
			item.checker,
			item.username,
			item.checker,
			item.time_ruku,
			item.ruku_danjia,
			item.ruku_num,
			item.ruku_money,
			item.time_chuku,
			item.chuku_danjia,
			item.chuku_num,
			item.chuku_money
		]);
	});
	//创建一个新的工作表
	const workBook = XLSX.utils.book_new();
	//将表格转换为工作表
	const worksheet = XLSX.utils.aoa_to_sheet(arr);
	//将工作表添加到workBook中
	XLSX.utils.book_append_sheet(workBook, worksheet, "Sheet1");
	XLSX.writeFile(workBook, excel_name);
};

//细分状态变颜色
function tableRowClassName({ row }) {
	if (row.reject_reason == "红冲数据") {
		return `background-color:${color}`;
	}
}

function previewPic(e: any) {
	console.log("e", e);
}
</script>
