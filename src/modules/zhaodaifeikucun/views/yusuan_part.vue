<template>
	<cl-crud ref="Crud">
		<el-row>
			<cl-refresh-btn />
			<!-- <cl-add-btn /> -->
			<!-- <input
				type="file"
				ref="upload"
				@change="handleFileChange"
				accept=".xlsx,.xls"
				v-show="false"
				id="fileyusuan"
			/>
			<el-button type="primary" @click="handleFileChange" style="margin-left: 10px"
				>上传文件</el-button
			>
			<el-button type="primary" @click="Download">下载模板</el-button> -->
			<!-- <el-button type="primary" @click="Editall">修改全部系数</el-button> -->
			<el-button type="primary" @click="Add" v-if="isAdd()">新增</el-button>
			<el-button type="primary" @click="Export">导出</el-button>
			<cl-flex1 />
			<cl-search-key />
		</el-row>
		<el-row>
			<cl-table ref="Table">
				<template #slot-edit="{ scope }">
					<el-button type="primary" @click="editxishu(scope.row)">编辑 </el-button>
				</template>
			</cl-table>
		</el-row>
		<el-dialog v-model="dialogFormVisible" width="30%" title="请填写系数" draggable>
			<el-form :model="form">
				<el-form-item label="系数：" :label-width="150" required>
					<el-input
						v-model="form.xishu"
						style="width: 50%"
						type="number"
						:step="0.01"
						:min="0"
						:max="1"
						@input="handleXishuInput"
					/>
				</el-form-item>
				<el-form-item label="新增系数：" :label-width="150" required>
					<el-input
						v-model="form.new_xishu"
						style="width: 50%"
						type="number"
						:step="0.01"
						:min="0"
						:max="1"
						@input="handleNewXishuInput"
					/>
				</el-form-item>
				<el-form-item label="控制类型：" :label-width="150" required>
					<el-select v-model="form.kongzhi_type" style="width: 50%">
						<el-option
							v-for="item in options"
							:key="item.value"
							:label="item.value"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
				<el-collapse v-model="activeNames" accordion>
					<el-collapse-item title="编辑其余信息" name="1">
						<el-form-item label="年份：" :label-width="100" required>
							<el-input
								v-model="form.year"
								style="width: 50%"
								type="number"
								:step="1"
								@input="handleYearInput"
							/>
						</el-form-item>
						<el-form-item label="项目：" :label-width="100">
							<el-input v-model="org.name" style="width: 90%" disabled />
						</el-form-item>
						<el-form-item label="部门名称：" :label-width="100" required>
							<el-tree-select
								v-model="form.part_name"
								:data="org.listpart"
								:props="{
									label: 'name',
									children: 'children',
									disabled: data => data.children && data.children.length > 0
								}"
								filterable
								check-strictly
								node-key="name"
								placeholder=" "
								style="width: 90%"
							/>
						</el-form-item>
						<el-form-item label="部门代码：" :label-width="100">
							<el-input v-model="form.part_code" style="width: 90%" disabled />
						</el-form-item>
						<el-form-item label="经费类别：" :label-width="100" required>
							<el-select v-model="form.jflb" style="width: 90%">
								<el-option
									v-for="item in org.listjflb"
									:key="item.value"
									:label="item.value"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
						<el-form-item label="预算大类：" :label-width="100" required>
							<el-select v-model="form.ysdl" style="width: 90%">
								<el-option
									v-for="item in org.listyskzdl"
									:key="item.value"
									:label="item.value"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
						<el-form-item label="年初预算：" :label-width="100" required>
							<el-input
								v-model="form.ncys"
								style="width: 50%"
								type="number"
								:step="0.01"
								:min="0"
								@input="handleNcysInput"
							/>
						</el-form-item>
						<el-form-item label="预算调整：" :label-width="100" required>
							<el-input
								v-model="form.ystz"
								style="width: 50%"
								type="number"
								:step="0.01"
								@input="handleYstzInput"
							/>
						</el-form-item>
						<el-form-item label="预算合计：" :label-width="100" required>
							<el-input v-model="form.jine" style="width: 50%" disabled />
						</el-form-item>
						<el-form-item label="预算控制数：" :label-width="100">
							<el-input v-model="form.yusuan_num" style="width: 50%" disabled />
						</el-form-item>
					</el-collapse-item>
				</el-collapse>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button type="primary" @click="submit">提交</el-button>
					<el-button type="primary" @click="cancel">取消</el-button>
				</span>
			</template>
		</el-dialog>
		<el-dialog v-model="dialogFormVisible_add" width="40%" title="新增数据" draggable>
			<el-form :model="form_add">
				<el-form-item label="年份：" :label-width="100" required>
					<el-input
						v-model="form_add.year"
						style="width: 20%"
						type="number"
						:step="1"
						@input="handleYearInput"
					/>
				</el-form-item>
				<el-form-item label="项目：" :label-width="100">
					<el-input v-model="org.name" style="width: 90%" disabled />
				</el-form-item>
				<el-form-item label="部门名称：" :label-width="100" required>
					<el-tree-select
						v-model="form_add.part_name"
						:data="org.listpart"
						:props="{
							label: 'name',
							children: 'children',
							disabled: data => data.children && data.children.length > 0
						}"
						filterable
						check-strictly
						node-key="name"
						placeholder=" "
						style="width: 90%"
					/>
				</el-form-item>
				<el-form-item label="部门代码：" :label-width="100">
					<el-input v-model="form_add.part_code" style="width: 90%" disabled />
				</el-form-item>
				<el-form-item label="经费类别：" :label-width="100" required>
					<el-select v-model="form_add.jflb" style="width: 90%">
						<el-option
							v-for="item in org.listjflb"
							:key="item.value"
							:label="item.value"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="预算大类：" :label-width="100" required>
					<el-select v-model="form_add.ysdl" style="width: 90%">
						<el-option
							v-for="item in org.listyskzdl"
							:key="item.value"
							:label="item.value"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="年初预算：" :label-width="100" required>
					<el-input
						v-model="form_add.ncys"
						style="width: 30%"
						type="number"
						:step="0.01"
						:min="0"
						@input="handleNcysInput"
					/>
				</el-form-item>
				<el-form-item label="预算调整：" :label-width="100" required>
					<el-input
						v-model="form_add.ystz"
						style="width: 30%"
						type="number"
						:step="0.01"
						@input="handleYstzInput"
					/>
				</el-form-item>
				<el-form-item label="系数：" :label-width="100" required>
					<el-input
						v-model="form_add.xishu"
						style="width: 30%"
						type="number"
						:step="0.01"
						:min="0"
						:max="1"
						@input="handleXishuInput"
					/>
				</el-form-item>
				<el-form-item label="新增系数：" :label-width="100" required>
					<el-input
						v-model="form_add.new_xishu"
						style="width: 30%"
						type="number"
						:step="0.01"
						:min="0"
						:max="1"
						@input="handleNewXishuInput"
					/>
				</el-form-item>
				<el-form-item label="预算合计：" :label-width="100" required>
					<el-input v-model="form_add.jine" style="width: 30%" disabled />
				</el-form-item>
				<el-form-item label="预算控制数：" :label-width="100">
					<el-input v-model="form_add.yusuan_num" style="width: 30%" disabled />
				</el-form-item>
				<el-form-item label="控制类型：" :label-width="100" required>
					<el-select v-model="form_add.kongzhi_type" style="width: 20%">
						<el-option
							v-for="item in options"
							:key="item.value"
							:label="item.value"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button type="primary" @click="submit">提交</el-button>
					<el-button type="primary" @click="cancel">取消</el-button>
				</span>
			</template>
		</el-dialog>
		<el-row>
			<cl-flex1 />
			<cl-pagination />
		</el-row>

		<!-- <cl-upsert ref="Upsert"></cl-upsert> -->
	</cl-crud>
</template>

<script lang="ts" setup>
import { useTable, useUpsert, useCrud } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { ref, reactive, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import * as XLSX from 'xlsx';
import { useBase } from '/$/base';

const { org, user } = useBase();
const part_code = ref('');
const yusuan_num = ref('');
const jine = ref('');
const username = user.info?.username;
// 默认展开折叠的序号
const activeNames = ref([]);

const { service } = useCool();

const rowId = ref();
// 编辑系数按钮
const dialogFormVisible = ref(false);
const form = reactive({
	year: null as number | null,
	ncys: null as number | null,
	ystz: null as number | null,
	xishu: null as number | null,
	jine: null as number | null,
	kongzhi_type: '',
	new_xishu: null as number | null,
	xm_name: null as string | null,
	part_code: null as string | null,
	part_name: null as string | null,
	jflb: '',
	ysdl: '',
	yusuan_num: null as number | null,
	yusuan_used: null as number | null,
	status: '',
	only_code: ''
});
// 新增数据
const dialogFormVisible_add = ref(false);
const form_add = reactive({
	year: null as number | null,
	ncys: null as number | null,
	ystz: null as number | null,
	xishu: null as number | null,
	jine: null as number | null,
	kongzhi_type: '',
	new_xishu: null as number | null,
	xm_name: null as string | null,
	part_code: null as string | null,
	part_name: null as string | null,
	jflb: '',
	ysdl: '',
	yusuan_num: null as number | null,
	yusuan_used: null as number | null,
	status: '',
	username: ''
});

const options = [
	{
		value: '强控',
		label: '强控'
	},
	{
		value: '软控',
		label: '软控'
	}
];

// let excelData = ref<{ index: number; data: any[] }[]>([]);
// let temporarylist: any = [];
// const url = `${import.meta.env.VITE_TEMPLATE_URL}/预算模板_按部门.xlsx`;

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.zhongtie.yusuan_part,
		async onRefresh(params, { next, done, render }) {
			// 1 默认调用
			const { list } = await next({ ...params, xm_name: org.name, status: '通过' });
		}
	},
	app => {
		app.refresh();
	}
);

// cl-table 配置
const Table = useTable({
	columns: [
		{
			prop: 'year',
			label: '年份',
			minWidth: 80
		},
		{
			prop: 'xm_name',
			label: '项目',
			minWidth: 300
		},
		{
			prop: 'part_code',
			label: '部门代码',
			minWidth: 250
		},
		{
			prop: 'part_name',
			label: '部门名称',
			minWidth: 220
		},
		{
			prop: 'jflb',
			label: '经费类别',
			minWidth: 120
		},
		{
			prop: 'ysdl',
			label: '预算控制大类',
			minWidth: 120
		},
		{
			prop: 'ncys',
			label: '年初预算',
			minWidth: 130
		},
		{
			prop: 'ystz',
			label: '预算调整',
			minWidth: 130
		},
		{
			prop: 'jine',
			label: '预算合计',
			minWidth: 130
		},
		{
			prop: 'xishu',
			label: '控制系数',
			minWidth: 100
		},
		{
			prop: 'new_xishu',
			label: '新增控制系数',
			minWidth: 120
		},
		{
			prop: 'yusuan_num',
			label: '预算控制数',
			minWidth: 130
		},
		{
			prop: 'yusuan_used',
			label: '预算占用数',
			minWidth: 130
		},
		{
			prop: 'kongzhi_type',
			label: '控制类型',
			minWidth: 90
		},
		{
			type: 'op',
			buttons: ['slot-edit']
			// width: 120
		}
	]
});

// cl-upsert 配置
const Upsert = useUpsert({
	dialog: {
		width: '800px'
	},

	items: [
		{
			prop: 'xm_name',
			label: '项目',
			span: 14,
			value: org.name,
			required: true,
			component: {
				name: 'el-input',
				props: {
					disabled: true, // 禁用输入
					placeholder: org.name // 设置占位符
				}
			}
		},
		{
			prop: 'part_name',
			label: '部门名称',
			span: 14,
			required: true,
			component: {
				name: 'el-tree-select',
				props: {
					data: org.listpart,
					checkStrictly: true,
					showCheckbox: false,
					defaultExpandAll: false,
					filterable: true,
					props: {
						children: 'children',
						label: 'name',
						value: 'name',
						disabled: data => data.children && data.children.length > 0
					}
				}
			}
		},
		{
			prop: 'part_code',
			label: '部门代码',
			span: 14,
			value: part_code,
			required: true,
			component: {
				name: 'el-input',
				props: {
					disabled: true, // 禁用输入
					placeholder: part_code // 设置占位符
				}
			}
		},
		{
			prop: 'jflb',
			label: '经费类别',
			span: 14,
			value: [],
			required: true,
			component: {
				name: 'el-select',
				options: org.listjflb,
				props: {
					// 添加筛选枚举项
					filterable: true
				}
			}
		},
		{
			prop: 'ysdl',
			label: '预算大类',
			span: 14,
			value: [],
			required: true,
			component: {
				name: 'el-select',
				options: org.listyskzdl,
				props: {
					// 添加筛选枚举项
					filterable: true
				}
			}
		},
		{
			prop: 'ncys',
			label: '年初预算',
			span: 12,
			required: true,
			component: {
				name: 'el-input',
				props: {
					type: 'number',
					precision: 2,
					step: 0.01,
					min: 0, // 设置最小值为0
					onChange: (value: string) => {
						// 如果是负数或非法值则返回空,否则保留两位小数
						const num = parseFloat(value);
						if (isNaN(num) || num < 0) {
							ElMessage({
								type: 'error',
								message: '输入年初预算不符合要求!'
							});
							return '';
						}
						return Number(num.toFixed(2));
					}
				}
			}
		},
		{
			prop: 'ystz',
			label: '预算调整',
			span: 12,
			required: true,
			value: 0,
			component: {
				name: 'el-input',
				props: {
					type: 'number',
					precision: 2,
					step: 0.01,
					onChange: (value: string) => {
						// 如果是负数或非法值则返回空,否则保留两位小数
						const num = parseFloat(value);
						if (isNaN(num)) {
							ElMessage({
								type: 'error',
								message: '输入预算调整不符合要求!'
							});
							return '';
						}
						return Number(num.toFixed(2));
					}
				}
			}
		},
		{
			prop: 'jine',
			label: '预算合计',
			span: 12,
			value: jine,
			required: true,
			component: {
				name: 'el-input',
				props: {
					disabled: true, // 禁用输入
					placeholder: jine // 设置占位符
				}
			}
		},
		{
			prop: 'xishu',
			label: '控制系数',
			span: 12,
			required: true,
			component: {
				name: 'el-input',
				props: {
					type: 'number',
					precision: 2,
					step: 0.01,
					min: 0,
					max: 1,
					onChange: (value: string) => {
						const num = parseFloat(value);
						if (isNaN(num) || num < 0 || num > 1) {
							ElMessage({
								type: 'error',
								message: '控制系数必须在0-1之间!'
							});
							return '';
						}
						return Number(num.toFixed(2));
					}
				}
			}
		},
		{
			prop: 'new_xishu',
			label: '新增系数',
			span: 12,
			required: true,
			component: {
				name: 'el-input',
				props: {
					type: 'number',
					precision: 2,
					step: 0.01,
					min: 0,
					max: 1,
					onChange: (value: string) => {
						const num = parseFloat(value);
						if (isNaN(num) || num < 0 || num > 1) {
							ElMessage({
								type: 'error',
								message: '新增系数必须在0-1之间!'
							});
							return '';
						}
						return Number(num.toFixed(2));
					}
				}
			}
		},
		{
			prop: 'yusuan_num',
			label: '预算控制数',
			span: 12,
			value: yusuan_num,
			required: true,
			component: {
				name: 'el-input',
				props: {
					disabled: true, // 禁用输入
					placeholder: yusuan_num // 设置占位符
				}
			}
		},
		{
			prop: 'kongzhi_type',
			label: '控制类型',
			span: 8,
			required: true,
			component: {
				name: 'el-select',
				options: [
					{
						value: '强控',
						label: '强控'
					},
					{
						value: '软控',
						label: '软控'
					}
				],
				props: {
					// 添加筛选枚举项
					filterable: true
				}
			}
		},
		{
			prop: 'status',
			span: 12,
			value: '待审核',
			component: {
				name: 'el-input',
				props: {
					style: {
						display: 'none'
					}
				}
			}
		},
		{
			prop: 'year',
			label: '年份',
			span: 8,
			required: true,
			value: new Date().getFullYear(),
			component: {
				name: 'el-input',
				props: {
					type: 'number',
					step: 1,
					onChange: (value: string) => {
						// 如果是负数或非法值则返回空,否则保留两位小数
						const num = parseInt(value);
						if (isNaN(num)) {
							ElMessage({
								type: 'error',
								message: '输入年份不符合要求!'
							});
							return '';
						}
						return Number(num);
					}
				}
			}
		},
		{
			prop: 'username',
			value: username,
			component: {
				name: 'el-input',
				props: {
					style: {
						display: 'none'
					}
				}
			}
		}
	],

	async onOpen() {
		Upsert.value?.setOptions(
			'part_name',
			org.listpart.map((part_name: any) => {
				return {
					label: part_name.name || '',
					value: part_name.name, // 或其他需要的值
					children: part_name.children
				};
			})
		);
	}
});

// 编辑系数
const editxishu = (row: any) => {
	rowId.value = row.id;
	form.xishu = row.xishu;
	form.jine = row.jine;
	form.ncys = row.ncys;
	form.ystz = row.ystz;
	form.new_xishu = row.new_xishu;
	form.kongzhi_type = row.kongzhi_type;
	form.xm_name = row.xm_name;
	form.part_code = row.part_code;
	form.part_name = row.part_name;
	form.jflb = row.jflb;
	form.ysdl = row.ysdl;
	form.yusuan_num = row.yusuan_num;
	form.yusuan_used = row.yusuan_used;
	form.status = row.status;
	form.year = row.year;
	form.only_code = row.only_code;
	dialogFormVisible.value = true;
};

function isAdd() {
	if (username == 'admin') {
		return true;
	} else {
		return false;
	}
}

// 新增数据
const Add = () => {
	dialogFormVisible_add.value = true;
	form_add.year = new Date().getFullYear();
	form_add.ystz = 0;
	form_add.new_xishu = 1;
	form_add.xishu = 1;
	form_add.status = '待审核';
	form_add.username = username || '';
	form_add.kongzhi_type = '软控';
};

// 导出excel
const Export = async () => {
	const arr: any[] = [];
	const excel_name = org.name + '_招待费预算.xlsx';
	arr[0] = [
		'项目',
		'部门代码',
		'部门名称',
		'经费类别',
		'预算控制大类',
		'年初预算',
		'预算调整',
		'预算合计',
		'控制系数',
		'新增控制系数',
		'预算控制数',
		'预算占用数',
		'控制类型'
	];
	const result = await service.zhongtie.yusuan_part.list({ xm_name: org.name });
	result.forEach(item => {
		arr.push([
			item.xm_name,
			item.part_code,
			item.part_name,
			item.jflb,
			item.ysdl,
			item.ncys,
			item.ystz,
			item.jine,
			item.xishu,
			item.new_xishu,
			item.yusuan_num,
			item.yusuan_used,
			item.kongzhi_type
		]);
	});
	//创建一个新的工作表
	const workBook = XLSX.utils.book_new();
	//将表格转换为工作表
	const worksheet = XLSX.utils.aoa_to_sheet(arr);
	//将工作表添加到workBook中

	XLSX.utils.book_append_sheet(workBook, worksheet, 'Sheet1');
	XLSX.writeFile(workBook, excel_name);
};

// 提交保存
const submit = async () => {
	let yusuan_num: number | null = null;
	// 新增数据
	if (rowId.value == undefined) {
		const only_code =
			org.name + form_add.part_code + form_add.jflb + form_add.ysdl + form_add.year;
		try {
			await service.zhongtie.yusuan_part
				.add({
					username: username,
					xishu: form_add.xishu,
					new_xishu: form_add.new_xishu,
					yusuan_num: form_add.yusuan_num,
					kongzhi_type: form_add.kongzhi_type,
					jine: form_add.jine,
					ncys: form_add.ncys,
					ystz: form_add.ystz,
					xm_name: org.name,
					part_name: form_add.part_name,
					part_code: form_add.part_code,
					jflb: form_add.jflb,
					ysdl: form_add.ysdl,
					yusuan_used: form_add.yusuan_used,
					status: '待审核',
					year: form_add.year,
					only_code: only_code
				})
				.then(() => {
					service.zhongtie.zdfysLog.add({
						username: username,
						xishu: form_add.xishu,
						new_xishu: form_add.new_xishu,
						yusuan_num: form_add.yusuan_num,
						kongzhi_type: form_add.kongzhi_type,
						jine: form_add.jine,
						ncys: form_add.ncys,
						ystz: form_add.ystz,
						xm_name: org.name,
						part_name: form_add.part_name,
						part_code: form_add.part_code,
						jflb: form_add.jflb,
						ysdl: form_add.ysdl,
						yusuan_used: form_add.yusuan_used,
						status: '待审核',
						year: form_add.year,
						only_code: only_code
					});
					Crud.value?.refresh();
				});
			ElMessage({
				type: 'success',
				message: '保存成功'
			});
		} catch (error) {
			ElMessage({
				type: 'error',
				message: '保存失败，请检查数据是否重复'
			});
		}
		form_add.ncys = null;
		form_add.jine = null;
		form_add.yusuan_num = null;
		dialogFormVisible_add.value = false;
	} else {
		yusuan_num = (form.xishu || 0) * (form.jine || 0) * (form.new_xishu || 0);
		await service.zhongtie.yusuan_part
			.update({
				id: rowId.value,
				xishu: form.xishu,
				new_xishu: form.new_xishu,
				yusuan_num: yusuan_num,
				kongzhi_type: form.kongzhi_type,
				jine: form.jine,
				ncys: form.ncys,
				ystz: form.ystz,
				xm_name: form.xm_name,
				part_name: form.part_name,
				part_code: form.part_code,
				jflb: form.jflb,
				ysdl: form.ysdl,
				yusuan_used: form.yusuan_used,
				year: form.year,
				status: '待审核',
				username: username,
				only_code: form.only_code
			})
			.then(() => {
				// 添加日志
				service.zhongtie.zdfysLog.add({
					username: username,
					xishu: form.xishu,
					new_xishu: form.new_xishu,
					yusuan_num: yusuan_num,
					kongzhi_type: form.kongzhi_type,
					jine: form.jine,
					ncys: form.ncys,
					ystz: form.ystz,
					xm_name: form.xm_name,
					part_name: form.part_name,
					part_code: form.part_code,
					jflb: form.jflb,
					ysdl: form.ysdl,
					yusuan_used: form.yusuan_used,
					status: '待审核',
					year: form.year,
					only_code: form.only_code
				});
				Crud.value?.refresh();
			});
		ElMessage({
			type: 'success',
			message: '保存成功'
		});
		// }
		dialogFormVisible.value = false;
	}
	rowId.value = undefined;
};

// 取消
const cancel = () => {
	dialogFormVisible_add.value = false;
	dialogFormVisible.value = false;
	rowId.value = undefined;
	form_add.ncys = null;
	form_add.jine = null;
	form_add.yusuan_num = null;
};

// 上传文件
// const handleFileChange = (event: Event) => {
// 	// 定义按钮
// 	const input = document.querySelector("#fileyusuan") as any;
// 	// 点击
// 	input.click();
// 	// console.log(input.value)
// 	const target = event.target as HTMLInputElement;
// 	const file = target.files?.[0];
// 	if (file) {
// 		excelData.value = [];
// 		temporarylist = [];
// 		const reader = new FileReader();
// 		reader.onload = e => {
// 			const data = new Uint8Array(e.target?.result as ArrayBuffer);
// 			const workbook = XLSX.read(data, { type: "array" });
// 			const worksheet = workbook.Sheets[workbook.SheetNames[0]];
// 			const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

// 			excelData.value = jsonData.map((row: any, index: number) => ({
// 				index: index + 1,
// 				data: row
// 			}));
// 		};
// 		reader.readAsArrayBuffer(file);
// 		ElMessage({
// 			type: "success",
// 			message: "上传文件成功!"
// 		});
// 		setTimeout(() => {
// 			ElMessageBox.confirm("请选择覆盖数据还是新增数据", {
// 				type: "warning",
// 				confirmButtonText: "覆盖",
// 				cancelButtonText: "新增",
// 				distinguishCancelAndClose: true
// 			})
// 				.then(() => {
// 					readExcelData("覆盖");
// 				})
// 				.catch(() => {
// 					readExcelData("新增");
// 				});
// 			// readExcelData()
// 			// 重置文件输入的元素
// 			input.value = "";
// 		}, 500);
// 	}
// };

// 读取excel上传数据库
// const readExcelData = async (key: any) => {
// 	if (excelData.value.length > 1) {
// 		const list_ys_part = await service.zhongtie.yusuan_part.list();
// 		let xm_name = -1;
// 		let part_code = -1;
// 		let part_name = -1;
// 		let jine = -1;
// 		let jflb = -1;
// 		let ysdl = -1;
// 		let kongzhi_type = -1;
// 		let xishu = -1;
// 		let new_xishu = -1;
// 		let yusuan_num = -1;
// 		let yusuan_used = -1;

// 		const Result = excelData.value[0].data;
// 		for (let i = 0; i < Result.length; i++) {
// 			if ("项目" == Result[i]) {
// 				xm_name = i;
// 			}
// 			if ("部门代码" == Result[i]) {
// 				part_code = i;
// 			}
// 			if ("部门名称" == Result[i]) {
// 				part_name = i;
// 			}
// 			if ("金额" == Result[i]) {
// 				jine = i;
// 			}
// 			if ("经费类别" == Result[i]) {
// 				jflb = i;
// 			}
// 			if ("预算控制大类" == Result[i]) {
// 				ysdl = i;
// 			}
// 			if ("控制类型" == Result[i]) {
// 				kongzhi_type = i;
// 			}
// 			if ("控制系数" == Result[i]) {
// 				xishu = i;
// 			}
// 			if ("新增控制系数" == Result[i]) {
// 				new_xishu = i;
// 			}
// 			if ("预算控制数" == Result[i]) {
// 				yusuan_num = i;
// 			}
// 			if ("预算占用数" == Result[i]) {
// 				yusuan_used = i;
// 			}
// 		}
// 		// 移除数组的第一个元素
// 		excelData.value.shift();
// 		if (key == "新增") {
// 			excelData.value.forEach((item: any) => {
// 				const result = item.data;
// 				// console.log('###########', result[name])
// 				if (result[xm_name] != undefined) {
// 					// 标记 判断数据库有无相同代码
// 					let flag_code = 0;
// 					for (let i = 0; i < list_ys_part.length; i++) {
// 						if (
// 							result[xm_name] == list_ys_part[i].xm_name &&
// 							result[part_code] == list_ys_part[i].part_code
// 						) {
// 							flag_code = 1;
// 							break;
// 						}
// 					}
// 					if (flag_code == 0) {
// 						temporarylist.push({
// 							xm_name: result[xm_name],
// 							part_code: result[part_code],
// 							part_name: result[part_name],
// 							jine: result[jine],
// 							jflb: result[jflb],
// 							ysdl: result[ysdl],
// 							kongzhi_type: result[kongzhi_type],
// 							xishu: result[xishu],
// 							new_xishu: result[new_xishu],
// 							yusuan_num: result[yusuan_num],
// 							yusuan_used: result[yusuan_used]
// 						});
// 					}
// 				}
// 			});
// 		} else {
// 			list_ys_part.forEach(async (por: any) => {
// 				const delete_por = por.id;
// 				await service.zhongtie.yusuan_part.delete({ ids: [delete_por] });
// 			});
// 			excelData.value.forEach((item: any) => {
// 				const result = item.data;
// 				temporarylist.push({
// 					xm_name: result[xm_name],
// 					part_code: result[part_code],
// 					part_name: result[part_name],
// 					jine: result[jine],
// 					jflb: result[jflb],
// 					ysdl: result[ysdl],
// 					kongzhi_type: result[kongzhi_type],
// 					xishu: result[xishu],
// 					new_xishu: result[new_xishu],
// 					yusuan_num: result[yusuan_num],
// 					yusuan_used: result[yusuan_used]
// 				});
// 			});
// 		}
// 	}
// 	// 读取 Excel 数据的逻辑
// 	const result_list: any[] = [];
// 	temporarylist.forEach((result: any) => {
// 		result_list.push({
// 			xm_name: result.xm_name,
// 			part_code: result.part_code,
// 			part_name: result.part_name,
// 			jine: result.jine,
// 			jflb: result.jflb,
// 			ysdl: result.ysdl,
// 			kongzhi_type: result.kongzhi_type,
// 			xishu: result.xishu,
// 			new_xishu: result.new_xishu,
// 			yusuan_num: result.yusuan_num,
// 			yusuan_used: result.yusuan_used
// 		});
// 	});
// 	if (result_list.length > 0) {
// 		service.zhongtie.yusuan_part.add(result_list);
// 		ElMessage({
// 			message: "保存成功！",
// 			type: "success"
// 		});
// 		setTimeout(() => {
// 			Crud.value?.refresh();
// 		}, 500);
// 	}
// 	// 将数据设置为空
// 	excelData = ref([]);
// 	temporarylist = [];
// };

// 下载模板
// const Download = () => {
// 	window.location.href = url;
// };

const handleYearInput = (value: string) => {
	const num = parseInt(value);
	if (isNaN(num)) {
		ElMessage({
			type: 'error',
			message: '年份必须是数字!'
		});
		form.year = null;
		return;
	}
	form.year = Number(num);
};

// 添加系数输入处理函数
const handleXishuInput = (value: string) => {
	const num = parseFloat(value);
	if (isNaN(num) || num < 0 || num > 1) {
		ElMessage({
			type: 'error',
			message: '系数必须在0-1之间!'
		});
		form.xishu = null;
		return;
	}
	form.xishu = Number(num.toFixed(2));
};

const handleNewXishuInput = (value: string) => {
	const num = parseFloat(value);
	if (isNaN(num) || num < 0 || num > 1) {
		ElMessage({
			type: 'error',
			message: '新增系数必须在0-1之间!'
		});
		form.new_xishu = null;
		return;
	}
	form.new_xishu = Number(num.toFixed(2));
};

const handleNcysInput = (value: string) => {
	const num = parseFloat(value);
	if (isNaN(num) || num < 0) {
		ElMessage({
			type: 'error',
			message: '年初预算必须大于等于0!'
		});
		form.ncys = null;
		return;
	}
	form.ncys = Number(num.toFixed(2));
};

const handleYstzInput = (value: string) => {
	const num = parseFloat(value);
	if (isNaN(num)) {
		ElMessage({
			type: 'error',
			message: '预算调整必须是数字!'
		});
		form.ystz = null;
		return;
	}
	form.ystz = Number(num.toFixed(2));
};

// watch(
// 	() => Upsert.value?.form.part_name,
// 	async newVal => {
// 		if (newVal && Upsert.value) {
// 			const findPartCode = (list: any[]): string => {
// 				for (const item of list) {
// 					if (item.name === newVal) {
// 						return item.value;
// 					}
// 					if (item.children) {
// 						const value = findPartCode(item.children);
// 						if (value) return value;
// 					}
// 				}
// 				return "";
// 			};
// 			const value = findPartCode(org.listpart);
// 			if (value) {
// 				Upsert.value.form.part_code = value;
// 			}
// 		}
// 	}
// );

// watch(
// 	[
// 		() => Upsert.value?.form.part_code,
// 		() => Upsert.value?.form.jflb,
// 		() => Upsert.value?.form.ysdl,
// 		() => Upsert.value?.form.year
// 	],
// 	([newPart_code, newJflb, newYsdl, Year]) => {
// 		if (Upsert.value && newPart_code && newJflb && newYsdl && Year) {
// 			const result = org.name + newPart_code + newJflb + newYsdl + Year;
// 			Upsert.value.form.only_code = result;
// 		}
// 	},
// 	{
// 		deep: true,
// 		immediate: true
// 	}
// );

// watch(
// 	[() => Upsert.value?.form.ncys, () => Upsert.value?.form.ystz],
// 	([newNcys, newnYstz]) => {
// 		console.log(newnYstz, newNcys);
// 		if (Upsert.value && newNcys) {
// 			const newncys = parseFloat(newNcys);
// 			const newnystz = parseFloat(newnYstz);
// 			console.log(newncys, newnystz);
// 			if (!isNaN(newncys) && !isNaN(newnystz)) {
// 				const result = (newncys + newnystz).toFixed(2);
// 				Upsert.value.form.jine = result;
// 			}
// 		}
// 	},
// 	{
// 		deep: true,
// 		immediate: true
// 	}
// );

// watch(
// 	[
// 		() => Upsert.value?.form.jine,
// 		() => Upsert.value?.form.xishu,
// 		() => Upsert.value?.form.new_xishu
// 	],
// 	([newJine, newXishu, newNewXishu]) => {
// 		if (Upsert.value && newJine && newXishu && newNewXishu) {
// 			const jine = parseFloat(newJine);
// 			const xishu = parseFloat(newXishu);
// 			const newXishuValue = parseFloat(newNewXishu);

// 			if (!isNaN(jine) && !isNaN(xishu) && !isNaN(newXishuValue)) {
// 				const result = (jine * xishu * newXishuValue).toFixed(2);
// 				Upsert.value.form.yusuan_num = result;
// 			}
// 		}
// 	},
// 	{
// 		deep: true,
// 		immediate: true
// 	}
// );

watch(
	// 监听 form、form_add 中 part_name 变化
	[() => form.part_name, () => form_add.part_name],
	([newVal, newValAdd]) => {
		if (newVal) {
			// 遍历组织列表找到对应的部门代码
			const findPartCode = (list: any[]): string => {
				for (const item of list) {
					if (item.name === newVal) {
						return item.value;
					}
					if (item.children) {
						const value = findPartCode(item.children);
						if (value) return value;
					}
				}
				return '';
			};

			const value = findPartCode(org.listpart);
			if (value) {
				form.part_code = value; // 移除可选链操作符
			}
		}
		if (newValAdd) {
			// 遍历组织列表找到对应的部门代码
			const findPartCode = (list: any[]): string => {
				for (const item of list) {
					if (item.name === newValAdd) {
						return item.value;
					}
					if (item.children) {
						const value = findPartCode(item.children);
						if (value) return value;
					}
				}
				return '';
			};

			const value = findPartCode(org.listpart);
			if (value) {
				form_add.part_code = value; // 移除可选链操作符
			}
		}
	},
	{
		deep: true, // 深度监听
		immediate: true // 立即执行
	}
);

watch(
	// 监听 form 中 ncys、ystz变化
	[() => form.ncys, () => form.ystz, () => form_add.ncys, () => form_add.ystz],
	([newNcys, newYstz, newNcysAdd, newYstzAdd]) => {
		if (newNcys && newYstz != null) {
			if (!isNaN(newNcys) && !isNaN(newYstz)) {
				form.jine = Number((newNcys + newYstz).toFixed(2));
			}
		}
		if (newNcysAdd && newYstzAdd != null) {
			if (!isNaN(newNcysAdd) && !isNaN(newYstzAdd)) {
				const ncys = parseFloat(newNcysAdd);
				const ystz = parseFloat(newYstzAdd);
				form_add.jine = Number((ncys + ystz).toFixed(2));
			}
		}
	},
	{
		deep: true, // 深度监听
		immediate: true // 立即执行
	}
);

watch(
	// 监听 form 中 jine、xishu、new_xishu 变化
	[
		() => form.jine,
		() => form.xishu,
		() => form.new_xishu,
		() => form_add.jine,
		() => form_add.xishu,
		() => form_add.new_xishu
	],
	([newJine, newXishu, newNewXishu, newJineAdd, newXishuAdd, newNewXishuAdd]) => {
		if (newJine && newXishu && newNewXishu) {
			if (!isNaN(newJine) && !isNaN(newXishu) && !isNaN(newNewXishu)) {
				form.yusuan_num = Number((newJine * newXishu * newNewXishu).toFixed(2));
			}
		}
		if (newJineAdd && newXishuAdd && newNewXishuAdd) {
			if (!isNaN(newJineAdd) && !isNaN(newXishuAdd) && !isNaN(newNewXishuAdd)) {
				form_add.yusuan_num = Number(
					(newJineAdd * newXishuAdd * newNewXishuAdd).toFixed(2)
				);
			}
		}
	},
	{
		deep: true, // 深度监听
		immediate: true // 立即执行
	}
);

// onMounted(async () => {
// 	// 获取预算控制大类和经费类别枚举项
// 	const list_yusuandalei = await service.dict.info.list({ typeId: 25 });
// 	org.setListyskzdl(
// 		list_yusuandalei.map(m => {
// 			return { value: m.name };
// 		})
// 	);

// 	const list_jingfeileibie = await service.dict.info.list({ typeId: 24 });
// 	org.setListjflb(
// 		list_jingfeileibie.map(m => {
// 			return { value: m.name };
// 		})
// 	);
// });
</script>
