<template>
	<cl-crud ref="Crud">
		<el-row>
			<cl-refresh-btn />
			<cl-flex1 />
			<cl-search-key />
		</el-row>
		<el-row>
			<cl-table ref="Table"> </cl-table>
		</el-row>

		<el-row>
			<cl-flex1 />
			<cl-pagination />
		</el-row>
	</cl-crud>
</template>

<script lang="ts" setup>
import { useTable, useCrud } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { ref, reactive } from "vue";
import { useBase } from "/$/base";

const { org, user } = useBase();
const { service } = useCool();

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.zhongtie.yusuan_part,
		async onRefresh(params, { next, done, render }) {
			// 1 默认调用
			const { list } = await next({ ...params, xm_name: org.name, status: "废止" });
		}
	},
	app => {
		app.refresh();
	}
);

// cl-table 配置
const Table = useTable({
	columns: [
		{
			prop: "year",
			label: "年份",
			minWidth: 80
		},
		{
			prop: "xm_name",
			label: "项目",
			minWidth: 300
		},
		{
			prop: "part_code",
			label: "部门代码",
			minWidth: 250
		},
		{
			prop: "part_name",
			label: "部门名称",
			minWidth: 220
		},
		{
			prop: "jflb",
			label: "经费类别",
			minWidth: 120
		},
		{
			prop: "ysdl",
			label: "预算控制大类",
			minWidth: 120
		},
		{
			prop: "ncys",
			label: "年初预算",
			minWidth: 130
		},
		{
			prop: "ystz",
			label: "预算调整",
			minWidth: 130
		},
		{
			prop: "jine",
			label: "预算合计",
			minWidth: 130
		},
		{
			prop: "xishu",
			label: "控制系数",
			minWidth: 100
		},
		{
			prop: "new_xishu",
			label: "新增控制系数",
			minWidth: 120
		},
		{
			prop: "yusuan_num",
			label: "预算控制数",
			minWidth: 130
		},
		{
			prop: "yusuan_used",
			label: "预算占用数",
			minWidth: 130
		},
		{
			prop: "kongzhi_type",
			label: "控制类型",
			minWidth: 90
		}
	]
});
</script>
