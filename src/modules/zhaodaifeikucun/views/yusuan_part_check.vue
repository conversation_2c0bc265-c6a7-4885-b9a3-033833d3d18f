<template>
	<cl-crud ref="Crud">
		<el-row>
			<cl-refresh-btn />
			<cl-flex1 />
			<cl-search-key />
		</el-row>
		<el-row>
			<cl-table ref="Table">
				<template #slot-pass="{ scope }">
					<el-button type="primary" @click="Pass(scope.row)">通过 </el-button>
				</template>
				<template #slot-reject="{ scope }">
					<el-button type="primary" @click="Reject(scope.row)">驳回 </el-button>
				</template>
			</cl-table>
			<el-dialog v-model="dialogFormVisible" title="请填写驳回原因" draggable>
				<el-form :model="form" center>
					<el-form-item :label-width="140">
						<el-input size="large" v-model="form.reject_reason" style="width: 80%" />
					</el-form-item>
				</el-form>
				<template #footer>
					<span class="dialog-footer">
						<el-button type="primary" @click="submit">提交</el-button>
						<el-button type="primary" @click="dialogFormVisible = false">
							取消
						</el-button>
					</span>
				</template>
			</el-dialog>
		</el-row>

		<el-row>
			<cl-flex1 />
			<cl-pagination />
		</el-row>
	</cl-crud>
</template>

<script lang="ts" setup>
import { useTable, useCrud } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { ref, reactive } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { useBase } from "/$/base";

const { org, user } = useBase();
const { service } = useCool();
const username = user.info?.username;
const rowId = ref();
const dialogFormVisible = ref(false);

const form = reactive({
	reject_reason: "",
	year: null as number | null,
	ncys: null as number | null,
	ystz: null as number | null,
	xishu: null as number | null,
	jine: null as number | null,
	kongzhi_type: "",
	new_xishu: null as number | null,
	xm_name: null as string | null,
	part_code: null as string | null,
	part_name: null as string | null,
	jflb: "",
	ysdl: "",
	yusuan_num: null as number | null,
	yusuan_used: null as number | null,
	status: "",
	only_code: ""
});

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.zhongtie.yusuan_part,
		async onRefresh(params, { next, done, render }) {
			// 1 默认调用
			const { list } = await next({ ...params, xm_name: org.name, status: "待审核" });
		}
	},
	app => {
		app.refresh();
	}
);

// cl-table 配置
const Table = useTable({
	columns: [
		{
			prop: "year",
			label: "年份",
			minWidth: 80
		},
		{
			prop: "xm_name",
			label: "项目",
			minWidth: 300
		},
		{
			prop: "part_code",
			label: "部门代码",
			minWidth: 250
		},
		{
			prop: "part_name",
			label: "部门名称",
			minWidth: 220
		},
		{
			prop: "jflb",
			label: "经费类别",
			minWidth: 120
		},
		{
			prop: "ysdl",
			label: "预算控制大类",
			minWidth: 120
		},
		{
			prop: "ncys",
			label: "年初预算",
			minWidth: 130
		},
		{
			prop: "ystz",
			label: "预算调整",
			minWidth: 130
		},
		{
			prop: "jine",
			label: "预算合计",
			minWidth: 130
		},
		{
			prop: "xishu",
			label: "控制系数",
			minWidth: 100
		},
		{
			prop: "new_xishu",
			label: "新增控制系数",
			minWidth: 120
		},
		{
			prop: "yusuan_num",
			label: "预算控制数",
			minWidth: 130
		},
		{
			prop: "yusuan_used",
			label: "预算占用数",
			minWidth: 130
		},
		{
			prop: "kongzhi_type",
			label: "控制类型",
			minWidth: 90
		},
		{
			type: "op",
			buttons: ["slot-pass", "slot-reject"]
		}
	]
});

// 通过
const Pass = (row: any) => {
	rowId.value = row.id;
	ElMessageBox.confirm("确认通过该数据", {
		type: "warning",
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		distinguishCancelAndClose: true
	}).then(async () => {
		await service.zhongtie.yusuan_part
			.update({
				id: rowId.value,
				status: "通过"
			})
			.then(() => {
				service.zhongtie.zdfysLog.add({
					username: username,
					xishu: row.xishu,
					new_xishu: row.new_xishu,
					yusuan_num: row.yusuan_num,
					kongzhi_type: row.kongzhi_type,
					jine: row.jine,
					ncys: row.ncys,
					ystz: row.ystz,
					xm_name: row.xm_name,
					part_name: row.part_name,
					part_code: row.part_code,
					jflb: row.jflb,
					ysdl: row.ysdl,
					yusuan_used: row.yusuan_used,
					status: "通过",
					year: row.year,
					only_code: row.only_code
				});
				Crud.value?.refresh();
			});
	});
};

// 驳回
const Reject = (row: any) => {
	rowId.value = row.id;
	form.reject_reason = row.reject_reason;
	form.xishu = row.xishu;
	form.jine = row.jine;
	form.ncys = row.ncys;
	form.ystz = row.ystz;
	form.new_xishu = row.new_xishu;
	form.kongzhi_type = row.kongzhi_type;
	form.xm_name = row.xm_name;
	form.part_code = row.part_code;
	form.part_name = row.part_name;
	form.jflb = row.jflb;
	form.ysdl = row.ysdl;
	form.yusuan_num = row.yusuan_num;
	form.yusuan_used = row.yusuan_used;
	form.status = row.status;
	form.year = row.year;
	form.only_code = row.only_code;
	dialogFormVisible.value = true;
};

// 驳回原因保存
const submit = () => {
	ElMessageBox.confirm("确认审核驳回？", "提示", {
		confirmButtonText: "确认",
		cancelButtonText: "取消",
		type: "warning"
	}).then(() => {
		service.zhongtie.yusuan_part
			.update({ id: rowId.value, mark: form.reject_reason, status: "驳回" })
			.then(() => {
				service.zhongtie.zdfysLog.add({
					username: username,
					id: rowId.value,
					xishu: form.xishu,
					new_xishu: form.new_xishu,
					yusuan_num: form.yusuan_num,
					kongzhi_type: form.kongzhi_type,
					jine: form.jine,
					ncys: form.ncys,
					ystz: form.ystz,
					xm_name: form.xm_name,
					part_name: form.part_name,
					part_code: form.part_code,
					jflb: form.jflb,
					ysdl: form.ysdl,
					yusuan_used: form.yusuan_used,
					status: "驳回",
					year: form.year,
					mark: form.reject_reason,
					only_code: form.only_code
				});
				ElMessage({
					type: "success",
					message: "已驳回!"
				});
				setTimeout(() => {
					dialogFormVisible.value = false;
					Crud.value?.refresh();
				}, 200);
			});
	});
};
</script>
