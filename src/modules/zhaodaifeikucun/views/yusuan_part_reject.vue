<template>
	<cl-crud ref="Crud">
		<el-row>
			<cl-refresh-btn />
			<!-- <cl-multi-delete-btn /> -->
			<cl-flex1 />
			<cl-search-key />
		</el-row>
		<el-row>
			<cl-table ref="Table">
				<template #slot-edit="{ scope }">
					<el-button type="primary" @click="editxishu(scope.row)">编辑</el-button>
				</template>
				<template #slot-cancel="{ scope }">
					<el-button type="danger" @click="cancelit(scope.row)">作废</el-button>
				</template>
			</cl-table>
		</el-row>
		<el-dialog v-model="dialogFormVisible" width="30%" title="请填写系数" draggable>
			<el-form :model="form">
				<el-form-item label="系数：" :label-width="150" required>
					<el-input
						v-model="form.xishu"
						style="width: 50%"
						type="number"
						:step="0.01"
						:min="0"
						:max="1"
						@input="handleXishuInput"
					/>
				</el-form-item>
				<el-form-item label="新增系数：" :label-width="150" required>
					<el-input
						v-model="form.new_xishu"
						style="width: 50%"
						type="number"
						:step="0.01"
						:min="0"
						:max="1"
						@input="handleNewXishuInput"
					/>
				</el-form-item>
				<el-form-item label="控制类型：" :label-width="150" required>
					<el-select v-model="form.kongzhi_type" style="width: 50%">
						<el-option
							v-for="item in options"
							:key="item.value"
							:label="item.value"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
				<el-collapse v-model="activeNames" accordion>
					<el-collapse-item title="编辑其余信息" name="1">
						<el-form-item label="年份：" :label-width="100" required>
							<el-input
								v-model="form.year"
								style="width: 50%"
								type="number"
								:step="1"
								@input="handleYearInput"
							/>
						</el-form-item>
						<el-form-item label="项目：" :label-width="100">
							<el-input v-model="org.name" style="width: 90%" disabled />
						</el-form-item>
						<el-form-item label="部门名称：" :label-width="100" required>
							<!-- <el-input v-model="form.part_name" style="width: 90%" /> -->
							<el-tree-select
								v-model="form.part_name"
								:data="org.listpart"
								:props="{
									label: 'name',
									children: 'children',
									disabled: data => data.children && data.children.length > 0
								}"
								filterable
								check-strictly
								node-key="name"
								placeholder=" "
								style="width: 90%"
							/>
						</el-form-item>
						<el-form-item label="部门代码：" :label-width="100">
							<el-input v-model="form.part_code" style="width: 90%" disabled />
						</el-form-item>
						<el-form-item label="经费类别：" :label-width="100" required>
							<el-select v-model="form.jflb" style="width: 90%">
								<el-option
									v-for="item in org.listjflb"
									:key="item.value"
									:label="item.value"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
						<el-form-item label="预算大类：" :label-width="100" required>
							<el-select v-model="form.ysdl" style="width: 90%">
								<el-option
									v-for="item in org.listyskzdl"
									:key="item.value"
									:label="item.value"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
						<el-form-item label="年初预算：" :label-width="100" required>
							<el-input
								v-model="form.ncys"
								style="width: 50%"
								type="number"
								:step="0.01"
								:min="0"
								@input="handleNcysInput"
							/>
						</el-form-item>
						<el-form-item label="预算调整：" :label-width="100" required>
							<el-input
								v-model="form.ystz"
								style="width: 50%"
								type="number"
								:step="0.01"
								@input="handleYstzInput"
							/>
						</el-form-item>
						<el-form-item label="预算合计：" :label-width="100" required>
							<el-input v-model="form.jine" style="width: 50%" disabled />
						</el-form-item>
						<el-form-item label="预算控制数：" :label-width="100">
							<el-input v-model="form.yusuan_num" style="width: 50%" disabled />
						</el-form-item>
					</el-collapse-item>
				</el-collapse>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button type="primary" @click="submit">提交</el-button>
					<el-button type="primary" @click="cancel">取消</el-button>
				</span>
			</template>
		</el-dialog>
		<el-row>
			<cl-flex1 />
			<cl-pagination />
		</el-row>
	</cl-crud>
</template>

<script lang="ts" setup>
import { useTable, useUpsert, useCrud } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { ref, reactive, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

import { useBase } from '/$/base';

const { org, user } = useBase();

const username = user.info?.username;
// 默认展开折叠的序号
const activeNames = ref([]);

const { service } = useCool();

const rowId = ref();
// 编辑系数按钮
const dialogFormVisible = ref(false);
const form = reactive({
	year: null as number | null,
	xishu: null as number | null,
	ncys: null as number | null,
	ystz: null as number | null,
	jine: null as number | null,
	kongzhi_type: '',
	new_xishu: null as number | null,
	xm_name: null as string | null,
	part_code: null as string | null,
	part_name: null as string | null,
	jflb: '',
	ysdl: '',
	yusuan_num: null as number | null,
	yusuan_used: null as number | null,
	status: '',
	only_code: ''
});

const options = [
	{
		value: '强控',
		label: '强控'
	},
	{
		value: '软控',
		label: '软控'
	}
];

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.zhongtie.yusuan_part,
		async onRefresh(params, { next, done, render }) {
			// 1 默认调用
			if (username == 'admin') {
				const { list } = await next({ ...params, xm_name: org.name, status: '驳回' });
			} else {
				const { list } = await next({
					...params,
					xm_name: org.name,
					status: '驳回',
					username: username
				});
			}
		}
	},
	app => {
		app.refresh();
	}
);

// cl-table 配置
const Table = useTable({
	columns: [
		{
			prop: 'year',
			label: '年份',
			minWidth: 80
		},
		{
			prop: 'xm_name',
			label: '项目',
			minWidth: 300
		},
		{
			prop: 'part_code',
			label: '部门代码',
			minWidth: 250
		},
		{
			prop: 'part_name',
			label: '部门名称',
			minWidth: 220
		},
		{
			prop: 'jflb',
			label: '经费类别',
			minWidth: 120
		},
		{
			prop: 'ysdl',
			label: '预算控制大类',
			minWidth: 120
		},
		{
			prop: 'ncys',
			label: '年初预算',
			minWidth: 130
		},
		{
			prop: 'ystz',
			label: '预算调整',
			minWidth: 130
		},
		{
			prop: 'jine',
			label: '预算合计',
			minWidth: 130
		},
		{
			prop: 'xishu',
			label: '控制系数',
			minWidth: 100
		},
		{
			prop: 'new_xishu',
			label: '新增控制系数',
			minWidth: 120
		},
		{
			prop: 'yusuan_num',
			label: '预算控制数',
			minWidth: 130
		},
		{
			prop: 'yusuan_used',
			label: '预算占用数',
			minWidth: 130
		},
		{
			prop: 'kongzhi_type',
			label: '控制类型',
			minWidth: 90
		},
		{
			prop: 'mark',
			label: '驳回原因',
			minWidth: 90
		},
		{
			type: 'op',
			buttons: username == 'admin' ? ['slot-edit', 'slot-cancel'] : ['slot-edit']
		}
	]
});

// 编辑系数
const editxishu = (row: any) => {
	rowId.value = row.id;
	form.xishu = row.xishu;
	form.jine = row.jine;
	form.ncys = row.ncys;
	form.ystz = row.ystz;
	form.new_xishu = row.new_xishu;
	form.kongzhi_type = row.kongzhi_type;
	form.xm_name = row.xm_name;
	form.part_code = row.part_code;
	form.part_name = row.part_name;
	form.jflb = row.jflb;
	form.ysdl = row.ysdl;
	form.yusuan_num = row.yusuan_num;
	form.yusuan_used = row.yusuan_used;
	form.status = row.status;
	form.year = row.year;
	form.only_code = row.only_code;
	dialogFormVisible.value = true;
};

// 废止数据
const cancelit = (row: any) => {
	rowId.value = row.id;
	ElMessageBox.confirm('确认废止该数据', {
		type: 'warning',
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		distinguishCancelAndClose: true
	}).then(async () => {
		await service.zhongtie.yusuan_part
			// .delete({
			// 	ids: [rowId.value]
			// })
			.update({
				id: rowId.value,
				status: '废止'
			})
			.then(() => {
				service.zhongtie.zdfysLog.add({
					username: username,
					xishu: row.xishu,
					new_xishu: row.new_xishu,
					yusuan_num: row.yusuan_num,
					kongzhi_type: row.kongzhi_type,
					jine: row.jine,
					ncys: row.ncys,
					ystz: row.ystz,
					xm_name: row.xm_name,
					part_name: row.part_name,
					part_code: row.part_code,
					jflb: row.jflb,
					ysdl: row.ysdl,
					yusuan_used: row.yusuan_used,
					status: '作废',
					year: row.year,
					only_code: row.only_code
				});
				Crud.value?.refresh();
			});
		ElMessage({
			type: 'success',
			message: '作废成功'
		});
	});
};

// 提交保存
const submit = async () => {
	let yusuan_num: number | null = null;
	// 一键修改的逻辑
	if (rowId.value == undefined) {
		console.log('待补充');
	} else {
		yusuan_num = (form.xishu || 0) * (form.jine || 0) * (form.new_xishu || 0);
		await service.zhongtie.yusuan_part
			.update({
				id: rowId.value,
				xishu: form.xishu,
				new_xishu: form.new_xishu,
				yusuan_num: yusuan_num,
				kongzhi_type: form.kongzhi_type,
				jine: form.jine,
				ncys: form.ncys,
				ystz: form.ystz,
				xm_name: form.xm_name,
				part_name: form.part_name,
				part_code: form.part_code,
				jflb: form.jflb,
				ysdl: form.ysdl,
				yusuan_used: form.yusuan_used,
				status: '待审核',
				mark: '',
				year: form.year,
				only_code: form.only_code
			})
			.then(() => {
				// 添加日志
				service.zhongtie.zdfysLog.add({
					username: username,
					xishu: form.xishu,
					new_xishu: form.new_xishu,
					yusuan_num: yusuan_num,
					kongzhi_type: form.kongzhi_type,
					ncys: form.ncys,
					ystz: form.ystz,
					jine: form.jine,
					xm_name: form.xm_name,
					part_name: form.part_name,
					part_code: form.part_code,
					jflb: form.jflb,
					ysdl: form.ysdl,
					yusuan_used: form.yusuan_used,
					status: '待审核',
					mark: '',
					year: form.year,
					only_code: form.only_code
				});
				Crud.value?.refresh();
			});
		ElMessage({
			type: 'success',
			message: '保存成功'
		});
	}
	dialogFormVisible.value = false;
};

// 取消
const cancel = () => {
	dialogFormVisible.value = false;
	rowId.value = undefined;
};

const handleYearInput = (value: string) => {
	const num = parseInt(value);
	if (isNaN(num)) {
		ElMessage({
			type: 'error',
			message: '年份必须是数字!'
		});
		form.year = null;
		return;
	}
	form.year = Number(num);
};

// 添加系数输入处理函数
const handleXishuInput = (value: string) => {
	const num = parseFloat(value);
	if (isNaN(num) || num < 0 || num > 1) {
		ElMessage({
			type: 'error',
			message: '系数必须在0-1之间!'
		});
		form.xishu = null;
		return;
	}
	form.xishu = Number(num.toFixed(2));
};

const handleNewXishuInput = (value: string) => {
	const num = parseFloat(value);
	if (isNaN(num) || num < 0 || num > 1) {
		ElMessage({
			type: 'error',
			message: '系数必须在0-1之间!'
		});
		form.new_xishu = null;
		return;
	}
	form.new_xishu = Number(num.toFixed(2));
};

const handleNcysInput = (value: string) => {
	const num = parseFloat(value);
	if (isNaN(num) || num < 0) {
		ElMessage({
			type: 'error',
			message: '年初预算必须大于等于0!'
		});
		form.ncys = null;
		return;
	}
	form.ncys = Number(num.toFixed(2));
};

const handleYstzInput = (value: string) => {
	const num = parseFloat(value);
	if (isNaN(num)) {
		ElMessage({
			type: 'error',
			message: '预算调整必须是数字!'
		});
		form.ystz = null;
		return;
	}
	form.ystz = Number(num.toFixed(2));
};

// 监听 form 中 part_name 变化
watch(
	() => form.part_name,
	async newVal => {
		if (newVal) {
			// 遍历组织列表找到对应的部门代码
			const findPartCode = (list: any[]): string => {
				for (const item of list) {
					if (item.name === newVal) {
						return item.value;
					}
					if (item.children) {
						const value = findPartCode(item.children);
						if (value) return value;
					}
				}
				return '';
			};

			const value = findPartCode(org.listpart);
			if (value) {
				form.part_code = value; // 移除可选链操作符
			}
		}
	}
);

// 监听 form 中 ncys、ystz变化
watch(
	[() => form.ncys, () => form.ystz],
	([newNcys, newYstz]) => {
		if (newNcys && newYstz != null) {
			if (!isNaN(newNcys) && !isNaN(newYstz)) {
				// 更新预算控制数
				form.jine = Number((newNcys + newYstz).toFixed(2));
			}
		}
	},
	{
		deep: true, // 深度监听
		immediate: true // 立即执行
	}
);

// 监听 form 中 jine、xishu、new_xishu 变化
watch(
	[() => form.jine, () => form.xishu, () => form.new_xishu],
	([newJine, newXishu, newNewXishu]) => {
		if (newJine && newXishu && newNewXishu) {
			if (!isNaN(newJine) && !isNaN(newXishu) && !isNaN(newNewXishu)) {
				// 更新预算控制数
				form.yusuan_num = Number((newJine * newXishu * newNewXishu).toFixed(2));
			}
		}
	},
	{
		deep: true, // 深度监听
		immediate: true // 立即执行
	}
);
</script>
