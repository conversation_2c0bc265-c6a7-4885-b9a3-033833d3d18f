<template>
	<cl-crud ref="Crud">
		<el-row>
			<cl-refresh-btn />
			<cl-multi-delete-btn />
			<input
				type="file"
				ref="upload"
				@change="handleFileChange"
				accept=".xlsx,.xls"
				v-show="false"
				id="fileyusuan"
			/>
			<el-button type="primary" @click="handleFileChange" style="margin-left: 10px"
				>上传文件</el-button
			>
			<el-button type="primary" @click="Download">下载模板</el-button>
			<el-button type="primary" @click="Editall">修改全部系数</el-button>
			<cl-flex1 />
			<cl-search-key />
		</el-row>
		<el-row>
			<cl-table ref="Table">
				<template #slot-edit="{ scope }">
					<el-button type="primary" @click="editxishu(scope.row)">编辑系数 </el-button>
				</template>
			</cl-table>
		</el-row>
		<el-dialog v-model="dialogFormVisible" width="30%" title="请填写系数" draggable>
			<el-form :model="form">
				<el-form-item label="系数：" :label-width="150">
					<el-input v-model="form.xishu" style="width: 50%" />
				</el-form-item>
				<el-form-item label="新增系数：" :label-width="150">
					<el-input v-model="form.new_xishu" style="width: 50%" />
				</el-form-item>
				<el-form-item label="控制类型：" :label-width="150">
					<el-select v-model="form.kongzhi_type" style="width: 50%">
						<el-option
							v-for="item in options"
							:key="item.value"
							:label="item.value"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button type="primary" @click="submit">提交</el-button>
					<el-button type="primary" @click="cancel">取消</el-button>
				</span>
			</template>
		</el-dialog>
		<el-row>
			<cl-flex1 />
			<cl-pagination />
		</el-row>
	</cl-crud>
</template>

<script lang="ts" setup>
import { useTable, useCrud } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import * as XLSX from 'xlsx';

const { service } = useCool();

const rowId = ref();
const dialogFormVisible = ref(false);
const form = reactive({
	xishu: null,
	jine: null,
	kongzhi_type: '',
	new_xishu: null
});

const options = [
	{
		value: '强控',
		label: '强控'
	},
	{
		value: '软控',
		label: '软控'
	}
];

let excelData = ref<{ index: number; data: any[] }[]>([]);
let temporarylist: any = [];
// let url = 'http://***************:9001/public/uploads/预算模板_按项目.xlsx';
const url = `${import.meta.env.VITE_TEMPLATE_URL}/预算模板_按项目.xlsx`;
// cl-crud 配置
const Crud = useCrud(
	{
		service: service.zhongtie.yusuan_xm
	},
	app => {
		app.refresh();
	}
);

// cl-table 配置
const Table = useTable({
	columns: [
		{
			type: 'selection',
			width: 60
		},
		{
			prop: 'xiangmu',
			label: '项目',
			minWidth: 220
		},
		{
			prop: 'name',
			label: '名称',
			minWidth: 220
		},
		{
			prop: 'jflb',
			label: '经费类别',
			minWidth: 120
		},
		{
			prop: 'ysdl',
			label: '预算控制大类',
			minWidth: 120
		},
		{
			prop: 'jine',
			label: '金额',
			minWidth: 130
		},
		{
			prop: 'xishu',
			label: '控制系数',
			minWidth: 100
		},
		{
			prop: 'new_xishu',
			label: '新增控制系数',
			minWidth: 120
		},
		{
			prop: 'yusuan_num',
			label: '预算控制数',
			minWidth: 130
		},
		{
			prop: 'yusuan_used',
			label: '预算占用数',
			minWidth: 130
		},
		{
			prop: 'kongzhi_type',
			label: '控制类型',
			minWidth: 90
		},
		{
			type: 'op',
			buttons: ['slot-edit'],
			width: 120
		}
	]
});

// 编辑系数
const editxishu = (row: any) => {
	rowId.value = row.id;
	form.xishu = row.xishu;
	form.jine = row.jine;
	form.new_xishu = row.new_xishu;
	form.kongzhi_type = row.kongzhi_type;
	dialogFormVisible.value = true;
};

// 一键编辑系数
const Editall = () => {
	dialogFormVisible.value = true;
};

// 提交保存
const submit = async () => {
	let yusuan_num: number | null = null;
	// 一键修改的逻辑
	if (rowId.value == undefined) {
		const result = await service.zhongtie.yusuan_xm.list();
		if (form.xishu == null && form.new_xishu == null) {
			result.forEach(async (item: any) => {
				let r_money: number | null = null;
				rowId.value = item.id;
				r_money = item.jine;
				await service.zhongtie.yusuan_xm
					.update({
						id: rowId.value,
						xishu: form.xishu,
						new_xishu: form.new_xishu,
						yusuan_num: r_money,
						kongzhi_type: form.kongzhi_type
					})
					.then(() => {
						Crud.value?.refresh();
					});
			});
		} else if (form.xishu == null) {
			result.forEach(async (item: any) => {
				let r_money: number | null = null;
				rowId.value = item.id;
				r_money = (item.jine || 0) * (form.new_xishu || 0);
				await service.zhongtie.yusuan_xm
					.update({
						id: rowId.value,
						xishu: form.xishu,
						new_xishu: form.new_xishu,
						yusuan_num: r_money,
						kongzhi_type: form.kongzhi_type
					})
					.then(() => {
						Crud.value?.refresh();
					});
			});
		} else if (form.new_xishu == null) {
			result.forEach(async (item: any) => {
				let r_money: number | null = null;
				rowId.value = item.id;
				r_money = item.jine * (form.new_xishu || 0);
				await service.zhongtie.yusuan_xm
					.update({
						id: rowId.value,
						xishu: form.xishu,
						new_xishu: form.new_xishu,
						yusuan_num: r_money,
						kongzhi_type: form.kongzhi_type
					})
					.then(() => {
						Crud.value?.refresh();
					});
			});
		} else {
			result.forEach(async (item: any) => {
				let r_money: number | null = null;
				rowId.value = item.id;
				r_money = (form.xishu || 0) * (item.jine || 0) * (form.new_xishu || 0);
				await service.zhongtie.yusuan_xm
					.update({
						id: rowId.value,
						xishu: form.xishu,
						new_xishu: form.new_xishu,
						yusuan_num: r_money,
						kongzhi_type: form.kongzhi_type
					})
					.then(() => {
						Crud.value?.refresh();
					});
			});
		}
	} else {
		if (form.xishu == null && form.new_xishu == null) {
			yusuan_num = form.jine;
		} else if (form.xishu == null) {
			yusuan_num = (form.jine || 0) * (form.new_xishu || 0);
		} else if (form.new_xishu == null) {
			yusuan_num = (form.xishu || 0) * (form.jine || 0);
		} else {
			yusuan_num = (form.xishu || 0) * (form.jine || 0) * (form.new_xishu || 0);
		}
		await service.zhongtie.yusuan_xm
			.update({
				id: rowId.value,
				xishu: form.xishu,
				new_xishu: form.new_xishu,
				yusuan_num: yusuan_num,
				kongzhi_type: form.kongzhi_type
			})
			.then(() => {
				Crud.value?.refresh();
			});
	}
	dialogFormVisible.value = false;
	rowId.value = undefined;
	form.xishu = null;
	form.jine = null;
	form.new_xishu = null;
	form.kongzhi_type = '';
};

// 取消
const cancel = () => {
	dialogFormVisible.value = false;
	rowId.value = undefined;
	form.xishu = null;
	form.jine = null;
	form.new_xishu = null;
	form.kongzhi_type = '';
};

const handleFileChange = (event: Event) => {
	// 定义按钮
	const input = document.querySelector('#fileyusuan') as any;
	// 点击
	input.click();
	console.log(input.value);
	const target = event.target as HTMLInputElement;
	const file = target.files?.[0];
	if (file) {
		excelData.value = [];
		temporarylist = [];
		const reader = new FileReader();
		reader.onload = e => {
			const data = new Uint8Array(e.target?.result as ArrayBuffer);
			const workbook = XLSX.read(data, { type: 'array' });
			const worksheet = workbook.Sheets[workbook.SheetNames[0]];
			const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

			excelData.value = jsonData.map((row: any, index: number) => ({
				index: index + 1,
				data: row
			}));
		};
		reader.readAsArrayBuffer(file);
		ElMessage({
			type: 'success',
			message: '上传文件成功!'
		});
		setTimeout(() => {
			readExcelData();
			// 重置文件输入的元素
			input.value = '';
		}, 500);
	}
};

const readExcelData = async () => {
	if (excelData.value.length > 1) {
		const list_ys_part = await service.zhongtie.yusuan_xm.list();
		let xiangmu = -1;
		let name = -1;
		let jine = -1;
		let jflb = -1;
		let ysdl = -1;

		const Result = excelData.value[0].data;
		for (let i = 0; i < Result.length; i++) {
			if ('项目' == Result[i]) {
				xiangmu = i;
			}
			if ('名称' == Result[i]) {
				name = i;
			}
			if ('金额' == Result[i]) {
				jine = i;
			}
			if ('经费类别' == Result[i]) {
				jflb = i;
			}
			if ('预算大类' == Result[i]) {
				ysdl = i;
			}
		}
		excelData.value.shift();
		excelData.value.forEach((item: any) => {
			const result = item.data;
			// 标记 判断数据库有无相同代码
			let flag_code = 0;
			for (let i = 0; i < list_ys_part.length; i++) {
				if (result[xiangmu] == list_ys_part[i].xiangmu) {
					flag_code = 1;
					break;
				}
			}
			if (flag_code == 0) {
				temporarylist.push({
					xiangmu: result[xiangmu],
					name: result[name],
					jine: result[jine],
					jflb: result[jflb],
					ysdl: result[ysdl]
				});
			}
		});
	}
	// 读取 Excel 数据的逻辑
	const result_list: any[] = [];
	temporarylist.forEach((result: any) => {
		result_list.push({
			xiangmu: result.xiangmu,
			name: result.name,
			jine: result.jine,
			jflb: result.jflb,
			ysdl: result.ysdl
		});
	});
	if (result_list.length > 0) {
		service.zhongtie.yusuan_xm.add(result_list);
		ElMessage({
			message: '保存成功！',
			type: 'success'
		});
		setTimeout(() => {
			Crud.value?.refresh();
		}, 500);
	}
	// 将数据设置为空
	excelData = ref([]);
	temporarylist = [];
};

const Download = () => {
	window.location.href = url;
};
</script>
