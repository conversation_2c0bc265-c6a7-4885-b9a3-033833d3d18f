<template>
	<div :ref="setRefs('editor')" class="cl-editor-wang" :class="{ disabled }">
		<!-- 工具栏 -->
		<toolbar v-if="!preview" :editor="Editor" :defaultConfig="toolbarConfig" :mode="mode" />

		<!-- 编辑框 -->
		<editor-input
			v-model="value"
			:default-config="editorConfig"
			:mode="mode"
			:style="{
				height: parsePx(height)
			}"
			@on-created="onCreated"
			@on-focus="onFocus"
			@on-blur="onBlur"
			@on-change="onChange"
			class="editor-content"
		/>

		<!-- 文件空间 - 视频 -->
		<cl-upload-space
			:ref="setRefs('video')"
			accept="video/*"
			:show-btn="false"
			@confirm="onFileConfirm"
		/>

		<!-- 文件空间 - 图片 -->
		<cl-upload-space
			v-if="isSpace"
			:ref="setRefs('image')"
			accept="image/*"
			:show-btn="false"
			@confirm="onFileConfirm"
		/>

		<!-- 直接上传 - 图片 -->
		<div v-else class="upload-inner">
			<cl-upload :ref="setRefs('image')" accept="image/*" @success="onFileConfirm" />
		</div>
	</div>
</template>

<script lang="ts" setup>
defineOptions({
	name: 'cl-editor-wang'
});

import { onBeforeUnmount, ref, shallowRef, watch, type PropType, computed } from 'vue';
import { Editor as EditorInput, Toolbar } from '@wangeditor/editor-for-vue';
import { type IEditorConfig } from '@wangeditor/editor';
import { useCool } from '/@/cool';
import { parsePx } from '/@/cool/utils';
import { isArray } from 'lodash-es';
import '@wangeditor/editor/dist/css/style.css';
import { useI18n } from 'vue-i18n';
import { useUpload } from '/#/upload';

const props = defineProps({
	modelValue: String,
	// 模式
	mode: {
		type: String as PropType<'default' | 'simple'>,
		default: 'default'
	},
	// 高度
	height: {
		type: [String, Number],
		default: 500
	},
	// 禁用
	disabled: Boolean,
	// 是否预览模式
	preview: Boolean,
	// 是否使用文件空间
	isSpace: {
		type: Boolean,
		default: false
	},
	filePath: String,
	// 是否使用所有工具栏
	isAll: {
		type: Boolean,
		default: true
	}
});

const emit = defineEmits(['update:modelValue', 'change', 'focus', 'blur']);

const { refs, setRefs } = useCool();
const { t } = useI18n();
const { toUpload } = useUpload();

// 编辑器
const Editor = shallowRef();

// 内容
const value = ref();

// 监听内容变化
watch(
	() => props.modelValue,
	val => {
		value.value = val || '';
		// 确保编辑器已创建并且有内容时，重新设置HTML
		if (Editor.value && val) {
			Editor.value.setHtml(val);
		}
	},
	{
		immediate: true
	}
);

// 临时
const temp: { insertFn: ((url: string) => void) | null } = {
	insertFn: null
};
console.log('props.isAll', props.isAll);
const toolbarConfig = computed(() => {
	if (!props.isAll) {
		console.log('已选择使用所有工具栏');
		return {
			toolbarKeys: [
				'bold',
				'italic',
				'underline',
				'fontSize',
				'color',
				'bgColor',
				'undo',
				'redo',
				'fullScreen',
				'justifyLeft',
				'justifyRight',
				'justifyCenter',
				'justifyJustify',
				'indent',
				'delIndent',
				'uploadImage'
			]
		};
	} else {
		return {
			excludeKeys: []
		};
	}
});

// 图片压缩函数
async function compressImage(
	file: File,
	maxSize = 150 * 1024, // 150KB
	options = {
		maxWidth: 1920,
		maxHeight: 1080,
		initialQuality: 0.9,
		minQuality: 0.5,
		scaleRatio: 0.9
	}
): Promise<File> {
	// 如果文件小于maxSize，直接返回原文件
	if (file.size <= maxSize) {
		return file;
	}

	// 压缩函数
	const compress = (width: number, height: number, quality: number): Promise<File> => {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.readAsDataURL(file);
			reader.onload = e => {
				const img = new Image();
				img.src = e.target?.result as string;
				img.onload = () => {
					const canvas = document.createElement('canvas');
					canvas.width = width;
					canvas.height = height;
					const ctx = canvas.getContext('2d');
					ctx?.drawImage(img, 0, 0, width, height);
					canvas.toBlob(
						blob => {
							if (!blob) {
								reject(new Error('Canvas to Blob failed'));
								return;
							}
							const newFile = new File([blob], file.name, {
								type: 'image/jpeg',
								lastModified: Date.now()
							});
							resolve(newFile);
						},
						'image/jpeg',
						quality
					);
				};
				img.onerror = reject;
			};
			reader.onerror = reject;
		});
	};

	// 获取图片原始尺寸
	const dimensions = await new Promise<{ width: number; height: number }>((resolve, reject) => {
		const reader = new FileReader();
		reader.readAsDataURL(file);
		reader.onload = e => {
			const img = new Image();
			img.src = e.target?.result as string;
			img.onload = () => {
				resolve({
					width: img.width,
					height: img.height
				});
			};
			img.onerror = reject;
		};
	});

	let currentWidth = dimensions.width;
	let currentHeight = dimensions.height;
	let quality = options.initialQuality;
	let compressedFile = file;

	// 第一步：如果尺寸超过限制，先进行尺寸压缩
	if (currentWidth > options.maxWidth || currentHeight > options.maxHeight) {
		const ratio = Math.min(options.maxWidth / currentWidth, options.maxHeight / currentHeight);
		currentWidth = Math.floor(currentWidth * ratio);
		currentHeight = Math.floor(currentHeight * ratio);
		compressedFile = await compress(currentWidth, currentHeight, quality);
	}

	// 第二步：如果文件仍然过大，先尝试质量压缩
	while (compressedFile.size > maxSize && quality >= options.minQuality) {
		quality -= 0.1;
		compressedFile = await compress(currentWidth, currentHeight, quality);
	}

	// 第三步：如果质量压缩到最小值仍然过大，则继续缩小尺寸
	while (compressedFile.size > maxSize && currentWidth > 100 && currentHeight > 100) {
		currentWidth = Math.floor(currentWidth * options.scaleRatio);
		currentHeight = Math.floor(currentHeight * options.scaleRatio);
		quality = options.initialQuality; // 重置质量
		compressedFile = await compress(currentWidth, currentHeight, quality);
	}

	return compressedFile;
}

// 配置
const editorConfig = computed(() => {
	console.log('配置');
	const config: Partial<IEditorConfig> = {
		placeholder: t('请输入'),
		MENU_CONF: {},
		EXTEND_CONF: {
			image: {
				parseImageSrc: (src: string) => src
			}
		}
	};

	// 图片上传配置
	if (props.isSpace) {
		config.MENU_CONF!.uploadImage = {
			customBrowseAndUpload: (fn: any) => {
				temp.insertFn = fn;
				refs.image.open();
			}
		};
	} else {
		// 直接上传
		config.MENU_CONF!.uploadImage = {
			customUpload: async (file: File, fn: any) => {
				temp.insertFn = fn;
				try {
					// 压缩图片，设置最大大小为200KB

					const compressedFile = await compressImage(file, 200 * 1024);
					console.log('压缩图片', compressedFile);

					//	file.name = fileName.split('.')[0] + '_' + Date.now() + '.' + fileName.split('.')[1];
					const res = await toUpload(compressedFile, {}, props.filePath);
					console.log('上传结果', res);
					if (res.url && temp.insertFn) {
						temp.insertFn(res.url);
					}
				} catch (err) {
					console.error('上传失败:', err);
				}
			}
		};
	}

	return config;
});

// 创建后
function onCreated(editor: any) {
	Editor.value = editor;
	onDisabled();

	// 确保内容被正确设置
	if (props.modelValue) {
		editor.setHtml(props.modelValue);
	}
	console.log('创建后');
	// 添加图片加载事件监听
	editor.on('image-uploaded', () => {
		editor.updateView();
	});
}

// 聚焦
function onFocus(editor: any) {
	emit('focus', editor);
	console.log('toolbarConfig', editor.getMenuConfig(), editor.getConfig());
}

// 失焦
function onBlur(editor: any) {
	emit('blur', editor);
}

// 值改变
function onChange() {
	if (value.value == '<p><br></p>') {
		value.value = '';
	}

	// 确保图片显示
	const imgs = refs.editor?.querySelectorAll('img');
	if (imgs?.length) {
		imgs.forEach((img: HTMLImageElement) => {
			if (!img.complete) {
				img.onload = () => {
					Editor.value?.updateView();
				};
			}
		});
	}

	emit('update:modelValue', value.value);
	emit('change', value.value);
}

// 文件选择
function onFileConfirm(files: any[]) {
	if (!isArray(files)) {
		files = [files];
	}

	if (files.length > 0) {
		files.forEach(file => {
			if (temp.insertFn) {
				temp.insertFn(file.url);
			}
		});
	}
}

// 禁用
function onDisabled() {
	if (props.disabled || props.preview) {
		Editor.value?.disable();
	} else {
		Editor.value?.enable();
	}
}

// 监听禁用状态
watch(() => [props.disabled, props.preview], onDisabled);

// 销毁
onBeforeUnmount(() => {
	const editor = Editor.value;
	if (editor == null) return;
	editor.destroy();
});
</script>

<style lang="scss" scoped>
.cl-editor-wang {
	border: 1px solid var(--el-border-color);
	box-sizing: border-box;
	line-height: normal;

	:deep(.w-e-toolbar) {
		border-bottom: 1px solid var(--el-border-color);
	}

	:deep(.editor-content) {
		.w-e-text-container {
			img {
				max-width: 100%;
				height: auto;
				display: block;
			}
		}
	}

	:deep(input) {
		font-size: 12px;
	}

	:deep(button) {
		font-size: 12px;

		&::before {
			font-size: 12px;
		}
	}

	.upload-inner {
		visibility: hidden;
		position: absolute;
		left: 0;
		top: 0;
	}

	&.disabled {
		:deep(.w-e-text-container) {
			background-color: var(--el-disabled-bg-color);
		}
	}

	&.w-e-full-screen-container {
		z-index: 999;
	}
}
</style>
