// @ts-nocheck
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import ExcelJS from 'exceljs';
interface EnumInfo {
	values: string[];
	codes: string[];
}
interface ColumnInfo {
	header: string;
	key: string;
	width: number;
	format?: string;
	enum?: EnumInfo;
	hidden?: boolean;
}
function generateArray(table) {
	const out = [];
	const rows = table.querySelectorAll('tr');
	const ranges = [];
	for (let R = 0; R < rows.length; ++R) {
		const outRow = [];
		const row = rows[R];
		const columns = row.querySelectorAll('td');
		for (let C = 0; C < columns.length; ++C) {
			const cell = columns[C];
			let colspan = cell.getAttribute('colspan');
			let rowspan = cell.getAttribute('rowspan');
			let cellValue = cell.innerText;
			if (cellValue !== '' && cellValue == +cellValue) cellValue = +cellValue;

			//Skip ranges
			ranges.forEach(function (range) {
				if (
					R >= range.s.r &&
					R <= range.e.r &&
					outRow.length >= range.s.c &&
					outRow.length <= range.e.c
				) {
					for (let i = 0; i <= range.e.c - range.s.c; ++i) outRow.push(null);
				}
			});

			//Handle Row Span
			if (rowspan || colspan) {
				rowspan = rowspan || 1;
				colspan = colspan || 1;
				ranges.push({
					s: {
						r: R,
						c: outRow.length
					},
					e: {
						r: R + rowspan - 1,
						c: outRow.length + colspan - 1
					}
				});
			}

			//Handle Value
			outRow.push(cellValue !== '' ? cellValue : null);

			//Handle Colspan
			if (colspan) for (let k = 0; k < colspan - 1; ++k) outRow.push(null);
		}
		out.push(outRow);
	}
	return [out, ranges];
}

function datenum(v, date1904) {
	if (date1904) v += 1462;
	const epoch = Date.parse(v);
	return (epoch - new Date(Date.UTC(1899, 11, 30))) / (24 * 60 * 60 * 1000);
}

function sheet_from_array_of_arrays(data, opts) {
	const ws = {};
	const range = {
		s: {
			c: 10000000,
			r: 10000000
		},
		e: {
			c: 0,
			r: 0
		}
	};
	for (let R = 0; R != data.length; ++R) {
		for (let C = 0; C != data[R].length; ++C) {
			if (range.s.r > R) range.s.r = R;
			if (range.s.c > C) range.s.c = C;
			if (range.e.r < R) range.e.r = R;
			if (range.e.c < C) range.e.c = C;
			const cell = {
				v: data[R][C]
			};
			if (cell.v == null) continue;
			const cell_ref = XLSX.utils.encode_cell({
				c: C,
				r: R
			});

			// 修改这里：无论原始类型是什么，都将单元格类型设置为 "s"
			cell.t = 's';

			ws[cell_ref] = cell;
		}
	}
	if (range.s.c < 10000000) ws['!ref'] = XLSX.utils.encode_range(range);
	return ws;
}

function Workbook() {
	if (!(this instanceof Workbook)) return new Workbook();
	this.SheetNames = [];
	this.Sheets = {};
}

function s2ab(s) {
	const buf = new ArrayBuffer(s.length);
	const view = new Uint8Array(buf);
	for (let i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
	return buf;
}

export function export_table_to_excel(id) {
	const theTable = document.getElementById(id);
	const oo = generateArray(theTable);
	const ranges = oo[1];

	/* original data */
	const data = oo[0];
	const ws_name = 'SheetJS';

	const wb = new Workbook(),
		ws = sheet_from_array_of_arrays(data);

	/* add ranges to worksheet */
	// ws['!cols'] = ['apple', 'banan'];
	ws['!merges'] = ranges;

	/* add worksheet to workbook */
	wb.SheetNames.push(ws_name);
	wb.Sheets[ws_name] = ws;

	const wbout = XLSX.write(wb, {
		bookType: 'xlsx',
		bookSST: false,
		type: 'binary'
	});

	saveAs(
		new Blob([s2ab(wbout)], {
			type: 'application/octet-stream'
		}),
		'test.xlsx'
	);
}

export function export_json_to_excel({
	multiHeader = [] as string[],
	header = [] as string[],
	data = [] as any[],
	filename = '' as string,
	merges = [],
	autoWidth = true,
	bookType = 'xlsx',
	enumMap,
	codeMap
} = {}) {
	/* original data */
	filename = filename || 'excel-list';
	data = [...data];
	data.unshift(header);

	for (let i = multiHeader.length - 1; i > -1; i--) {
		data.unshift(multiHeader[i]);
	}
	const ws_name = 'SheetJS';
	const wb = new Workbook(),
		ws = sheet_from_array_of_arrays(data);

	if (merges.length > 0) {
		if (!ws['!merges']) ws['!merges'] = [];
		merges.forEach(item => {
			ws['!merges'].push(XLSX.utils.decode_range(item));
		});
	}

	if (autoWidth) {
		/*设置worksheet每列的最大宽度*/
		const colWidth = data.map(row =>
			row.map(val => {
				/*先判断是否为null/undefined*/
				if (val == null) {
					return {
						wch: 10
					};
				} else if (val.toString().charCodeAt(0) > 255) {
					/*再判断是否为中文*/
					return {
						wch: val.toString().length * 2
					};
				} else {
					return {
						wch: val.toString().length
					};
				}
			})
		);
		/*以第一行为初始值*/
		const result = colWidth[0];
		for (let i = 1; i < colWidth.length; i++) {
			for (let j = 0; j < colWidth[i].length; j++) {
				if (result[j]['wch'] < colWidth[i][j]['wch']) {
					result[j]['wch'] = colWidth[i][j]['wch'];
				}
			}
		}
		ws['!cols'] = result;
	}
	const enumList = [];
	for (const key in enumMap) {
		const values = Object.values(enumMap[key]);
		const enumValidation = {
			type: 'list',
			allowBlank: true,
			showInputMessage: true,
			showErrorMessage: true,
			formula1: `"${values.join(',')}"`
		};
		const range = { s: { r: 1, c: Number(key) }, e: { r: data.length + 200, c: Number(key) } };
		enumList.push({ range, rule: enumValidation });
	}

	// 将数据验证规则应用到 Worksheet
	enumList.forEach(item => {
		const { range, rule } = item;
		const encodedRange = XLSX.utils.encode_range(range);
		if (!ws['!dataValidation']) {
			ws['!dataValidation'] = [];
		}
		ws['!dataValidation'].push({
			sqref: encodedRange,
			...rule
		});
	});
	//	const enumValues = Object.values(enumMap);
	// const enumValidation = {
	//   type: 'list',
	//   values: enumValues
	// };

	// 设置数据验证范围（假设status列从第二行到最后一行）
	//const range = { s: { r: 1, c: 2 }, e: { r: data.length+5, c: 2 } }; // status列的范围
	// console.log('enumList', enumList);
	//ws['!dataValidation'] = enumList;

	// 将Worksheet添加到Workbook中
	//XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
	/* add worksheet to workbook */
	wb.SheetNames.push(ws_name);
	wb.Sheets[ws_name] = ws;

	const wbout = XLSX.write(wb, {
		bookType: bookType,
		bookSST: false,
		type: 'binary'
	});
	saveAs(
		new Blob([s2ab(wbout)], {
			type: 'application/octet-stream'
		}),
		`${filename}.${bookType}`
	);
}
async function createExcelTemplate(columns: ColumnInfo[], data, filename) {
	const workbook = new ExcelJS.Workbook();
	const worksheet = workbook.addWorksheet('Sheet1');

	// 创建一个隐藏的工作表来存储所有枚举选项
	const hiddenSheet = workbook.addWorksheet('Hidden');
	hiddenSheet.state = 'hidden';
	let hiddenSheetRow = 1;

	// 定义列
	worksheet.columns = columns.flatMap(col => {
		const baseColumn = {
			header: col.header,
			key: col.key,
			width: col.width,
			hidden: col.hidden,
			style: {} // 添加样式对象
		};

		if (col.format) {
			baseColumn.style.numFmt = col.format; // 设置数字格式
		}

		if (col.enum) {
			if (col.isProject) {
				return [
					baseColumn,
					{
						header: `项目代码`,
						key: `projectCode`,
						width: col.width,
						hidden: false
					}
				];
			} else {
				return [
					baseColumn,
					{
						header: `${col.header}代码`,
						key: `${col.header}代码`,
						width: col.width,
						hidden: true
					}
				];
			}
		}

		return [baseColumn];
	});

	// 设置单元格格式和枚举选项
	let currentColIndex = 1;
	if (data && data.length > 0) {
		worksheet.addRows(data);
	}

	columns.forEach(col => {
		const colLetter = String.fromCharCode(64 + currentColIndex);

		// 添加列头备注
		const headerCell = worksheet.getCell(`${colLetter}1`);
		let comment = '';
		if (col.format) {
			comment += `格式: ${col.format}\n`;
		}
		if (col.enum) {
			comment += '请从下拉列表中选择一个选项';
		}
		if (comment) {
			headerCell.note = comment.trim();
		}

		if (col.enum) {
			const codeColLetter = String.fromCharCode(65 + currentColIndex);

			// 在隐藏的工作表中添加枚举选项
			const enumStartRow = hiddenSheetRow;
			col.enum.values.forEach((option, i) => {
				hiddenSheet.getCell(`A${hiddenSheetRow}`).value = option;
				hiddenSheet.getCell(`B${hiddenSheetRow}`).value = col.enum!.codes[i];
				hiddenSheetRow++;
			});
			const maxRow = data.length + 1000;
			// 为枚举列设置数据验证
			worksheet.dataValidations.add(`${colLetter}2:${colLetter}${maxRow}`, {
				type: 'list',
				allowBlank: true,
				showErrorMessage: true,
				errorStyle: 'error',
				error: '请从列表中选择一个有效选项',
				showInputMessage: true,
				promptTitle: '枚举选择',
				prompt: '请从下拉列表中选择一个选项',
				formulae: [`'Hidden'!$A$${enumStartRow}:$A$${hiddenSheetRow - 1}`]
			});

			// 为枚举代码列设置公式
			worksheet.getCell(`${codeColLetter}2`).value = {
				formula: `IFERROR(VLOOKUP(${colLetter}2,Hidden!$A$${enumStartRow}:$B$${
					hiddenSheetRow - 1
				},2,FALSE),"")`
			};

			for (let i = 2; i <= maxRow; i++) {
				worksheet.getCell(`${codeColLetter}${i}`).value = {
					formula: `IFERROR(VLOOKUP(${colLetter}${i},Hidden!$A$${enumStartRow}:$B$${
						hiddenSheetRow - 1
					},2,FALSE),"")`
				};
			}

			currentColIndex += 2;
		} else {
			currentColIndex++;
		}
	});
	if (!filename) {
		filename = 'template';
	}
	// 修改这里：使用 Blob URL 来下载文件
	const buffer = await workbook.xlsx.writeBuffer();
	const blob = new Blob([buffer], {
		type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	});
	const url = window.URL.createObjectURL(blob);

	const isInIframe = window !== window.parent;
	const doc = isInIframe ? window.parent.document : document;
	const a = doc.createElement('a');
	a.href = url;
	a.download = `${filename}.xlsx`;
	a.click();

	// 清理 Blob URL
	window.URL.revokeObjectURL(url);

	console.log('Excel模板已创建');
}

export async function exportExcel(columnList, data, filename) {
	const columns: ColumnInfo[] = [];
	columnList.map(item => {
		if (item.type == 'number') {
			if (item.isPercent) {
				const percentFormat = getFormatString(
					item.decimals - 2 > 0 ? item.decimals - 2 : 0
				);
				columns.push({
					header: item.name,
					key: item.name,
					width: 15,
					//format: "0.00%"
					format: percentFormat
				});
			} else if (item.decimals && Number(item.decimals) > 0) {
				const numFmt = `#,##0.${'0'.repeat(Number(item.decimals))}`;
				columns.push({
					header: item.name,
					key: item.name,
					width: 15,
					format: numFmt
				});
			} else {
				columns.push({
					header: item.name,
					key: item.name,
					width: 15,
					format: '#,##0'
				});
			}
		} else if (item.type == 'date') {
			if (item.dateType == 'YYYY-MM-DD HH:mm:ss') {
				columns.push({
					header: item.name,
					key: item.name,
					width: 20,
					format: 'yyyy-mm-dd hh:mm:ss'
				});
			} else if (item.dateType == 'YYYY-MM') {
				columns.push({
					header: item.name,
					key: item.name,
					width: 20,
					format: 'yyyy-mm'
				});
			} else if (item.dateType == 'YYYY') {
				columns.push({
					header: item.name,
					key: item.name,
					width: 20,
					format: 'yyyy'
				});
			} else {
				columns.push({
					header: item.name,
					key: item.name,
					width: 20,
					format: 'yyyy-mm-dd'
				});
			}
		} else if (item.type == 'dict') {
			if (item.isProject) {
				columns.push({
					header: item.name,
					key: item.name,
					width: 15,
					isProject: true,
					enum: {
						values: item.values,
						codes: item.codes
					}
				});
			} else {
				columns.push({
					header: item.name,
					key: item.name,
					width: 15,
					enum: {
						values: item.values,
						codes: item.codes
					}
				});
			}
		} else {
			columns.push({
				header: item.name,
				key: item.name,
				width: 15
			});
		}
	});
	console.log('最后生成', columns);
	const processedData = data.map(row => {
		const newRow = {};
		for (const key in row) {
			if (row[key] === null || row[key] === 'undefined') {
				newRow[key] = ''; // 将null和"undefined"转换为空字符串
			} else if (
				key === '税率' ||
				columnList.find(col => col.name === key && col.isPercent)
			) {
				newRow[key] = row[key] / 100; // 将百分比转换为小数
			} else {
				newRow[key] = row[key];
			}
		}
		return newRow;
	});

	console.log('处理后的数据', processedData);
	await createExcelTemplate(columns, processedData, filename);
	return columns;
}
function getFormatString(decimalPlaces) {
	if (decimalPlaces === 0) {
		return '0%'; // 如果小数位数为0，只显示整数部分
	}
	return `0.${'0'.repeat(decimalPlaces)}%`;
}
