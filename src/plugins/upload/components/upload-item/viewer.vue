<template>
	<div class="viewer-image">
		<!-- 图片 -->
		<el-image-viewer
			v-if="img.visible"
			:url-list="[img.url]"
			infinite
			teleported
			@close="close"
		/>
	</div>

	<!-- 文档 -->
	<cl-dialog
		v-model="doc.visible"
		:title="$t('文档预览')"
		width="80%"
		:scrollbar="false"
		top="5vh"
	>
		<div v-loading="doc.loading" class="viewer-doc">
			<div v-if="isPdfFile" class="pdf-container">
				<div class="pdf-toolbar">
					<el-button-group>
						<el-button @click="changePage(-1)" :disabled="currentPage <= 1"
							>上一页</el-button
						>
						<el-button>{{ currentPage }} / {{ totalPages }}</el-button>
						<el-button @click="changePage(1)" :disabled="currentPage >= totalPages"
							>下一页</el-button
						>
					</el-button-group>
				</div>
				<div class="pdf-viewer" ref="pdfContainer">
					<canvas ref="pdfCanvas"></canvas>
				</div>
			</div>
			<iframe v-else :src="doc.url" />
		</div>
	</cl-dialog>
</template>

<script lang="ts" setup>
defineOptions({
	name: 'file-viewer'
});

import { reactive, nextTick, ref, watch, toRaw } from 'vue';
import { getType } from '../../utils';
import { useCool } from '/@/cool';
import * as pdfjsLib from 'pdfjs-dist';
import workerUrl from 'pdfjs-dist/build/pdf.worker.min.mjs?url';

// 设置 worker
pdfjsLib.GlobalWorkerOptions.workerSrc = workerUrl;

const { refs, setRefs } = useCool();

// 状态变量
const currentPage = ref(1);
const totalPages = ref(0);
const scale = ref(1.0);
const pdfDoc = ref<any>(null);
const isPdfFile = ref(false);
const pdfContainer = ref<HTMLElement | null>(null);
const pdfCanvas = ref<HTMLCanvasElement | null>(null);
const renderTasks = ref<any[]>([]); // 存储渲染任务

// 图片预览
const img = reactive({
	visible: false,
	url: ''
});

// 文档预览
const doc = reactive({
	visible: false,
	loading: false,
	url: ''
});

// 清理渲染任务
function cancelRenderTasks() {
	renderTasks.value.forEach(task => {
		try {
			task.cancel();
		} catch (e) {
			console.warn('取消渲染任务失败:', e);
		}
	});
	renderTasks.value = [];
}

// 销毁 PDF 文档
function destroyPdf() {
	cancelRenderTasks();

	if (pdfDoc.value) {
		try {
			// 使用 toRaw 获取原始对象
			const rawPdfDoc = toRaw(pdfDoc.value);

			// 检查 destroy 方法是否存在
			if (typeof rawPdfDoc.destroy === 'function') {
				rawPdfDoc.destroy();
			} else if (typeof rawPdfDoc.cleanup === 'function') {
				// 某些版本可能使用 cleanup
				rawPdfDoc.cleanup();
			}
		} catch (e) {
			console.warn('PDF销毁时发生错误:', e);
		} finally {
			// 清理相关状态
			pdfDoc.value = null;
			currentPage.value = 1;
			totalPages.value = 0;
			scale.value = 1.0;
		}
	}
}

// 渲染 PDF 页面
async function renderPage(pageNum: number) {
	if (!pdfDoc.value || !pdfCanvas.value) return;

	try {
		// 取消之前的渲染任务
		cancelRenderTasks();

		// 获取页面
		const page = await toRaw(pdfDoc.value).getPage(pageNum);

		// 根据当前缩放比例创建视口
		const viewport = page.getViewport({ scale: scale.value });
		const canvas = pdfCanvas.value;
		const context = canvas.getContext('2d');

		if (!context) {
			throw new Error('无法获取canvas上下文');
		}

		// 更新 canvas 尺寸以适应缩放后的大小
		canvas.height = viewport.height;
		canvas.width = viewport.width;

		// 清除之前的内容
		context.clearRect(0, 0, canvas.width, canvas.height);

		// 创建新的渲染任务
		const renderTask = page.render({
			canvasContext: context,
			viewport,
			intent: 'display',
			annotationMode: 0
		});

		// 保存渲染任务以便后续可以取消
		renderTasks.value.push(renderTask);

		// 等待渲染完成
		await renderTask.promise;
	} catch (error) {
		console.error('渲染 PDF 页面失败:', error);
		throw error;
	}
}

// 页面切换
async function changePage(delta: number) {
	const newPage = currentPage.value + delta;
	if (newPage >= 1 && newPage <= totalPages.value) {
		currentPage.value = newPage;
		await renderPage(currentPage.value);
	}
}

// 缩放控制
async function zoomIn() {
	if (pdfDoc.value) {
		scale.value = Math.min(scale.value * 1.2, 3.0);
		try {
			await renderPage(currentPage.value);
		} catch (error) {
			console.error('放大失败:', error);
		}
	}
}

async function zoomOut() {
	if (pdfDoc.value) {
		scale.value = Math.max(scale.value * 0.8, 0.5);
		try {
			await renderPage(currentPage.value);
		} catch (error) {
			console.error('缩小失败:', error);
		}
	}
}

// 加载 PDF 文档
async function loadPdf(url: string) {
	try {
		doc.loading = true;
		isPdfFile.value = true;
		console.log('loadPdf', url);
		// 清理之前的 PDF 文档
		destroyPdf();

		// 加载新文档，添加更多配置选项
		const loadingTask = pdfjsLib.getDocument({
			url,
			withCredentials: true,
			cMapUrl: '/cmaps/',
			cMapPacked: true,
			enableXfa: true,
			useSystemFonts: true,
			standardFontDataUrl: '/standard_fonts/',
			disableFontFace: false,
			verbosity: 0,
			isEvalSupported: true,
			useWorkerFetch: true,
			stopAtErrors: false,
			rangeChunkSize: 65536
		});

		// 添加加载进度处理
		loadingTask.onProgress = (data: { loaded: number; total: number }) => {
			if (data.total && data.loaded) {
				const progress = Math.min((data.loaded / data.total) * 100, 100);
				console.log(`Loading: ${Math.round(progress)}%`);
			}
		};

		const pdf = await loadingTask.promise;

		// 确保在设置 pdfDoc.value 之前进行验证
		if (!pdf || typeof pdf.numPages !== 'number') {
			throw new Error('无效的 PDF 文档');
		}

		pdfDoc.value = pdf;
		totalPages.value = pdf.numPages;
		currentPage.value = 1;

		await nextTick();
		if (totalPages.value > 0) {
			await renderPage(currentPage.value);
		} else {
			throw new Error('PDF 文档页数为0');
		}
	} catch (error) {
		console.error('加载 PDF 失败:', error);
		isPdfFile.value = false;
		// 确保在错误时清理资源
		destroyPdf();
	} finally {
		doc.loading = false;
	}
}

// 打开预览
async function open(item: Upload.Item) {
	console.log('打开预览', item);
	if (item?.type) {
		const url = item.url || '';
		const type = getType(url);

		if (type == 'image') {
			img.visible = true;
			img.url = url;
			return true;
		}

		if (['word', 'excel', 'ppt', 'pdf'].includes(type)) {
			doc.visible = true;
			doc.url = url;
			isPdfFile.value = type === 'pdf';

			if (isPdfFile.value) {
				await loadPdf(url);
			}

			return true;
		}

		window.open(item.url);
	}
}

// 关闭
function close() {
	img.visible = false;
}

// 监听对话框关闭
watch(
	() => doc.visible,
	visible => {
		if (!visible) {
			destroyPdf();
		}
	}
);

defineExpose({
	open
});
</script>

<style lang="scss" scoped>
.viewer-image {
	position: absolute;
}

.viewer-doc {
	height: calc(100vh - 100px);
	width: 100%;
	display: flex;
	flex-direction: column;

	.pdf-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		height: 100%;
		overflow: hidden;

		.pdf-toolbar {
			padding: 10px;
			display: flex;
			gap: 10px;
			justify-content: center;
			border-bottom: 1px solid #eee;
			flex-shrink: 0;
		}

		.pdf-viewer {
			flex: 1;
			overflow: auto;
			padding: 20px;
			display: flex;
			justify-content: center;
			align-items: flex-start;
			min-height: 0;

			canvas {
				box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
				max-width: 100%;
				height: auto;
			}
		}
	}

	iframe {
		border: 0;
		height: 100%;
		width: 100%;
	}
}
</style>
