import { ElMessage } from 'element-plus';
import { module, service } from '/@/cool';
import { extname, filename, uuid } from '/@/cool/utils';
import { pathJoin } from '../utils';
import { useBase } from '/$/base';
import { type AxiosProgressEvent } from 'axios';
import { merge } from 'lodash-es';
import { useI18n } from 'vue-i18n';

export function useUpload() {
	const { options } = module.get('upload');
	const { user } = useBase();
	const { t } = useI18n();

	// 上传
	async function toUpload(file: File, opts: Upload.Options = {}, filePath1?): Upload.Response {
		return new Promise((resolve, reject) => {
			const executor = async () => {
				// 合并配置
				const { prefixPath, onProgress } = merge({}, options, opts);

				// 文件id
				const fileId = uuid('');

				try {
					// 上传模式、类型
					const { mode, type } = await service.base.comm.uploadMode();

					// 本地上传
					const isLocal = mode == 'local';
					let textName = file.name;
					let textDate;
					if (file.name.includes('合同函证影像')) {
						textName = file.name.split('&')[0];
						textDate = file.name.split('&')[1];
					}

					// 文件名
					const fileName = textName.includes('合同函证影像')
						? textName
						: uuid() + '_' + textName;
					const date1 = getDate();

					// Key
					let filePath = file.name.includes('合同函证影像')
						? '询证函回函/' + textDate
						: 'luru/' + date1;
					//let key = isLocal ? fileName : pathJoin(prefixPath!, fileName);
					let key = file.name.includes('合同函证影像')
						? '询证函回函/' + textDate + '/' + fileName
						: 'luru/' + date1 + '/' + fileName;
					console.log('key11111', key);
					if (filePath1) {
						key = filePath1 + '/' + fileName;
						filePath = filePath1;
					}
					console.log('filePath1', filePath1, 'key', key, filePath);
					// 多种上传请求
					const next = async ({ host, preview, data }: Upload.Request) => {
						const fd = new FormData();

						// key
						fd.append('key', key);

						// 签名数据
						for (const i in data) {
							if (!fd.has(i)) {
								fd.append(i, data[i]);
							}
						}

						// 文件
						fd.append('file', file);

						// 上传进度
						let progress = 0;

						const reqData = {
							url: host,
							method: 'POST',
							headers: {
								'Content-Type': 'multipart/form-data',
								Authorization: isLocal ? user.token : null,
								language: null
							},
							timeout: 600000,
							data: fd as any,
							onUploadProgress(e: AxiosProgressEvent) {
								progress = e.total ? Math.floor((e.loaded / e.total) * 100) : 0;
								onProgress?.(progress);
							},
							proxy: isLocal
						};

						if (type == 'minio') {
							reqData.headers['Content-Type'] = file.type;
							reqData.method = 'PUT';
							reqData.data = file;
						}

						// 上传
						await service
							.request({
								url: host,
								method: 'POST',
								headers: {
									'Content-Type': 'multipart/form-data',
									Authorization: isLocal ? user.token : null
								},
								timeout: 600000,
								data: fd,
								onUploadProgress(e: AxiosProgressEvent) {
									progress = e.total ? Math.floor((e.loaded / e.total) * 100) : 0;
									onProgress?.(progress);
								},
								proxy: isLocal,
								NProgress: false
							})
							.then(res => {
								if (progress != 100) {
									onProgress?.(100);
								}

								key = encodeURIComponent(key);

								let url = '';

								if (isLocal) {
									console.log('isLocal', res);
									url = res;
								} else {
									console.log('host', host);
									const url1 = `${host}/${filePath}/${fileName}`;
									url = url1;
									console.log('item.url ', url);
									//url = pathJoin(preview || host, key);
								}

								resolve({
									key,
									url,
									fileId
								});
							})
							.catch(err => {
								ElMessage.error(err.message);
								reject(err);
							});
					};

					if (isLocal) {
						next({
							host: 'admin/base/comm/upload'
						});
					} else {
						service.base.comm
							.upload(
								type == 'aws'
									? {
											key
										}
									: {}
							)
							.then(res => {
								switch (type) {
									// 腾讯
									case 'cos':
										next({
											host: res.url,
											data: res.credentials
										});
										break;
									// 阿里
									case 'oss':
										next({
											host: res.host,
											preview: res.publicDomain,
											data: {
												OSSAccessKeyId: res.OSSAccessKeyId,
												policy: res.policy,
												signature: res.signature
											}
										});
										break;
									// 七牛
									case 'qiniu':
										next({
											host: res.uploadUrl,
											preview: res.publicDomain,
											data: {
												token: res.token
											}
										});
										break;
									// aws
									case 'aws':
										next({
											host: res.url,
											data: res.fields
										});
										break;

									default:
										next({
											host: res.url,
											preview: res.previewUrl
										});
										break;
								}
							})
							.catch(reject);
					}
				} catch (err) {
					ElMessage.error(t('文件上传失败'));
					console.error('[upload]', err);
					reject(err);
				}
			};

			executor();
		});
	}

	return {
		options,
		toUpload
	};
}
function getDate() {
	const currentDate = new Date();
	const year = currentDate.getFullYear();
	const month = String(currentDate.getMonth() + 1).padStart(2, '0');
	const day = String(currentDate.getDate()).padStart(2, '0');
	const formattedDate = `${year}-${month}-${day}`;
	return formattedDate;
}
